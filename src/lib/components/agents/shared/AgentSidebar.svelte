<script lang="ts">
  import { page } from "$app/stores"
  import { onMount } from "svelte"
  import {
    Search,
    Grid3X3,
    Settings,
    Menu,
    X,
    LogOut,
    ChevronDown,
  } from "lucide-svelte"

  // Navigation item interface
  interface NavigationItem {
    id: string
    label: string
    icon: any
    active?: boolean
    badge?: string
    href?: string
    onClick?: () => void
  }

  // Updated for Svelte 5 compatibility
  let {
    collapsed = $bindable(false),
    isMobile = false,
    session = null,
    profile = null,
    agentType = "research",
    navigationItems = [],
    brandName = "Robynn AI",
    searchPlaceholder = "Search"
  }: {
    collapsed?: boolean
    isMobile?: boolean
    session?: any
    profile?: any
    agentType?: string
    navigationItems?: NavigationItem[]
    brandName?: string
    searchPlaceholder?: string
  } = $props()

  // Profile menu state
  let showProfileMenu = $state(false)
  let profileMenuElement: HTMLElement

  // Toggle function
  function toggleSidebar() {
    collapsed = !collapsed
  }

  // Profile menu toggle
  function toggleProfileMenu() {
    showProfileMenu = !showProfileMenu
  }

  // Close profile menu when clicking outside
  function handleClickOutside(event: MouseEvent) {
    if (
      profileMenuElement &&
      !profileMenuElement.contains(event.target as Node)
    ) {
      showProfileMenu = false
    }
  }

  // Get user display data
  let user = $derived({
    name:
      profile?.full_name ||
      session?.user?.user_metadata?.full_name ||
      session?.user?.email?.split("@")[0] ||
      "User",
    email: session?.user?.email || "<EMAIL>",
    initials: getInitials(
      profile?.full_name ||
        session?.user?.user_metadata?.full_name ||
        session?.user?.email?.split("@")[0] ||
        "User",
    ),
  })

  function getInitials(name: string): string {
    return name
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase())
      .slice(0, 2)
      .join("")
  }

  // Handle sign out
  async function handleSignOut() {
    showProfileMenu = false
    try {
      window.location.href = "/sign_out"
    } catch (error) {
      console.error("Error signing out:", error)
    }
  }

  // Handle settings navigation
  function handleSettings() {
    showProfileMenu = false
    const envSlug = $page.params.envSlug
    window.location.href = `/dashboard/${envSlug}/settings`
  }

  // Handle navigation item click
  function handleNavClick(item: NavigationItem) {
    if (item.onClick) {
      item.onClick()
    } else if (item.href) {
      window.location.href = item.href
    }
  }

  onMount(() => {
    document.addEventListener("click", handleClickOutside)
    return () => {
      document.removeEventListener("click", handleClickOutside)
    }
  })
</script>

<!-- Left Sidebar -->
<aside
  class="bg-sidebar border-r border-sidebar-border flex flex-col transition-all duration-300 ease-in-out relative"
  class:w-64={!collapsed}
  class:w-16={collapsed && !isMobile}
  class:w-0={collapsed && isMobile}
  class:overflow-hidden={collapsed && isMobile}
>
  <!-- Toggle Button -->
  <button
    onclick={toggleSidebar}
    class="absolute -right-3 top-6 z-10 w-6 h-6 bg-background border border-border rounded-full flex items-center justify-center hover:bg-accent transition-colors"
    class:hidden={isMobile && collapsed}
  >
    {#if collapsed}
      <Menu class="w-3 h-3" />
    {:else}
      <X class="w-3 h-3" />
    {/if}
  </button>

  <!-- Header -->
  <header
    class="p-4 border-b border-sidebar-border"
    class:hidden={collapsed && isMobile}
  >
    <div class="flex items-center space-x-2 mb-4">
      <div
        class="w-6 h-6 bg-sidebar-primary rounded-sm flex items-center justify-center"
      >
        <Grid3X3 class="w-4 h-4 text-sidebar-primary-foreground" />
      </div>
      {#if !collapsed}
        <span class="font-semibold text-lg text-sidebar-foreground"
          >{brandName}</span
        >
      {/if}
    </div>

    <!-- Search -->
    {#if !collapsed}
      <div class="relative">
        <Search
          class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"
        />
        <input
          type="text"
          placeholder={searchPlaceholder}
          class="w-full pl-10 pr-8 py-2 bg-sidebar-accent rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sidebar-ring border border-sidebar-border text-sidebar-foreground placeholder:text-muted-foreground"
        />
        <kbd
          class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground"
          >⌘K</kbd
        >
      </div>
    {:else}
      <!-- Collapsed search icon -->
      <div class="flex justify-center">
        <button class="p-2 hover:bg-sidebar-accent rounded-lg">
          <Search class="w-4 h-4 text-muted-foreground" />
        </button>
      </div>
    {/if}
  </header>

  <!-- Navigation -->
  <nav class="flex-1 p-4">
    <div class="space-y-1">
      {#each navigationItems as item (item.id)}
        <button
          onclick={() => handleNavClick(item)}
          class="flex items-center w-full px-3 py-2 text-sm rounded-lg transition-colors"
          class:text-sidebar-primary-foreground={item.active}
          class:bg-sidebar-primary={item.active}
          class:text-sidebar-foreground={!item.active}
          class:hover:bg-sidebar-accent={!item.active}
          class:justify-center={collapsed}
          title={collapsed ? item.label : ""}
        >
          <svelte:component this={item.icon} class="w-4 h-4 {!collapsed ? 'mr-3' : ''}" />
          {#if !collapsed}
            {item.label}
            {#if item.badge}
              <span
                class="ml-auto bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full"
                >{item.badge}</span
              >
            {/if}
          {/if}
        </button>
      {/each}
    </div>
  </nav>

  <!-- Footer -->
  <footer
    class="p-4 border-t border-sidebar-border"
    class:hidden={collapsed && isMobile}
  >
    <!-- User Profile -->
    <div
      class="relative mt-4 pt-4 border-t border-sidebar-border"
      bind:this={profileMenuElement}
    >
      <button
        onclick={toggleProfileMenu}
        class="flex items-center w-full p-2 hover:bg-sidebar-accent rounded-lg transition-colors group"
        class:justify-center={collapsed}
        title={collapsed ? user.email : "Profile menu"}
      >
        <div
          class="w-8 h-8 bg-primary rounded-full flex items-center justify-center"
          class:mr-3={!collapsed}
        >
          <span class="text-sm font-medium text-primary-foreground"
            >{user.initials}</span
          >
        </div>
        {#if !collapsed}
          <div class="flex-1 min-w-0">
            <p
              class="text-sm font-medium text-sidebar-foreground truncate text-left"
            >
              {user.name}
            </p>
            <p class="text-xs text-muted-foreground truncate text-left">
              {user.email}
            </p>
          </div>
          <ChevronDown
            class="w-4 h-4 text-muted-foreground transition-transform duration-200 {showProfileMenu
              ? 'rotate-180'
              : ''}"
          />
        {/if}
      </button>

      <!-- Profile Menu Dropdown -->
      {#if showProfileMenu && !collapsed}
        <div
          class="absolute bottom-full left-0 right-0 mb-2 bg-background border border-border rounded-lg shadow-lg z-50 py-2 animate-slide-up"
          style="box-shadow: var(--shadow-lg);"
        >
          <button
            onclick={handleSettings}
            class="flex items-center w-full px-3 py-2 text-sm text-foreground hover:bg-accent transition-colors"
          >
            <Settings class="w-4 h-4 mr-3" />
            Settings
          </button>
          <button
            onclick={handleSignOut}
            class="flex items-center w-full px-3 py-2 text-sm text-foreground hover:bg-accent transition-colors"
          >
            <LogOut class="w-4 h-4 mr-3" />
            Sign Out
          </button>
        </div>
      {/if}
    </div>
  </footer>
</aside>

<style>
  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-slide-up {
    animation: slide-up 0.2s ease-out;
  }

  /* Enhanced hover effects */
  .group:hover .w-8 {
    transform: scale(1.05);
  }

  /* Smooth transitions */
  .w-8 {
    transition: transform 0.2s ease;
  }

  /* Focus states for accessibility */
  button:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }
</style>
