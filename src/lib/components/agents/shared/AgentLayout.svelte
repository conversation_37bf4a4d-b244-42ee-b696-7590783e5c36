<script lang="ts">
  import { onMount } from "svelte"
  import AgentSidebar from "./AgentSidebar.svelte"
  import ErrorBoundary from "./ErrorBoundary.svelte"

  // Navigation item interface
  interface NavigationItem {
    id: string
    label: string
    icon: any
    active?: boolean
    badge?: string
    href?: string
    onClick?: () => void
  }

  // Updated for Svelte 5 compatibility
  let {
    session = null,
    profile = null,
    agentType = "research",
    navigationItems = [],
    brandName = "Robynn AI",
    searchPlaceholder = "Search",
    leftSidebarCollapsed = $bindable(false),
    rightSidebarCollapsed = $bindable(false),
    showRightSidebar = true,
    rightSidebarWidth = "w-80",
    children,
    rightSidebar,
  }: {
    session?: any
    profile?: any
    agentType?: string
    navigationItems?: NavigationItem[]
    brandName?: string
    searchPlaceholder?: string
    leftSidebarCollapsed?: boolean
    rightSidebarCollapsed?: boolean
    showRightSidebar?: boolean
    rightSidebarWidth?: string
    children?: any
    rightSidebar?: any
  } = $props()

  // Responsive state
  let innerWidth = $state(0)
  let isMobile = $derived(innerWidth < 768)

  // Auto-collapse sidebars on mobile
  $effect(() => {
    if (isMobile) {
      leftSidebarCollapsed = true
      rightSidebarCollapsed = true
    }
  })

  onMount(() => {
    // Set initial window width
    innerWidth = window.innerWidth
  })
</script>

<!-- Window width binding for responsive behavior -->
<svelte:window bind:innerWidth />

<!-- Error Boundary Wrapper -->
<ErrorBoundary>
  <!-- Three-Column Layout -->
  <div class="flex h-screen bg-background text-foreground">
    <!-- Left Sidebar -->
    <AgentSidebar
      bind:collapsed={leftSidebarCollapsed}
      {isMobile}
      {session}
      {profile}
      {agentType}
      {navigationItems}
      {brandName}
      {searchPlaceholder}
    />

    <!-- Main Content Area -->
    <main class="flex-1 flex flex-col min-w-0">
      <!-- Main Content Slot -->
      <div class="flex-1 overflow-hidden">
        {@render children?.()}
      </div>
    </main>

    <!-- Right Sidebar -->
    {#if showRightSidebar}
      <aside
        class="bg-sidebar border-l border-sidebar-border flex flex-col transition-all duration-300 ease-in-out {rightSidebarWidth}"
        class:w-0={rightSidebarCollapsed}
        class:overflow-hidden={rightSidebarCollapsed}
      >
        <!-- Right Sidebar Content -->
        {@render rightSidebar?.()}
      </aside>
    {/if}
  </div>
</ErrorBoundary>

<style>
  /* Ensure proper layout constraints */
  main {
    min-width: 0; /* Allows flex item to shrink below content size */
  }

  /* Smooth transitions for sidebar animations */
  aside {
    transition: width 0.3s ease-in-out;
  }

  /* Mobile optimizations */
  @media (max-width: 767px) {
    .flex {
      overflow-x: hidden;
    }
  }

  /* Focus management for accessibility */
  :global(button:focus-visible) {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Ensure proper z-index stacking */
  aside {
    z-index: 10;
  }

  main {
    z-index: 1;
  }
</style>
