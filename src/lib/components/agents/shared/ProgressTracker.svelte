<script lang="ts">
  import { onMount } from 'svelte'
  import { CheckCircle2, Circle, Loader2, Clock } from 'lucide-svelte'

  // Updated for Svelte 5 compatibility
  let { 
    steps = [],
    currentProgress = 0,
    isVisible = false,
    title = "Progress"
  }: {
    steps?: any[]
    currentProgress?: number
    isVisible?: boolean
    title?: string
  } = $props()

  let progressElement: HTMLElement
  let animatedProgress = $state(0)

  // Animate progress bar using $effect
  $effect(() => {
    if (isVisible && currentProgress !== animatedProgress) {
      animateProgress(currentProgress)
    }
  })

  function animateProgress(targetProgress: number) {
    const startProgress = animatedProgress
    const duration = 800 // ms
    const startTime = Date.now()

    function animate() {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // Easing function for smooth animation
      const easeOutCubic = 1 - Math.pow(1 - progress, 3)
      
      animatedProgress = startProgress + (targetProgress - startProgress) * easeOutCubic
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        animatedProgress = targetProgress
      }
    }
    
    requestAnimationFrame(animate)
  }

  function getStepIcon(step: any, index: number) {
    if (step.status === 'completed') {
      return CheckCircle2
    } else if (step.status === 'pending' && index === steps.findIndex(s => s.status === 'pending')) {
      return Loader2
    } else {
      return Circle
    }
  }

  function getStepClass(step: any, index: number) {
    const baseClass = "flex items-center space-x-3 p-3 rounded-lg transition-all duration-500"
    
    if (step.status === 'completed') {
      return `${baseClass} bg-green-50 border border-green-200 text-green-800`
    } else if (step.status === 'pending' && index === steps.findIndex(s => s.status === 'pending')) {
      return `${baseClass} bg-blue-50 border border-blue-200 text-blue-800 animate-pulse`
    } else {
      return `${baseClass} bg-muted/30 border border-border text-muted-foreground`
    }
  }
</script>

{#if isVisible && steps.length > 0}
  <div 
    bind:this={progressElement}
    class="fixed top-4 right-4 w-80 bg-background border-2 border-border rounded-lg shadow-lg z-50 p-4 animate-slide-in"
    style="box-shadow: var(--shadow-lg);"
  >
    <!-- Header -->
    <div class="flex items-center justify-between mb-4">
      <h3 class="font-semibold text-foreground flex items-center gap-2">
        <Clock class="w-4 h-4" />
        {title}
      </h3>
      <span class="text-sm font-medium text-muted-foreground">
        {Math.round(animatedProgress)}%
      </span>
    </div>

    <!-- Progress Bar -->
    <div class="mb-4">
      <div class="w-full bg-muted rounded-full h-2 overflow-hidden">
        <div 
          class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out"
          style="width: {animatedProgress}%"
        ></div>
      </div>
    </div>

    <!-- Steps -->
    <div class="space-y-2 max-h-64 overflow-y-auto">
      {#each steps as step, index (step.id)}
        <div class={getStepClass(step, index)}>
          <svelte:component 
            this={getStepIcon(step, index)} 
            class="w-4 h-4 flex-shrink-0 {step.status === 'pending' && index === steps.findIndex(s => s.status === 'pending') ? 'animate-spin' : ''}"
          />
          <div class="flex-1 min-w-0">
            <p class="font-medium text-sm truncate">{step.title}</p>
            <p class="text-xs opacity-80 truncate">{step.description}</p>
          </div>
        </div>
      {/each}
    </div>
  </div>
{/if}

<style>
  @keyframes slide-in {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .animate-slide-in {
    animation: slide-in 0.3s ease-out;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }
</style>
