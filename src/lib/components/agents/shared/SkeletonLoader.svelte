<script lang="ts">
  // Updated for Svelte 5 compatibility
  let { 
    type = 'message',
    count = 1
  }: {
    type?: 'message' | 'sidebar' | 'progress' | 'project'
    count?: number
  } = $props()
</script>

<!-- Message Skeleton -->
{#if type === 'message'}
  {#each Array(count) as _, i}
    <div class="flex items-start space-x-4 animate-pulse">
      <div class="w-10 h-10 bg-muted rounded-full flex-shrink-0"></div>
      <div class="flex-1 space-y-3">
        <div class="flex items-center gap-2">
          <div class="h-4 bg-muted rounded w-16"></div>
          <div class="h-3 bg-muted rounded w-20"></div>
        </div>
        <div class="space-y-2">
          <div class="h-4 bg-muted rounded w-full"></div>
          <div class="h-4 bg-muted rounded w-4/5"></div>
          <div class="h-4 bg-muted rounded w-3/4"></div>
        </div>
      </div>
    </div>
  {/each}
{/if}

<!-- Sidebar Skeleton -->
{#if type === 'sidebar'}
  <div class="animate-pulse space-y-4">
    <!-- Header -->
    <div class="flex items-center space-x-2">
      <div class="w-6 h-6 bg-muted rounded"></div>
      <div class="h-5 bg-muted rounded w-24"></div>
    </div>
    
    <!-- Search -->
    <div class="h-10 bg-muted rounded-lg"></div>
    
    <!-- Navigation Items -->
    {#each Array(5) as _, i}
      <div class="flex items-center space-x-3">
        <div class="w-4 h-4 bg-muted rounded"></div>
        <div class="h-4 bg-muted rounded w-20"></div>
      </div>
    {/each}
  </div>
{/if}

<!-- Progress Skeleton -->
{#if type === 'progress'}
  <div class="animate-pulse space-y-3">
    <div class="flex items-center justify-between">
      <div class="h-4 bg-muted rounded w-32"></div>
      <div class="h-4 bg-muted rounded w-12"></div>
    </div>
    <div class="h-2 bg-muted rounded-full w-full"></div>
    <div class="space-y-2">
      {#each Array(3) as _, i}
        <div class="flex items-center space-x-2">
          <div class="w-4 h-4 bg-muted rounded-full"></div>
          <div class="h-3 bg-muted rounded w-40"></div>
        </div>
      {/each}
    </div>
  </div>
{/if}

<!-- Project Skeleton -->
{#if type === 'project'}
  {#each Array(count) as _, i}
    <div class="animate-pulse p-4 border border-border rounded-lg space-y-2">
      <div class="h-4 bg-muted rounded w-3/4"></div>
      <div class="h-3 bg-muted rounded w-full"></div>
      <div class="h-3 bg-muted rounded w-2/3"></div>
    </div>
  {/each}
{/if}

<style>
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
  
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
</style>
