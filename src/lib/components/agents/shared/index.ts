// Shared Agent Components
// Export all shared components for easy importing across agent pages

export { default as AgentLayout } from './AgentLayout.svelte'
export { default as AgentSidebar } from './AgentSidebar.svelte'
export { default as SkeletonLoader } from './SkeletonLoader.svelte'
export { default as ProgressTracker } from './ProgressTracker.svelte'
export { default as ErrorBoundary } from './ErrorBoundary.svelte'

// Type definitions for shared interfaces
export interface NavigationItem {
  id: string
  label: string
  icon: any
  active?: boolean
  badge?: string
  href?: string
  onClick?: () => void
}

export interface AgentLayoutProps {
  session?: any
  profile?: any
  agentType?: string
  navigationItems?: NavigationItem[]
  brandName?: string
  searchPlaceholder?: string
  leftSidebarCollapsed?: boolean
  rightSidebarCollapsed?: boolean
  showRightSidebar?: boolean
  rightSidebarWidth?: string
}

export interface ProgressStep {
  id: string
  title: string
  description: string
  status: 'pending' | 'completed' | 'error'
}
