<script lang="ts">
  import { writable } from "svelte/store"
  import { onMount } from "svelte"
  import {
    Search,
    Target,
    BarChart,
    Sparkles,
    BookOpen,
    TrendingUp,
  } from "lucide-svelte"

  // Import new shared components
  import { AgentLayout } from "$lib/components/agents/shared"
  import {
    SEOChatArea,
    SEOToolsSidebar,
    type NicheKeyword,
    type GapKeyword,
    type SEOProgressStep,
  } from "$lib/components/agents/seo"

  // Page data using Svelte 5 syntax
  let { data } = $props()

  // State variables using Svelte 5 syntax
  const messages = writable<
    Array<{
      id: string
      role: "user" | "assistant"
      content: string
      timestamp: Date
      isReport?: boolean
    }>
  >([])

  let isLoading = $state(false)
  let progressSteps = $state<SEOProgressStep[]>([])
  let currentProgress = $state(0)
  let nicheKeywords = $state<NicheKeyword[]>([])
  let gapKeywords = $state<GapKeyword[]>([])
  let clusterData = $state<any[]>([])
  let rightSidebarCollapsed = $state(false)
  let activeToolTab = $state("niche")

  // Responsive behavior - auto-collapse on mobile
  let innerWidth = $state(0)
  let isMobile = $derived(innerWidth < 768)

  $effect(() => {
    if (isMobile) {
      rightSidebarCollapsed = true
    }
  })

  // Navigation items for the left sidebar
  const navigationItems = [
    { id: "chat", label: "Chat", icon: Search, active: true },
    { id: "niche", label: "Niche Discovery", icon: Sparkles },
    { id: "gap", label: "Gap Analysis", icon: BarChart },
    { id: "cluster", label: "Content Clusters", icon: BookOpen },
  ]

  // Helper functions
  function generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  // Event handlers for the new architecture
  async function handleSendMessage(message: string, filters?: any) {
    if (!message.trim() || isLoading) return

    isLoading = true

    // Initialize progress steps
    progressSteps = [
      {
        id: 1,
        title: "Industry Research",
        description: "Analyzing your business niche...",
        status: "pending",
      },
      {
        id: 2,
        title: "Keyword Discovery",
        description: "Finding relevant keywords...",
        status: "pending",
      },
      {
        id: 3,
        title: "Volume Analysis",
        description: "Checking search volumes...",
        status: "pending",
      },
      {
        id: 4,
        title: "Competition Analysis",
        description: "Analyzing keyword difficulty...",
        status: "pending",
      },
      {
        id: 5,
        title: "Report Generation",
        description: "Creating your SEO strategy...",
        status: "pending",
      },
    ]
    currentProgress = 0

    // Add user message to chat
    messages.update((msgs) => [
      ...msgs,
      {
        id: generateId(),
        role: "user",
        content: message,
        timestamp: new Date(),
      },
    ])

    try {
      // Use streaming endpoint
      const response = await fetch(
        `/dashboard/${data.environment?.slug}/agent-seo?stream=true`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error("No response body")
      }

      let assistantMessage = {
        id: generateId(),
        role: "assistant" as const,
        content: "",
        timestamp: new Date(),
        isReport: true,
      }

      messages.update((msgs) => [...msgs, assistantMessage])

      // Read the stream
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split("\n")

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.content) {
                assistantMessage.content += data.content
                messages.update((msgs) => [
                  ...msgs.slice(0, -1),
                  { ...assistantMessage },
                ])
              }
              if (data.progress) {
                currentProgress = data.progress
                if (data.step) {
                  progressSteps = progressSteps.map((step) =>
                    step.id === data.step
                      ? { ...step, status: "active" }
                      : step,
                  )
                }
              }
            } catch (e) {
              console.error("Error parsing SSE data:", e)
            }
          }
        }
      }
    } catch (error) {
      console.error("Error sending message:", error)
      messages.update((msgs) => [
        ...msgs,
        {
          id: generateId(),
          role: "assistant",
          content:
            "I apologize, but I encountered an error while processing your request. Please try again.",
          timestamp: new Date(),
        },
      ])
    } finally {
      isLoading = false
      progressSteps = []
    }
  }

  // Handlers for SEO tools
  function handleNicheDiscovery(data: any) {
    console.log("Niche discovery:", data)
    // Implementation for niche discovery
  }

  function handleGapAnalysis(data: any) {
    console.log("Gap analysis:", data)
    // Implementation for gap analysis
  }

  function handleContentCluster(data: any) {
    console.log("Content cluster:", data)
    // Implementation for content cluster mapping
  }
</script>

<svelte:head>
  <title>SEO Agent - Robynn AI</title>
  <meta
    name="description"
    content="AI-powered SEO strategy and keyword research assistant"
  />
</svelte:head>

<!-- Window width binding for responsive behavior -->
<svelte:window bind:innerWidth />

<AgentLayout
  session={data.session}
  profile={data.profile}
  agentType="seo"
  {navigationItems}
  brandName="Robynn AI"
  searchPlaceholder="Search SEO tools..."
  {rightSidebarCollapsed}
  showRightSidebar={true}
  rightSidebarWidth="320px"
>
  <!-- Main Chat Area -->
  <SEOChatArea
    {messages}
    {isLoading}
    onSendMessage={handleSendMessage}
    {progressSteps}
    {currentProgress}
    envSlug={data.environment?.slug}
  />

  {#snippet rightSidebar()}
    <SEOToolsSidebar
      bind:collapsed={rightSidebarCollapsed}
      {isMobile}
      bind:activeTab={activeToolTab}
      onNicheDiscovery={handleNicheDiscovery}
      onGapAnalysis={handleGapAnalysis}
      onContentCluster={handleContentCluster}
      {isLoading}
      {nicheKeywords}
      {gapKeywords}
      {clusterData}
    />
  {/snippet}
</AgentLayout>
