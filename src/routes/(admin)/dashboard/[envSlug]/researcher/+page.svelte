<script lang="ts">
  import { writable } from "svelte/store"
  import { page } from "$app/stores"
  import { onMount } from "svelte"
  import { ChartBar, Target, Brain } from "lucide-svelte"

  // Import new components
  import ResearchSidebar from "$lib/components/research/ResearchSidebar.svelte"
  import ResearchChatArea from "$lib/components/research/ResearchChatArea.svelte"
  import ProjectsSidebar from "$lib/components/research/ProjectsSidebar.svelte"

  // Get data from layout
  let { data } = $props()
  let { session, profile } = data

  interface Message {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: Date
    isReport?: boolean
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: "pending" | "active" | "completed"
    progress?: number
  }

  const messages = writable<Message[]>([])
  let input = $state("")
  let isLoading = $state(false)
  // let selectedMessageId = "" // Not currently used
  let placeholderIndex = $state(0)
  let currentPlaceholder = $state("")
  let outputFormat = $state("executive")
  // let agentMode = "general" // Removed agent mode functionality
  let progressSteps = $state<ProgressStep[]>([])
  let currentProgress = $state(0)

  // Sidebar collapse state
  let leftSidebarCollapsed = $state(false)
  let rightSidebarCollapsed = $state(false)

  // Responsive behavior - auto-collapse on mobile
  let innerWidth = $state(0)
  let isMobile = $derived(innerWidth < 768)

  $effect(() => {
    if (isMobile) {
      leftSidebarCollapsed = true
      rightSidebarCollapsed = true
    }
  })

  // Animated placeholder examples
  const placeholderExamples = [
    "Compare Drift and Intercom's messaging and channel mix based on the last 30 days...",
    "How is Adobe marketing Firefly across its channels based on recent data...",
    "What influencer or social campaigns has Notion run recently...",
    "Map Figma's demand-gen strategy from 2022 to now...",
    "Analyze Stripe's developer marketing evolution over the past quarter...",
  ]

  // Quick start templates
  const quickStartTemplates = [
    {
      icon: ChartBar,
      title: "Company Snapshot",
      description: "Get a summary of performance, team, and competitors",
      prompt:
        "Provide a comprehensive company snapshot for [Company Name], including recent financial performance, leadership team overview, main competitors, and key business metrics.",
    },
    {
      icon: Target,
      title: "Go-to-Market Audit",
      description: "Evaluate positioning, messaging, channels, and campaigns",
      prompt:
        "Analyze [Company Name]'s go-to-market strategy including their positioning, messaging, marketing channels, recent campaigns, and overall effectiveness in reaching their target audience.",
    },
    {
      icon: Brain,
      title: "Brand Perception & Category",
      description: "Analyze how a brand is perceived in its space",
      prompt:
        "Research [Company Name]'s brand perception, category positioning, competitive differentiation, and customer sentiment. Include analysis of their brand identity and market perception.",
    },
  ]

  // Output format options
  const outputFormats = [
    {
      value: "executive",
      label: "Executive Summary",
      description: "Bullet points + key insights",
    },
    {
      value: "slide-ready",
      label: "Slide-ready",
      description: "Sections + headers for export",
    },
    {
      value: "battlecard",
      label: "Competitive Battlecard",
      description: "Strategic comparison format",
    },
  ]

  // Removed filter options - functionality not needed

  function generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  async function sendMessage() {
    if (!input.trim() || isLoading) return

    // Add output format context to the message with enhanced format-specific instructions
    let formatPrefix = ""
    if (outputFormat === "executive") {
      formatPrefix =
        "[Executive Summary Format] Provide concise bullet points, key insights, and executive-level highlights. Focus on high-level strategic information that executives need to know quickly. Use clear sections and numbered citations. "
    } else if (outputFormat === "slide-ready") {
      formatPrefix =
        "[Slide-ready Format] Structure with clear sections and headers suitable for presentation export. Use numbered sections, clear headings, and bullet points that can be easily converted to slides. Include numbered citations for all claims. "
    } else if (outputFormat === "battlecard") {
      formatPrefix =
        "[Competitive Battlecard Format] Focus on strategic comparison and competitive positioning. Emphasize competitive advantages, market differentiation, and head-to-head comparisons. Include numbered citations and competitive analysis. "
    }

    const userMessage = formatPrefix + input.trim()
    input = ""
    isLoading = true

    // Initialize progress steps
    progressSteps = [
      {
        id: 1,
        title: "Initial Analysis",
        description: "Analyzing research request...",
        status: "pending",
      },
      {
        id: 2,
        title: "Web Search",
        description: "Conducting comprehensive search...",
        status: "pending",
      },
      {
        id: 3,
        title: "Financial Analysis",
        description: "Gathering financial data...",
        status: "pending",
      },
      {
        id: 4,
        title: "Market Research",
        description: "Analyzing market position...",
        status: "pending",
      },
      {
        id: 5,
        title: "Report Generation",
        description: "Creating research report...",
        status: "pending",
      },
    ]
    currentProgress = 0

    // Add user message to chat
    messages.update((msgs) => [
      ...msgs,
      {
        id: generateId(),
        role: "user",
        content: userMessage,
        timestamp: new Date(),
      },
    ])

    try {
      // Use streaming for progress updates
      const response = await fetch(
        `/dashboard/${$page.params.envSlug}/researcher?stream=true`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message: userMessage }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split("\n")

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6))

                if (data.type === "final_response") {
                  // Add assistant response to chat
                  const assistantMessageId = generateId()
                  messages.update((msgs) => [
                    ...msgs,
                    {
                      id: assistantMessageId,
                      role: "assistant",
                      content: data.response,
                      timestamp: new Date(),
                      isReport: true,
                    },
                  ])
                } else if (data.step) {
                  // Update progress
                  currentProgress = data.progress
                  progressSteps = progressSteps.map((step) => {
                    if (step.id === data.step) {
                      return {
                        ...step,
                        status: data.status,
                        description: data.action,
                      }
                    } else if (step.id < data.step) {
                      return { ...step, status: "completed" }
                    }
                    return step
                  })
                }
              } catch (e) {
                console.error("Error parsing SSE data:", e)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("Error sending message:", error)
      messages.update((msgs) => [
        ...msgs,
        {
          id: generateId(),
          role: "assistant",
          content:
            "I apologize, but Compass encountered an error while processing your request. Please try again.",
          timestamp: new Date(),
        },
      ])
    } finally {
      isLoading = false
      // Reset progress
      progressSteps = []
      currentProgress = 0
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
  }

  function handleTemplateClick(template: (typeof quickStartTemplates)[0]) {
    input = template.prompt
    // Focus the textarea
    const textarea = document.querySelector("textarea")
    if (textarea) {
      textarea.focus()
    }
  }

  // Removed toggleFilters function - not needed

  function extractInsights(content: string): {
    summary: string
    insights: string[]
    badges: string[]
  } {
    // Simple insight extraction - in a real app this would be more sophisticated
    const lines = content.split("\n").filter((line) => line.trim())
    const summary = lines.slice(0, 2).join(" ").substring(0, 200) + "..."

    const insights = lines
      .filter(
        (line) =>
          line.includes("key") ||
          line.includes("important") ||
          line.includes("significant"),
      )
      .slice(0, 3)

    const badges = []
    if (content.includes("growth") || content.includes("increase"))
      badges.push("↑ Trending")
    if (content.includes("insight") || content.includes("analysis"))
      badges.push("💡 Insight")
    if (content.includes("challenge") || content.includes("weakness"))
      badges.push("⚠ Weakness")

    return { summary, insights, badges }
  }

  // Animated placeholder effect
  onMount(() => {
    currentPlaceholder = placeholderExamples[0]

    const interval = setInterval(() => {
      placeholderIndex = (placeholderIndex + 1) % placeholderExamples.length
      currentPlaceholder = placeholderExamples[placeholderIndex]
    }, 4000)

    return () => clearInterval(interval)
  })

  function downloadAsMarkdown(message: Message) {
    const companyName = extractCompanyName(message.content)
    const timestamp = message.timestamp.toISOString().split("T")[0]
    const filename = `${companyName || "research-report"}-${timestamp}.md`

    const markdownContent = `# Marketing Research Report by Compass
**Generated on:** ${message.timestamp.toLocaleDateString()}
**Time:** ${message.timestamp.toLocaleTimeString()}
**Format:** ${outputFormat.charAt(0).toUpperCase() + outputFormat.slice(1)}

---

${message.content}

---

*Report generated by Compass - Your AI Market Researcher*
`

    const blob = new Blob([markdownContent], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }

  function extractCompanyName(content: string): string {
    // Simple extraction - looks for company name in the first line or headers
    const lines = content.split("\n")
    for (const line of lines.slice(0, 5)) {
      if (line.includes("Company:") || line.includes("Company Name:")) {
        return (
          line
            .split(":")[1]
            ?.trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
      if (
        line.startsWith("# ") &&
        !line.includes("Research") &&
        !line.includes("Report")
      ) {
        return (
          line
            .replace("# ", "")
            .trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
    }
    return ""
  }

  function formatContent(content: string): string {
    // Enhanced markdown to HTML conversion for professional display
    let formatted = content
      // Headers
      .replace(
        /^# (.*$)/gim,
        '<h1 class="text-3xl font-bold mb-6 mt-8 first:mt-0" style="color: var(--foreground); border-bottom: 2px solid var(--border); padding-bottom: 0.5rem;">$1</h1>',
      )
      .replace(
        /^## (.*$)/gim,
        '<h2 class="text-2xl font-bold mb-4 mt-8" style="color: var(--foreground);">$1</h2>',
      )
      .replace(
        /^### (.*$)/gim,
        '<h3 class="text-xl font-semibold mb-3 mt-6" style="color: var(--foreground);">$1</h3>',
      )
      .replace(
        /^#### (.*$)/gim,
        '<h4 class="text-lg font-semibold mb-2 mt-4" style="color: var(--foreground);">$1</h4>',
      )

      // Bold text
      .replace(
        /\*\*(.*?)\*\*/g,
        '<strong class="font-bold" style="color: var(--foreground);">$1</strong>',
      )

      // Italic text
      .replace(
        /\*(.*?)\*/g,
        '<em class="italic" style="color: var(--muted-foreground);">$1</em>',
      )

      // Code blocks
      .replace(
        /```([\s\S]*?)```/g,
        '<pre class="bg-muted p-4 rounded border-2 border-border my-4 overflow-x-auto"><code class="text-sm font-mono" style="color: var(--foreground);">$1</code></pre>',
      )

      // Inline code
      .replace(
        /`([^`]+)`/g,
        '<code class="bg-muted px-2 py-1 rounded text-sm font-mono" style="color: var(--foreground);">$1</code>',
      )

      // Tables
      .replace(/\|(.+)\|/g, (match) => {
        const cells = match
          .split("|")
          .filter((cell) => cell.trim())
          .map((cell) => cell.trim())
        return (
          "<tr>" +
          cells
            .map(
              (cell) =>
                `<td class="border border-border px-3 py-2" style="color: var(--foreground);">${cell}</td>`,
            )
            .join("") +
          "</tr>"
        )
      })

      // Lists
      .replace(
        /^[\s]*[-*+] (.+)$/gim,
        '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$1</li>',
      )
      // Numbered lists - but avoid matching citation references in "Sources and References" section
      .replace(
        /^[\s]*(\d+)\.\s+(.+)$/gim,
        '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$2</li>',
      )

      // Blockquotes
      .replace(
        /^> (.+)$/gim,
        '<blockquote class="border-l-4 border-primary pl-4 italic my-4" style="color: var(--muted-foreground);">$1</blockquote>',
      )

      // Citations - Handle [1], [2], [3] etc. (but not markdown links)
      .replace(
        /\[(\d+)\]/g,
        '<sup class="citation-number bg-primary text-primary-foreground px-1 py-0.5 rounded text-xs font-bold ml-1">[$1]</sup>',
      )

      // Links
      .replace(
        /\[([^\]]+)\]\(([^)]+)\)/g,
        '<a href="$2" class="text-primary underline hover:opacity-70" target="_blank" rel="noopener noreferrer">$1</a>',
      )

      // Line breaks and paragraphs
      .replace(
        /\n\n/g,
        '</p><p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">',
      )
      .replace(/\n/g, "<br>")

    // Wrap content in paragraph if it doesn't start with a block element
    if (
      !formatted.startsWith("<h") &&
      !formatted.startsWith("<p") &&
      !formatted.startsWith("<ul") &&
      !formatted.startsWith("<ol") &&
      !formatted.startsWith("<blockquote")
    ) {
      formatted =
        '<p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">' +
        formatted +
        "</p>"
    }

    // Wrap lists in proper ul/ol tags
    formatted = formatted.replace(/(<li[^>]*>.*?<\/li>)/gs, (match) => {
      if (match.includes("list-style-type: disc")) {
        return '<ul class="mb-4">' + match + "</ul>"
      } else if (match.includes("list-style-type: decimal")) {
        return '<ol class="mb-4">' + match + "</ol>"
      }
      return match
    })

    // Wrap table rows in table
    if (formatted.includes("<tr>")) {
      formatted = formatted.replace(
        /(<tr>.*?<\/tr>)/gs,
        '<table class="w-full border-collapse border border-border my-4">$1</table>',
      )
    }

    return formatted
  }

  // Auto-scroll to bottom when new messages are added (modern layout)
  $effect(() => {
    if ($messages.length > 0) {
      setTimeout(() => {
        // Scroll to the bottom of the page to show the latest message
        window.scrollTo({
          top: document.body.scrollHeight,
          behavior: "smooth",
        })
      }, 100)
    }
  })

  // Removed - replaced with quickStartTemplates
</script>

<svelte:head>
  <title>Compass - AI Market Researcher</title>
</svelte:head>

<!-- Window width binding for responsive behavior -->
<svelte:window bind:innerWidth />

<!-- New Three-Column Layout -->
<div class="flex h-screen bg-background text-foreground">
  <!-- Left Sidebar -->
  <ResearchSidebar
    bind:collapsed={leftSidebarCollapsed}
    {isMobile}
    {session}
    {profile}
  />

  <!-- Main Content Area -->
  <ResearchChatArea
    messages={$messages}
    bind:isLoading
    bind:progressSteps
    bind:currentProgress
    {leftSidebarCollapsed}
    {rightSidebarCollapsed}
  />

  <!-- Right Sidebar -->
  <ProjectsSidebar bind:collapsed={rightSidebarCollapsed} {isMobile} />
</div>
