export class FireGeoApiClient {
    constructor(baseUrl, apiSecret) {
        this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
        this.apiSecret = apiSecret;
    }
    /**
     * Make a request to the FireGeo API
     */
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Secret': this.apiSecret,
                ...options.headers,
            },
        };
        if (options.body && (config.method === 'POST' || config.method === 'PUT' || config.method === 'PATCH')) {
            config.body = JSON.stringify(options.body);
        }
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
            }
            return data;
        }
        catch (error) {
            console.error('FireGeo API request failed:', error);
            throw error;
        }
    }
    /**
     * Make an authenticated request with Supabase token
     */
    async makeAuthenticatedRequest(endpoint, supabaseToken, options = {}) {
        return this.makeRequest(endpoint, {
            ...options,
            headers: {
                'Authorization': `Bearer ${supabaseToken}`,
                ...options.headers,
            },
        });
    }
    /**
     * Authenticate user with Supabase token
     */
    async authenticateUser(supabaseToken) {
        const request = {
            supabaseToken,
            user: {} // Will be validated by the API
        };
        return this.makeRequest('/api/auth/bridge', {
            method: 'POST',
            body: request,
        });
    }
    /**
     * Start a new brand analysis
     */
    async startBrandAnalysis(data, supabaseToken) {
        const response = await this.makeAuthenticatedRequest('/api/brand-monitor/analyze', supabaseToken, {
            method: 'POST',
            body: data,
        });
        return response.data;
    }
    /**
     * Get all brand analyses for the authenticated user
     */
    async getBrandAnalyses(supabaseToken) {
        const response = await this.makeAuthenticatedRequest('/api/brand-monitor/analyses', supabaseToken);
        return response.data || [];
    }
    /**
     * Get a specific brand analysis by ID
     */
    async getBrandAnalysis(analysisId, supabaseToken) {
        try {
            const response = await this.makeAuthenticatedRequest(`/api/brand-monitor/analyses/${analysisId}`, supabaseToken);
            return response.data || null;
        }
        catch (error) {
            console.error('Failed to get brand analysis:', error);
            return null;
        }
    }
    /**
     * Check AI provider availability
     */
    async checkProviders(supabaseToken) {
        return this.makeAuthenticatedRequest('/api/brand-monitor/check-providers', supabaseToken);
    }
    /**
     * Scrape a website URL
     */
    async scrapeWebsite(url, supabaseToken) {
        return this.makeAuthenticatedRequest('/api/brand-monitor/scrape', supabaseToken, {
            method: 'POST',
            body: { url },
        });
    }
    /**
     * Health check endpoint
     */
    async healthCheck() {
        return this.makeRequest('/api/auth/bridge');
    }
    /**
     * Create SSE connection for real-time updates
     */
    createEventSource(endpoint, supabaseToken, onMessage, onError) {
        const url = new URL(`${this.baseUrl}${endpoint}`);
        url.searchParams.set('token', supabaseToken);
        const eventSource = new EventSource(url.toString());
        if (onMessage) {
            eventSource.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    onMessage(data);
                }
                catch (error) {
                    console.error('Failed to parse SSE data:', error);
                }
            };
        }
        if (onError) {
            eventSource.onerror = onError;
        }
        return eventSource;
    }
}
