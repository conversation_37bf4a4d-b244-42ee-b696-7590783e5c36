# Codebase Restructuring Plan

## Step 1: Create Monorepo Structure

```bash
# 1. Create apps directory and move existing code
mkdir -p apps/dashboard
mkdir -p apps/firegeo-api
mkdir -p packages/shared/src/{types,utils,api-client}

# 2. Move existing SvelteKit app to apps/dashboard
mv src apps/dashboard/
mv static apps/dashboard/
mv svelte.config.js apps/dashboard/
mv vite.config.ts apps/dashboard/
mv tsconfig.json apps/dashboard/
mv vercel.json apps/dashboard/

# 3. Move FireGeo to apps/firegeo-api
mv firegeo/firegeo/* apps/firegeo-api/
rmdir firegeo/firegeo firegeo

# 4. Create shared package structure
```

## Step 2: Update Package Configurations

### Root package.json
```json
{
  "name": "robynn-workspace",
  "private": true,
  "scripts": {
    "dev": "pnpm --parallel --filter \"./apps/*\" dev",
    "dev:dashboard": "pnpm --filter dashboard dev",
    "dev:firegeo": "pnpm --filter firegeo-api dev",
    "build": "pnpm --filter shared build && pnpm --filter dashboard build && pnpm --filter firegeo-api build",
    "build:dashboard": "pnpm --filter shared build && pnpm --filter dashboard build",
    "build:firegeo": "pnpm --filter shared build && pnpm --filter firegeo-api build",
    "deploy": "vercel --prod",
    "deploy:dashboard": "vercel --prod --cwd apps/dashboard",
    "deploy:firegeo": "vercel --prod --cwd apps/firegeo-api"
  },
  "devDependencies": {
    "typescript": "^5.8.3"
  }
}
```

### pnpm-workspace.yaml
```yaml
packages:
  - 'apps/*'
  - 'packages/*'
```

### apps/dashboard/package.json
```json
{
  "name": "dashboard",
  "version": "0.0.1",
  "private": true,
  "scripts": {
    "dev": "vite dev --port 3000",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "shared": "workspace:*"
  }
}
```

### apps/firegeo-api/package.json
```json
{
  "name": "firegeo-api",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev --port 3001",
    "build": "next build",
    "start": "next start"
  },
  "dependencies": {
    "shared": "workspace:*"
  }
}
```

### packages/shared/package.json
```json
{
  "name": "shared",
  "version": "0.0.1",
  "private": true,
  "main": "./dist/index.js",
  "types": "./dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch"
  },
  "devDependencies": {
    "typescript": "^5.8.3"
  }
}
```
