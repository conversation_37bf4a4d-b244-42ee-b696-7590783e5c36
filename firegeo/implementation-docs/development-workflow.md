# Development Workflow Configuration

## 5. Development Workflow & Local Setup

### Local Development Setup

#### Root .env.local
```bash
# Shared development configuration
NODE_ENV=development
PNPM_WORKSPACE=true

# API URLs for local development
FIREGEO_API_URL=http://localhost:3001
PUBLIC_FIREGEO_API_URL=http://localhost:3001
FIREGEO_API_SECRET=dev_secret_key_123

# Dashboard port
DASHBOARD_PORT=3000
FIREGEO_PORT=3001
```

#### apps/dashboard/.env.local
```bash
# Existing Supabase configuration
DATABASE_URL=your_supabase_url
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
PUBLIC_SUPABASE_URL=your_supabase_url
PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# FireGeo integration
FIREGEO_API_URL=http://localhost:3001
PUBLIC_FIREGEO_API_URL=http://localhost:3001
FIREGEO_API_SECRET=dev_secret_key_123

# Existing other variables...
```

#### apps/firegeo-api/.env.local
```bash
# FireGeo database (separate from Supabase)
DATABASE_URL=your_firegeo_postgres_url
BETTER_AUTH_SECRET=your_better_auth_secret
BETTER_AUTH_URL=http://localhost:3001
NEXT_PUBLIC_APP_URL=http://localhost:3001

# API keys
FIRECRAWL_API_KEY=your_firecrawl_key
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_GENERATIVE_AI_API_KEY=your_google_key
PERPLEXITY_API_KEY=your_perplexity_key

# Integration with dashboard
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
DASHBOARD_URL=http://localhost:3000
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key
API_SECRET=dev_secret_key_123

# Remove Autumn billing for API-only mode
# AUTUMN_SECRET_KEY=  # Commented out
```

### Development Scripts

#### Root package.json scripts
```json
{
  "scripts": {
    "dev": "pnpm --parallel --filter \"./apps/*\" dev",
    "dev:dashboard": "pnpm --filter dashboard dev",
    "dev:firegeo": "pnpm --filter firegeo-api dev",
    "dev:shared": "pnpm --filter shared dev",
    "build": "pnpm --filter shared build && pnpm --recursive --filter \"./apps/*\" build",
    "build:dashboard": "pnpm --filter shared build && pnpm --filter dashboard build",
    "build:firegeo": "pnpm --filter shared build && pnpm --filter firegeo-api build",
    "clean": "pnpm --recursive exec rm -rf node_modules .next .svelte-kit build dist",
    "install:all": "pnpm install",
    "type-check": "pnpm --recursive --filter \"./apps/*\" run check",
    "lint": "pnpm --recursive --filter \"./apps/*\" run lint",
    "test": "pnpm --recursive --filter \"./apps/*\" run test"
  }
}
```

### VS Code Workspace Configuration

#### .vscode/settings.json
```json
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.workspaceSymbols.scope": "allOpenProjects",
  "eslint.workingDirectories": [
    "apps/dashboard",
    "apps/firegeo-api",
    "packages/shared"
  ],
  "files.exclude": {
    "**/node_modules": true,
    "**/.next": true,
    "**/.svelte-kit": true,
    "**/build": true,
    "**/dist": true
  }
}
```

#### .vscode/launch.json
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Dashboard",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}/apps/dashboard",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["dev"]
    },
    {
      "name": "Debug FireGeo API",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}/apps/firegeo-api",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["dev"]
    }
  ],
  "compounds": [
    {
      "name": "Debug Both Apps",
      "configurations": ["Debug Dashboard", "Debug FireGeo API"]
    }
  ]
}
```

### Docker Development (Optional)

#### docker-compose.dev.yml
```yaml
version: '3.8'
services:
  dashboard:
    build:
      context: .
      dockerfile: apps/dashboard/Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    depends_on:
      - firegeo-api

  firegeo-api:
    build:
      context: .
      dockerfile: apps/firegeo-api/Dockerfile.dev
    ports:
      - "3001:3001"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: firegeo
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### Git Configuration

#### .gitignore additions
```gitignore
# Workspace
.pnpm-debug.log*

# App-specific
apps/dashboard/.env.local
apps/dashboard/.svelte-kit/
apps/dashboard/build/

apps/firegeo-api/.env.local
apps/firegeo-api/.next/
apps/firegeo-api/out/

packages/shared/dist/

# Vercel
.vercel

# Development
*.log
.DS_Store
```

### Development Workflow Commands

```bash
# Initial setup
git clone <your-repo>
cd robynnv3
pnpm install

# Start development (both apps)
pnpm dev

# Start individual apps
pnpm dev:dashboard  # Runs on :3000
pnpm dev:firegeo    # Runs on :3001

# Build for production
pnpm build

# Type checking
pnpm type-check

# Linting
pnpm lint

# Clean everything
pnpm clean && pnpm install
```

### Testing the Integration

#### Test Authentication Flow
```bash
# 1. Start both services
pnpm dev

# 2. Login to dashboard at http://localhost:3000
# 3. Navigate to brand monitor
# 4. Verify API calls reach FireGeo at :3001
# 5. Check browser network tab for CORS issues
```

#### Test API Endpoints
```bash
# Test FireGeo API directly
curl -X POST http://localhost:3001/api/auth/bridge \
  -H "Content-Type: application/json" \
  -d '{"token":"your_supabase_token"}'

# Test brand analysis
curl -X GET http://localhost:3001/api/brand-monitor/analyses \
  -H "Authorization: Bearer your_supabase_token"
```
