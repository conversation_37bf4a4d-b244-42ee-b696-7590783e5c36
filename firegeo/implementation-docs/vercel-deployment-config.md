# Vercel Deployment Configuration

## 2. Vercel Deployment Architecture

### Root vercel.json (Project Root)
```json
{
  "version": 2,
  "builds": [
    {
      "src": "apps/dashboard/package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "apps/dashboard/build"
      }
    },
    {
      "src": "apps/firegeo-api/package.json", 
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/api/firegeo/(.*)",
      "dest": "apps/firegeo-api/api/$1"
    },
    {
      "src": "/(.*)",
      "dest": "apps/dashboard/$1"
    }
  ],
  "buildCommand": "pnpm build",
  "devCommand": "pnpm dev",
  "installCommand": "pnpm install"
}
```

### apps/dashboard/vercel.json
```json
{
  "buildCommand": "cd ../.. && pnpm build:dashboard",
  "devCommand": "cd ../.. && pnpm dev:dashboard",
  "installCommand": "cd ../.. && pnpm install",
  "framework": "sveltekit"
}
```

### apps/firegeo-api/vercel.json
```json
{
  "buildCommand": "cd ../.. && pnpm build:firegeo",
  "devCommand": "cd ../.. && pnpm dev:firegeo", 
  "installCommand": "cd ../.. && pnpm install",
  "framework": "nextjs"
}
```

## Alternative: Separate Vercel Projects (Recommended)

### Option A: Two Separate Vercel Projects

#### Dashboard Project (Primary)
- Deploy from: `apps/dashboard/`
- Domain: `yourdomain.com`
- Environment Variables:
  ```
  FIREGEO_API_URL=https://firegeo-api.vercel.app
  PUBLIC_FIREGEO_API_URL=https://firegeo-api.vercel.app
  ```

#### FireGeo API Project
- Deploy from: `apps/firegeo-api/`
- Domain: `firegeo-api.vercel.app` (or custom subdomain)
- Environment Variables:
  ```
  ALLOWED_ORIGINS=https://yourdomain.com,http://localhost:3000
  DASHBOARD_URL=https://yourdomain.com
  ```

### Deployment Commands

```bash
# Deploy dashboard
cd apps/dashboard
vercel --prod

# Deploy FireGeo API
cd apps/firegeo-api  
vercel --prod
```

## Environment Variables Configuration

### Dashboard (.env.local)
```bash
# Existing variables
DATABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# New FireGeo integration
FIREGEO_API_URL=https://firegeo-api.vercel.app
PUBLIC_FIREGEO_API_URL=https://firegeo-api.vercel.app
FIREGEO_API_SECRET=shared_secret_key
```

### FireGeo API (.env.local)
```bash
# Existing FireGeo variables
DATABASE_URL=your_firegeo_postgres_url
BETTER_AUTH_SECRET=your_better_auth_secret
FIRECRAWL_API_KEY=your_firecrawl_key
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# New integration variables
ALLOWED_ORIGINS=https://yourdomain.com,http://localhost:3000
DASHBOARD_URL=https://yourdomain.com
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key
API_SECRET=shared_secret_key
```

## CORS Configuration

### apps/firegeo-api/middleware.ts
```typescript
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Handle CORS
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || []
  const origin = request.headers.get('origin')
  
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': origin && allowedOrigins.includes(origin) ? origin : '',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Credentials': 'true',
      },
    })
  }

  const response = NextResponse.next()
  
  if (origin && allowedOrigins.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin)
    response.headers.set('Access-Control-Allow-Credentials', 'true')
  }

  return response
}

export const config = {
  matcher: '/api/:path*',
}
```
