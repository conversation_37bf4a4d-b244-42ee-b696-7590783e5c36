# Authentication Bridge Configuration

## 3. Authentication & Data Flow

### Supabase to FireGeo Authentication Bridge

#### packages/shared/src/types/auth.ts
```typescript
export interface SupabaseUser {
  id: string
  email: string
  user_metadata?: {
    full_name?: string
    avatar_url?: string
  }
}

export interface FireGeoAuthContext {
  userId: string
  email: string
  name?: string
  avatarUrl?: string
}

export interface AuthBridgeRequest {
  supabaseToken: string
  user: SupabaseUser
}
```

#### apps/firegeo-api/lib/auth-bridge.ts
```typescript
import { createClient } from '@supabase/supabase-js'
import { NextRequest } from 'next/server'

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_KEY!
)

export async function validateSupabaseToken(token: string) {
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token)
    
    if (error || !user) {
      return null
    }
    
    return {
      userId: user.id,
      email: user.email!,
      name: user.user_metadata?.full_name,
      avatarUrl: user.user_metadata?.avatar_url
    }
  } catch (error) {
    console.error('Token validation error:', error)
    return null
  }
}

export async function extractAuthFromRequest(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  
  if (!authHeader?.startsWith('Bearer ')) {
    return null
  }
  
  const token = authHeader.substring(7)
  return validateSupabaseToken(token)
}

export function createFireGeoSession(authContext: any) {
  // Create a session context that FireGeo can use
  return {
    user: {
      id: authContext.userId,
      email: authContext.email,
      name: authContext.name || authContext.email,
      image: authContext.avatarUrl
    }
  }
}
```

#### apps/firegeo-api/app/api/auth/bridge/route.ts
```typescript
import { NextRequest, NextResponse } from 'next/server'
import { validateSupabaseToken, createFireGeoSession } from '@/lib/auth-bridge'

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json()
    
    if (!token) {
      return NextResponse.json({ error: 'Token required' }, { status: 400 })
    }
    
    const authContext = await validateSupabaseToken(token)
    
    if (!authContext) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }
    
    const session = createFireGeoSession(authContext)
    
    return NextResponse.json({ 
      success: true, 
      session,
      user: authContext 
    })
  } catch (error) {
    console.error('Auth bridge error:', error)
    return NextResponse.json({ error: 'Authentication failed' }, { status: 500 })
  }
}
```

### API Client for Dashboard

#### packages/shared/src/api-client/firegeo-client.ts
```typescript
export class FireGeoApiClient {
  private baseUrl: string
  private apiSecret: string
  
  constructor(baseUrl: string, apiSecret: string) {
    this.baseUrl = baseUrl
    this.apiSecret = apiSecret
  }
  
  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseUrl}${endpoint}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Secret': this.apiSecret,
        ...options.headers,
      },
    })
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`)
    }
    
    return response.json()
  }
  
  async authenticateUser(supabaseToken: string) {
    return this.makeRequest('/api/auth/bridge', {
      method: 'POST',
      body: JSON.stringify({ token: supabaseToken }),
    })
  }
  
  async startBrandAnalysis(data: any, supabaseToken: string) {
    return this.makeRequest('/api/brand-monitor/analyze', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseToken}`,
      },
      body: JSON.stringify(data),
    })
  }
  
  async getBrandAnalyses(supabaseToken: string) {
    return this.makeRequest('/api/brand-monitor/analyses', {
      headers: {
        'Authorization': `Bearer ${supabaseToken}`,
      },
    })
  }
}
```

#### apps/dashboard/src/lib/firegeo-client.ts
```typescript
import { FireGeoApiClient } from 'shared/api-client/firegeo-client'
import { PUBLIC_FIREGEO_API_URL } from '$env/static/public'
import { FIREGEO_API_SECRET } from '$env/static/private'

export const fireGeoClient = new FireGeoApiClient(
  PUBLIC_FIREGEO_API_URL,
  FIREGEO_API_SECRET
)
```

### Middleware for FireGeo API Routes

#### apps/firegeo-api/app/api/brand-monitor/[...path]/route.ts
```typescript
import { NextRequest, NextResponse } from 'next/server'
import { extractAuthFromRequest } from '@/lib/auth-bridge'

async function handleRequest(request: NextRequest, { params }: { params: { path: string[] } }) {
  // Validate authentication
  const authContext = await extractAuthFromRequest(request)
  
  if (!authContext) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }
  
  // Add user context to request
  const enhancedRequest = new Request(request.url, {
    ...request,
    headers: {
      ...Object.fromEntries(request.headers.entries()),
      'x-user-id': authContext.userId,
      'x-user-email': authContext.email,
    },
  })
  
  // Forward to original FireGeo handlers
  const path = params.path.join('/')
  // ... route to appropriate handler based on path
}

export { handleRequest as GET, handleRequest as POST, handleRequest as PUT, handleRequest as DELETE }
```
