# Dashboard Integration Plan

## 4. Dashboard Integration & Brand Monitor Agent

### Add Brand Monitor Agent to Dashboard

#### apps/dashboard/src/routes/(admin)/dashboard/[envSlug]/(menu)/+page.svelte
Add new agent card after existing agents:

```svelte
<!-- Add after existing agent cards -->
<a
  href="/dashboard/{$page.params.envSlug}/brand-monitor"
  class="card-brutal p-6 transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 block"
>
  <div class="space-y-4">
    <div class="flex items-center gap-3">
      <div
        class="w-12 h-12 bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm flex items-center justify-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </div>
      <div>
        <h3 class="text-xl font-semibold text-foreground">FireGeo</h3>
        <div class="flex items-center gap-2 mt-1">
          <span
            class="bg-accent text-accent-foreground px-2 py-1 text-xs font-bold border border-border"
            >AI-Powered</span
          >
          <span
            class="bg-secondary text-secondary-foreground px-2 py-1 text-xs font-bold border border-border"
            >Brand Monitor</span
          >
        </div>
      </div>
    </div>
    <p class="text-muted-foreground font-medium">
      Track how AI models rank your brand against competitors in search results and recommendations.
    </p>
    <div class="text-sm text-muted-foreground">
      <p>• AI model brand visibility tracking</p>
      <p>• Competitor analysis & positioning</p>
      <p>• Multi-provider comparison matrix</p>
    </div>
  </div>
</a>
```

### Create Brand Monitor Route

#### apps/dashboard/src/routes/(admin)/dashboard/[envSlug]/brand-monitor/+page.server.ts
```typescript
import type { PageServerLoad } from './$types'
import { redirect } from '@sveltejs/kit'

export const load: PageServerLoad = async ({ locals: { supabase, getSession } }) => {
  const session = await getSession()

  if (!session) {
    throw redirect(303, '/login/sign_in')
  }

  return {
    session
  }
}
```

#### apps/dashboard/src/routes/(admin)/dashboard/[envSlug]/brand-monitor/+page.svelte
```svelte
<script lang="ts">
  import { page } from '$app/stores'
  import { onMount } from 'svelte'
  import { fireGeoClient } from '$lib/firegeo-client'
  import BrandMonitorInterface from '$lib/components/brand-monitor/BrandMonitorInterface.svelte'
  import type { PageData } from './$types'

  export let data: PageData

  let analyses: any[] = []
  let loading = true
  let error: string | null = null

  onMount(async () => {
    try {
      const token = data.session?.access_token
      if (token) {
        // Authenticate with FireGeo API
        await fireGeoClient.authenticateUser(token)
        
        // Load existing analyses
        analyses = await fireGeoClient.getBrandAnalyses(token)
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load brand monitor'
    } finally {
      loading = false
    }
  })

  async function handleNewAnalysis(analysisData: any) {
    try {
      const token = data.session?.access_token
      if (token) {
        const result = await fireGeoClient.startBrandAnalysis(analysisData, token)
        analyses = [result, ...analyses]
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to start analysis'
    }
  }
</script>

<svelte:head>
  <title>Brand Monitor - FireGeo</title>
</svelte:head>

<div class="min-h-screen bg-background">
  <!-- Breadcrumb Navigation -->
  <nav class="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
    <a href="/dashboard/{$page.params.envSlug}" class="hover:text-foreground">Dashboard</a>
    <span>/</span>
    <span class="text-foreground font-medium">Brand Monitor</span>
  </nav>

  <!-- Hero Header -->
  <div class="mb-8">
    <h1 class="text-4xl font-semibold mb-4 text-foreground">FireGeo Brand Monitor</h1>
    <p class="text-lg text-muted-foreground">
      Track how AI models rank your brand against competitors in search results and recommendations.
    </p>
  </div>

  {#if loading}
    <div class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  {:else if error}
    <div class="card-brutal p-6 bg-destructive/10 border-destructive">
      <p class="text-destructive font-medium">{error}</p>
    </div>
  {:else}
    <BrandMonitorInterface 
      {analyses}
      session={data.session}
      on:newAnalysis={handleNewAnalysis}
    />
  {/if}
</div>
```

### Create Brand Monitor Components

#### apps/dashboard/src/lib/components/brand-monitor/BrandMonitorInterface.svelte
```svelte
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import UrlInputSection from './UrlInputSection.svelte'
  import AnalysisResults from './AnalysisResults.svelte'
  import AnalysisList from './AnalysisList.svelte'

  export let analyses: any[] = []
  export let session: any

  const dispatch = createEventDispatcher()

  let selectedAnalysis: any = null
  let isAnalyzing = false

  function handleStartAnalysis(event: CustomEvent) {
    isAnalyzing = true
    dispatch('newAnalysis', event.detail)
  }

  function handleAnalysisComplete() {
    isAnalyzing = false
  }

  function selectAnalysis(analysis: any) {
    selectedAnalysis = analysis
  }
</script>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
  <!-- Sidebar with analysis list -->
  <div class="lg:col-span-1">
    <AnalysisList 
      {analyses}
      {selectedAnalysis}
      on:select={(e) => selectAnalysis(e.detail)}
    />
  </div>

  <!-- Main content area -->
  <div class="lg:col-span-3">
    {#if selectedAnalysis}
      <AnalysisResults analysis={selectedAnalysis} />
    {:else}
      <UrlInputSection 
        {isAnalyzing}
        on:startAnalysis={handleStartAnalysis}
        on:complete={handleAnalysisComplete}
      />
    {/if}
  </div>
</div>
```

#### apps/dashboard/src/lib/components/brand-monitor/UrlInputSection.svelte
```svelte
<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { Button } from '$lib/components/ui/button'
  import { Input } from '$lib/components/ui/input'
  import { Label } from '$lib/components/ui/label'

  export let isAnalyzing = false

  const dispatch = createEventDispatcher()

  let url = ''
  let urlValid = false

  $: urlValid = isValidUrl(url)

  function isValidUrl(str: string): boolean {
    try {
      new URL(str.startsWith('http') ? str : `https://${str}`)
      return true
    } catch {
      return false
    }
  }

  function handleSubmit() {
    if (urlValid && !isAnalyzing) {
      dispatch('startAnalysis', { url })
    }
  }
</script>

<div class="card-brutal p-8">
  <div class="space-y-6">
    <div>
      <h2 class="text-2xl font-semibold text-foreground mb-2">Start Brand Analysis</h2>
      <p class="text-muted-foreground">
        Enter your company's website URL to analyze how AI models represent your brand.
      </p>
    </div>

    <div class="space-y-4">
      <div>
        <Label for="url">Company Website URL</Label>
        <Input
          id="url"
          bind:value={url}
          placeholder="example.com or https://example.com"
          disabled={isAnalyzing}
          class="mt-1"
        />
      </div>

      <Button
        on:click={handleSubmit}
        disabled={!urlValid || isAnalyzing}
        class="w-full"
      >
        {#if isAnalyzing}
          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
          Analyzing...
        {:else}
          Start Analysis
        {/if}
      </Button>
    </div>
  </div>
</div>
```
