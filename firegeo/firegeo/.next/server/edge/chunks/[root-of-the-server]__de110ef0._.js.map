{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getSessionCookie } from 'better-auth/cookies';\n\n// Define protected routes\nconst protectedRoutes = ['/dashboard', '/chat', '/brand-monitor'];\n\nexport async function middleware(request: NextRequest) {\n  const pathname = request.nextUrl.pathname;\n  \n  // Check if the route is protected\n  const isProtectedRoute = protectedRoutes.some(route => \n    pathname.startsWith(route)\n  );\n\n  if (isProtectedRoute) {\n    // Check for session cookie\n    const sessionCookie = await getSessionCookie(request);\n    \n    if (!sessionCookie) {\n      // Redirect to login with return URL\n      const url = new URL('/login', request.url);\n      url.searchParams.set('from', pathname);\n      return NextResponse.redirect(url);\n    }\n  }\n\n  const response = NextResponse.next();\n  \n  response.headers.set('X-Frame-Options', 'DENY');\n  response.headers.set('X-Content-Type-Options', 'nosniff');\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n  response.headers.set('X-XSS-Protection', '1; mode=block');\n  \n  return response;\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico|.*\\\\..*).)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,0BAA0B;AAC1B,MAAM,kBAAkB;IAAC;IAAc;IAAS;CAAiB;AAE1D,eAAe,WAAW,OAAoB;IACnD,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IAEzC,kCAAkC;IAClC,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAC5C,SAAS,UAAU,CAAC;IAGtB,IAAI,kBAAkB;QACpB,2BAA2B;QAC3B,MAAM,gBAAgB,MAAM,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE;QAE7C,IAAI,CAAC,eAAe;YAClB,oCAAoC;YACpC,MAAM,MAAM,IAAI,IAAI,UAAU,QAAQ,GAAG;YACzC,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;YAC7B,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAElC,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB;IAEzC,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;;KAOC,GACD;KACD;AACH"}}]}