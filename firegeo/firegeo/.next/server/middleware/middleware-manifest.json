{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_438e5f93._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_733f891f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|.*\\..*).){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\..*).)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "wCatVcDjxM0kYCU8RSdDKm1sALoFZkpXPJiFhXoXOXM=", "__NEXT_PREVIEW_MODE_ID": "c99c8c6e6c064196d80c80bd8f1ac0be", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2a17e8d44a00717fb5d643a10d3e06d1391fcafe7fdaa837aa674f173e53b01a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ea4800346a7f56dac5f4706dfc1ed3f406ce5d1acecdc2357d044d4d79f88c44"}}}, "instrumentation": null, "functions": {}}