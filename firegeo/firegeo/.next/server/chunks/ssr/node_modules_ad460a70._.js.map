{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40swc/helpers/cjs/_class_private_field_loose_base.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _class_private_field_loose_base(receiver, privateKey) {\n    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n        throw new TypeError(\"attempted to use private field on non-instance\");\n    }\n\n    return receiver;\n}\nexports._ = _class_private_field_loose_base;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,gCAAgC,QAAQ,EAAE,UAAU;IACzD,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,aAAa;QAC7D,MAAM,IAAI,UAAU;IACxB;IAEA,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40swc/helpers/cjs/_class_private_field_loose_key.cjs"], "sourcesContent": ["\"use strict\";\n\nvar id = 0;\n\nfunction _class_private_field_loose_key(name) {\n    return \"__private_\" + id++ + \"_\" + name;\n}\nexports._ = _class_private_field_loose_key;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,KAAK;AAET,SAAS,+BAA+B,IAAI;IACxC,OAAO,eAAe,OAAO,MAAM;AACvC;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40swc/helpers/cjs/_tagged_template_literal_loose.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _tagged_template_literal_loose(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    strings.raw = raw;\n\n    return strings;\n}\nexports._ = _tagged_template_literal_loose;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,+BAA+B,OAAO,EAAE,GAAG;IAChD,IAAI,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC;IAE9B,QAAQ,GAAG,GAAG;IAEd,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40better-fetch/fetch/src/error.ts", "file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40better-fetch/fetch/src/plugins.ts", "file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40better-fetch/fetch/src/retry.ts", "file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40better-fetch/fetch/src/auth.ts", "file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40better-fetch/fetch/src/utils.ts", "file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40better-fetch/fetch/src/create-fetch/schema.ts", "file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40better-fetch/fetch/src/create-fetch/index.ts", "file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40better-fetch/fetch/src/url.ts", "file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40better-fetch/fetch/src/fetch.ts"], "sourcesContent": ["export class BetterFetchError extends Error {\n\tconstructor(\n\t\tpublic status: number,\n\t\tpublic statusText: string,\n\t\tpublic error: any,\n\t) {\n\t\tsuper(statusText || status.toString(), {\n\t\t\tcause: error,\n\t\t});\n\t}\n}\n", "import type { StandardSchemaV1 } from \"./standard-schema\";\nimport { Schema } from \"./create-fetch\";\nimport { BetterFetchError } from \"./error\";\nimport type { BetterFetchOption } from \"./types\";\n\nexport type RequestContext<T extends Record<string, any> = any> = {\n\turl: URL | string;\n\theaders: Headers;\n\tbody: any;\n\tmethod: string;\n\tsignal: AbortSignal;\n} & BetterFetchOption<any, any, any, T>;\nexport type ResponseContext = {\n\tresponse: Response;\n\trequest: RequestContext;\n};\nexport type SuccessContext<Res = any> = {\n\tdata: Res;\n\tresponse: Response;\n\trequest: RequestContext;\n};\nexport type ErrorContext = {\n\tresponse: Response;\n\trequest: RequestContext;\n\terror: BetterFetchError & Record<string, any>;\n};\nexport interface FetchHooks<Res = any> {\n\t/**\n\t * a callback function that will be called when a\n\t * request is made.\n\t *\n\t * The returned context object will be reassigned to\n\t * the original request context.\n\t */\n\tonRequest?: <T extends Record<string, any>>(\n\t\tcontext: RequestContext<T>,\n\t) => Promise<RequestContext | void> | RequestContext | void;\n\t/**\n\t * a callback function that will be called when\n\t * response is received. This will be called before\n\t * the response is parsed and returned.\n\t *\n\t * The returned response will be reassigned to the\n\t * original response if it's changed.\n\t */\n\tonResponse?: (\n\t\tcontext: ResponseContext,\n\t) =>\n\t\t| Promise<Response | void | ResponseContext>\n\t\t| Response\n\t\t| ResponseContext\n\t\t| void;\n\t/**\n\t * a callback function that will be called when a\n\t * response is successful.\n\t */\n\tonSuccess?: (context: SuccessContext<Res>) => Promise<void> | void;\n\t/**\n\t * a callback function that will be called when an\n\t * error occurs.\n\t */\n\tonError?: (context: ErrorContext) => Promise<void> | void;\n\t/**\n\t * a callback function that will be called when a\n\t * request is retried.\n\t */\n\tonRetry?: (response: ResponseContext) => Promise<void> | void;\n\t/**\n\t * Options for the hooks\n\t */\n\thookOptions?: {\n\t\t/**\n\t\t * Clone the response\n\t\t * @see https://developer.mozilla.org/en-US/docs/Web/API/Response/clone\n\t\t */\n\t\tcloneResponse?: boolean;\n\t};\n}\n\n/**\n * A plugin that returns an id and hooks\n */\nexport type BetterFetchPlugin = {\n\t/**\n\t * A unique id for the plugin\n\t */\n\tid: string;\n\t/**\n\t * A name for the plugin\n\t */\n\tname: string;\n\t/**\n\t * A description for the plugin\n\t */\n\tdescription?: string;\n\t/**\n\t * A version for the plugin\n\t */\n\tversion?: string;\n\t/**\n\t * Hooks for the plugin\n\t */\n\thooks?: FetchHooks;\n\t/**\n\t * A function that will be called when the plugin is\n\t * initialized. This will be called before the any\n\t * of the other internal functions.\n\t *\n\t * The returned options will be merged with the\n\t * original options.\n\t */\n\tinit?: (\n\t\turl: string,\n\t\toptions?: BetterFetchOption,\n\t) =>\n\t\t| Promise<{\n\t\t\t\turl: string;\n\t\t\t\toptions?: BetterFetchOption;\n\t\t  }>\n\t\t| {\n\t\t\t\turl: string;\n\t\t\t\toptions?: BetterFetchOption;\n\t\t  };\n\t/**\n\t * A schema for the plugin\n\t */\n\tschema?: Schema;\n\t/**\n\t * Additional options that can be passed to the plugin\n\t */\n\tgetOptions?: () => StandardSchemaV1;\n};\n\nexport const initializePlugins = async (\n\turl: string,\n\toptions?: BetterFetchOption,\n) => {\n\tlet opts = options || {};\n\tconst hooks: {\n\t\tonRequest: Array<FetchHooks[\"onRequest\"]>;\n\t\tonResponse: Array<FetchHooks[\"onResponse\"]>;\n\t\tonSuccess: Array<FetchHooks[\"onSuccess\"]>;\n\t\tonError: Array<FetchHooks[\"onError\"]>;\n\t\tonRetry: Array<FetchHooks[\"onRetry\"]>;\n\t} = {\n\t\tonRequest: [options?.onRequest],\n\t\tonResponse: [options?.onResponse],\n\t\tonSuccess: [options?.onSuccess],\n\t\tonError: [options?.onError],\n\t\tonRetry: [options?.onRetry],\n\t};\n\tif (!options || !options?.plugins) {\n\t\treturn {\n\t\t\turl,\n\t\t\toptions: opts,\n\t\t\thooks,\n\t\t};\n\t}\n\tfor (const plugin of options?.plugins || []) {\n\t\tif (plugin.init) {\n\t\t\tconst pluginRes = await plugin.init?.(url.toString(), options);\n\t\t\topts = pluginRes.options || opts;\n\t\t\turl = pluginRes.url;\n\t\t}\n\t\thooks.onRequest.push(plugin.hooks?.onRequest);\n\t\thooks.onResponse.push(plugin.hooks?.onResponse);\n\t\thooks.onSuccess.push(plugin.hooks?.onSuccess);\n\t\thooks.onError.push(plugin.hooks?.onError);\n\t\thooks.onRetry.push(plugin.hooks?.onRetry);\n\t}\n\n\treturn {\n\t\turl,\n\t\toptions: opts,\n\t\thooks,\n\t};\n};\n", "export type RetryCondition = (\n\tresponse: Response | null,\n) => boolean | Promise<boolean>;\n\nexport type LinearRetry = {\n\ttype: \"linear\";\n\tattempts: number;\n\tdelay: number;\n\tshouldRetry?: RetryCondition;\n};\n\nexport type ExponentialRetry = {\n\ttype: \"exponential\";\n\tattempts: number;\n\tbaseDelay: number;\n\tmaxDelay: number;\n\tshouldRetry?: RetryCondition;\n};\n\nexport type RetryOptions = LinearRetry | ExponentialRetry | number;\n\nexport interface RetryStrategy {\n\tshouldAttemptRetry(\n\t\tattempt: number,\n\t\tresponse: Response | null,\n\t): Promise<boolean>;\n\tgetDelay(attempt: number): number;\n}\n\nclass LinearRetryStrategy implements RetryStrategy {\n\tconstructor(private options: LinearRetry) {}\n\n\tshouldAttemptRetry(\n\t\tattempt: number,\n\t\tresponse: Response | null,\n\t): Promise<boolean> {\n\t\tif (this.options.shouldRetry) {\n\t\t\treturn Promise.resolve(\n\t\t\t\tattempt < this.options.attempts && this.options.shouldRetry(response),\n\t\t\t);\n\t\t}\n\t\treturn Promise.resolve(attempt < this.options.attempts);\n\t}\n\n\tgetDelay(): number {\n\t\treturn this.options.delay;\n\t}\n}\n\nclass ExponentialRetryStrategy implements RetryStrategy {\n\tconstructor(private options: ExponentialRetry) {}\n\n\tshouldAttemptRetry(\n\t\tattempt: number,\n\t\tresponse: Response | null,\n\t): Promise<boolean> {\n\t\tif (this.options.shouldRetry) {\n\t\t\treturn Promise.resolve(\n\t\t\t\tattempt < this.options.attempts && this.options.shouldRetry(response),\n\t\t\t);\n\t\t}\n\t\treturn Promise.resolve(attempt < this.options.attempts);\n\t}\n\n\tgetDelay(attempt: number): number {\n\t\tconst delay = Math.min(\n\t\t\tthis.options.maxDelay,\n\t\t\tthis.options.baseDelay * 2 ** attempt,\n\t\t);\n\t\treturn delay;\n\t}\n}\n\nexport function createRetryStrategy(options: RetryOptions): RetryStrategy {\n\tif (typeof options === \"number\") {\n\t\treturn new LinearRetryStrategy({\n\t\t\ttype: \"linear\",\n\t\t\tattempts: options,\n\t\t\tdelay: 1000,\n\t\t});\n\t}\n\n\tswitch (options.type) {\n\t\tcase \"linear\":\n\t\t\treturn new LinearRetryStrategy(options);\n\t\tcase \"exponential\":\n\t\t\treturn new ExponentialRetryStrategy(options);\n\t\tdefault:\n\t\t\tthrow new Error(\"Invalid retry strategy\");\n\t}\n}\n", "import type { BetterFetchOption } from \"./types\";\n\nexport type typeOrTypeReturning<T> = T | (() => T);\n/**\n * Bearer token authentication\n *\n * the value of `token` will be added to a header as\n * `auth: Bearer token`,\n */\nexport type Bearer = {\n\ttype: \"Bearer\";\n\ttoken: typeOrTypeReturning<string | undefined | Promise<string | undefined>>;\n};\n\n/**\n * Basic auth\n */\nexport type Basic = {\n\ttype: \"Basic\";\n\tusername: typeOrTypeReturning<string | undefined>;\n\tpassword: typeOrTypeReturning<string | undefined>;\n};\n\n/**\n * Custom auth\n *\n * @param prefix - prefix of the header\n * @param value - value of the header\n *\n * @example\n * ```ts\n * {\n *  type: \"Custom\",\n *  prefix: \"Token\",\n *  value: \"token\"\n * }\n * ```\n */\nexport type Custom = {\n\ttype: \"Custom\";\n\tprefix: typeOrTypeReturning<string | undefined>;\n\tvalue: typeOrTypeReturning<string | undefined>;\n};\n\nexport type Auth = Bearer | Basic | Custom;\n\nexport const getAuthHeader = async (options?: BetterFetchOption) => {\n\tconst headers: Record<string, string> = {};\n\tconst getValue = async (\n\t\tvalue: typeOrTypeReturning<\n\t\t\tstring | undefined | Promise<string | undefined>\n\t\t>,\n\t) => (typeof value === \"function\" ? await value() : value);\n\tif (options?.auth) {\n\t\tif (options.auth.type === \"Bearer\") {\n\t\t\tconst token = await getValue(options.auth.token);\n\t\t\tif (!token) {\n\t\t\t\treturn headers;\n\t\t\t}\n\t\t\theaders[\"authorization\"] = `Bearer ${token}`;\n\t\t} else if (options.auth.type === \"Basic\") {\n\t\t\tconst username = getValue(options.auth.username);\n\t\t\tconst password = getValue(options.auth.password);\n\t\t\tif (!username || !password) {\n\t\t\t\treturn headers;\n\t\t\t}\n\t\t\theaders[\"authorization\"] = `Basic ${btoa(`${username}:${password}`)}`;\n\t\t} else if (options.auth.type === \"Custom\") {\n\t\t\tconst value = getValue(options.auth.value);\n\t\t\tif (!value) {\n\t\t\t\treturn headers;\n\t\t\t}\n\t\t\theaders[\"authorization\"] = `${getValue(options.auth.prefix)} ${value}`;\n\t\t}\n\t}\n\treturn headers;\n};\n", "import type { StandardSchemaV1 } from \"./standard-schema\";\nimport { getAuthHeader } from \"./auth\";\nimport { methods } from \"./create-fetch\";\nimport type { BetterFetchOption, FetchEsque } from \"./types\";\n\nconst JSON_RE = /^application\\/(?:[\\w!#$%&*.^`~-]*\\+)?json(;.+)?$/i;\n\nexport type ResponseType = \"json\" | \"text\" | \"blob\";\nexport function detectResponseType(request: Response): ResponseType {\n\tconst _contentType = request.headers.get(\"content-type\");\n\tconst textTypes = new Set([\n\t\t\"image/svg\",\n\t\t\"application/xml\",\n\t\t\"application/xhtml\",\n\t\t\"application/html\",\n\t]);\n\tif (!_contentType) {\n\t\treturn \"json\";\n\t}\n\tconst contentType = _contentType.split(\";\").shift() || \"\";\n\tif (JSON_RE.test(contentType)) {\n\t\treturn \"json\";\n\t}\n\tif (textTypes.has(contentType) || contentType.startsWith(\"text/\")) {\n\t\treturn \"text\";\n\t}\n\treturn \"blob\";\n}\n\nexport function isJSONParsable(value: any) {\n\ttry {\n\t\tJSON.parse(value);\n\t\treturn true;\n\t} catch (error) {\n\t\treturn false;\n\t}\n}\n\n//https://github.com/unjs/ofetch/blob/main/src/utils.ts\nexport function isJSONSerializable(value: any) {\n\tif (value === undefined) {\n\t\treturn false;\n\t}\n\tconst t = typeof value;\n\tif (t === \"string\" || t === \"number\" || t === \"boolean\" || t === null) {\n\t\treturn true;\n\t}\n\tif (t !== \"object\") {\n\t\treturn false;\n\t}\n\tif (Array.isArray(value)) {\n\t\treturn true;\n\t}\n\tif (value.buffer) {\n\t\treturn false;\n\t}\n\treturn (\n\t\t(value.constructor && value.constructor.name === \"Object\") ||\n\t\ttypeof value.toJSON === \"function\"\n\t);\n}\n\nexport function jsonParse(text: string) {\n\ttry {\n\t\treturn JSON.parse(text);\n\t} catch (error) {\n\t\treturn text;\n\t}\n}\n\nexport function isFunction(value: any): value is () => any {\n\treturn typeof value === \"function\";\n}\n\nexport function getFetch(options?: BetterFetchOption): FetchEsque {\n\tif (options?.customFetchImpl) {\n\t\treturn options.customFetchImpl;\n\t}\n\tif (typeof globalThis !== \"undefined\" && isFunction(globalThis.fetch)) {\n\t\treturn globalThis.fetch;\n\t}\n\tif (typeof window !== \"undefined\" && isFunction(window.fetch)) {\n\t\treturn window.fetch;\n\t}\n\tthrow new Error(\"No fetch implementation found\");\n}\n\nexport function isPayloadMethod(method?: string) {\n\tif (!method) {\n\t\treturn false;\n\t}\n\tconst payloadMethod = [\"POST\", \"PUT\", \"PATCH\", \"DELETE\"];\n\treturn payloadMethod.includes(method.toUpperCase());\n}\n\nexport function isRouteMethod(method?: string) {\n\tconst routeMethod = [\"GET\", \"POST\", \"PUT\", \"PATCH\", \"DELETE\"];\n\tif (!method) {\n\t\treturn false;\n\t}\n\treturn routeMethod.includes(method.toUpperCase());\n}\n\nexport async function getHeaders(opts?: BetterFetchOption) {\n\tconst headers = new Headers(opts?.headers);\n\tconst authHeader = await getAuthHeader(opts);\n\tfor (const [key, value] of Object.entries(authHeader || {})) {\n\t\theaders.set(key, value);\n\t}\n\tif (!headers.has(\"content-type\")) {\n\t\tconst t = detectContentType(opts?.body);\n\t\tif (t) {\n\t\t\theaders.set(\"content-type\", t);\n\t\t}\n\t}\n\n\treturn headers;\n}\n\nexport function getURL(url: string, options?: BetterFetchOption) {\n\tif (url.startsWith(\"@\")) {\n\t\tconst m = url.toString().split(\"@\")[1].split(\"/\")[0];\n\t\tif (methods.includes(m)) {\n\t\t\turl = url.replace(`@${m}/`, \"/\");\n\t\t}\n\t}\n\tlet _url: string | URL;\n\ttry {\n\t\tif (url.startsWith(\"http\")) {\n\t\t\t_url = url;\n\t\t} else {\n\t\t\tlet baseURL = options?.baseURL;\n\t\t\tif (baseURL && !baseURL?.endsWith(\"/\")) {\n\t\t\t\tbaseURL = baseURL + \"/\";\n\t\t\t}\n\t\t\tif (url.startsWith(\"/\")) {\n\t\t\t\t_url = new URL(url.substring(1), baseURL);\n\t\t\t} else {\n\t\t\t\t_url = new URL(url, options?.baseURL);\n\t\t\t}\n\t\t}\n\t} catch (e) {\n\t\tif (e instanceof TypeError) {\n\t\t\tif (!options?.baseURL) {\n\t\t\t\tthrow TypeError(\n\t\t\t\t\t`Invalid URL ${url}. Are you passing in a relative url but not setting the baseURL?`,\n\t\t\t\t);\n\t\t\t}\n\t\t\tthrow TypeError(\n\t\t\t\t`Invalid URL ${url}. Please validate that you are passing the correct input.`,\n\t\t\t);\n\t\t}\n\t\tthrow e;\n\t}\n\n\t/**\n\t * Dynamic Parameters.\n\t */\n\tif (options?.params) {\n\t\tif (Array.isArray(options?.params)) {\n\t\t\tconst params = options?.params\n\t\t\t\t? Array.isArray(options.params)\n\t\t\t\t\t? `/${options.params.join(\"/\")}`\n\t\t\t\t\t: `/${Object.values(options.params).join(\"/\")}`\n\t\t\t\t: \"\";\n\t\t\t_url = _url.toString().split(\"/:\")[0];\n\t\t\t_url = `${_url.toString()}${params}`;\n\t\t} else {\n\t\t\tfor (const [key, value] of Object.entries(options?.params)) {\n\t\t\t\t_url = _url.toString().replace(`:${key}`, String(value));\n\t\t\t}\n\t\t}\n\t}\n\tconst __url = new URL(_url);\n\t/**\n\t * Query Parameters\n\t */\n\tconst queryParams = options?.query;\n\tif (queryParams) {\n\t\tfor (const [key, value] of Object.entries(queryParams)) {\n\t\t\t__url.searchParams.append(key, String(value));\n\t\t}\n\t}\n\treturn __url;\n}\n\nexport function detectContentType(body: any) {\n\tif (isJSONSerializable(body)) {\n\t\treturn \"application/json\";\n\t}\n\n\treturn null;\n}\n\nexport function getBody(options?: BetterFetchOption) {\n\tif (!options?.body) {\n\t\treturn null;\n\t}\n\tconst headers = new Headers(options?.headers);\n\tif (isJSONSerializable(options.body) && !headers.has(\"content-type\")) {\n\t\tfor (const [key, value] of Object.entries(options?.body)) {\n\t\t\tif (value instanceof Date) {\n\t\t\t\toptions.body[key] = value.toISOString();\n\t\t\t}\n\t\t}\n\t\treturn JSON.stringify(options.body);\n\t}\n\n\treturn options.body;\n}\n\nexport function getMethod(url: string, options?: BetterFetchOption) {\n\tif (options?.method) {\n\t\treturn options.method.toUpperCase();\n\t}\n\tif (url.startsWith(\"@\")) {\n\t\tconst pMethod = url.split(\"@\")[1]?.split(\"/\")[0];\n\t\tif (!methods.includes(pMethod)) {\n\t\t\treturn options?.body ? \"POST\" : \"GET\";\n\t\t}\n\t\treturn pMethod.toUpperCase();\n\t}\n\treturn options?.body ? \"POST\" : \"GET\";\n}\n\nexport function getTimeout(\n\toptions?: BetterFetchOption,\n\tcontroller?: AbortController,\n) {\n\tlet abortTimeout: ReturnType<typeof setTimeout> | undefined;\n\tif (!options?.signal && options?.timeout) {\n\t\tabortTimeout = setTimeout(() => controller?.abort(), options?.timeout);\n\t}\n\treturn {\n\t\tabortTimeout,\n\t\tclearTimeout: () => {\n\t\t\tif (abortTimeout) {\n\t\t\t\tclearTimeout(abortTimeout);\n\t\t\t}\n\t\t},\n\t};\n}\n\nexport function bodyParser(data: any, responseType: ResponseType) {\n\tif (responseType === \"json\") {\n\t\treturn JSON.parse(data);\n\t}\n\treturn data;\n}\n\nexport class ValidationError extends Error {\n\tpublic readonly issues: ReadonlyArray<StandardSchemaV1.Issue>;\n\n\tconstructor(issues: ReadonlyArray<StandardSchemaV1.Issue>, message?: string) {\n\t\t// Default message fallback in case one isn't supplied.\n\t\tsuper(message || JSON.stringify(issues, null, 2));\n\t\tthis.issues = issues;\n\n\t\t// Set the prototype explicitly to ensure that instanceof works correctly.\n\t\tObject.setPrototypeOf(this, ValidationError.prototype);\n\t}\n}\n\nexport async function parseStandardSchema<TSchema extends StandardSchemaV1>(\n\tschema: TSchema,\n\tinput: StandardSchemaV1.InferInput<TSchema>,\n): Promise<StandardSchemaV1.InferOutput<TSchema>> {\n\tlet result = await schema[\"~standard\"].validate(input);\n\n\tif (result.issues) {\n\t\tthrow new ValidationError(result.issues);\n\t}\n\treturn result.value;\n}\n", "import type { StandardSchemaV1 } from \"../standard-schema\";\nimport type { StringLiteralUnion } from \"../type-utils\";\n\nexport type FetchSchema = {\n\tinput?: StandardSchemaV1;\n\toutput?: StandardSchemaV1;\n\tquery?: StandardSchemaV1;\n\tparams?: StandardSchemaV1<Record<string, unknown>> | undefined;\n\tmethod?: Methods;\n};\n\nexport type Methods = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\n\nexport const methods = [\"get\", \"post\", \"put\", \"patch\", \"delete\"];\n\ntype RouteKey = StringLiteralUnion<`@${Methods}/`>;\n\nexport type FetchSchemaRoutes = {\n\t[key in RouteKey]?: FetchSchema;\n};\n\nexport const createSchema = <\n\tF extends FetchSchemaRoutes,\n\tS extends SchemaConfig,\n>(\n\tschema: F,\n\tconfig?: S,\n) => {\n\treturn {\n\t\tschema: schema as F,\n\t\tconfig: config as S,\n\t};\n};\n\nexport type SchemaConfig = {\n\tstrict?: boolean;\n\t/**\n\t * A prefix that will be prepended when it's\n\t * calling the schema.\n\t *\n\t * NOTE: Make sure to handle converting\n\t * the prefix to the baseURL in the init\n\t * function if you you are defining for a\n\t * plugin.\n\t */\n\tprefix?: \"\" | (string & Record<never, never>);\n\t/**\n\t * The base url of the schema. By default it's the baseURL of the fetch instance.\n\t */\n\tbaseURL?: \"\" | (string & Record<never, never>);\n};\n\nexport type Schema = {\n\tschema: FetchSchemaRoutes;\n\tconfig: SchemaConfig;\n};\n", "import { betterFetch } from \"../fetch\";\nimport { BetterFetchPlugin } from \"../plugins\";\nimport type { BetterFetchOption } from \"../types\";\nimport { parseStandardSchema } from \"../utils\";\nimport type { BetterFetch, CreateFetchOption } from \"./types\";\n\nexport const applySchemaPlugin = (config: CreateFetchOption) =>\n\t({\n\t\tid: \"apply-schema\",\n\t\tname: \"Apply Schema\",\n\t\tversion: \"1.0.0\",\n\t\tasync init(url, options) {\n\t\t\tconst schema =\n\t\t\t\tconfig.plugins?.find((plugin) =>\n\t\t\t\t\tplugin.schema?.config\n\t\t\t\t\t\t? url.startsWith(plugin.schema.config.baseURL || \"\") ||\n\t\t\t\t\t\t\turl.startsWith(plugin.schema.config.prefix || \"\")\n\t\t\t\t\t\t: false,\n\t\t\t\t)?.schema || config.schema;\n\t\t\tif (schema) {\n\t\t\t\tlet urlKey = url;\n\t\t\t\tif (schema.config?.prefix) {\n\t\t\t\t\tif (urlKey.startsWith(schema.config.prefix)) {\n\t\t\t\t\t\turlKey = urlKey.replace(schema.config.prefix, \"\");\n\t\t\t\t\t\tif (schema.config.baseURL) {\n\t\t\t\t\t\t\turl = url.replace(schema.config.prefix, schema.config.baseURL);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (schema.config?.baseURL) {\n\t\t\t\t\tif (urlKey.startsWith(schema.config.baseURL)) {\n\t\t\t\t\t\turlKey = urlKey.replace(schema.config.baseURL, \"\");\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst keySchema = schema.schema[urlKey];\n\t\t\t\tif (keySchema) {\n\t\t\t\t\tlet opts = {\n\t\t\t\t\t\t...options,\n\t\t\t\t\t\tmethod: keySchema.method,\n\t\t\t\t\t\toutput: keySchema.output,\n\t\t\t\t\t};\n\t\t\t\t\tif (!options?.disableValidation) {\n\t\t\t\t\t\topts = {\n\t\t\t\t\t\t\t...opts,\n\t\t\t\t\t\t\tbody: keySchema.input\n\t\t\t\t\t\t\t\t? await parseStandardSchema(keySchema.input, options?.body)\n\t\t\t\t\t\t\t\t: options?.body,\n\t\t\t\t\t\t\tparams: keySchema.params\n\t\t\t\t\t\t\t\t? await parseStandardSchema(keySchema.params, options?.params)\n\t\t\t\t\t\t\t\t: options?.params,\n\t\t\t\t\t\t\tquery: keySchema.query\n\t\t\t\t\t\t\t\t? await parseStandardSchema(keySchema.query, options?.query)\n\t\t\t\t\t\t\t\t: options?.query,\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\treturn {\n\t\t\t\t\t\turl,\n\t\t\t\t\t\toptions: opts,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn {\n\t\t\t\turl,\n\t\t\t\toptions,\n\t\t\t};\n\t\t},\n\t}) satisfies BetterFetchPlugin;\n\nexport const createFetch = <Option extends CreateFetchOption>(\n\tconfig?: Option,\n) => {\n\tasync function $fetch(url: string, options?: BetterFetchOption) {\n\t\tconst opts = {\n\t\t\t...config,\n\t\t\t...options,\n\t\t\tplugins: [...(config?.plugins || []), applySchemaPlugin(config || {})],\n\t\t} as BetterFetchOption;\n\n\t\tif (config?.catchAllError) {\n\t\t\ttry {\n\t\t\t\treturn await betterFetch(url, opts);\n\t\t\t} catch (error) {\n\t\t\t\treturn {\n\t\t\t\t\tdata: null,\n\t\t\t\t\terror: {\n\t\t\t\t\t\tstatus: 500,\n\t\t\t\t\t\tstatusText: \"Fetch Error\",\n\t\t\t\t\t\tmessage:\n\t\t\t\t\t\t\t\"Fetch related error. Captured by catchAllError option. See error property for more details.\",\n\t\t\t\t\t\terror,\n\t\t\t\t\t},\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\treturn await betterFetch(url, opts);\n\t}\n\treturn $fetch as BetterFetch<Option>;\n};\n\nexport * from \"./schema\";\nexport * from \"./types\";\n", "import { methods } from \"./create-fetch\";\nimport { BetterFetchOption } from \"./types\";\n\n/**\n * Normalize URL\n */\nexport function getURL(url: string, option?: BetterFetchOption) {\n\tlet { baseURL, params, query } = option || {\n\t\tquery: {},\n\t\tparams: {},\n\t\tbaseURL: \"\",\n\t};\n\tlet basePath = url.startsWith(\"http\")\n\t\t? url.split(\"/\").slice(0, 3).join(\"/\")\n\t\t: baseURL || \"\";\n\n\t/**\n\t * Remove method modifiers\n\t */\n\tif (url.startsWith(\"@\")) {\n\t\tconst m = url.toString().split(\"@\")[1].split(\"/\")[0];\n\t\tif (methods.includes(m)) {\n\t\t\turl = url.replace(`@${m}/`, \"/\");\n\t\t}\n\t}\n\n\tif (!basePath.endsWith(\"/\")) basePath += \"/\";\n\tlet [path, urlQuery] = url.replace(basePath, \"\").split(\"?\");\n\tconst queryParams = new URLSearchParams(urlQuery);\n\tfor (const [key, value] of Object.entries(query || {})) {\n\t\tif (value == null) continue;\n\t\tqueryParams.set(key, String(value));\n\t}\n\tif (params) {\n\t\tif (Array.isArray(params)) {\n\t\t\tconst paramPaths = path.split(\"/\").filter((p) => p.startsWith(\":\"));\n\t\t\tfor (const [index, key] of paramPaths.entries()) {\n\t\t\t\tconst value = params[index];\n\t\t\t\tpath = path.replace(key, value);\n\t\t\t}\n\t\t} else {\n\t\t\tfor (const [key, value] of Object.entries(params)) {\n\t\t\t\tpath = path.replace(`:${key}`, String(value));\n\t\t\t}\n\t\t}\n\t}\n\n\tpath = path.split(\"/\").map(encodeURIComponent).join(\"/\");\n\tif (path.startsWith(\"/\")) path = path.slice(1);\n\tlet queryParamString = queryParams.toString();\n\tqueryParamString =\n\t\tqueryParamString.length > 0 ? `?${queryParamString}`.replace(/\\+/g, \"%20\") : \"\";\n\tif (!basePath.startsWith(\"http\")) {\n\t\treturn `${basePath}${path}${queryParamString}`;\n\t}\n\tconst _url = new URL(`${path}${queryParamString}`, basePath);\n\treturn _url;\n}\n", "import type { StandardSchemaV1 } from \"./standard-schema\";\nimport { BetterFetchError } from \"./error\";\nimport { initializePlugins } from \"./plugins\";\nimport { createRetryStrategy } from \"./retry\";\nimport type { BetterFetchOption, BetterFetchResponse } from \"./types\";\nimport { getURL } from \"./url\";\nimport {\n\tdetectResponseType,\n\tgetBody,\n\tgetFetch,\n\tgetHeaders,\n\tgetMethod,\n\tgetTimeout,\n\tisJSONParsable,\n\tjsonParse,\n\tparseStandardSchema,\n} from \"./utils\";\n\nexport const betterFetch = async <\n\tTRes extends Option[\"output\"] extends StandardSchemaV1\n\t\t? StandardSchemaV1.InferOutput<Option[\"output\"]>\n\t\t: unknown,\n\tTErr = unknown,\n\tOption extends BetterFetchOption = BetterFetchOption<any, any, any, TRes>,\n>(\n\turl: string,\n\toptions?: Option,\n): Promise<\n\tBetterFetchResponse<\n\t\tTRes,\n\t\tTErr,\n\t\tOption[\"throw\"] extends true ? true : TErr extends false ? true : false\n\t>\n> => {\n\tconst {\n\t\thooks,\n\t\turl: __url,\n\t\toptions: opts,\n\t} = await initializePlugins(url, options);\n\tconst fetch = getFetch(opts);\n\tconst controller = new AbortController();\n\tconst signal = opts.signal ?? controller.signal;\n\tconst _url = getURL(__url, opts);\n\tconst body = getBody(opts);\n\tconst headers = await getHeaders(opts);\n\tconst method = getMethod(__url, opts);\n\tlet context = {\n\t\t...opts,\n\t\turl: _url,\n\t\theaders,\n\t\tbody,\n\t\tmethod,\n\t\tsignal,\n\t};\n\t/**\n\t * Run all on request hooks\n\t */\n\tfor (const onRequest of hooks.onRequest) {\n\t\tif (onRequest) {\n\t\t\tconst res = await onRequest(context);\n\t\t\tif (res instanceof Object) {\n\t\t\t\tcontext = res;\n\t\t\t}\n\t\t}\n\t}\n\tif (\n\t\t(\"pipeTo\" in (context as any) &&\n\t\t\ttypeof (context as any).pipeTo === \"function\") ||\n\t\ttypeof options?.body?.pipe === \"function\"\n\t) {\n\t\tif (!(\"duplex\" in context)) {\n\t\t\tcontext.duplex = \"half\";\n\t\t}\n\t}\n\n\tconst { clearTimeout } = getTimeout(opts, controller);\n\tlet response = await fetch(context.url, context);\n\tclearTimeout();\n\n\tconst responseContext = {\n\t\tresponse,\n\t\trequest: context,\n\t};\n\n\tfor (const onResponse of hooks.onResponse) {\n\t\tif (onResponse) {\n\t\t\tconst r = await onResponse({\n\t\t\t\t...responseContext,\n\t\t\t\tresponse: options?.hookOptions?.cloneResponse\n\t\t\t\t\t? response.clone()\n\t\t\t\t\t: response,\n\t\t\t});\n\t\t\tif (r instanceof Response) {\n\t\t\t\tresponse = r;\n\t\t\t} else if (r instanceof Object) {\n\t\t\t\tresponse = r.response;\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * OK Branch\n\t */\n\tif (response.ok) {\n\t\tconst hasBody = context.method !== \"HEAD\";\n\t\tif (!hasBody) {\n\t\t\treturn {\n\t\t\t\tdata: \"\" as any,\n\t\t\t\terror: null,\n\t\t\t} as any;\n\t\t}\n\t\tconst responseType = detectResponseType(response);\n\t\tconst successContext = {\n\t\t\tdata: \"\" as any,\n\t\t\tresponse,\n\t\t\trequest: context,\n\t\t};\n\t\tif (responseType === \"json\" || responseType === \"text\") {\n\t\t\tconst text = await response.text();\n\t\t\tconst parser = context.jsonParser ?? jsonParse;\n\t\t\tconst data = await parser(text);\n\t\t\tsuccessContext.data = data;\n\t\t} else {\n\t\t\tsuccessContext.data = await response[responseType]();\n\t\t}\n\n\t\t/**\n\t\t * Parse the data if the output schema is defined\n\t\t */\n\t\tif (context?.output) {\n\t\t\tif (context.output && !context.disableValidation) {\n\t\t\t\tsuccessContext.data = await parseStandardSchema(\n\t\t\t\t\tcontext.output as StandardSchemaV1,\n\t\t\t\t\tsuccessContext.data,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tfor (const onSuccess of hooks.onSuccess) {\n\t\t\tif (onSuccess) {\n\t\t\t\tawait onSuccess({\n\t\t\t\t\t...successContext,\n\t\t\t\t\tresponse: options?.hookOptions?.cloneResponse\n\t\t\t\t\t\t? response.clone()\n\t\t\t\t\t\t: response,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (options?.throw) {\n\t\t\treturn successContext.data as any;\n\t\t}\n\n\t\treturn {\n\t\t\tdata: successContext.data,\n\t\t\terror: null,\n\t\t} as any;\n\t}\n\tconst parser = options?.jsonParser ?? jsonParse;\n\tconst responseText = await response.text();\n\tconst isJSONResponse = isJSONParsable(responseText);\n\tconst errorObject = isJSONResponse ? await parser(responseText) : null;\n\t/**\n\t * Error Branch\n\t */\n\tconst errorContext = {\n\t\tresponse,\n\t\tresponseText,\n\t\trequest: context,\n\t\terror: {\n\t\t\t...errorObject,\n\t\t\tstatus: response.status,\n\t\t\tstatusText: response.statusText,\n\t\t},\n\t};\n\tfor (const onError of hooks.onError) {\n\t\tif (onError) {\n\t\t\tawait onError({\n\t\t\t\t...errorContext,\n\t\t\t\tresponse: options?.hookOptions?.cloneResponse\n\t\t\t\t\t? response.clone()\n\t\t\t\t\t: response,\n\t\t\t});\n\t\t}\n\t}\n\n\tif (options?.retry) {\n\t\tconst retryStrategy = createRetryStrategy(options.retry);\n\t\tconst _retryAttempt = options.retryAttempt ?? 0;\n\t\tif (await retryStrategy.shouldAttemptRetry(_retryAttempt, response)) {\n\t\t\tfor (const onRetry of hooks.onRetry) {\n\t\t\t\tif (onRetry) {\n\t\t\t\t\tawait onRetry(responseContext);\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst delay = retryStrategy.getDelay(_retryAttempt);\n\t\t\tawait new Promise((resolve) => setTimeout(resolve, delay));\n\t\t\treturn await betterFetch(url, {\n\t\t\t\t...options,\n\t\t\t\tretryAttempt: _retryAttempt + 1,\n\t\t\t});\n\t\t}\n\t}\n\n\tif (options?.throw) {\n\t\tthrow new BetterFetchError(\n\t\t\tresponse.status,\n\t\t\tresponse.statusText,\n\t\t\tisJSONResponse ? errorObject : responseText,\n\t\t);\n\t}\n\treturn {\n\t\tdata: null,\n\t\terror: {\n\t\t\t...errorObject,\n\t\t\tstatus: response.status,\n\t\t\tstatusText: response.statusText,\n\t\t},\n\t} as any;\n};\n"], "names": ["_a", "getURL", "getURL", "clearTimeout", "parser"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAM,mBAAN,cAA+B,MAAM;IAC3C,YACQ,MAAA,EACA,UAAA,EACA,KAAA,CACN;QACD,KAAA,CAAM,cAAc,OAAO,QAAA,CAAS,GAAG;YACtC,OAAO;QACR,CAAC;QANM,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QACA,IAAA,CAAA,KAAA,GAAA;IAKR;AACD;;AC2HO,IAAM,oBAAoB,OAChC,KACA,YACI;IAxIL,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;IAyIC,IAAI,OAAO,WAAW,CAAC;IACvB,MAAM,QAMF;QACH,WAAW;YAAC,WAAA,OAAA,KAAA,IAAA,QAAS,SAAS;SAAA;QAC9B,YAAY;YAAC,WAAA,OAAA,KAAA,IAAA,QAAS,UAAU;SAAA;QAChC,WAAW;YAAC,WAAA,OAAA,KAAA,IAAA,QAAS,SAAS;SAAA;QAC9B,SAAS;YAAC,WAAA,OAAA,KAAA,IAAA,QAAS,OAAO;SAAA;QAC1B,SAAS;YAAC,WAAA,OAAA,KAAA,IAAA,QAAS,OAAO;SAAA;IAC3B;IACA,IAAI,CAAC,WAAW,CAAA,CAAC,WAAA,OAAA,KAAA,IAAA,QAAS,OAAA,GAAS;QAClC,OAAO;YACN;YACA,SAAS;YACT;QACD;IACD;IACA,KAAA,MAAW,UAAA,CAAU,WAAA,OAAA,KAAA,IAAA,QAAS,OAAA,KAAW,CAAC,CAAA,CAAG;QAC5C,IAAI,OAAO,IAAA,EAAM;YAChB,MAAM,YAAY,MAAA,CAAA,CAAM,KAAA,OAAO,IAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAc,IAAI,QAAA,CAAS,GAAG,QAAA;YACtD,OAAO,UAAU,OAAA,IAAW;YAC5B,MAAM,UAAU,GAAA;QACjB;QACA,MAAM,SAAA,CAAU,IAAA,CAAA,CAAK,KAAA,OAAO,KAAA,KAAP,OAAA,KAAA,IAAA,GAAc,SAAS;QAC5C,MAAM,UAAA,CAAW,IAAA,CAAA,CAAK,KAAA,OAAO,KAAA,KAAP,OAAA,KAAA,IAAA,GAAc,UAAU;QAC9C,MAAM,SAAA,CAAU,IAAA,CAAA,CAAK,KAAA,OAAO,KAAA,KAAP,OAAA,KAAA,IAAA,GAAc,SAAS;QAC5C,MAAM,OAAA,CAAQ,IAAA,CAAA,CAAK,KAAA,OAAO,KAAA,KAAP,OAAA,KAAA,IAAA,GAAc,OAAO;QACxC,MAAM,OAAA,CAAQ,IAAA,CAAA,CAAK,KAAA,OAAO,KAAA,KAAP,OAAA,KAAA,IAAA,GAAc,OAAO;IACzC;IAEA,OAAO;QACN;QACA,SAAS;QACT;IACD;AACD;;ACnJA,IAAM,sBAAN,MAAmD;IAClD,YAAoB,OAAA,CAAsB;QAAtB,IAAA,CAAA,OAAA,GAAA;IAAuB;IAE3C,mBACC,OAAA,EACA,QAAA,EACmB;QACnB,IAAI,IAAA,CAAK,OAAA,CAAQ,WAAA,EAAa;YAC7B,OAAO,QAAQ,OAAA,CACd,UAAU,IAAA,CAAK,OAAA,CAAQ,QAAA,IAAY,IAAA,CAAK,OAAA,CAAQ,WAAA,CAAY,QAAQ;QAEtE;QACA,OAAO,QAAQ,OAAA,CAAQ,UAAU,IAAA,CAAK,OAAA,CAAQ,QAAQ;IACvD;IAEA,WAAmB;QAClB,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAA;IACrB;AACD;AAEA,IAAM,2BAAN,MAAwD;IACvD,YAAoB,OAAA,CAA2B;QAA3B,IAAA,CAAA,OAAA,GAAA;IAA4B;IAEhD,mBACC,OAAA,EACA,QAAA,EACmB;QACnB,IAAI,IAAA,CAAK,OAAA,CAAQ,WAAA,EAAa;YAC7B,OAAO,QAAQ,OAAA,CACd,UAAU,IAAA,CAAK,OAAA,CAAQ,QAAA,IAAY,IAAA,CAAK,OAAA,CAAQ,WAAA,CAAY,QAAQ;QAEtE;QACA,OAAO,QAAQ,OAAA,CAAQ,UAAU,IAAA,CAAK,OAAA,CAAQ,QAAQ;IACvD;IAEA,SAAS,OAAA,EAAyB;QACjC,MAAM,QAAQ,KAAK,GAAA,CAClB,IAAA,CAAK,OAAA,CAAQ,QAAA,EACb,IAAA,CAAK,OAAA,CAAQ,SAAA,GAAY,KAAK;QAE/B,OAAO;IACR;AACD;AAEO,SAAS,oBAAoB,OAAA,EAAsC;IACzE,IAAI,OAAO,YAAY,UAAU;QAChC,OAAO,IAAI,oBAAoB;YAC9B,MAAM;YACN,UAAU;YACV,OAAO;QACR,CAAC;IACF;IAEA,OAAQ,QAAQ,IAAA,EAAM;QACrB,KAAK;YACJ,OAAO,IAAI,oBAAoB,OAAO;QACvC,KAAK;YACJ,OAAO,IAAI,yBAAyB,OAAO;QAC5C;YACC,MAAM,IAAI,MAAM,wBAAwB;IAC1C;AACD;;AC5CO,IAAM,gBAAgB,OAAO,YAAgC;IACnE,MAAM,UAAkC,CAAC;IACzC,MAAM,WAAW,OAChB,QAGK,OAAO,UAAU,aAAa,MAAM,MAAM,IAAI;IACpD,IAAI,WAAA,OAAA,KAAA,IAAA,QAAS,IAAA,EAAM;QAClB,IAAI,QAAQ,IAAA,CAAK,IAAA,KAAS,UAAU;YACnC,MAAM,QAAQ,MAAM,SAAS,QAAQ,IAAA,CAAK,KAAK;YAC/C,IAAI,CAAC,OAAO;gBACX,OAAO;YACR;YACA,OAAA,CAAQ,eAAe,CAAA,GAAI,CAAA,OAAA,EAAU,KAAK,EAAA;QAC3C,OAAA,IAAW,QAAQ,IAAA,CAAK,IAAA,KAAS,SAAS;YACzC,MAAM,WAAW,SAAS,QAAQ,IAAA,CAAK,QAAQ;YAC/C,MAAM,WAAW,SAAS,QAAQ,IAAA,CAAK,QAAQ;YAC/C,IAAI,CAAC,YAAY,CAAC,UAAU;gBAC3B,OAAO;YACR;YACA,OAAA,CAAQ,eAAe,CAAA,GAAI,CAAA,MAAA,EAAS,KAAK,GAAG,QAAQ,CAAA,CAAA,EAAI,QAAQ,EAAE,CAAC,EAAA;QACpE,OAAA,IAAW,QAAQ,IAAA,CAAK,IAAA,KAAS,UAAU;YAC1C,MAAM,QAAQ,SAAS,QAAQ,IAAA,CAAK,KAAK;YACzC,IAAI,CAAC,OAAO;gBACX,OAAO;YACR;YACA,OAAA,CAAQ,eAAe,CAAA,GAAI,GAAG,SAAS,QAAQ,IAAA,CAAK,MAAM,CAAC,CAAA,CAAA,EAAI,KAAK,EAAA;QACrE;IACD;IACA,OAAO;AACR;;ACvEA,IAAM,UAAU;AAGT,SAAS,mBAAmB,OAAA,EAAiC;IACnE,MAAM,eAAe,QAAQ,OAAA,CAAQ,GAAA,CAAI,cAAc;IACvD,MAAM,YAAY,aAAA,GAAA,IAAI,IAAI;QACzB;QACA;QACA;QACA;KACA;IACD,IAAI,CAAC,cAAc;QAClB,OAAO;IACR;IACA,MAAM,cAAc,aAAa,KAAA,CAAM,GAAG,EAAE,KAAA,CAAM,KAAK;IACvD,IAAI,QAAQ,IAAA,CAAK,WAAW,GAAG;QAC9B,OAAO;IACR;IACA,IAAI,UAAU,GAAA,CAAI,WAAW,KAAK,YAAY,UAAA,CAAW,OAAO,GAAG;QAClE,OAAO;IACR;IACA,OAAO;AACR;AAEO,SAAS,eAAe,KAAA,EAAY;IAC1C,IAAI;QACH,KAAK,KAAA,CAAM,KAAK;QAChB,OAAO;IACR,EAAA,OAAS,OAAO;QACf,OAAO;IACR;AACD;AAGO,SAAS,mBAAmB,KAAA,EAAY;IAC9C,IAAI,UAAU,KAAA,GAAW;QACxB,OAAO;IACR;IACA,MAAM,IAAI,OAAO;IACjB,IAAI,MAAM,YAAY,MAAM,YAAY,MAAM,aAAa,MAAM,MAAM;QACtE,OAAO;IACR;IACA,IAAI,MAAM,UAAU;QACnB,OAAO;IACR;IACA,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;QACzB,OAAO;IACR;IACA,IAAI,MAAM,MAAA,EAAQ;QACjB,OAAO;IACR;IACA,OACE,MAAM,WAAA,IAAe,MAAM,WAAA,CAAY,IAAA,KAAS,YACjD,OAAO,MAAM,MAAA,KAAW;AAE1B;AAEO,SAAS,UAAU,IAAA,EAAc;IACvC,IAAI;QACH,OAAO,KAAK,KAAA,CAAM,IAAI;IACvB,EAAA,OAAS,OAAO;QACf,OAAO;IACR;AACD;AAEO,SAAS,WAAW,KAAA,EAAgC;IAC1D,OAAO,OAAO,UAAU;AACzB;AAEO,SAAS,SAAS,OAAA,EAAyC;IACjE,IAAI,WAAA,OAAA,KAAA,IAAA,QAAS,eAAA,EAAiB;QAC7B,OAAO,QAAQ,eAAA;IAChB;IACA,IAAI,OAAO,eAAe,eAAe,WAAW,WAAW,KAAK,GAAG;QACtE,OAAO,WAAW,KAAA;IACnB;IACA,IAAI,OAAO,WAAW,eAAe,WAAW,OAAO,KAAK,GAAG;QAC9D,OAAO,OAAO,KAAA;IACf;IACA,MAAM,IAAI,MAAM,+BAA+B;AAChD;AAEO,SAAS,gBAAgB,MAAA,EAAiB;IAChD,IAAI,CAAC,QAAQ;QACZ,OAAO;IACR;IACA,MAAM,gBAAgB;QAAC;QAAQ;QAAO;QAAS,QAAQ;KAAA;IACvD,OAAO,cAAc,QAAA,CAAS,OAAO,WAAA,CAAY,CAAC;AACnD;AAEO,SAAS,cAAc,MAAA,EAAiB;IAC9C,MAAM,cAAc;QAAC;QAAO;QAAQ;QAAO;QAAS,QAAQ;KAAA;IAC5D,IAAI,CAAC,QAAQ;QACZ,OAAO;IACR;IACA,OAAO,YAAY,QAAA,CAAS,OAAO,WAAA,CAAY,CAAC;AACjD;AAEA,eAAsB,WAAW,IAAA,EAA0B;IAC1D,MAAM,UAAU,IAAI,QAAQ,QAAA,OAAA,KAAA,IAAA,KAAM,OAAO;IACzC,MAAM,aAAa,MAAM,cAAc,IAAI;IAC3C,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,cAAc,CAAC,CAAC,EAAG;QAC5D,QAAQ,GAAA,CAAI,KAAK,KAAK;IACvB;IACA,IAAI,CAAC,QAAQ,GAAA,CAAI,cAAc,GAAG;QACjC,MAAM,IAAI,kBAAkB,QAAA,OAAA,KAAA,IAAA,KAAM,IAAI;QACtC,IAAI,GAAG;YACN,QAAQ,GAAA,CAAI,gBAAgB,CAAC;QAC9B;IACD;IAEA,OAAO;AACR;AAEO,SAAS,OAAO,GAAA,EAAa,OAAA,EAA6B;IAChE,IAAI,IAAI,UAAA,CAAW,GAAG,GAAG;QACxB,MAAM,IAAI,IAAI,QAAA,CAAS,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA;QACnD,IAAI,QAAQ,QAAA,CAAS,CAAC,GAAG;YACxB,MAAM,IAAI,OAAA,CAAQ,CAAA,CAAA,EAAI,CAAC,CAAA,CAAA,CAAA,EAAK,GAAG;QAChC;IACD;IACA,IAAI;IACJ,IAAI;QACH,IAAI,IAAI,UAAA,CAAW,MAAM,GAAG;YAC3B,OAAO;QACR,OAAO;YACN,IAAI,UAAU,WAAA,OAAA,KAAA,IAAA,QAAS,OAAA;YACvB,IAAI,WAAW,CAAA,CAAC,WAAA,OAAA,KAAA,IAAA,QAAS,QAAA,CAAS,IAAA,GAAM;gBACvC,UAAU,UAAU;YACrB;YACA,IAAI,IAAI,UAAA,CAAW,GAAG,GAAG;gBACxB,OAAO,IAAI,IAAI,IAAI,SAAA,CAAU,CAAC,GAAG,OAAO;YACzC,OAAO;gBACN,OAAO,IAAI,IAAI,KAAK,WAAA,OAAA,KAAA,IAAA,QAAS,OAAO;YACrC;QACD;IACD,EAAA,OAAS,GAAG;QACX,IAAI,aAAa,WAAW;YAC3B,IAAI,CAAA,CAAC,WAAA,OAAA,KAAA,IAAA,QAAS,OAAA,GAAS;gBACtB,MAAM,UACL,CAAA,YAAA,EAAe,GAAG,CAAA,gEAAA,CAAA;YAEpB;YACA,MAAM,UACL,CAAA,YAAA,EAAe,GAAG,CAAA,yDAAA,CAAA;QAEpB;QACA,MAAM;IACP;IAKA,IAAI,WAAA,OAAA,KAAA,IAAA,QAAS,MAAA,EAAQ;QACpB,IAAI,MAAM,OAAA,CAAQ,WAAA,OAAA,KAAA,IAAA,QAAS,MAAM,GAAG;YACnC,MAAM,SAAA,CAAS,WAAA,OAAA,KAAA,IAAA,QAAS,MAAA,IACrB,MAAM,OAAA,CAAQ,QAAQ,MAAM,IAC3B,CAAA,CAAA,EAAI,QAAQ,MAAA,CAAO,IAAA,CAAK,GAAG,CAAC,EAAA,GAC5B,CAAA,CAAA,EAAI,OAAO,MAAA,CAAO,QAAQ,MAAM,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA,GAC5C;YACH,OAAO,KAAK,QAAA,CAAS,EAAE,KAAA,CAAM,IAAI,CAAA,CAAE,CAAC,CAAA;YACpC,OAAO,GAAG,KAAK,QAAA,CAAS,CAAC,GAAG,MAAM,EAAA;QACnC,OAAO;YACN,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,WAAA,OAAA,KAAA,IAAA,QAAS,MAAM,EAAG;gBAC3D,OAAO,KAAK,QAAA,CAAS,EAAE,OAAA,CAAQ,CAAA,CAAA,EAAI,GAAG,EAAA,EAAI,OAAO,KAAK,CAAC;YACxD;QACD;IACD;IACA,MAAM,QAAQ,IAAI,IAAI,IAAI;IAI1B,MAAM,cAAc,WAAA,OAAA,KAAA,IAAA,QAAS,KAAA;IAC7B,IAAI,aAAa;QAChB,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,WAAW,EAAG;YACvD,MAAM,YAAA,CAAa,MAAA,CAAO,KAAK,OAAO,KAAK,CAAC;QAC7C;IACD;IACA,OAAO;AACR;AAEO,SAAS,kBAAkB,IAAA,EAAW;IAC5C,IAAI,mBAAmB,IAAI,GAAG;QAC7B,OAAO;IACR;IAEA,OAAO;AACR;AAEO,SAAS,QAAQ,OAAA,EAA6B;IACpD,IAAI,CAAA,CAAC,WAAA,OAAA,KAAA,IAAA,QAAS,IAAA,GAAM;QACnB,OAAO;IACR;IACA,MAAM,UAAU,IAAI,QAAQ,WAAA,OAAA,KAAA,IAAA,QAAS,OAAO;IAC5C,IAAI,mBAAmB,QAAQ,IAAI,KAAK,CAAC,QAAQ,GAAA,CAAI,cAAc,GAAG;QACrE,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,WAAA,OAAA,KAAA,IAAA,QAAS,IAAI,EAAG;YACzD,IAAI,iBAAiB,MAAM;gBAC1B,QAAQ,IAAA,CAAK,GAAG,CAAA,GAAI,MAAM,WAAA,CAAY;YACvC;QACD;QACA,OAAO,KAAK,SAAA,CAAU,QAAQ,IAAI;IACnC;IAEA,OAAO,QAAQ,IAAA;AAChB;AAEO,SAAS,UAAU,GAAA,EAAa,OAAA,EAA6B;IAnNpE,IAAA;IAoNC,IAAI,WAAA,OAAA,KAAA,IAAA,QAAS,MAAA,EAAQ;QACpB,OAAO,QAAQ,MAAA,CAAO,WAAA,CAAY;IACnC;IACA,IAAI,IAAI,UAAA,CAAW,GAAG,GAAG;QACxB,MAAM,UAAA,CAAU,KAAA,IAAI,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,KAAhB,OAAA,KAAA,IAAA,GAAmB,KAAA,CAAM,IAAA,CAAK,EAAA;QAC9C,IAAI,CAAC,QAAQ,QAAA,CAAS,OAAO,GAAG;YAC/B,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,IAAA,IAAO,SAAS;QACjC;QACA,OAAO,QAAQ,WAAA,CAAY;IAC5B;IACA,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,IAAA,IAAO,SAAS;AACjC;AAEO,SAAS,WACf,OAAA,EACA,UAAA,EACC;IACD,IAAI;IACJ,IAAI,CAAA,CAAC,WAAA,OAAA,KAAA,IAAA,QAAS,MAAA,KAAA,CAAU,WAAA,OAAA,KAAA,IAAA,QAAS,OAAA,GAAS;QACzC,eAAe,WAAW,IAAM,cAAA,OAAA,KAAA,IAAA,WAAY,KAAA,IAAS,WAAA,OAAA,KAAA,IAAA,QAAS,OAAO;IACtE;IACA,OAAO;QACN;QACA,cAAc,MAAM;YACnB,IAAI,cAAc;gBACjB,aAAa,YAAY;YAC1B;QACD;IACD;AACD;AAEO,SAAS,WAAW,IAAA,EAAW,YAAA,EAA4B;IACjE,IAAI,iBAAiB,QAAQ;QAC5B,OAAO,KAAK,KAAA,CAAM,IAAI;IACvB;IACA,OAAO;AACR;AAEO,IAAM,kBAAN,MAAM,yBAAwB,MAAM;IAG1C,YAAY,MAAA,EAA+C,OAAA,CAAkB;QAE5E,KAAA,CAAM,WAAW,KAAK,SAAA,CAAU,QAAQ,MAAM,CAAC,CAAC;QAChD,IAAA,CAAK,MAAA,GAAS;QAGd,OAAO,cAAA,CAAe,IAAA,EAAM,iBAAgB,SAAS;IACtD;AACD;AAEA,eAAsB,oBACrB,MAAA,EACA,KAAA,EACiD;IACjD,IAAI,SAAS,MAAM,MAAA,CAAO,WAAW,CAAA,CAAE,QAAA,CAAS,KAAK;IAErD,IAAI,OAAO,MAAA,EAAQ;QAClB,MAAM,IAAI,gBAAgB,OAAO,MAAM;IACxC;IACA,OAAO,OAAO,KAAA;AACf;;ACpQO,IAAM,UAAU;IAAC;IAAO;IAAQ;IAAO;IAAS,QAAQ;CAAA;AAQxD,IAAM,eAAe,CAI3B,QACA,WACI;IACJ,OAAO;QACN;QACA;IACD;AACD;;AC1BO,IAAM,oBAAoB,CAAC,SAAA,CAChC;QACA,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM,MAAK,GAAA,EAAK,OAAA,EAAS;YAX3B,IAAA,IAAA,IAAA,IAAA;YAYG,MAAM,SAAA,CAAA,CACL,KAAA,CAAA,KAAA,OAAO,OAAA,KAAP,OAAA,KAAA,IAAA,GAAgB,IAAA,CAAK,CAAC,WAAQ;gBAblC,IAAAA;gBAcK,OAAA,CAAA,CAAAA,MAAA,OAAO,MAAA,KAAP,OAAA,KAAA,IAAAA,IAAe,MAAA,IACZ,IAAI,UAAA,CAAW,OAAO,MAAA,CAAO,MAAA,CAAO,OAAA,IAAW,EAAE,KAClD,IAAI,UAAA,CAAW,OAAO,MAAA,CAAO,MAAA,CAAO,MAAA,IAAU,EAAE,IAC/C;YAAA,EAAA,KAJJ,OAAA,KAAA,IAAA,GAKG,MAAA,KAAU,OAAO,MAAA;YACrB,IAAI,QAAQ;gBACX,IAAI,SAAS;gBACb,IAAA,CAAI,KAAA,OAAO,MAAA,KAAP,OAAA,KAAA,IAAA,GAAe,MAAA,EAAQ;oBAC1B,IAAI,OAAO,UAAA,CAAW,OAAO,MAAA,CAAO,MAAM,GAAG;wBAC5C,SAAS,OAAO,OAAA,CAAQ,OAAO,MAAA,CAAO,MAAA,EAAQ,EAAE;wBAChD,IAAI,OAAO,MAAA,CAAO,OAAA,EAAS;4BAC1B,MAAM,IAAI,OAAA,CAAQ,OAAO,MAAA,CAAO,MAAA,EAAQ,OAAO,MAAA,CAAO,OAAO;wBAC9D;oBACD;gBACD;gBACA,IAAA,CAAI,KAAA,OAAO,MAAA,KAAP,OAAA,KAAA,IAAA,GAAe,OAAA,EAAS;oBAC3B,IAAI,OAAO,UAAA,CAAW,OAAO,MAAA,CAAO,OAAO,GAAG;wBAC7C,SAAS,OAAO,OAAA,CAAQ,OAAO,MAAA,CAAO,OAAA,EAAS,EAAE;oBAClD;gBACD;gBACA,MAAM,YAAY,OAAO,MAAA,CAAO,MAAM,CAAA;gBACtC,IAAI,WAAW;oBACd,IAAI,OAAO,cAAA,eAAA,CAAA,GACP,UADO;wBAEV,QAAQ,UAAU,MAAA;wBAClB,QAAQ,UAAU,MAAA;oBACnB;oBACA,IAAI,CAAA,CAAC,WAAA,OAAA,KAAA,IAAA,QAAS,iBAAA,GAAmB;wBAChC,OAAO,cAAA,eAAA,CAAA,GACH,OADG;4BAEN,MAAM,UAAU,KAAA,GACb,MAAM,oBAAoB,UAAU,KAAA,EAAO,WAAA,OAAA,KAAA,IAAA,QAAS,IAAI,IACxD,WAAA,OAAA,KAAA,IAAA,QAAS,IAAA;4BACZ,QAAQ,UAAU,MAAA,GACf,MAAM,oBAAoB,UAAU,MAAA,EAAQ,WAAA,OAAA,KAAA,IAAA,QAAS,MAAM,IAC3D,WAAA,OAAA,KAAA,IAAA,QAAS,MAAA;4BACZ,OAAO,UAAU,KAAA,GACd,MAAM,oBAAoB,UAAU,KAAA,EAAO,WAAA,OAAA,KAAA,IAAA,QAAS,KAAK,IACzD,WAAA,OAAA,KAAA,IAAA,QAAS,KAAA;wBACb;oBACD;oBACA,OAAO;wBACN;wBACA,SAAS;oBACV;gBACD;YACD;YACA,OAAO;gBACN;gBACA;YACD;QACD;IACD,CAAA;AAEM,IAAM,cAAc,CAC1B,WACI;IACJ,eAAe,OAAO,GAAA,EAAa,OAAA,EAA6B;QAC/D,MAAM,OAAO,cAAA,eAAA,eAAA,CAAA,GACT,SACA,UAFS;YAGZ,SAAS,CAAC;mBAAA,CAAI,UAAA,OAAA,KAAA,IAAA,OAAQ,OAAA,KAAW,CAAC,CAAA;gBAAI,kBAAkB,UAAU,CAAC,CAAC,CAAC;aAAA;QACtE;QAEA,IAAI,UAAA,OAAA,KAAA,IAAA,OAAQ,aAAA,EAAe;YAC1B,IAAI;gBACH,OAAO,MAAM,YAAY,KAAK,IAAI;YACnC,EAAA,OAAS,OAAO;gBACf,OAAO;oBACN,MAAM;oBACN,OAAO;wBACN,QAAQ;wBACR,YAAY;wBACZ,SACC;wBACD;oBACD;gBACD;YACD;QACD;QACA,OAAO,MAAM,YAAY,KAAK,IAAI;IACnC;IACA,OAAO;AACR;;AC3FO,SAASC,QAAO,GAAA,EAAa,MAAA,EAA4B;IAC/D,IAAI,EAAE,OAAA,EAAS,MAAA,EAAQ,KAAA,CAAM,CAAA,GAAI,UAAU;QAC1C,OAAO,CAAC;QACR,QAAQ,CAAC;QACT,SAAS;IACV;IACA,IAAI,WAAW,IAAI,UAAA,CAAW,MAAM,IACjC,IAAI,KAAA,CAAM,GAAG,EAAE,KAAA,CAAM,GAAG,CAAC,EAAE,IAAA,CAAK,GAAG,IACnC,WAAW;IAKd,IAAI,IAAI,UAAA,CAAW,GAAG,GAAG;QACxB,MAAM,IAAI,IAAI,QAAA,CAAS,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,CAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA;QACnD,IAAI,QAAQ,QAAA,CAAS,CAAC,GAAG;YACxB,MAAM,IAAI,OAAA,CAAQ,CAAA,CAAA,EAAI,CAAC,CAAA,CAAA,CAAA,EAAK,GAAG;QAChC;IACD;IAEA,IAAI,CAAC,SAAS,QAAA,CAAS,GAAG,EAAG,CAAA,YAAY;IACzC,IAAI,CAAC,MAAM,QAAQ,CAAA,GAAI,IAAI,OAAA,CAAQ,UAAU,EAAE,EAAE,KAAA,CAAM,GAAG;IAC1D,MAAM,cAAc,IAAI,gBAAgB,QAAQ;IAChD,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,SAAS,CAAC,CAAC,EAAG;QACvD,IAAI,SAAS,KAAM,CAAA;QACnB,YAAY,GAAA,CAAI,KAAK,OAAO,KAAK,CAAC;IACnC;IACA,IAAI,QAAQ;QACX,IAAI,MAAM,OAAA,CAAQ,MAAM,GAAG;YAC1B,MAAM,aAAa,KAAK,KAAA,CAAM,GAAG,EAAE,MAAA,CAAO,CAAC,IAAM,EAAE,UAAA,CAAW,GAAG,CAAC;YAClE,KAAA,MAAW,CAAC,OAAO,GAAG,CAAA,IAAK,WAAW,OAAA,CAAQ,EAAG;gBAChD,MAAM,QAAQ,MAAA,CAAO,KAAK,CAAA;gBAC1B,OAAO,KAAK,OAAA,CAAQ,KAAK,KAAK;YAC/B;QACD,OAAO;YACN,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,MAAM,EAAG;gBAClD,OAAO,KAAK,OAAA,CAAQ,CAAA,CAAA,EAAI,GAAG,EAAA,EAAI,OAAO,KAAK,CAAC;YAC7C;QACD;IACD;IAEA,OAAO,KAAK,KAAA,CAAM,GAAG,EAAE,GAAA,CAAI,kBAAkB,EAAE,IAAA,CAAK,GAAG;IACvD,IAAI,KAAK,UAAA,CAAW,GAAG,EAAG,CAAA,OAAO,KAAK,KAAA,CAAM,CAAC;IAC7C,IAAI,mBAAmB,YAAY,QAAA,CAAS;IAC5C,mBACC,iBAAiB,MAAA,GAAS,IAAI,CAAA,CAAA,EAAI,gBAAgB,EAAA,CAAG,OAAA,CAAQ,OAAO,KAAK,IAAI;IAC9E,IAAI,CAAC,SAAS,UAAA,CAAW,MAAM,GAAG;QACjC,OAAO,GAAG,QAAQ,GAAG,IAAI,GAAG,gBAAgB,EAAA;IAC7C;IACA,MAAM,OAAO,IAAI,IAAI,GAAG,IAAI,GAAG,gBAAgB,EAAA,EAAI,QAAQ;IAC3D,OAAO;AACR;;ACvCO,IAAM,cAAc,OAO1B,KACA,YAOI;IAjCL,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;IAkCC,MAAM,EACL,KAAA,EACA,KAAK,KAAA,EACL,SAAS,IAAA,EACV,GAAI,MAAM,kBAAkB,KAAK,OAAO;IACxC,MAAM,QAAQ,SAAS,IAAI;IAC3B,MAAM,aAAa,IAAI,gBAAgB;IACvC,MAAM,SAAA,CAAS,KAAA,KAAK,MAAA,KAAL,OAAA,KAAe,WAAW,MAAA;IACzC,MAAM,OAAOC,QAAO,OAAO,IAAI;IAC/B,MAAM,OAAO,QAAQ,IAAI;IACzB,MAAM,UAAU,MAAM,WAAW,IAAI;IACrC,MAAM,SAAS,UAAU,OAAO,IAAI;IACpC,IAAI,UAAU,cAAA,eAAA,CAAA,GACV,OADU;QAEb,KAAK;QACL;QACA;QACA;QACA;IACD;IAIA,KAAA,MAAW,aAAa,MAAM,SAAA,CAAW;QACxC,IAAI,WAAW;YACd,MAAM,MAAM,MAAM,UAAU,OAAO;YACnC,IAAI,eAAe,QAAQ;gBAC1B,UAAU;YACX;QACD;IACD;IACA,IACE,YAAa,WACb,OAAQ,QAAgB,MAAA,KAAW,cACpC,OAAA,CAAA,CAAO,KAAA,WAAA,OAAA,KAAA,IAAA,QAAS,IAAA,KAAT,OAAA,KAAA,IAAA,GAAe,IAAA,MAAS,YAC9B;QACD,IAAI,CAAA,CAAE,YAAY,OAAA,GAAU;YAC3B,QAAQ,MAAA,GAAS;QAClB;IACD;IAEA,MAAM,EAAE,cAAAC,aAAAA,CAAa,CAAA,GAAI,WAAW,MAAM,UAAU;IACpD,IAAI,WAAW,MAAM,MAAM,QAAQ,GAAA,EAAK,OAAO;IAC/CA,cAAa;IAEb,MAAM,kBAAkB;QACvB;QACA,SAAS;IACV;IAEA,KAAA,MAAW,cAAc,MAAM,UAAA,CAAY;QAC1C,IAAI,YAAY;YACf,MAAM,IAAI,MAAM,WAAW,cAAA,eAAA,CAAA,GACvB,kBADuB;gBAE1B,UAAA,CAAA,CAAU,KAAA,WAAA,OAAA,KAAA,IAAA,QAAS,WAAA,KAAT,OAAA,KAAA,IAAA,GAAsB,aAAA,IAC7B,SAAS,KAAA,CAAM,IACf;YACJ,EAAC;YACD,IAAI,aAAa,UAAU;gBAC1B,WAAW;YACZ,OAAA,IAAW,aAAa,QAAQ;gBAC/B,WAAW,EAAE,QAAA;YACd;QACD;IACD;IAKA,IAAI,SAAS,EAAA,EAAI;QAChB,MAAM,UAAU,QAAQ,MAAA,KAAW;QACnC,IAAI,CAAC,SAAS;YACb,OAAO;gBACN,MAAM;gBACN,OAAO;YACR;QACD;QACA,MAAM,eAAe,mBAAmB,QAAQ;QAChD,MAAM,iBAAiB;YACtB,MAAM;YACN;YACA,SAAS;QACV;QACA,IAAI,iBAAiB,UAAU,iBAAiB,QAAQ;YACvD,MAAM,OAAO,MAAM,SAAS,IAAA,CAAK;YACjC,MAAMC,UAAAA,CAAS,KAAA,QAAQ,UAAA,KAAR,OAAA,KAAsB;YACrC,MAAM,OAAO,MAAMA,QAAO,IAAI;YAC9B,eAAe,IAAA,GAAO;QACvB,OAAO;YACN,eAAe,IAAA,GAAO,MAAM,QAAA,CAAS,YAAY,CAAA,CAAE;QACpD;QAKA,IAAI,WAAA,OAAA,KAAA,IAAA,QAAS,MAAA,EAAQ;YACpB,IAAI,QAAQ,MAAA,IAAU,CAAC,QAAQ,iBAAA,EAAmB;gBACjD,eAAe,IAAA,GAAO,MAAM,oBAC3B,QAAQ,MAAA,EACR,eAAe,IAAA;YAEjB;QACD;QAEA,KAAA,MAAW,aAAa,MAAM,SAAA,CAAW;YACxC,IAAI,WAAW;gBACd,MAAM,UAAU,cAAA,eAAA,CAAA,GACZ,iBADY;oBAEf,UAAA,CAAA,CAAU,KAAA,WAAA,OAAA,KAAA,IAAA,QAAS,WAAA,KAAT,OAAA,KAAA,IAAA,GAAsB,aAAA,IAC7B,SAAS,KAAA,CAAM,IACf;gBACJ,EAAC;YACF;QACD;QAEA,IAAI,WAAA,OAAA,KAAA,IAAA,QAAS,KAAA,EAAO;YACnB,OAAO,eAAe,IAAA;QACvB;QAEA,OAAO;YACN,MAAM,eAAe,IAAA;YACrB,OAAO;QACR;IACD;IACA,MAAM,SAAA,CAAS,KAAA,WAAA,OAAA,KAAA,IAAA,QAAS,UAAA,KAAT,OAAA,KAAuB;IACtC,MAAM,eAAe,MAAM,SAAS,IAAA,CAAK;IACzC,MAAM,iBAAiB,eAAe,YAAY;IAClD,MAAM,cAAc,iBAAiB,MAAM,OAAO,YAAY,IAAI;IAIlE,MAAM,eAAe;QACpB;QACA;QACA,SAAS;QACT,OAAO,cAAA,eAAA,CAAA,GACH,cADG;YAEN,QAAQ,SAAS,MAAA;YACjB,YAAY,SAAS,UAAA;QACtB;IACD;IACA,KAAA,MAAW,WAAW,MAAM,OAAA,CAAS;QACpC,IAAI,SAAS;YACZ,MAAM,QAAQ,cAAA,eAAA,CAAA,GACV,eADU;gBAEb,UAAA,CAAA,CAAU,KAAA,WAAA,OAAA,KAAA,IAAA,QAAS,WAAA,KAAT,OAAA,KAAA,IAAA,GAAsB,aAAA,IAC7B,SAAS,KAAA,CAAM,IACf;YACJ,EAAC;QACF;IACD;IAEA,IAAI,WAAA,OAAA,KAAA,IAAA,QAAS,KAAA,EAAO;QACnB,MAAM,gBAAgB,oBAAoB,QAAQ,KAAK;QACvD,MAAM,gBAAA,CAAgB,KAAA,QAAQ,YAAA,KAAR,OAAA,KAAwB;QAC9C,IAAI,MAAM,cAAc,kBAAA,CAAmB,eAAe,QAAQ,GAAG;YACpE,KAAA,MAAW,WAAW,MAAM,OAAA,CAAS;gBACpC,IAAI,SAAS;oBACZ,MAAM,QAAQ,eAAe;gBAC9B;YACD;YACA,MAAM,QAAQ,cAAc,QAAA,CAAS,aAAa;YAClD,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS,KAAK,CAAC;YACzD,OAAO,MAAM,YAAY,KAAK,cAAA,eAAA,CAAA,GAC1B,UAD0B;gBAE7B,cAAc,gBAAgB;YAC/B,EAAC;QACF;IACD;IAEA,IAAI,WAAA,OAAA,KAAA,IAAA,QAAS,KAAA,EAAO;QACnB,MAAM,IAAI,iBACT,SAAS,MAAA,EACT,SAAS,UAAA,EACT,iBAAiB,cAAc;IAEjC;IACA,OAAO;QACN,MAAM;QACN,OAAO,cAAA,eAAA,CAAA,GACH,cADG;YAEN,QAAQ,SAAS,MAAA;YACjB,YAAY,SAAS,UAAA;QACtB;IACD;AACD", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8], "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/shared/better-auth.8zoxzg-F.mjs"], "sourcesContent": ["const _envShim = /* @__PURE__ */ Object.create(null);\nconst _getEnv = (useShim) => globalThis.process?.env || //@ts-expect-error\nglobalThis.Deno?.env.toObject() || //@ts-expect-error\nglobalThis.__env__ || (useShim ? _envShim : globalThis);\nconst env = new Proxy(_envShim, {\n  get(_, prop) {\n    const env2 = _getEnv();\n    return env2[prop] ?? _envShim[prop];\n  },\n  has(_, prop) {\n    const env2 = _getEnv();\n    return prop in env2 || prop in _envShim;\n  },\n  set(_, prop, value) {\n    const env2 = _getEnv(true);\n    env2[prop] = value;\n    return true;\n  },\n  deleteProperty(_, prop) {\n    if (!prop) {\n      return false;\n    }\n    const env2 = _getEnv(true);\n    delete env2[prop];\n    return true;\n  },\n  ownKeys() {\n    const env2 = _getEnv(true);\n    return Object.keys(env2);\n  }\n});\nfunction toBoolean(val) {\n  return val ? val !== \"false\" : false;\n}\nconst nodeENV = typeof process !== \"undefined\" && process.env && process.env.NODE_ENV || \"\";\nconst isProduction = nodeENV === \"production\";\nconst isDevelopment = nodeENV === \"dev\" || nodeENV === \"development\";\nconst isTest = nodeENV === \"test\" || toBoolean(env.TEST);\n\nexport { isProduction as a, isDevelopment as b, env as e, isTest as i };\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,WAAW,aAAa,GAAG,OAAO,MAAM,CAAC;AAC/C,MAAM,UAAU,CAAC,UAAY,WAAW,OAAO,EAAE,OAAO,kBAAkB;IAC1E,WAAW,IAAI,EAAE,IAAI,cAAc,kBAAkB;IACrD,WAAW,OAAO,IAAI,CAAC,UAAU,WAAW,UAAU;AACtD,MAAM,MAAM,IAAI,MAAM,UAAU;IAC9B,KAAI,CAAC,EAAE,IAAI;QACT,MAAM,OAAO;QACb,OAAO,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK;IACrC;IACA,KAAI,CAAC,EAAE,IAAI;QACT,MAAM,OAAO;QACb,OAAO,QAAQ,QAAQ,QAAQ;IACjC;IACA,KAAI,CAAC,EAAE,IAAI,EAAE,KAAK;QAChB,MAAM,OAAO,QAAQ;QACrB,IAAI,CAAC,KAAK,GAAG;QACb,OAAO;IACT;IACA,gBAAe,CAAC,EAAE,IAAI;QACpB,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QACA,MAAM,OAAO,QAAQ;QACrB,OAAO,IAAI,CAAC,KAAK;QACjB,OAAO;IACT;IACA;QACE,MAAM,OAAO,QAAQ;QACrB,OAAO,OAAO,IAAI,CAAC;IACrB;AACF;AACA,SAAS,UAAU,GAAG;IACpB,OAAO,MAAM,QAAQ,UAAU;AACjC;AACA,MAAM,UAAU,OAAO,YAAY,eAAe,QAAQ,GAAG,uDAA4B;AACzF,MAAM,eAAe,YAAY;AACjC,MAAM,gBAAgB,YAAY,SAAS,YAAY;AACvD,MAAM,SAAS,YAAY,UAAU,UAAU,IAAI,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/shared/better-auth.DdzSJf-n.mjs"], "sourcesContent": ["class BetterAuthError extends Error {\n  constructor(message, cause) {\n    super(message);\n    this.name = \"BetterAuthError\";\n    this.message = message;\n    this.cause = cause;\n    this.stack = \"\";\n  }\n}\nclass MissingDependencyError extends BetterAuthError {\n  constructor(pkgName) {\n    super(\n      `The package \"${pkgName}\" is required. Make sure it is installed.`,\n      pkgName\n    );\n  }\n}\n\nexport { BetterAuthError as B, MissingDependencyError as M };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,wBAAwB;IAC5B,YAAY,OAAO,EAAE,KAAK,CAAE;QAC1B,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG;IACf;AACF;AACA,MAAM,+BAA+B;IACnC,YAAY,OAAO,CAAE;QACnB,KAAK,CACH,CAAC,aAAa,EAAE,QAAQ,yCAAyC,CAAC,EAClE;IAEJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/shared/better-auth.VTXNLFMT.mjs"], "sourcesContent": ["import { e as env } from './better-auth.8zoxzg-F.mjs';\nimport { B as BetterAuthError } from './better-auth.DdzSJf-n.mjs';\n\nfunction checkHasPath(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.pathname !== \"/\";\n  } catch (error) {\n    throw new BetterAuthError(\n      `Invalid base URL: ${url}. Please provide a valid base URL.`\n    );\n  }\n}\nfunction withPath(url, path = \"/api/auth\") {\n  const hasPath = checkHasPath(url);\n  if (hasPath) {\n    return url;\n  }\n  path = path.startsWith(\"/\") ? path : `/${path}`;\n  return `${url.replace(/\\/+$/, \"\")}${path}`;\n}\nfunction getBaseURL(url, path, request) {\n  if (url) {\n    return withPath(url, path);\n  }\n  const fromEnv = env.BETTER_AUTH_URL || env.NEXT_PUBLIC_BETTER_AUTH_URL || env.PUBLIC_BETTER_AUTH_URL || env.NUXT_PUBLIC_BETTER_AUTH_URL || env.NUXT_PUBLIC_AUTH_URL || (env.BASE_URL !== \"/\" ? env.BASE_URL : void 0);\n  if (fromEnv) {\n    return withPath(fromEnv, path);\n  }\n  const fromRequest = request?.headers.get(\"x-forwarded-host\");\n  const fromRequestProto = request?.headers.get(\"x-forwarded-proto\");\n  if (fromRequest && fromRequestProto) {\n    return withPath(`${fromRequestProto}://${fromRequest}`, path);\n  }\n  if (request) {\n    const url2 = getOrigin(request.url);\n    if (!url2) {\n      throw new BetterAuthError(\n        \"Could not get origin from request. Please provide a valid base URL.\"\n      );\n    }\n    return withPath(url2, path);\n  }\n  if (typeof window !== \"undefined\" && window.location) {\n    return withPath(window.location.origin, path);\n  }\n  return void 0;\n}\nfunction getOrigin(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.origin;\n  } catch (error) {\n    return null;\n  }\n}\nfunction getProtocol(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.protocol;\n  } catch (error) {\n    return null;\n  }\n}\nfunction getHost(url) {\n  try {\n    const parsedUrl = new URL(url);\n    return parsedUrl.host;\n  } catch (error) {\n    return url;\n  }\n}\n\nexport { getBaseURL as a, getHost as b, getProtocol as c, getOrigin as g };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,SAAS,aAAa,GAAG;IACvB,IAAI;QACF,MAAM,YAAY,IAAI,IAAI;QAC1B,OAAO,UAAU,QAAQ,KAAK;IAChC,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,kLAAA,CAAA,IAAe,CACvB,CAAC,kBAAkB,EAAE,IAAI,kCAAkC,CAAC;IAEhE;AACF;AACA,SAAS,SAAS,GAAG,EAAE,OAAO,WAAW;IACvC,MAAM,UAAU,aAAa;IAC7B,IAAI,SAAS;QACX,OAAO;IACT;IACA,OAAO,KAAK,UAAU,CAAC,OAAO,OAAO,CAAC,CAAC,EAAE,MAAM;IAC/C,OAAO,GAAG,IAAI,OAAO,CAAC,QAAQ,MAAM,MAAM;AAC5C;AACA,SAAS,WAAW,GAAG,EAAE,IAAI,EAAE,OAAO;IACpC,IAAI,KAAK;QACP,OAAO,SAAS,KAAK;IACvB;IACA,MAAM,UAAU,kLAAA,CAAA,IAAG,CAAC,eAAe,IAAI,kLAAA,CAAA,IAAG,CAAC,2BAA2B,IAAI,kLAAA,CAAA,IAAG,CAAC,sBAAsB,IAAI,kLAAA,CAAA,IAAG,CAAC,2BAA2B,IAAI,kLAAA,CAAA,IAAG,CAAC,oBAAoB,IAAI,CAAC,kLAAA,CAAA,IAAG,CAAC,QAAQ,KAAK,MAAM,kLAAA,CAAA,IAAG,CAAC,QAAQ,GAAG,KAAK,CAAC;IACpN,IAAI,SAAS;QACX,OAAO,SAAS,SAAS;IAC3B;IACA,MAAM,cAAc,SAAS,QAAQ,IAAI;IACzC,MAAM,mBAAmB,SAAS,QAAQ,IAAI;IAC9C,IAAI,eAAe,kBAAkB;QACnC,OAAO,SAAS,GAAG,iBAAiB,GAAG,EAAE,aAAa,EAAE;IAC1D;IACA,IAAI,SAAS;QACX,MAAM,OAAO,UAAU,QAAQ,GAAG;QAClC,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,kLAAA,CAAA,IAAe,CACvB;QAEJ;QACA,OAAO,SAAS,MAAM;IACxB;IACA,IAAI,OAAO,WAAW,eAAe,OAAO,QAAQ,EAAE;QACpD,OAAO,SAAS,OAAO,QAAQ,CAAC,MAAM,EAAE;IAC1C;IACA,OAAO,KAAK;AACd;AACA,SAAS,UAAU,GAAG;IACpB,IAAI;QACF,MAAM,YAAY,IAAI,IAAI;QAC1B,OAAO,UAAU,MAAM;IACzB,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AACA,SAAS,YAAY,GAAG;IACtB,IAAI;QACF,MAAM,YAAY,IAAI,IAAI;QAC1B,OAAO,UAAU,QAAQ;IAC3B,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AACA,SAAS,QAAQ,GAAG;IAClB,IAAI;QACF,MAAM,YAAY,IAAI,IAAI;QAC1B,OAAO,UAAU,IAAI;IACvB,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 942, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/shared/better-auth.Buni1mmI.mjs"], "sourcesContent": ["import { atom, onMount } from 'nanostores';\n\nconst isServer = typeof window === \"undefined\";\nconst useAuthQuery = (initializedAtom, path, $fetch, options) => {\n  const value = atom({\n    data: null,\n    error: null,\n    isPending: true,\n    isRefetching: false,\n    refetch: () => {\n      return fn();\n    }\n  });\n  const fn = () => {\n    const opts = typeof options === \"function\" ? options({\n      data: value.get().data,\n      error: value.get().error,\n      isPending: value.get().isPending\n    }) : options;\n    return $fetch(path, {\n      ...opts,\n      async onSuccess(context) {\n        value.set({\n          data: context.data,\n          error: null,\n          isPending: false,\n          isRefetching: false,\n          refetch: value.value.refetch\n        });\n        await opts?.onSuccess?.(context);\n      },\n      async onError(context) {\n        const { request } = context;\n        const retryAttempts = typeof request.retry === \"number\" ? request.retry : request.retry?.attempts;\n        const retryAttempt = request.retryAttempt || 0;\n        if (retryAttempts && retryAttempt < retryAttempts) return;\n        value.set({\n          error: context.error,\n          data: null,\n          isPending: false,\n          isRefetching: false,\n          refetch: value.value.refetch\n        });\n        await opts?.onError?.(context);\n      },\n      async onRequest(context) {\n        const currentValue = value.get();\n        value.set({\n          isPending: currentValue.data === null,\n          data: currentValue.data,\n          error: null,\n          isRefetching: true,\n          refetch: value.value.refetch\n        });\n        await opts?.onRequest?.(context);\n      }\n    });\n  };\n  initializedAtom = Array.isArray(initializedAtom) ? initializedAtom : [initializedAtom];\n  let isMounted = false;\n  for (const initAtom of initializedAtom) {\n    initAtom.subscribe(() => {\n      if (isServer) {\n        return;\n      }\n      if (isMounted) {\n        fn();\n      } else {\n        onMount(value, () => {\n          setTimeout(() => {\n            fn();\n          }, 0);\n          isMounted = true;\n          return () => {\n            value.off();\n            initAtom.off();\n          };\n        });\n      }\n    });\n  }\n  return value;\n};\n\nexport { useAuthQuery as u };\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM,WAAW,OAAO,WAAW;AACnC,MAAM,eAAe,CAAC,iBAAiB,MAAM,QAAQ;IACnD,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,OAAI,AAAD,EAAE;QACjB,MAAM;QACN,OAAO;QACP,WAAW;QACX,cAAc;QACd,SAAS;YACP,OAAO;QACT;IACF;IACA,MAAM,KAAK;QACT,MAAM,OAAO,OAAO,YAAY,aAAa,QAAQ;YACnD,MAAM,MAAM,GAAG,GAAG,IAAI;YACtB,OAAO,MAAM,GAAG,GAAG,KAAK;YACxB,WAAW,MAAM,GAAG,GAAG,SAAS;QAClC,KAAK;QACL,OAAO,OAAO,MAAM;YAClB,GAAG,IAAI;YACP,MAAM,WAAU,OAAO;gBACrB,MAAM,GAAG,CAAC;oBACR,MAAM,QAAQ,IAAI;oBAClB,OAAO;oBACP,WAAW;oBACX,cAAc;oBACd,SAAS,MAAM,KAAK,CAAC,OAAO;gBAC9B;gBACA,MAAM,MAAM,YAAY;YAC1B;YACA,MAAM,SAAQ,OAAO;gBACnB,MAAM,EAAE,OAAO,EAAE,GAAG;gBACpB,MAAM,gBAAgB,OAAO,QAAQ,KAAK,KAAK,WAAW,QAAQ,KAAK,GAAG,QAAQ,KAAK,EAAE;gBACzF,MAAM,eAAe,QAAQ,YAAY,IAAI;gBAC7C,IAAI,iBAAiB,eAAe,eAAe;gBACnD,MAAM,GAAG,CAAC;oBACR,OAAO,QAAQ,KAAK;oBACpB,MAAM;oBACN,WAAW;oBACX,cAAc;oBACd,SAAS,MAAM,KAAK,CAAC,OAAO;gBAC9B;gBACA,MAAM,MAAM,UAAU;YACxB;YACA,MAAM,WAAU,OAAO;gBACrB,MAAM,eAAe,MAAM,GAAG;gBAC9B,MAAM,GAAG,CAAC;oBACR,WAAW,aAAa,IAAI,KAAK;oBACjC,MAAM,aAAa,IAAI;oBACvB,OAAO;oBACP,cAAc;oBACd,SAAS,MAAM,KAAK,CAAC,OAAO;gBAC9B;gBACA,MAAM,MAAM,YAAY;YAC1B;QACF;IACF;IACA,kBAAkB,MAAM,OAAO,CAAC,mBAAmB,kBAAkB;QAAC;KAAgB;IACtF,IAAI,YAAY;IAChB,KAAK,MAAM,YAAY,gBAAiB;QACtC,SAAS,SAAS,CAAC;YACjB,IAAI,UAAU;gBACZ;YACF;YACA,IAAI,WAAW;gBACb;YACF,OAAO;gBACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EAAE,OAAO;oBACb,WAAW;wBACT;oBACF,GAAG;oBACH,YAAY;oBACZ,OAAO;wBACL,MAAM,GAAG;wBACT,SAAS,GAAG;oBACd;gBACF;YACF;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/shared/better-auth.ffWeg50w.mjs"], "sourcesContent": ["const PROTO_POLLUTION_PATTERNS = {\n  proto: /\"(?:_|\\\\u0{2}5[Ff]){2}(?:p|\\\\u0{2}70)(?:r|\\\\u0{2}72)(?:o|\\\\u0{2}6[Ff])(?:t|\\\\u0{2}74)(?:o|\\\\u0{2}6[Ff])(?:_|\\\\u0{2}5[Ff]){2}\"\\s*:/,\n  constructor: /\"(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)\"\\s*:/,\n  protoShort: /\"__proto__\"\\s*:/,\n  constructorShort: /\"constructor\"\\s*:/\n};\nconst JSON_SIGNATURE = /^\\s*[\"[{]|^\\s*-?\\d{1,16}(\\.\\d{1,17})?([Ee][+-]?\\d+)?\\s*$/;\nconst SPECIAL_VALUES = {\n  true: true,\n  false: false,\n  null: null,\n  undefined: void 0,\n  nan: Number.NaN,\n  infinity: Number.POSITIVE_INFINITY,\n  \"-infinity\": Number.NEGATIVE_INFINITY\n};\nconst ISO_DATE_REGEX = /^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{1,7}))?(?:Z|([+-])(\\d{2}):(\\d{2}))$/;\nfunction isValidDate(date) {\n  return date instanceof Date && !isNaN(date.getTime());\n}\nfunction parseISODate(value) {\n  const match = ISO_DATE_REGEX.exec(value);\n  if (!match) return null;\n  const [\n    ,\n    year,\n    month,\n    day,\n    hour,\n    minute,\n    second,\n    ms,\n    offsetSign,\n    offsetHour,\n    offsetMinute\n  ] = match;\n  let date = new Date(\n    Date.UTC(\n      parseInt(year, 10),\n      parseInt(month, 10) - 1,\n      parseInt(day, 10),\n      parseInt(hour, 10),\n      parseInt(minute, 10),\n      parseInt(second, 10),\n      ms ? parseInt(ms.padEnd(3, \"0\"), 10) : 0\n    )\n  );\n  if (offsetSign) {\n    const offset = (parseInt(offsetHour, 10) * 60 + parseInt(offsetMinute, 10)) * (offsetSign === \"+\" ? -1 : 1);\n    date.setUTCMinutes(date.getUTCMinutes() + offset);\n  }\n  return isValidDate(date) ? date : null;\n}\nfunction betterJSONParse(value, options = {}) {\n  const {\n    strict = false,\n    warnings = false,\n    reviver,\n    parseDates = true\n  } = options;\n  if (typeof value !== \"string\") {\n    return value;\n  }\n  const trimmed = value.trim();\n  if (trimmed[0] === '\"' && trimmed.endsWith('\"') && !trimmed.slice(1, -1).includes('\"')) {\n    return trimmed.slice(1, -1);\n  }\n  const lowerValue = trimmed.toLowerCase();\n  if (lowerValue.length <= 9 && lowerValue in SPECIAL_VALUES) {\n    return SPECIAL_VALUES[lowerValue];\n  }\n  if (!JSON_SIGNATURE.test(trimmed)) {\n    if (strict) {\n      throw new SyntaxError(\"[better-json] Invalid JSON\");\n    }\n    return value;\n  }\n  const hasProtoPattern = Object.entries(PROTO_POLLUTION_PATTERNS).some(\n    ([key, pattern]) => {\n      const matches = pattern.test(trimmed);\n      if (matches && warnings) {\n        console.warn(\n          `[better-json] Detected potential prototype pollution attempt using ${key} pattern`\n        );\n      }\n      return matches;\n    }\n  );\n  if (hasProtoPattern && strict) {\n    throw new Error(\n      \"[better-json] Potential prototype pollution attempt detected\"\n    );\n  }\n  try {\n    const secureReviver = (key, value2) => {\n      if (key === \"__proto__\" || key === \"constructor\" && value2 && typeof value2 === \"object\" && \"prototype\" in value2) {\n        if (warnings) {\n          console.warn(\n            `[better-json] Dropping \"${key}\" key to prevent prototype pollution`\n          );\n        }\n        return void 0;\n      }\n      if (parseDates && typeof value2 === \"string\") {\n        const date = parseISODate(value2);\n        if (date) {\n          return date;\n        }\n      }\n      return reviver ? reviver(key, value2) : value2;\n    };\n    return JSON.parse(trimmed, secureReviver);\n  } catch (error) {\n    if (strict) {\n      throw error;\n    }\n    return value;\n  }\n}\nfunction parseJSON(value, options = { strict: true }) {\n  return betterJSONParse(value, options);\n}\n\nexport { parseJSON as p };\n"], "names": [], "mappings": ";;;AAAA,MAAM,2BAA2B;IAC/B,OAAO;IACP,aAAa;IACb,YAAY;IACZ,kBAAkB;AACpB;AACA,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;IACrB,MAAM;IACN,OAAO;IACP,MAAM;IACN,WAAW,KAAK;IAChB,KAAK,OAAO,GAAG;IACf,UAAU,OAAO,iBAAiB;IAClC,aAAa,OAAO,iBAAiB;AACvC;AACA,MAAM,iBAAiB;AACvB,SAAS,YAAY,IAAI;IACvB,OAAO,gBAAgB,QAAQ,CAAC,MAAM,KAAK,OAAO;AACpD;AACA,SAAS,aAAa,KAAK;IACzB,MAAM,QAAQ,eAAe,IAAI,CAAC;IAClC,IAAI,CAAC,OAAO,OAAO;IACnB,MAAM,GAEJ,MACA,OACA,KACA,MACA,QACA,QACA,IACA,YACA,YACA,aACD,GAAG;IACJ,IAAI,OAAO,IAAI,KACb,KAAK,GAAG,CACN,SAAS,MAAM,KACf,SAAS,OAAO,MAAM,GACtB,SAAS,KAAK,KACd,SAAS,MAAM,KACf,SAAS,QAAQ,KACjB,SAAS,QAAQ,KACjB,KAAK,SAAS,GAAG,MAAM,CAAC,GAAG,MAAM,MAAM;IAG3C,IAAI,YAAY;QACd,MAAM,SAAS,CAAC,SAAS,YAAY,MAAM,KAAK,SAAS,cAAc,GAAG,IAAI,CAAC,eAAe,MAAM,CAAC,IAAI,CAAC;QAC1G,KAAK,aAAa,CAAC,KAAK,aAAa,KAAK;IAC5C;IACA,OAAO,YAAY,QAAQ,OAAO;AACpC;AACA,SAAS,gBAAgB,KAAK,EAAE,UAAU,CAAC,CAAC;IAC1C,MAAM,EACJ,SAAS,KAAK,EACd,WAAW,KAAK,EAChB,OAAO,EACP,aAAa,IAAI,EAClB,GAAG;IACJ,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,MAAM,UAAU,MAAM,IAAI;IAC1B,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM;QACtF,OAAO,QAAQ,KAAK,CAAC,GAAG,CAAC;IAC3B;IACA,MAAM,aAAa,QAAQ,WAAW;IACtC,IAAI,WAAW,MAAM,IAAI,KAAK,cAAc,gBAAgB;QAC1D,OAAO,cAAc,CAAC,WAAW;IACnC;IACA,IAAI,CAAC,eAAe,IAAI,CAAC,UAAU;QACjC,IAAI,QAAQ;YACV,MAAM,IAAI,YAAY;QACxB;QACA,OAAO;IACT;IACA,MAAM,kBAAkB,OAAO,OAAO,CAAC,0BAA0B,IAAI,CACnE,CAAC,CAAC,KAAK,QAAQ;QACb,MAAM,UAAU,QAAQ,IAAI,CAAC;QAC7B,IAAI,WAAW,UAAU;YACvB,QAAQ,IAAI,CACV,CAAC,mEAAmE,EAAE,IAAI,QAAQ,CAAC;QAEvF;QACA,OAAO;IACT;IAEF,IAAI,mBAAmB,QAAQ;QAC7B,MAAM,IAAI,MACR;IAEJ;IACA,IAAI;QACF,MAAM,gBAAgB,CAAC,KAAK;YAC1B,IAAI,QAAQ,eAAe,QAAQ,iBAAiB,UAAU,OAAO,WAAW,YAAY,eAAe,QAAQ;gBACjH,IAAI,UAAU;oBACZ,QAAQ,IAAI,CACV,CAAC,wBAAwB,EAAE,IAAI,oCAAoC,CAAC;gBAExE;gBACA,OAAO,KAAK;YACd;YACA,IAAI,cAAc,OAAO,WAAW,UAAU;gBAC5C,MAAM,OAAO,aAAa;gBAC1B,IAAI,MAAM;oBACR,OAAO;gBACT;YACF;YACA,OAAO,UAAU,QAAQ,KAAK,UAAU;QAC1C;QACA,OAAO,KAAK,KAAK,CAAC,SAAS;IAC7B,EAAE,OAAO,OAAO;QACd,IAAI,QAAQ;YACV,MAAM;QACR;QACA,OAAO;IACT;AACF;AACA,SAAS,UAAU,KAAK,EAAE,UAAU;IAAE,QAAQ;AAAK,CAAC;IAClD,OAAO,gBAAgB,OAAO;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/shared/better-auth.A_Crzln-.mjs"], "sourcesContent": ["import { createFetch } from '@better-fetch/fetch';\nimport { a as getBaseURL } from './better-auth.VTXNLFMT.mjs';\nimport { atom } from 'nanostores';\nimport { u as useAuthQuery } from './better-auth.Buni1mmI.mjs';\nimport { p as parseJSON } from './better-auth.ffWeg50w.mjs';\n\nconst redirectPlugin = {\n  id: \"redirect\",\n  name: \"Redirect\",\n  hooks: {\n    onSuccess(context) {\n      if (context.data?.url && context.data?.redirect) {\n        if (typeof window !== \"undefined\" && window.location) {\n          if (window.location) {\n            try {\n              window.location.href = context.data.url;\n            } catch {\n            }\n          }\n        }\n      }\n    }\n  }\n};\n\nfunction getSessionAtom($fetch) {\n  const $signal = atom(false);\n  const session = useAuthQuery($signal, \"/get-session\", $fetch, {\n    method: \"GET\"\n  });\n  return {\n    session,\n    $sessionSignal: $signal\n  };\n}\n\nconst getClientConfig = (options) => {\n  const isCredentialsSupported = \"credentials\" in Request.prototype;\n  const baseURL = getBaseURL(options?.baseURL, options?.basePath);\n  const pluginsFetchPlugins = options?.plugins?.flatMap((plugin) => plugin.fetchPlugins).filter((pl) => pl !== void 0) || [];\n  const lifeCyclePlugin = {\n    id: \"lifecycle-hooks\",\n    name: \"lifecycle-hooks\",\n    hooks: {\n      onSuccess: options?.fetchOptions?.onSuccess,\n      onError: options?.fetchOptions?.onError,\n      onRequest: options?.fetchOptions?.onRequest,\n      onResponse: options?.fetchOptions?.onResponse\n    }\n  };\n  const { onSuccess, onError, onRequest, onResponse, ...restOfFetchOptions } = options?.fetchOptions || {};\n  const $fetch = createFetch({\n    baseURL,\n    ...isCredentialsSupported ? { credentials: \"include\" } : {},\n    method: \"GET\",\n    jsonParser(text) {\n      if (!text) {\n        return null;\n      }\n      return parseJSON(text, {\n        strict: false\n      });\n    },\n    customFetchImpl: async (input, init) => {\n      try {\n        return await fetch(input, init);\n      } catch (error) {\n        return Response.error();\n      }\n    },\n    ...restOfFetchOptions,\n    plugins: [\n      lifeCyclePlugin,\n      ...restOfFetchOptions.plugins || [],\n      ...options?.disableDefaultFetchPlugins ? [] : [redirectPlugin],\n      ...pluginsFetchPlugins\n    ]\n  });\n  const { $sessionSignal, session } = getSessionAtom($fetch);\n  const plugins = options?.plugins || [];\n  let pluginsActions = {};\n  let pluginsAtoms = {\n    $sessionSignal,\n    session\n  };\n  let pluginPathMethods = {\n    \"/sign-out\": \"POST\",\n    \"/revoke-sessions\": \"POST\",\n    \"/revoke-other-sessions\": \"POST\",\n    \"/delete-user\": \"POST\"\n  };\n  const atomListeners = [\n    {\n      signal: \"$sessionSignal\",\n      matcher(path) {\n        return path === \"/sign-out\" || path === \"/update-user\" || path.startsWith(\"/sign-in\") || path.startsWith(\"/sign-up\") || path === \"/delete-user\" || path === \"/verify-email\";\n      }\n    }\n  ];\n  for (const plugin of plugins) {\n    if (plugin.getAtoms) {\n      Object.assign(pluginsAtoms, plugin.getAtoms?.($fetch));\n    }\n    if (plugin.pathMethods) {\n      Object.assign(pluginPathMethods, plugin.pathMethods);\n    }\n    if (plugin.atomListeners) {\n      atomListeners.push(...plugin.atomListeners);\n    }\n  }\n  const $store = {\n    notify: (signal) => {\n      pluginsAtoms[signal].set(\n        !pluginsAtoms[signal].get()\n      );\n    },\n    listen: (signal, listener) => {\n      pluginsAtoms[signal].subscribe(listener);\n    },\n    atoms: pluginsAtoms\n  };\n  for (const plugin of plugins) {\n    if (plugin.getActions) {\n      Object.assign(\n        pluginsActions,\n        plugin.getActions?.($fetch, $store, options)\n      );\n    }\n  }\n  return {\n    pluginsActions,\n    pluginsAtoms,\n    pluginPathMethods,\n    atomListeners,\n    $fetch,\n    $store\n  };\n};\n\nfunction getMethod(path, knownPathMethods, args) {\n  const method = knownPathMethods[path];\n  const { fetchOptions, query, ...body } = args || {};\n  if (method) {\n    return method;\n  }\n  if (fetchOptions?.method) {\n    return fetchOptions.method;\n  }\n  if (body && Object.keys(body).length > 0) {\n    return \"POST\";\n  }\n  return \"GET\";\n}\nfunction createDynamicPathProxy(routes, client, knownPathMethods, atoms, atomListeners) {\n  function createProxy(path = []) {\n    return new Proxy(function() {\n    }, {\n      get(target, prop) {\n        const fullPath = [...path, prop];\n        let current = routes;\n        for (const segment of fullPath) {\n          if (current && typeof current === \"object\" && segment in current) {\n            current = current[segment];\n          } else {\n            current = void 0;\n            break;\n          }\n        }\n        if (typeof current === \"function\") {\n          return current;\n        }\n        return createProxy(fullPath);\n      },\n      apply: async (_, __, args) => {\n        const routePath = \"/\" + path.map(\n          (segment) => segment.replace(/[A-Z]/g, (letter) => `-${letter.toLowerCase()}`)\n        ).join(\"/\");\n        const arg = args[0] || {};\n        const fetchOptions = args[1] || {};\n        const { query, fetchOptions: argFetchOptions, ...body } = arg;\n        const options = {\n          ...fetchOptions,\n          ...argFetchOptions\n        };\n        const method = getMethod(routePath, knownPathMethods, arg);\n        return await client(routePath, {\n          ...options,\n          body: method === \"GET\" ? void 0 : {\n            ...body,\n            ...options?.body || {}\n          },\n          query: query || options?.query,\n          method,\n          async onSuccess(context) {\n            await options?.onSuccess?.(context);\n            const matches = atomListeners?.find((s) => s.matcher(routePath));\n            if (!matches) return;\n            const signal = atoms[matches.signal];\n            if (!signal) return;\n            const val = signal.get();\n            setTimeout(() => {\n              signal.set(!val);\n            }, 10);\n          }\n        });\n      }\n    });\n  }\n  return createProxy();\n}\n\nexport { createDynamicPathProxy as c, getClientConfig as g };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB;IACrB,IAAI;IACJ,MAAM;IACN,OAAO;QACL,WAAU,OAAO;YACf,IAAI,QAAQ,IAAI,EAAE,OAAO,QAAQ,IAAI,EAAE,UAAU;gBAC/C,IAAI,OAAO,WAAW,eAAe,OAAO,QAAQ,EAAE;oBACpD,IAAI,OAAO,QAAQ,EAAE;wBACnB,IAAI;4BACF,OAAO,QAAQ,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,GAAG;wBACzC,EAAE,OAAM,CACR;oBACF;gBACF;YACF;QACF;IACF;AACF;AAEA,SAAS,eAAe,MAAM;IAC5B,MAAM,UAAU,CAAA,GAAA,2IAAA,CAAA,OAAI,AAAD,EAAE;IACrB,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,IAAY,AAAD,EAAE,SAAS,gBAAgB,QAAQ;QAC5D,QAAQ;IACV;IACA,OAAO;QACL;QACA,gBAAgB;IAClB;AACF;AAEA,MAAM,kBAAkB,CAAC;IACvB,MAAM,yBAAyB,iBAAiB,QAAQ,SAAS;IACjE,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,IAAU,AAAD,EAAE,SAAS,SAAS,SAAS;IACtD,MAAM,sBAAsB,SAAS,SAAS,QAAQ,CAAC,SAAW,OAAO,YAAY,EAAE,OAAO,CAAC,KAAO,OAAO,KAAK,MAAM,EAAE;IAC1H,MAAM,kBAAkB;QACtB,IAAI;QACJ,MAAM;QACN,OAAO;YACL,WAAW,SAAS,cAAc;YAClC,SAAS,SAAS,cAAc;YAChC,WAAW,SAAS,cAAc;YAClC,YAAY,SAAS,cAAc;QACrC;IACF;IACA,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,oBAAoB,GAAG,SAAS,gBAAgB,CAAC;IACvG,MAAM,SAAS,CAAA,GAAA,2JAAA,CAAA,cAAW,AAAD,EAAE;QACzB;QACA,GAAG,yBAAyB;YAAE,aAAa;QAAU,IAAI,CAAC,CAAC;QAC3D,QAAQ;QACR,YAAW,IAAI;YACb,IAAI,CAAC,MAAM;gBACT,OAAO;YACT;YACA,OAAO,CAAA,GAAA,+KAAA,CAAA,IAAS,AAAD,EAAE,MAAM;gBACrB,QAAQ;YACV;QACF;QACA,iBAAiB,OAAO,OAAO;YAC7B,IAAI;gBACF,OAAO,MAAM,MAAM,OAAO;YAC5B,EAAE,OAAO,OAAO;gBACd,OAAO,SAAS,KAAK;YACvB;QACF;QACA,GAAG,kBAAkB;QACrB,SAAS;YACP;eACG,mBAAmB,OAAO,IAAI,EAAE;eAChC,SAAS,6BAA6B,EAAE,GAAG;gBAAC;aAAe;eAC3D;SACJ;IACH;IACA,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,eAAe;IACnD,MAAM,UAAU,SAAS,WAAW,EAAE;IACtC,IAAI,iBAAiB,CAAC;IACtB,IAAI,eAAe;QACjB;QACA;IACF;IACA,IAAI,oBAAoB;QACtB,aAAa;QACb,oBAAoB;QACpB,0BAA0B;QAC1B,gBAAgB;IAClB;IACA,MAAM,gBAAgB;QACpB;YACE,QAAQ;YACR,SAAQ,IAAI;gBACV,OAAO,SAAS,eAAe,SAAS,kBAAkB,KAAK,UAAU,CAAC,eAAe,KAAK,UAAU,CAAC,eAAe,SAAS,kBAAkB,SAAS;YAC9J;QACF;KACD;IACD,KAAK,MAAM,UAAU,QAAS;QAC5B,IAAI,OAAO,QAAQ,EAAE;YACnB,OAAO,MAAM,CAAC,cAAc,OAAO,QAAQ,GAAG;QAChD;QACA,IAAI,OAAO,WAAW,EAAE;YACtB,OAAO,MAAM,CAAC,mBAAmB,OAAO,WAAW;QACrD;QACA,IAAI,OAAO,aAAa,EAAE;YACxB,cAAc,IAAI,IAAI,OAAO,aAAa;QAC5C;IACF;IACA,MAAM,SAAS;QACb,QAAQ,CAAC;YACP,YAAY,CAAC,OAAO,CAAC,GAAG,CACtB,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG;QAE7B;QACA,QAAQ,CAAC,QAAQ;YACf,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC;QACjC;QACA,OAAO;IACT;IACA,KAAK,MAAM,UAAU,QAAS;QAC5B,IAAI,OAAO,UAAU,EAAE;YACrB,OAAO,MAAM,CACX,gBACA,OAAO,UAAU,GAAG,QAAQ,QAAQ;QAExC;IACF;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,UAAU,IAAI,EAAE,gBAAgB,EAAE,IAAI;IAC7C,MAAM,SAAS,gBAAgB,CAAC,KAAK;IACrC,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,GAAG,QAAQ,CAAC;IAClD,IAAI,QAAQ;QACV,OAAO;IACT;IACA,IAAI,cAAc,QAAQ;QACxB,OAAO,aAAa,MAAM;IAC5B;IACA,IAAI,QAAQ,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,GAAG;QACxC,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,uBAAuB,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,KAAK,EAAE,aAAa;IACpF,SAAS,YAAY,OAAO,EAAE;QAC5B,OAAO,IAAI,MAAM,YACjB,GAAG;YACD,KAAI,MAAM,EAAE,IAAI;gBACd,MAAM,WAAW;uBAAI;oBAAM;iBAAK;gBAChC,IAAI,UAAU;gBACd,KAAK,MAAM,WAAW,SAAU;oBAC9B,IAAI,WAAW,OAAO,YAAY,YAAY,WAAW,SAAS;wBAChE,UAAU,OAAO,CAAC,QAAQ;oBAC5B,OAAO;wBACL,UAAU,KAAK;wBACf;oBACF;gBACF;gBACA,IAAI,OAAO,YAAY,YAAY;oBACjC,OAAO;gBACT;gBACA,OAAO,YAAY;YACrB;YACA,OAAO,OAAO,GAAG,IAAI;gBACnB,MAAM,YAAY,MAAM,KAAK,GAAG,CAC9B,CAAC,UAAY,QAAQ,OAAO,CAAC,UAAU,CAAC,SAAW,CAAC,CAAC,EAAE,OAAO,WAAW,IAAI,GAC7E,IAAI,CAAC;gBACP,MAAM,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC;gBACxB,MAAM,eAAe,IAAI,CAAC,EAAE,IAAI,CAAC;gBACjC,MAAM,EAAE,KAAK,EAAE,cAAc,eAAe,EAAE,GAAG,MAAM,GAAG;gBAC1D,MAAM,UAAU;oBACd,GAAG,YAAY;oBACf,GAAG,eAAe;gBACpB;gBACA,MAAM,SAAS,UAAU,WAAW,kBAAkB;gBACtD,OAAO,MAAM,OAAO,WAAW;oBAC7B,GAAG,OAAO;oBACV,MAAM,WAAW,QAAQ,KAAK,IAAI;wBAChC,GAAG,IAAI;wBACP,GAAG,SAAS,QAAQ,CAAC,CAAC;oBACxB;oBACA,OAAO,SAAS,SAAS;oBACzB;oBACA,MAAM,WAAU,OAAO;wBACrB,MAAM,SAAS,YAAY;wBAC3B,MAAM,UAAU,eAAe,KAAK,CAAC,IAAM,EAAE,OAAO,CAAC;wBACrD,IAAI,CAAC,SAAS;wBACd,MAAM,SAAS,KAAK,CAAC,QAAQ,MAAM,CAAC;wBACpC,IAAI,CAAC,QAAQ;wBACb,MAAM,MAAM,OAAO,GAAG;wBACtB,WAAW;4BACT,OAAO,GAAG,CAAC,CAAC;wBACd,GAAG;oBACL;gBACF;YACF;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/client/react/index.mjs"], "sourcesContent": ["import { g as getClientConfig, c as createDynamicPathProxy } from '../../shared/better-auth.A_Crzln-.mjs';\nimport { listenKeys } from 'nanostores';\nimport { useRef, useCallback, useSyncExternalStore } from 'react';\nimport '@better-fetch/fetch';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.Buni1mmI.mjs';\nimport '../../shared/better-auth.ffWeg50w.mjs';\n\nfunction useStore(store, options = {}) {\n  let snapshotRef = useRef(store.get());\n  const { keys, deps = [store, keys] } = options;\n  let subscribe = useCallback((onChange) => {\n    const emitChange = (value) => {\n      if (snapshotRef.current === value) return;\n      snapshotRef.current = value;\n      onChange();\n    };\n    emitChange(store.value);\n    if (keys?.length) {\n      return listenKeys(store, keys, emitChange);\n    }\n    return store.listen(emitChange);\n  }, deps);\n  let get = () => snapshotRef.current;\n  return useSyncExternalStore(subscribe, get, get);\n}\n\nfunction getAtomKey(str) {\n  return `use${capitalizeFirstLetter(str)}`;\n}\nfunction capitalizeFirstLetter(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nfunction createAuthClient(options) {\n  const {\n    pluginPathMethods,\n    pluginsActions,\n    pluginsAtoms,\n    $fetch,\n    $store,\n    atomListeners\n  } = getClientConfig(options);\n  let resolvedHooks = {};\n  for (const [key, value] of Object.entries(pluginsAtoms)) {\n    resolvedHooks[getAtomKey(key)] = () => useStore(value);\n  }\n  const routes = {\n    ...pluginsActions,\n    ...resolvedHooks,\n    $fetch,\n    $store\n  };\n  const proxy = createDynamicPathProxy(\n    routes,\n    $fetch,\n    pluginPathMethods,\n    pluginsAtoms,\n    atomListeners\n  );\n  return proxy;\n}\n\nexport { capitalizeFirstLetter, createAuthClient, useStore };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,SAAS,SAAS,KAAK,EAAE,UAAU,CAAC,CAAC;IACnC,IAAI,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,MAAM,GAAG;IAClC,MAAM,EAAE,IAAI,EAAE,OAAO;QAAC;QAAO;KAAK,EAAE,GAAG;IACvC,IAAI,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3B,MAAM,aAAa,CAAC;YAClB,IAAI,YAAY,OAAO,KAAK,OAAO;YACnC,YAAY,OAAO,GAAG;YACtB;QACF;QACA,WAAW,MAAM,KAAK;QACtB,IAAI,MAAM,QAAQ;YAChB,OAAO,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,OAAO,MAAM;QACjC;QACA,OAAO,MAAM,MAAM,CAAC;IACtB,GAAG;IACH,IAAI,MAAM,IAAM,YAAY,OAAO;IACnC,OAAO,CAAA,GAAA,qMAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,KAAK;AAC9C;AAEA,SAAS,WAAW,GAAG;IACrB,OAAO,CAAC,GAAG,EAAE,sBAAsB,MAAM;AAC3C;AACA,SAAS,sBAAsB,GAAG;IAChC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AACA,SAAS,iBAAiB,OAAO;IAC/B,MAAM,EACJ,iBAAiB,EACjB,cAAc,EACd,YAAY,EACZ,MAAM,EACN,MAAM,EACN,aAAa,EACd,GAAG,CAAA,GAAA,gLAAA,CAAA,IAAe,AAAD,EAAE;IACpB,IAAI,gBAAgB,CAAC;IACrB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,cAAe;QACvD,aAAa,CAAC,WAAW,KAAK,GAAG,IAAM,SAAS;IAClD;IACA,MAAM,SAAS;QACb,GAAG,cAAc;QACjB,GAAG,aAAa;QAChB;QACA;IACF;IACA,MAAM,QAAQ,CAAA,GAAA,gLAAA,CAAA,IAAsB,AAAD,EACjC,QACA,QACA,mBACA,cACA;IAEF,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1429, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/nanostores/task/index.js"], "sourcesContent": ["let tasks = 0\nlet resolves = []\n\nexport function startTask() {\n  tasks += 1\n  return () => {\n    tasks -= 1\n    if (tasks === 0) {\n      let prevResolves = resolves\n      resolves = []\n      for (let i of prevResolves) i()\n    }\n  }\n}\n\nexport function task(cb) {\n  let endTask = startTask()\n  let promise = cb().finally(endTask)\n  promise.t = true\n  return promise\n}\n\nexport function allTasks() {\n  if (tasks === 0) {\n    return Promise.resolve()\n  } else {\n    return new Promise(resolve => {\n      resolves.push(resolve)\n    })\n  }\n}\n\nexport function cleanTasks() {\n  tasks = 0\n}\n"], "names": [], "mappings": ";;;;;;AAAA,IAAI,QAAQ;AACZ,IAAI,WAAW,EAAE;AAEV,SAAS;IACd,SAAS;IACT,OAAO;QACL,SAAS;QACT,IAAI,UAAU,GAAG;YACf,IAAI,eAAe;YACnB,WAAW,EAAE;YACb,KAAK,IAAI,KAAK,aAAc;QAC9B;IACF;AACF;AAEO,SAAS,KAAK,EAAE;IACrB,IAAI,UAAU;IACd,IAAI,UAAU,KAAK,OAAO,CAAC;IAC3B,QAAQ,CAAC,GAAG;IACZ,OAAO;AACT;AAEO,SAAS;IACd,IAAI,UAAU,GAAG;QACf,OAAO,QAAQ,OAAO;IACxB,OAAO;QACL,OAAO,IAAI,QAAQ,CAAA;YACjB,SAAS,IAAI,CAAC;QAChB;IACF;AACF;AAEO,SAAS;IACd,QAAQ;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1472, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/nanostores/clean-stores/index.js"], "sourcesContent": ["import { cleanTasks } from '../task/index.js'\n\nexport let clean = Symbol('clean')\n\nexport let cleanStores = (...stores) => {\n  if (process.env.NODE_ENV === 'production') {\n    throw new Error(\n      'cleanStores() can be used only during development or tests'\n    )\n  }\n  cleanTasks()\n  for (let $store of stores) {\n    if ($store) {\n      if ($store.mocked) delete $store.mocked\n      if ($store[clean]) $store[clean]()\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,IAAI,QAAQ,OAAO;AAEnB,IAAI,cAAc,CAAC,GAAG;IAC3B,uCAA2C;;IAI3C;IACA,CAAA,GAAA,2IAAA,CAAA,aAAU,AAAD;IACT,KAAK,IAAI,UAAU,OAAQ;QACzB,IAAI,QAAQ;YACV,IAAI,OAAO,MAAM,EAAE,OAAO,OAAO,MAAM;YACvC,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM;QAClC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1497, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/nanostores/atom/index.js"], "sourcesContent": ["import { clean } from '../clean-stores/index.js'\n\nlet listenerQueue = []\nlet lqIndex = 0\nconst QUEUE_ITEMS_PER_LISTENER = 4\nexport let epoch = 0\n\nexport let atom = (initialValue) => {\n  let listeners = []\n  let $atom = {\n    get() {\n      if (!$atom.lc) {\n        $atom.listen(() => {})()\n      }\n      return $atom.value\n    },\n    lc: 0,\n    listen(listener) {\n      $atom.lc = listeners.push(listener)\n\n      return () => {\n        for (let i = lqIndex + QUEUE_ITEMS_PER_LISTENER; i < listenerQueue.length;) {\n          if (listenerQueue[i] === listener) {\n            listenerQueue.splice(i, QUEUE_ITEMS_PER_LISTENER)\n          } else {\n            i += QUEUE_ITEMS_PER_LISTENER\n          }\n        }\n\n        let index = listeners.indexOf(listener)\n        if (~index) {\n          listeners.splice(index, 1)\n          if (!--$atom.lc) $atom.off()\n        }\n      }\n    },\n    notify(oldValue, changedKey) {\n      epoch++\n      let runListenerQueue = !listenerQueue.length\n      for (let listener of listeners) {\n        listenerQueue.push(\n          listener,\n          $atom.value,\n          oldValue,\n          changedKey\n        )\n      }\n\n      if (runListenerQueue) {\n        for (lqIndex = 0; lqIndex < listenerQueue.length; lqIndex += QUEUE_ITEMS_PER_LISTENER) {\n            listenerQueue[lqIndex](\n              listenerQueue[lqIndex + 1],\n              listenerQueue[lqIndex + 2],\n              listenerQueue[lqIndex + 3]\n            )\n        }\n        listenerQueue.length = 0\n      }\n    },\n    /* It will be called on last listener unsubscribing.\n       We will redefine it in onMount and onStop. */\n    off() {},\n    set(newValue) {\n      let oldValue = $atom.value\n      if (oldValue !== newValue) {\n        $atom.value = newValue\n        $atom.notify(oldValue)\n      }\n    },\n    subscribe(listener) {\n      let unbind = $atom.listen(listener)\n      listener($atom.value)\n      return unbind\n    },\n    value: initialValue\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    $atom[clean] = () => {\n      listeners = []\n      $atom.lc = 0\n      $atom.off()\n    }\n  }\n\n  return $atom\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,IAAI,gBAAgB,EAAE;AACtB,IAAI,UAAU;AACd,MAAM,2BAA2B;AAC1B,IAAI,QAAQ;AAEZ,IAAI,OAAO,CAAC;IACjB,IAAI,YAAY,EAAE;IAClB,IAAI,QAAQ;QACV;YACE,IAAI,CAAC,MAAM,EAAE,EAAE;gBACb,MAAM,MAAM,CAAC,KAAO;YACtB;YACA,OAAO,MAAM,KAAK;QACpB;QACA,IAAI;QACJ,QAAO,QAAQ;YACb,MAAM,EAAE,GAAG,UAAU,IAAI,CAAC;YAE1B,OAAO;gBACL,IAAK,IAAI,IAAI,UAAU,0BAA0B,IAAI,cAAc,MAAM,EAAG;oBAC1E,IAAI,aAAa,CAAC,EAAE,KAAK,UAAU;wBACjC,cAAc,MAAM,CAAC,GAAG;oBAC1B,OAAO;wBACL,KAAK;oBACP;gBACF;gBAEA,IAAI,QAAQ,UAAU,OAAO,CAAC;gBAC9B,IAAI,CAAC,OAAO;oBACV,UAAU,MAAM,CAAC,OAAO;oBACxB,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,MAAM,GAAG;gBAC5B;YACF;QACF;QACA,QAAO,QAAQ,EAAE,UAAU;YACzB;YACA,IAAI,mBAAmB,CAAC,cAAc,MAAM;YAC5C,KAAK,IAAI,YAAY,UAAW;gBAC9B,cAAc,IAAI,CAChB,UACA,MAAM,KAAK,EACX,UACA;YAEJ;YAEA,IAAI,kBAAkB;gBACpB,IAAK,UAAU,GAAG,UAAU,cAAc,MAAM,EAAE,WAAW,yBAA0B;oBACnF,aAAa,CAAC,QAAQ,CACpB,aAAa,CAAC,UAAU,EAAE,EAC1B,aAAa,CAAC,UAAU,EAAE,EAC1B,aAAa,CAAC,UAAU,EAAE;gBAEhC;gBACA,cAAc,MAAM,GAAG;YACzB;QACF;QACA;kDAC8C,GAC9C,QAAO;QACP,KAAI,QAAQ;YACV,IAAI,WAAW,MAAM,KAAK;YAC1B,IAAI,aAAa,UAAU;gBACzB,MAAM,KAAK,GAAG;gBACd,MAAM,MAAM,CAAC;YACf;QACF;QACA,WAAU,QAAQ;YAChB,IAAI,SAAS,MAAM,MAAM,CAAC;YAC1B,SAAS,MAAM,KAAK;YACpB,OAAO;QACT;QACA,OAAO;IACT;IAEA,wCAA2C;QACzC,KAAK,CAAC,sJAAA,CAAA,QAAK,CAAC,GAAG;YACb,YAAY,EAAE;YACd,MAAM,EAAE,GAAG;YACX,MAAM,GAAG;QACX;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1578, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/nanostores/lifecycle/index.js"], "sourcesContent": ["import { clean } from '../clean-stores/index.js'\n\nconst START = 0\nconst STOP = 1\nconst SET = 2\nconst NOTIFY = 3\nconst MOUNT = 5\nconst UNMOUNT = 6\nconst REVERT_MUTATION = 10\n\nexport let on = (object, listener, eventKey, mutateStore) => {\n  object.events = object.events || {}\n  if (!object.events[eventKey + REVERT_MUTATION]) {\n    object.events[eventKey + REVERT_MUTATION] = mutateStore(eventProps => {\n      // eslint-disable-next-line no-sequences\n      object.events[eventKey].reduceRight((event, l) => (l(event), event), {\n        shared: {},\n        ...eventProps\n      })\n    })\n  }\n  object.events[eventKey] = object.events[eventKey] || []\n  object.events[eventKey].push(listener)\n  return () => {\n    let currentListeners = object.events[eventKey]\n    let index = currentListeners.indexOf(listener)\n    currentListeners.splice(index, 1)\n    if (!currentListeners.length) {\n      delete object.events[eventKey]\n      object.events[eventKey + REVERT_MUTATION]()\n      delete object.events[eventKey + REVERT_MUTATION]\n    }\n  }\n}\n\nexport let onStart = ($store, listener) =>\n  on($store, listener, START, runListeners => {\n    let originListen = $store.listen\n    $store.listen = arg => {\n      if (!$store.lc && !$store.starting) {\n        $store.starting = true\n        runListeners()\n        delete $store.starting\n      }\n      return originListen(arg)\n    }\n    return () => {\n      $store.listen = originListen\n    }\n  })\n\nexport let onStop = ($store, listener) =>\n  on($store, listener, STOP, runListeners => {\n    let originOff = $store.off\n    $store.off = () => {\n      runListeners()\n      originOff()\n    }\n    return () => {\n      $store.off = originOff\n    }\n  })\n\nexport let onSet = ($store, listener) =>\n  on($store, listener, SET, runListeners => {\n    let originSet = $store.set\n    let originSetKey = $store.setKey\n    if ($store.setKey) {\n      $store.setKey = (changed, changedValue) => {\n        let isAborted\n        let abort = () => {\n          isAborted = true\n        }\n\n        runListeners({\n          abort,\n          changed,\n          newValue: { ...$store.value, [changed]: changedValue }\n        })\n        if (!isAborted) return originSetKey(changed, changedValue)\n      }\n    }\n    $store.set = newValue => {\n      let isAborted\n      let abort = () => {\n        isAborted = true\n      }\n\n      runListeners({ abort, newValue })\n      if (!isAborted) return originSet(newValue)\n    }\n    return () => {\n      $store.set = originSet\n      $store.setKey = originSetKey\n    }\n  })\n\nexport let onNotify = ($store, listener) =>\n  on($store, listener, NOTIFY, runListeners => {\n    let originNotify = $store.notify\n    $store.notify = (oldValue, changed) => {\n      let isAborted\n      let abort = () => {\n        isAborted = true\n      }\n\n      runListeners({ abort, changed, oldValue })\n      if (!isAborted) return originNotify(oldValue, changed)\n    }\n    return () => {\n      $store.notify = originNotify\n    }\n  })\n\nexport let STORE_UNMOUNT_DELAY = 1000\n\nexport let onMount = ($store, initialize) => {\n  let listener = payload => {\n    let destroy = initialize(payload)\n    if (destroy) $store.events[UNMOUNT].push(destroy)\n  }\n  return on($store, listener, MOUNT, runListeners => {\n    let originListen = $store.listen\n    $store.listen = (...args) => {\n      if (!$store.lc && !$store.active) {\n        $store.active = true\n        runListeners()\n      }\n      return originListen(...args)\n    }\n\n    let originOff = $store.off\n    $store.events[UNMOUNT] = []\n    $store.off = () => {\n      originOff()\n      setTimeout(() => {\n        if ($store.active && !$store.lc) {\n          $store.active = false\n          for (let destroy of $store.events[UNMOUNT]) destroy()\n          $store.events[UNMOUNT] = []\n        }\n      }, STORE_UNMOUNT_DELAY)\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      let originClean = $store[clean]\n      $store[clean] = () => {\n        for (let destroy of $store.events[UNMOUNT]) destroy()\n        $store.events[UNMOUNT] = []\n        $store.active = false\n        originClean()\n      }\n    }\n\n    return () => {\n      $store.listen = originListen\n      $store.off = originOff\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,MAAM,UAAU;AAChB,MAAM,kBAAkB;AAEjB,IAAI,KAAK,CAAC,QAAQ,UAAU,UAAU;IAC3C,OAAO,MAAM,GAAG,OAAO,MAAM,IAAI,CAAC;IAClC,IAAI,CAAC,OAAO,MAAM,CAAC,WAAW,gBAAgB,EAAE;QAC9C,OAAO,MAAM,CAAC,WAAW,gBAAgB,GAAG,YAAY,CAAA;YACtD,wCAAwC;YACxC,OAAO,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,OAAO,IAAM,CAAC,EAAE,QAAQ,KAAK,GAAG;gBACnE,QAAQ,CAAC;gBACT,GAAG,UAAU;YACf;QACF;IACF;IACA,OAAO,MAAM,CAAC,SAAS,GAAG,OAAO,MAAM,CAAC,SAAS,IAAI,EAAE;IACvD,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;IAC7B,OAAO;QACL,IAAI,mBAAmB,OAAO,MAAM,CAAC,SAAS;QAC9C,IAAI,QAAQ,iBAAiB,OAAO,CAAC;QACrC,iBAAiB,MAAM,CAAC,OAAO;QAC/B,IAAI,CAAC,iBAAiB,MAAM,EAAE;YAC5B,OAAO,OAAO,MAAM,CAAC,SAAS;YAC9B,OAAO,MAAM,CAAC,WAAW,gBAAgB;YACzC,OAAO,OAAO,MAAM,CAAC,WAAW,gBAAgB;QAClD;IACF;AACF;AAEO,IAAI,UAAU,CAAC,QAAQ,WAC5B,GAAG,QAAQ,UAAU,OAAO,CAAA;QAC1B,IAAI,eAAe,OAAO,MAAM;QAChC,OAAO,MAAM,GAAG,CAAA;YACd,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,QAAQ,EAAE;gBAClC,OAAO,QAAQ,GAAG;gBAClB;gBACA,OAAO,OAAO,QAAQ;YACxB;YACA,OAAO,aAAa;QACtB;QACA,OAAO;YACL,OAAO,MAAM,GAAG;QAClB;IACF;AAEK,IAAI,SAAS,CAAC,QAAQ,WAC3B,GAAG,QAAQ,UAAU,MAAM,CAAA;QACzB,IAAI,YAAY,OAAO,GAAG;QAC1B,OAAO,GAAG,GAAG;YACX;YACA;QACF;QACA,OAAO;YACL,OAAO,GAAG,GAAG;QACf;IACF;AAEK,IAAI,QAAQ,CAAC,QAAQ,WAC1B,GAAG,QAAQ,UAAU,KAAK,CAAA;QACxB,IAAI,YAAY,OAAO,GAAG;QAC1B,IAAI,eAAe,OAAO,MAAM;QAChC,IAAI,OAAO,MAAM,EAAE;YACjB,OAAO,MAAM,GAAG,CAAC,SAAS;gBACxB,IAAI;gBACJ,IAAI,QAAQ;oBACV,YAAY;gBACd;gBAEA,aAAa;oBACX;oBACA;oBACA,UAAU;wBAAE,GAAG,OAAO,KAAK;wBAAE,CAAC,QAAQ,EAAE;oBAAa;gBACvD;gBACA,IAAI,CAAC,WAAW,OAAO,aAAa,SAAS;YAC/C;QACF;QACA,OAAO,GAAG,GAAG,CAAA;YACX,IAAI;YACJ,IAAI,QAAQ;gBACV,YAAY;YACd;YAEA,aAAa;gBAAE;gBAAO;YAAS;YAC/B,IAAI,CAAC,WAAW,OAAO,UAAU;QACnC;QACA,OAAO;YACL,OAAO,GAAG,GAAG;YACb,OAAO,MAAM,GAAG;QAClB;IACF;AAEK,IAAI,WAAW,CAAC,QAAQ,WAC7B,GAAG,QAAQ,UAAU,QAAQ,CAAA;QAC3B,IAAI,eAAe,OAAO,MAAM;QAChC,OAAO,MAAM,GAAG,CAAC,UAAU;YACzB,IAAI;YACJ,IAAI,QAAQ;gBACV,YAAY;YACd;YAEA,aAAa;gBAAE;gBAAO;gBAAS;YAAS;YACxC,IAAI,CAAC,WAAW,OAAO,aAAa,UAAU;QAChD;QACA,OAAO;YACL,OAAO,MAAM,GAAG;QAClB;IACF;AAEK,IAAI,sBAAsB;AAE1B,IAAI,UAAU,CAAC,QAAQ;IAC5B,IAAI,WAAW,CAAA;QACb,IAAI,UAAU,WAAW;QACzB,IAAI,SAAS,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC3C;IACA,OAAO,GAAG,QAAQ,UAAU,OAAO,CAAA;QACjC,IAAI,eAAe,OAAO,MAAM;QAChC,OAAO,MAAM,GAAG,CAAC,GAAG;YAClB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,MAAM,EAAE;gBAChC,OAAO,MAAM,GAAG;gBAChB;YACF;YACA,OAAO,gBAAgB;QACzB;QAEA,IAAI,YAAY,OAAO,GAAG;QAC1B,OAAO,MAAM,CAAC,QAAQ,GAAG,EAAE;QAC3B,OAAO,GAAG,GAAG;YACX;YACA,WAAW;gBACT,IAAI,OAAO,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE;oBAC/B,OAAO,MAAM,GAAG;oBAChB,KAAK,IAAI,WAAW,OAAO,MAAM,CAAC,QAAQ,CAAE;oBAC5C,OAAO,MAAM,CAAC,QAAQ,GAAG,EAAE;gBAC7B;YACF,GAAG;QACL;QAEA,wCAA2C;YACzC,IAAI,cAAc,MAAM,CAAC,sJAAA,CAAA,QAAK,CAAC;YAC/B,MAAM,CAAC,sJAAA,CAAA,QAAK,CAAC,GAAG;gBACd,KAAK,IAAI,WAAW,OAAO,MAAM,CAAC,QAAQ,CAAE;gBAC5C,OAAO,MAAM,CAAC,QAAQ,GAAG,EAAE;gBAC3B,OAAO,MAAM,GAAG;gBAChB;YACF;QACF;QAEA,OAAO;YACL,OAAO,MAAM,GAAG;YAChB,OAAO,GAAG,GAAG;QACf;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1746, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/nanostores/listen-keys/index.js"], "sourcesContent": ["export function listenKeys($store, keys, listener) {\n  let keysSet = new Set(keys).add(undefined)\n  return $store.listen((value, oldValue, changed) => {\n    if (keysSet.has(changed)) {\n      listener(value, oldValue, changed)\n    }\n  })\n}\n\nexport function subscribeKeys($store, keys, listener) {\n  let unbind = listenKeys($store, keys, listener)\n  listener($store.value)\n  return unbind\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,WAAW,MAAM,EAAE,IAAI,EAAE,QAAQ;IAC/C,IAAI,UAAU,IAAI,IAAI,MAAM,GAAG,CAAC;IAChC,OAAO,OAAO,MAAM,CAAC,CAAC,OAAO,UAAU;QACrC,IAAI,QAAQ,GAAG,CAAC,UAAU;YACxB,SAAS,OAAO,UAAU;QAC5B;IACF;AACF;AAEO,SAAS,cAAc,MAAM,EAAE,IAAI,EAAE,QAAQ;IAClD,IAAI,SAAS,WAAW,QAAQ,MAAM;IACtC,SAAS,OAAO,KAAK;IACrB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1768, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,qBACE,KAAK,MAAM,MAAM,eAAe,IAChC,CAAC,AAAC,oBAAoB,CAAC,GACvB,QAAQ,KAAK,CACX,iMACD;QACH,IAAI,QAAQ;QACZ,IAAI,CAAC,4BAA4B;YAC/B,IAAI,cAAc;YAClB,SAAS,OAAO,gBACd,CAAC,QAAQ,KAAK,CACZ,yEAED,6BAA6B,CAAC,CAAE;QACrC;QACA,cAAc,SAAS;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;YAAY;QACjD;QACA,IAAI,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,EAC5B,cAAc,WAAW,CAAC,EAAE;QAC9B,gBACE;YACE,KAAK,KAAK,GAAG;YACb,KAAK,WAAW,GAAG;YACnB,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;QAC3D,GACA;YAAC;YAAW;YAAO;SAAY;QAEjC,UACE;YACE,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;YACzD,OAAO,UAAU;gBACf,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;YAC3D;QACF,GACA;YAAC;SAAU;QAEb,cAAc;QACd,OAAO;IACT;IACA,SAAS,uBAAuB,IAAI;QAClC,IAAI,oBAAoB,KAAK,WAAW;QACxC,OAAO,KAAK,KAAK;QACjB,IAAI;YACF,IAAI,YAAY;YAChB,OAAO,CAAC,SAAS,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,gJACF,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,CAAC,GACrB,6BAA6B,CAAC,GAC9B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,QAAQ,IACtC,gBAAgB,OAAO,OAAO,QAAQ,CAAC,aAAa,GAChD,yBACA;IACR,QAAQ,oBAAoB,GAC1B,KAAK,MAAM,MAAM,oBAAoB,GAAG,MAAM,oBAAoB,GAAG;IACvE,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1844, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/use-sync-external-store/shim/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1856, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/swr/dist/_internal/events.mjs"], "sourcesContent": ["const FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\nexport { ERROR_REVALIDATE_EVENT, FOCUS_EVENT, MUTATE_EVENT, RECONNECT_EVENT };\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,cAAc;AACpB,MAAM,kBAAkB;AACxB,MAAM,eAAe;AACrB,MAAM,yBAAyB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1873, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/swr/dist/_internal/config-context-client-BoS53ST9.mjs"], "sourcesContent": ["'use client';\nimport React, { useEffect, useLayoutEffect, createContext, useContext, useMemo, useRef, createElement } from 'react';\nimport * as revalidateEvents from './events.mjs';\nimport { dequal } from 'dequal/lite';\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\n\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = typeof window != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst isLegacyDeno = isWindowDefined && 'Deno' in window;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\n\nconst IS_REACT_LEGACY = !React.useId;\nconst IS_SERVER = !isWindowDefined || isLegacyDeno;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? useEffect : useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst getTypeName = (value)=>OBJECT.prototype.toString.call(value);\nconst isObjectTypeName = (typeName, type)=>typeName === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const typeName = getTypeName(arg);\n    const isDate = isObjectTypeName(typeName, 'Date');\n    const isRegex = isObjectTypeName(typeName, 'RegExp');\n    const isPlainObject = isObjectTypeName(typeName, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\n\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (// Skip the special useSWRInfinite and useSWRSubscription keys.\n            !/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](revalidateEvents.MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        let isError = false;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n                isError = true;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n                isError = true;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (isError) throw error;\n                return data;\n            } else if (isError && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!isError) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (isError) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\n\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = Object.create(null);\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = Object.create(null);\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    Object.create(null),\n                    Object.create(null),\n                    Object.create(null),\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, revalidateEvents.FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, revalidateEvents.RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = dequal;\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, // use web preset by default\npreset);\n\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\n\nconst SWRConfigContext = createContext({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = useContext(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = useMemo(()=>isFunctionalConfig ? value(parentConfig) : value, [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = useMemo(()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = useRef(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect(()=>{\n        if (cacheContext) {\n            cacheContext[2] && cacheContext[2]();\n            return cacheContext[3];\n        }\n    }, []);\n    return createElement(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\nexport { noop as A, isPromiseLike as B, IS_REACT_LEGACY as I, OBJECT as O, SWRConfigContext as S, UNDEFINED as U, isFunction as a, SWRGlobalState as b, cache as c, defaultConfig as d, isUndefined as e, mergeConfigs as f, SWRConfig as g, initCache as h, isWindowDefined as i, mutate as j, compare as k, stableHash as l, mergeObjects as m, internalMutate as n, getTimestamp as o, preset as p, defaultConfigOptions as q, IS_SERVER as r, serialize as s, rAF as t, useIsomorphicLayoutEffect as u, slowConnection as v, isDocumentDefined as w, isLegacyDeno as x, hasRequestAnimationFrame as y, createCacheHelper as z };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AAHA;;;;AAKA,gEAAgE;AAChE,MAAM,iBAAiB,IAAI;AAE3B,+DAA+D;AAC/D,MAAM,OAAO,KAAK;AAClB,mEAAmE;AACnE,8EAA8E;AAC9E,8DAA8D;AAC9D,kBAAkB;AAClB,MAAM,YAAY,eAAe,GAAG;AACpC,MAAM,SAAS;AACf,MAAM,cAAc,CAAC,IAAI,MAAM;AAC/B,MAAM,aAAa,CAAC,IAAI,OAAO,KAAK;AACpC,MAAM,eAAe,CAAC,GAAG,IAAI,CAAC;QACtB,GAAG,CAAC;QACJ,GAAG,CAAC;IACR,CAAC;AACL,MAAM,gBAAgB,CAAC,IAAI,WAAW,EAAE,IAAI;AAE5C,MAAM,cAAc,CAAC;AACrB,MAAM,gBAAgB,CAAC;AACvB,MAAM,gBAAgB;AACtB,kGAAkG;AAClG,MAAM,kBAAkB,OAAO,UAAU;AACzC,MAAM,oBAAoB,OAAO,YAAY;AAC7C,MAAM,eAAe,mBAAmB,UAAU;AAClD,MAAM,2BAA2B,IAAI,mBAAmB,OAAO,MAAM,CAAC,wBAAwB,IAAI;AAClG,MAAM,oBAAoB,CAAC,OAAO;IAC9B,MAAM,QAAQ,eAAe,GAAG,CAAC;IACjC,OAAO;QACH,SAAS;QACT,IAAI,CAAC,YAAY,QAAQ,MAAM,GAAG,CAAC,QAAQ;QAC3C,SAAS;QACT,CAAC;YACG,IAAI,CAAC,YAAY,MAAM;gBACnB,MAAM,OAAO,MAAM,GAAG,CAAC;gBACvB,sEAAsE;gBACtE,yBAAyB;gBACzB,IAAI,CAAC,CAAC,OAAO,aAAa,GAAG;oBACzB,aAAa,CAAC,IAAI,GAAG;gBACzB;gBACA,KAAK,CAAC,EAAE,CAAC,KAAK,aAAa,MAAM,OAAO,QAAQ;YACpD;QACJ;QACA,aAAa;QACb,KAAK,CAAC,EAAE;QACR,4BAA4B;QAC5B;YACI,IAAI,CAAC,YAAY,MAAM;gBACnB,8EAA8E;gBAC9E,IAAI,OAAO,eAAe,OAAO,aAAa,CAAC,IAAI;YACvD;YACA,2EAA2E;YAC3E,OAAO,CAAC,YAAY,QAAQ,MAAM,GAAG,CAAC,QAAQ;QAClD;KACH;AACL,EAAE,qFAAqF;;AAGvF;;;;;;CAMC,GAAG,IAAI,SAAS;AACjB,MAAM,WAAW,IAAI;AACrB,gFAAgF;AAChF,MAAM,CAAC,eAAe,eAAe,GAAG,mBAAmB,OAAO,gBAAgB,GAAG;IACjF,OAAO,gBAAgB,CAAC,IAAI,CAAC;IAC7B,OAAO,mBAAmB,CAAC,IAAI,CAAC;CACnC,GAAG;IACA;IACA;CACH;AACD,MAAM,YAAY;IACd,MAAM,kBAAkB,qBAAqB,SAAS,eAAe;IACrE,OAAO,YAAY,oBAAoB,oBAAoB;AAC/D;AACA,MAAM,YAAY,CAAC;IACf,mBAAmB;IACnB,IAAI,mBAAmB;QACnB,SAAS,gBAAgB,CAAC,oBAAoB;IAClD;IACA,cAAc,SAAS;IACvB,OAAO;QACH,IAAI,mBAAmB;YACnB,SAAS,mBAAmB,CAAC,oBAAoB;QACrD;QACA,eAAe,SAAS;IAC5B;AACJ;AACA,MAAM,gBAAgB,CAAC;IACnB,4BAA4B;IAC5B,MAAM,WAAW;QACb,SAAS;QACT;IACJ;IACA,gDAAgD;IAChD,MAAM,YAAY;QACd,SAAS;IACb;IACA,cAAc,UAAU;IACxB,cAAc,WAAW;IACzB,OAAO;QACH,eAAe,UAAU;QACzB,eAAe,WAAW;IAC9B;AACJ;AACA,MAAM,SAAS;IACX;IACA;AACJ;AACA,MAAM,uBAAuB;IACzB;IACA;AACJ;AAEA,MAAM,kBAAkB,CAAC,qMAAA,CAAA,UAAK,CAAC,KAAK;AACpC,MAAM,YAAY,CAAC,mBAAmB;AACtC,iCAAiC;AACjC,MAAM,MAAM,CAAC,IAAI,6BAA6B,MAAM,CAAC,wBAAwB,CAAC,KAAK,WAAW,GAAG;AACjG,6EAA6E;AAC7E,6EAA6E;AAC7E,kCAAkC;AAClC,MAAM,4BAA4B,YAAY,qMAAA,CAAA,YAAS,GAAG,qMAAA,CAAA,kBAAe;AACzE,wEAAwE;AACxE,MAAM,sBAAsB,OAAO,cAAc,eAAe,UAAU,UAAU;AACpF,iEAAiE;AACjE,MAAM,iBAAiB,CAAC,aAAa,uBAAuB,CAAC;IACzD;IACA;CACH,CAAC,QAAQ,CAAC,oBAAoB,aAAa,KAAK,oBAAoB,QAAQ;AAE7E,+CAA+C;AAC/C,2CAA2C;AAC3C,yDAAyD;AACzD,6BAA6B;AAC7B,MAAM,QAAQ,IAAI;AAClB,MAAM,cAAc,CAAC,QAAQ,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC5D,MAAM,mBAAmB,CAAC,UAAU,OAAO,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC1E,qBAAqB;AACrB,IAAI,UAAU;AACd,8CAA8C;AAC9C,4CAA4C;AAC5C,kCAAkC;AAClC,gCAAgC;AAChC,4BAA4B;AAC5B,EAAE;AACF,+EAA+E;AAC/E,YAAY;AACZ,MAAM,aAAa,CAAC;IAChB,MAAM,OAAO,OAAO;IACpB,MAAM,WAAW,YAAY;IAC7B,MAAM,SAAS,iBAAiB,UAAU;IAC1C,MAAM,UAAU,iBAAiB,UAAU;IAC3C,MAAM,gBAAgB,iBAAiB,UAAU;IACjD,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,SAAS,OAAO,CAAC,UAAU,CAAC,SAAS;QAC5C,4EAA4E;QAC5E,sDAAsD;QACtD,SAAS,MAAM,GAAG,CAAC;QACnB,IAAI,QAAQ,OAAO;QACnB,4EAA4E;QAC5E,gCAAgC;QAChC,2EAA2E;QAC3E,SAAS,EAAE,UAAU;QACrB,MAAM,GAAG,CAAC,KAAK;QACf,IAAI,MAAM,OAAO,CAAC,MAAM;YACpB,SAAS;YACT,SAAS;YACT,IAAI,QAAQ,GAAG,QAAQ,IAAI,MAAM,EAAE,QAAQ;gBACvC,UAAU,WAAW,GAAG,CAAC,MAAM,IAAI;YACvC;YACA,MAAM,GAAG,CAAC,KAAK;QACnB;QACA,IAAI,eAAe;YACf,qBAAqB;YACrB,SAAS;YACT,MAAM,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI;YAClC,MAAM,CAAC,YAAY,QAAQ,KAAK,GAAG,IAAI;gBACnC,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM,GAAG;oBAC1B,UAAU,QAAQ,MAAM,WAAW,GAAG,CAAC,MAAM,IAAI;gBACrD;YACJ;YACA,MAAM,GAAG,CAAC,KAAK;QACnB;IACJ,OAAO;QACH,SAAS,SAAS,IAAI,MAAM,KAAK,QAAQ,WAAW,IAAI,QAAQ,KAAK,QAAQ,WAAW,KAAK,SAAS,CAAC,OAAO,KAAK;IACvH;IACA,OAAO;AACX;AAEA,MAAM,YAAY,CAAC;IACf,IAAI,WAAW,MAAM;QACjB,IAAI;YACA,MAAM;QACV,EAAE,OAAO,KAAK;YACV,yBAAyB;YACzB,MAAM;QACV;IACJ;IACA,8EAA8E;IAC9E,mBAAmB;IACnB,MAAM,OAAO;IACb,uDAAuD;IACvD,MAAM,OAAO,OAAO,WAAW,MAAM,CAAC,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,WAAW,OAAO;IACjG,OAAO;QACH;QACA;KACH;AACL;AAEA,oBAAoB;AACpB,IAAI,cAAc;AAClB,MAAM,eAAe,IAAI,EAAE;AAE3B,eAAe,eAAe,GAAG,IAAI;IACjC,MAAM,CAAC,OAAO,MAAM,OAAO,MAAM,GAAG;IACpC,oEAAoE;IACpE,gBAAgB;IAChB,MAAM,UAAU,aAAa;QACzB,eAAe;QACf,cAAc;IAClB,GAAG,OAAO,UAAU,YAAY;QAC5B,YAAY;IAChB,IAAI,SAAS,CAAC;IACd,IAAI,gBAAgB,QAAQ,aAAa;IACzC,MAAM,wBAAwB,QAAQ,eAAe;IACrD,IAAI,iBAAiB,QAAQ,cAAc;IAC3C,MAAM,kBAAkB,CAAC;QACrB,OAAO,OAAO,0BAA0B,aAAa,sBAAsB,SAAS,0BAA0B;IAClH;IACA,MAAM,eAAe,QAAQ,YAAY;IACzC,8EAA8E;IAC9E,iBAAiB;IACjB,IAAI,WAAW,OAAO;QAClB,MAAM,YAAY;QAClB,MAAM,cAAc,EAAE;QACtB,MAAM,KAAK,MAAM,IAAI;QACrB,KAAK,MAAM,OAAO,GAAG;YACjB,IACA,CAAC,iBAAiB,IAAI,CAAC,QAAQ,UAAU,MAAM,GAAG,CAAC,KAAK,EAAE,GAAG;gBACzD,YAAY,IAAI,CAAC;YACrB;QACJ;QACA,OAAO,QAAQ,GAAG,CAAC,YAAY,GAAG,CAAC;IACvC;IACA,OAAO,YAAY;;IACnB,eAAe,YAAY,EAAE;QACzB,gBAAgB;QAChB,MAAM,CAAC,IAAI,GAAG,UAAU;QACxB,IAAI,CAAC,KAAK;QACV,MAAM,CAAC,KAAK,IAAI,GAAG,kBAAkB,OAAO;QAC5C,MAAM,CAAC,oBAAoB,UAAU,OAAO,QAAQ,GAAG,eAAe,GAAG,CAAC;QAC1E,MAAM,kBAAkB;YACpB,MAAM,eAAe,kBAAkB,CAAC,IAAI;YAC5C,MAAM,aAAa,WAAW,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,IAAI,EAAE,MAAM,QAAQ,UAAU,KAAK;YAChH,IAAI,YAAY;gBACZ,uEAAuE;gBACvE,gCAAgC;gBAChC,OAAO,KAAK,CAAC,IAAI;gBACjB,OAAO,OAAO,CAAC,IAAI;gBACnB,IAAI,gBAAgB,YAAY,CAAC,EAAE,EAAE;oBACjC,OAAO,YAAY,CAAC,EAAE,CAAC,mJAAA,CAAA,eAA6B,EAAE,IAAI,CAAC,IAAI,MAAM,IAAI;gBAC7E;YACJ;YACA,OAAO,MAAM,IAAI;QACrB;QACA,2EAA2E;QAC3E,IAAI,KAAK,MAAM,GAAG,GAAG;YACjB,kCAAkC;YAClC,OAAO;QACX;QACA,IAAI,OAAO;QACX,IAAI;QACJ,IAAI,UAAU;QACd,4BAA4B;QAC5B,MAAM,mBAAmB;QACzB,QAAQ,CAAC,IAAI,GAAG;YACZ;YACA;SACH;QACD,MAAM,oBAAoB,CAAC,YAAY;QACvC,MAAM,QAAQ;QACd,mFAAmF;QACnF,6EAA6E;QAC7E,8EAA8E;QAC9E,MAAM,gBAAgB,MAAM,IAAI;QAChC,MAAM,cAAc,MAAM,EAAE;QAC5B,MAAM,gBAAgB,YAAY,eAAe,gBAAgB;QACjE,6BAA6B;QAC7B,IAAI,mBAAmB;YACnB,iBAAiB,WAAW,kBAAkB,eAAe,eAAe,iBAAiB;YAC7F,8EAA8E;YAC9E,IAAI;gBACA,MAAM;gBACN,IAAI;YACR;QACJ;QACA,IAAI,WAAW,OAAO;YAClB,6DAA6D;YAC7D,IAAI;gBACA,OAAO,KAAK;YAChB,EAAE,OAAO,KAAK;gBACV,sEAAsE;gBACtE,QAAQ;gBACR,UAAU;YACd;QACJ;QACA,8DAA8D;QAC9D,IAAI,QAAQ,cAAc,OAAO;YAC7B,wEAAwE;YACxE,yBAAyB;YACzB,OAAO,MAAM,KAAK,KAAK,CAAC,CAAC;gBACrB,QAAQ;gBACR,UAAU;YACd;YACA,4EAA4E;YAC5E,mEAAmE;YACnE,wBAAwB;YACxB,IAAI,qBAAqB,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE;gBACvC,IAAI,SAAS,MAAM;gBACnB,OAAO;YACX,OAAO,IAAI,WAAW,qBAAqB,gBAAgB,QAAQ;gBAC/D,+DAA+D;gBAC/D,yBAAyB;gBACzB,gBAAgB;gBAChB,wEAAwE;gBACxE,IAAI;oBACA,MAAM;oBACN,IAAI;gBACR;YACJ;QACJ;QACA,mDAAmD;QACnD,IAAI,eAAe;YACf,IAAI,CAAC,SAAS;gBACV,kCAAkC;gBAClC,IAAI,WAAW,gBAAgB;oBAC3B,MAAM,qBAAqB,cAAc,MAAM;oBAC/C,IAAI;wBACA,MAAM;wBACN,OAAO;wBACP,IAAI;oBACR;gBACJ,OAAO;oBACH,iGAAiG;oBACjG,IAAI;wBACA;wBACA,OAAO;wBACP,IAAI;oBACR;gBACJ;YACJ;QACJ;QACA,sDAAsD;QACtD,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG;QACnB,8CAA8C;QAC9C,QAAQ,OAAO,CAAC,mBAAmB,IAAI,CAAC;YACpC,6EAA6E;YAC7E,mCAAmC;YACnC,IAAI;gBACA,IAAI;YACR;QACJ;QACA,6BAA6B;QAC7B,IAAI,SAAS;YACT,IAAI,cAAc,MAAM;YACxB;QACJ;QACA,OAAO;IACX;AACJ;AAEA,MAAM,oBAAoB,CAAC,cAAc;IACrC,IAAI,MAAM,OAAO,aAAa;QAC1B,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;IACnD;AACJ;AACA,MAAM,YAAY,CAAC,UAAU;IACzB,uEAAuE;IACvE,8EAA8E;IAC9E,aAAa;IACb,iFAAiF;IACjF,mDAAmD;IACnD,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;QAC/B,MAAM,OAAO,aAAa,sBAAsB;QAChD,8EAA8E;QAC9E,uBAAuB;QACvB,MAAM,qBAAqB,OAAO,MAAM,CAAC;QACzC,MAAM,SAAS,eAAe,IAAI,CAAC,WAAW;QAC9C,IAAI,UAAU;QACd,MAAM,gBAAgB,OAAO,MAAM,CAAC;QACpC,MAAM,YAAY,CAAC,KAAK;YACpB,MAAM,OAAO,aAAa,CAAC,IAAI,IAAI,EAAE;YACrC,aAAa,CAAC,IAAI,GAAG;YACrB,KAAK,IAAI,CAAC;YACV,OAAO,IAAI,KAAK,MAAM,CAAC,KAAK,OAAO,CAAC,WAAW;QACnD;QACA,MAAM,SAAS,CAAC,KAAK,OAAO;YACxB,SAAS,GAAG,CAAC,KAAK;YAClB,MAAM,OAAO,aAAa,CAAC,IAAI;YAC/B,IAAI,MAAM;gBACN,KAAK,MAAM,MAAM,KAAK;oBAClB,GAAG,OAAO;gBACd;YACJ;QACJ;QACA,MAAM,eAAe;YACjB,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBAC/B,sEAAsE;gBACtE,eAAe,GAAG,CAAC,UAAU;oBACzB;oBACA,OAAO,MAAM,CAAC;oBACd,OAAO,MAAM,CAAC;oBACd,OAAO,MAAM,CAAC;oBACd;oBACA;oBACA;iBACH;gBACD,IAAI,CAAC,WAAW;oBACZ,8DAA8D;oBAC9D,uEAAuE;oBACvE,4DAA4D;oBAC5D,yBAAyB;oBACzB,qDAAqD;oBACrD,6CAA6C;oBAC7C,MAAM,eAAe,KAAK,SAAS,CAAC,WAAW,IAAI,CAAC,WAAW,kBAAkB,IAAI,CAAC,WAAW,oBAAoB,mJAAA,CAAA,cAA4B;oBACjJ,MAAM,mBAAmB,KAAK,aAAa,CAAC,WAAW,IAAI,CAAC,WAAW,kBAAkB,IAAI,CAAC,WAAW,oBAAoB,mJAAA,CAAA,kBAAgC;oBAC7J,UAAU;wBACN,gBAAgB;wBAChB,oBAAoB;wBACpB,wEAAwE;wBACxE,yEAAyE;wBACzE,8CAA8C;wBAC9C,eAAe,MAAM,CAAC;oBAC1B;gBACJ;YACJ;QACJ;QACA;QACA,wEAAwE;QACxE,iDAAiD;QACjD,6EAA6E;QAC7E,2CAA2C;QAC3C,kEAAkE;QAClE,OAAO;YACH;YACA;YACA;YACA;SACH;IACL;IACA,OAAO;QACH;QACA,eAAe,GAAG,CAAC,SAAS,CAAC,EAAE;KAClC;AACL;AAEA,cAAc;AACd,MAAM,eAAe,CAAC,GAAG,IAAI,QAAQ,YAAY;IAC7C,MAAM,gBAAgB,OAAO,eAAe;IAC5C,MAAM,oBAAoB,KAAK,UAAU;IACzC,sBAAsB;IACtB,MAAM,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,CAAC,CAAC,IAAI,OAAO,kBAAkB;IAC9H,IAAI,CAAC,YAAY,kBAAkB,oBAAoB,eAAe;QAClE;IACJ;IACA,WAAW,YAAY,SAAS;AACpC;AACA,MAAM,UAAU,wIAAA,CAAA,SAAM;AACtB,yBAAyB;AACzB,MAAM,CAAC,OAAO,OAAO,GAAG,UAAU,IAAI;AACtC,iBAAiB;AACjB,MAAM,gBAAgB,aAAa;IAC/B,SAAS;IACT,eAAe;IACf,WAAW;IACX,SAAS;IACT;IACA,aAAa;IACb,WAAW;IACX,mBAAmB;IACnB,uBAAuB;IACvB,mBAAmB;IACnB,oBAAoB;IACpB,WAAW;IACX,oBAAoB,iBAAiB,QAAQ;IAC7C,uBAAuB,IAAI;IAC3B,kBAAkB,IAAI;IACtB,gBAAgB,iBAAiB,OAAO;IACxC,YAAY;IACZ;IACA,UAAU,IAAI;IACd;IACA;IACA,UAAU,CAAC;AACf,GACA;AAEA,MAAM,eAAe,CAAC,GAAG;IACrB,mEAAmE;IACnE,MAAM,IAAI,aAAa,GAAG;IAC1B,yEAAyE;IACzE,IAAI,GAAG;QACH,MAAM,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,EAAE,GAAG;QAClC,MAAM,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,EAAE,GAAG;QAClC,IAAI,MAAM,IAAI;YACV,EAAE,GAAG,GAAG,GAAG,MAAM,CAAC;QACtB;QACA,IAAI,MAAM,IAAI;YACV,EAAE,QAAQ,GAAG,aAAa,IAAI;QAClC;IACJ;IACA,OAAO;AACX;AAEA,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,CAAC;AACxC,MAAM,YAAY,CAAC;IACf,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAChC,MAAM,qBAAqB,WAAW;IACtC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAI,qBAAqB,MAAM,gBAAgB,OAAO;QACzE;QACA;QACA;KACH;IACD,+CAA+C;IAC/C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAI,qBAAqB,SAAS,aAAa,cAAc,SAAS;QACjG;QACA;QACA;KACH;IACD,yCAAyC;IACzC,MAAM,WAAW,UAAU,OAAO,QAAQ;IAC1C,6CAA6C;IAC7C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,IAAI,YAAY,CAAC,gBAAgB,OAAO,EAAE;QACtC,gBAAgB,OAAO,GAAG,UAAU,SAAS,eAAe,KAAK,IAAI,QAAQ;IACjF;IACA,MAAM,eAAe,gBAAgB,OAAO;IAC5C,iDAAiD;IACjD,IAAI,cAAc;QACd,eAAe,KAAK,GAAG,YAAY,CAAC,EAAE;QACtC,eAAe,MAAM,GAAG,YAAY,CAAC,EAAE;IAC3C;IACA,sBAAsB;IACtB,0BAA0B;QACtB,IAAI,cAAc;YACd,YAAY,CAAC,EAAE,IAAI,YAAY,CAAC,EAAE;YAClC,OAAO,YAAY,CAAC,EAAE;QAC1B;IACJ,GAAG,EAAE;IACL,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB,QAAQ,EAAE,aAAa,OAAO;QAChE,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2497, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/swr/dist/_internal/constants.mjs"], "sourcesContent": ["const INFINITE_PREFIX = '$inf$';\n\nexport { INFINITE_PREFIX };\n"], "names": [], "mappings": ";;;AAAA,MAAM,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2508, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/swr/dist/_internal/index.mjs"], "sourcesContent": ["import { i as isWindowDefined, a as isFunction, m as mergeObjects, S as SWRConfigContext, d as defaultConfig, s as serialize, b as SWRGlobalState, c as cache, e as isUndefined, f as mergeConfigs } from './config-context-client-BoS53ST9.mjs';\nexport { I as IS_REACT_LEGACY, r as IS_SERVER, O as OBJECT, g as SWRConfig, U as UNDEFINED, k as compare, z as createCacheHelper, q as defaultConfigOptions, o as getTimestamp, y as hasRequestAnimationFrame, h as initCache, n as internalMutate, w as isDocumentDefined, x as isLegacyDeno, B as isPromiseLike, j as mutate, A as noop, p as preset, t as rAF, v as slowConnection, l as stableHash, u as useIsomorphicLayoutEffect } from './config-context-client-BoS53ST9.mjs';\nimport * as revalidateEvents from './events.mjs';\nexport { revalidateEvents };\nimport { INFINITE_PREFIX } from './constants.mjs';\nexport { INFINITE_PREFIX } from './constants.mjs';\nimport React, { useContext } from 'react';\nexport * from './types.mjs';\n\n// @ts-expect-error\nconst enableDevtools = isWindowDefined && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = React;\n    }\n};\n\nconst normalize = (args)=>{\n    return isFunction(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return mergeObjects(defaultConfig, useContext(SWRConfigContext));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = serialize(key_);\n    const [, , , PRELOAD] = SWRGlobalState.get(cache);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = serialize(key_);\n            const [, , , PRELOAD] = SWRGlobalState.get(cache);\n            if (key.startsWith(INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if (isUndefined(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = mergeConfigs(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\nexport { SWRGlobalState, cache, defaultConfig, isFunction, isUndefined, isWindowDefined, mergeConfigs, mergeObjects, normalize, preload, serialize, subscribeCallback, useSWRConfig, withArgs, withMiddleware };\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAIA;AAEA;;;;;;;;;AAGA,mBAAmB;AACnB,MAAM,iBAAiB,oLAAA,CAAA,IAAe,IAAI,OAAO,oBAAoB;AACrE,MAAM,MAAM,iBAAiB,OAAO,oBAAoB,GAAG,EAAE;AAC7D,MAAM,gBAAgB;IAClB,IAAI,gBAAgB;QAChB,mBAAmB;QACnB,OAAO,sBAAsB,GAAG,qMAAA,CAAA,UAAK;IACzC;AACJ;AAEA,MAAM,YAAY,CAAC;IACf,OAAO,CAAA,GAAA,oLAAA,CAAA,IAAU,AAAD,EAAE,IAAI,CAAC,EAAE,IAAI;QACzB,IAAI,CAAC,EAAE;QACP,IAAI,CAAC,EAAE;QACP,IAAI,CAAC,EAAE,IAAI,CAAC;KACf,GAAG;QACA,IAAI,CAAC,EAAE;QACP;QACA,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,KAAK,CAAC;KAC9C;AACL;AAEA,MAAM,eAAe;IACjB,OAAO,CAAA,GAAA,oLAAA,CAAA,IAAY,AAAD,EAAE,oLAAA,CAAA,IAAa,EAAE,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,oLAAA,CAAA,IAAgB;AAClE;AAEA,MAAM,UAAU,CAAC,MAAM;IACnB,MAAM,CAAC,KAAK,MAAM,GAAG,CAAA,GAAA,oLAAA,CAAA,IAAS,AAAD,EAAE;IAC/B,MAAM,OAAO,QAAQ,GAAG,oLAAA,CAAA,IAAc,CAAC,GAAG,CAAC,oLAAA,CAAA,IAAK;IAChD,2DAA2D;IAC3D,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,OAAO,CAAC,IAAI;IACrC,MAAM,MAAM,QAAQ;IACpB,OAAO,CAAC,IAAI,GAAG;IACf,OAAO;AACX;AACA,MAAM,aAAa,CAAC,aAAa,CAAC,MAAM,UAAU;QAC1C,4EAA4E;QAC5E,MAAM,UAAU,YAAY,CAAC,CAAC,GAAG;YAC7B,MAAM,CAAC,IAAI,GAAG,CAAA,GAAA,oLAAA,CAAA,IAAS,AAAD,EAAE;YACxB,MAAM,OAAO,QAAQ,GAAG,oLAAA,CAAA,IAAc,CAAC,GAAG,CAAC,oLAAA,CAAA,IAAK;YAChD,IAAI,IAAI,UAAU,CAAC,sJAAA,CAAA,kBAAe,GAAG;gBACjC,6CAA6C;gBAC7C,+CAA+C;gBAC/C,OAAO,YAAY;YACvB;YACA,MAAM,MAAM,OAAO,CAAC,IAAI;YACxB,IAAI,CAAA,GAAA,oLAAA,CAAA,IAAW,AAAD,EAAE,MAAM,OAAO,YAAY;YACzC,OAAO,OAAO,CAAC,IAAI;YACnB,OAAO;QACX,CAAC;QACD,OAAO,WAAW,MAAM,SAAS;IACrC;AAEJ,MAAM,sBAAsB,IAAI,MAAM,CAAC;AAEvC,gFAAgF;AAChF,kBAAkB;AAClB,MAAM,WAAW,CAAC;IACd,OAAO,SAAS,WAAW,GAAG,IAAI;QAC9B,+CAA+C;QAC/C,MAAM,iBAAiB;QACvB,uBAAuB;QACvB,MAAM,CAAC,KAAK,IAAI,QAAQ,GAAG,UAAU;QACrC,wBAAwB;QACxB,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,IAAY,AAAD,EAAE,gBAAgB;QAC5C,mBAAmB;QACnB,IAAI,OAAO;QACX,MAAM,EAAE,GAAG,EAAE,GAAG;QAChB,MAAM,aAAa,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC;QACtC,IAAI,IAAI,IAAI,WAAW,MAAM,EAAE,KAAK;YAChC,OAAO,UAAU,CAAC,EAAE,CAAC;QACzB;QACA,OAAO,KAAK,KAAK,MAAM,OAAO,OAAO,IAAI,MAAM;IACnD;AACJ;AAEA,2EAA2E;AAC3E,4BAA4B;AAC5B,MAAM,oBAAoB,CAAC,KAAK,WAAW;IACvC,MAAM,oBAAoB,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE;IAChE,kBAAkB,IAAI,CAAC;IACvB,OAAO;QACH,MAAM,QAAQ,kBAAkB,OAAO,CAAC;QACxC,IAAI,SAAS,GAAG;YACZ,2BAA2B;YAC3B,iBAAiB,CAAC,MAAM,GAAG,iBAAiB,CAAC,kBAAkB,MAAM,GAAG,EAAE;YAC1E,kBAAkB,GAAG;QACzB;IACJ;AACJ;AAEA,yCAAyC;AACzC,MAAM,iBAAiB,CAAC,QAAQ;IAC5B,OAAO,CAAC,GAAG;QACP,MAAM,CAAC,KAAK,IAAI,OAAO,GAAG,UAAU;QACpC,MAAM,OAAO,CAAC,OAAO,GAAG,IAAI,EAAE,EAAE,MAAM,CAAC;QACvC,OAAO,OAAO,KAAK,IAAI;YACnB,GAAG,MAAM;YACT,KAAK;QACT;IACJ;AACJ;AAEA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2780, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/swr/dist/index/index.mjs"], "sourcesContent": ["import React, { useRef, useMemo, useCallback, useDebugValue } from 'react';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nimport { OBJECT as OBJECT$1, SWRConfig as SWRConfig$1, defaultConfig, with<PERSON>rgs, SWRGlobalState, serialize as serialize$1, createCacheHel<PERSON>, isUndefined as isUndefined$1, UNDEFINED as UNDEFINED$1, isPromiseLike, getTimestamp, isFunction as isFunction$1, revalidateEvents, internalMutate, useIsomorphicLayoutEffect, subscribeCallback, IS_SERVER, rAF, IS_REACT_LEGACY, mergeObjects } from '../_internal/index.mjs';\nexport { mutate, preload, useSWRConfig } from '../_internal/index.mjs';\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst getTypeName = (value)=>OBJECT.prototype.toString.call(value);\nconst isObjectTypeName = (typeName, type)=>typeName === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const typeName = getTypeName(arg);\n    const isDate = isObjectTypeName(typeName, 'Date');\n    const isRegex = isObjectTypeName(typeName, 'RegExp');\n    const isPlainObject = isObjectTypeName(typeName, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = React.use || // This extra generic is to avoid TypeScript mixing up the generic and JSX sytax\n// and emitting an error.\n// We assume that this is only for the `use(thenable)` case, not `use(context)`.\n// https://github.com/facebook/react/blob/aed00dacfb79d17c53218404c52b1c7aa59c4a89/packages/react-server/src/ReactFizzThenable.js#L45\n((thenable)=>{\n    switch(thenable.status){\n        case 'pending':\n            throw thenable;\n        case 'fulfilled':\n            return thenable.value;\n        case 'rejected':\n            throw thenable.reason;\n        default:\n            thenable.status = 'pending';\n            thenable.then((v)=>{\n                thenable.status = 'fulfilled';\n                thenable.value = v;\n            }, (e)=>{\n                thenable.status = 'rejected';\n                thenable.reason = e;\n            });\n            throw thenable;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = serialize$1(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = useRef(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = useRef(false);\n    // Refs to keep the key and config.\n    const keyRef = useRef(key);\n    const fetcherRef = useRef(fetcher);\n    const configRef = useRef(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = createCacheHelper(cache, key);\n    const stateDependencies = useRef({}).current;\n    // Resolve the fallback data from either the inline option, or the global provider.\n    // If it's a promise, we simply let React suspend and resolve it for us.\n    const fallback = isUndefined$1(fallbackData) ? isUndefined$1(config.fallback) ? UNDEFINED$1 : config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!isUndefined$1(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = useMemo(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!isUndefined$1(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            return revalidateIfStale !== false;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = mergeObjects(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = useSyncExternalStore(useCallback((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = isUndefined$1(cachedData) ? fallback && isPromiseLike(fallback) ? use(fallback) : fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = useRef(data);\n    const returnedData = keepPreviousData ? isUndefined$1(cachedData) ? isUndefined$1(laggyDataRef.current) ? data : laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !isUndefined$1(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !isUndefined$1(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return isUndefined$1(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return isUndefined$1(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = isUndefined$1(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = isUndefined$1(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = useCallback(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (IS_REACT_LEGACY) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if (isUndefined$1(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && isUndefined$1(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    getTimestamp()\n                ];\n            }\n            // Wait until the ongoing request is done. Deduplication is also\n            // considered here.\n            ;\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = UNDEFINED$1;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!isUndefined$1(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || isFunction$1(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](revalidateEvents.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = useCallback(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return internalMutate(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    useIsomorphicLayoutEffect(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!isUndefined$1(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    useIsomorphicLayoutEffect(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(UNDEFINED$1, WITH_DEDUPE);\n        let nextFocusRevalidatedAt = 0;\n        if (getConfig().revalidateOnFocus) {\n            const initNow = Date.now();\n            nextFocusRevalidatedAt = initNow + getConfig().focusThrottleInterval;\n        }\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        const onRevalidate = (type, opts = {})=>{\n            if (type == revalidateEvents.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == revalidateEvents.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = subscribeCallback(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            // Performance optimization: if a request is already in progress for this key,\n            // skip the revalidation to avoid redundant work\n            if (!FETCH[key]) {\n                if (isUndefined$1(data) || IS_SERVER) {\n                    // Revalidate immediately.\n                    softRevalidate();\n                } else {\n                    // Delay the revalidate if we have data to return so we won't block\n                    // rendering.\n                    rAF(softRevalidate);\n                }\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    useIsomorphicLayoutEffect(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = isFunction$1(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    useDebugValue(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && isUndefined$1(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any fallback data. This causes hydration errors. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!IS_REACT_LEGACY && IS_SERVER) {\n            throw new Error('Fallback data is required when using Suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!isUndefined$1(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if (isUndefined$1(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!isUndefined$1(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    const swrResponse = {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n    return swrResponse;\n};\nconst SWRConfig = OBJECT$1.defineProperty(SWRConfig$1, 'defaultValue', {\n    value: defaultConfig\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = withArgs(useSWRHandler);\n\n// useSWR\n\nexport { SWRConfig, useSWR as default, unstable_serialize };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAGA,+DAA+D;AAC/D,MAAM,OAAO,KAAK;AAClB,mEAAmE;AACnE,8EAA8E;AAC9E,8DAA8D;AAC9D,kBAAkB;AAClB,MAAM,YAAY,eAAe,GAAG;AACpC,MAAM,SAAS;AACf,MAAM,cAAc,CAAC,IAAI,MAAM;AAC/B,MAAM,aAAa,CAAC,IAAI,OAAO,KAAK;AAEpC,+CAA+C;AAC/C,2CAA2C;AAC3C,yDAAyD;AACzD,6BAA6B;AAC7B,MAAM,QAAQ,IAAI;AAClB,MAAM,cAAc,CAAC,QAAQ,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC5D,MAAM,mBAAmB,CAAC,UAAU,OAAO,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC1E,qBAAqB;AACrB,IAAI,UAAU;AACd,8CAA8C;AAC9C,4CAA4C;AAC5C,kCAAkC;AAClC,gCAAgC;AAChC,4BAA4B;AAC5B,EAAE;AACF,+EAA+E;AAC/E,YAAY;AACZ,MAAM,aAAa,CAAC;IAChB,MAAM,OAAO,OAAO;IACpB,MAAM,WAAW,YAAY;IAC7B,MAAM,SAAS,iBAAiB,UAAU;IAC1C,MAAM,UAAU,iBAAiB,UAAU;IAC3C,MAAM,gBAAgB,iBAAiB,UAAU;IACjD,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,SAAS,OAAO,CAAC,UAAU,CAAC,SAAS;QAC5C,4EAA4E;QAC5E,sDAAsD;QACtD,SAAS,MAAM,GAAG,CAAC;QACnB,IAAI,QAAQ,OAAO;QACnB,4EAA4E;QAC5E,gCAAgC;QAChC,2EAA2E;QAC3E,SAAS,EAAE,UAAU;QACrB,MAAM,GAAG,CAAC,KAAK;QACf,IAAI,MAAM,OAAO,CAAC,MAAM;YACpB,SAAS;YACT,SAAS;YACT,IAAI,QAAQ,GAAG,QAAQ,IAAI,MAAM,EAAE,QAAQ;gBACvC,UAAU,WAAW,GAAG,CAAC,MAAM,IAAI;YACvC;YACA,MAAM,GAAG,CAAC,KAAK;QACnB;QACA,IAAI,eAAe;YACf,qBAAqB;YACrB,SAAS;YACT,MAAM,OAAO,OAAO,IAAI,CAAC,KAAK,IAAI;YAClC,MAAM,CAAC,YAAY,QAAQ,KAAK,GAAG,IAAI;gBACnC,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM,GAAG;oBAC1B,UAAU,QAAQ,MAAM,WAAW,GAAG,CAAC,MAAM,IAAI;gBACrD;YACJ;YACA,MAAM,GAAG,CAAC,KAAK;QACnB;IACJ,OAAO;QACH,SAAS,SAAS,IAAI,MAAM,KAAK,QAAQ,WAAW,IAAI,QAAQ,KAAK,QAAQ,WAAW,KAAK,SAAS,CAAC,OAAO,KAAK;IACvH;IACA,OAAO;AACX;AAEA,MAAM,YAAY,CAAC;IACf,IAAI,WAAW,MAAM;QACjB,IAAI;YACA,MAAM;QACV,EAAE,OAAO,KAAK;YACV,yBAAyB;YACzB,MAAM;QACV;IACJ;IACA,8EAA8E;IAC9E,mBAAmB;IACnB,MAAM,OAAO;IACb,uDAAuD;IACvD,MAAM,OAAO,OAAO,WAAW,MAAM,CAAC,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,WAAW,OAAO;IACjG,OAAO;QACH;QACA;KACH;AACL;AAEA,MAAM,qBAAqB,CAAC,MAAM,UAAU,IAAI,CAAC,EAAE;AAEnD,4CAA4C;AAC5C,MAAM,MAAM,qMAAA,CAAA,UAAK,CAAC,GAAG,IAAI,gFAAgF;AACzG,yBAAyB;AACzB,gFAAgF;AAChF,qIAAqI;AACrI,CAAC,CAAC;IACE,OAAO,SAAS,MAAM;QAClB,KAAK;YACD,MAAM;QACV,KAAK;YACD,OAAO,SAAS,KAAK;QACzB,KAAK;YACD,MAAM,SAAS,MAAM;QACzB;YACI,SAAS,MAAM,GAAG;YAClB,SAAS,IAAI,CAAC,CAAC;gBACX,SAAS,MAAM,GAAG;gBAClB,SAAS,KAAK,GAAG;YACrB,GAAG,CAAC;gBACA,SAAS,MAAM,GAAG;gBAClB,SAAS,MAAM,GAAG;YACtB;YACA,MAAM;IACd;AACJ,CAAC;AACD,MAAM,cAAc;IAChB,QAAQ;AACZ;AACA,MAAM,gBAAgB,CAAC,MAAM,SAAS;IAClC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,GAAG;IACnK,MAAM,CAAC,oBAAoB,UAAU,OAAO,QAAQ,GAAG,2NAAA,CAAA,iBAAc,CAAC,GAAG,CAAC;IAC1E,qDAAqD;IACrD,8EAA8E;IAC9E,kBAAkB;IAClB,uCAAuC;IACvC,MAAM,CAAC,KAAK,MAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,YAAW,AAAD,EAAE;IACjC,2CAA2C;IAC3C,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACjC,8EAA8E;IAC9E,iCAAiC;IACjC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,mCAAmC;IACnC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,YAAY,IAAI,UAAU,OAAO;IACvC,MAAM,WAAW,IAAI,YAAY,SAAS,MAAM,YAAY,QAAQ;IACpE,MAAM,CAAC,UAAU,UAAU,gBAAgB,gBAAgB,GAAG,CAAA,GAAA,8NAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;IACvF,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,OAAO;IAC5C,mFAAmF;IACnF,wEAAwE;IACxE,MAAM,WAAW,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,gBAAgB,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,OAAO,QAAQ,IAAI,sNAAA,CAAA,YAAW,GAAG,OAAO,QAAQ,CAAC,IAAI,GAAG;IACrH,MAAM,UAAU,CAAC,MAAM;QACnB,IAAI,MAAM,KAAK,kBAAkB;YAC7B,MAAM,IAAI;YACV,IAAI,MAAM,QAAQ;gBACd,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,GAAG;oBAC/B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,IAAI,CAAC,EAAE,GAAG;wBACzB,OAAO;oBACX;oBACA,IAAI,CAAC,QAAQ,cAAc,OAAO,CAAC,EAAE,GAAG;wBACpC,OAAO;oBACX;gBACJ;YACJ,OAAO;gBACH,IAAI,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;oBACxB,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,qBAAqB,CAAC;YACxB,IAAI,CAAC,KAAK,OAAO;YACjB,IAAI,CAAC,SAAS,OAAO;YACrB,6DAA6D;YAC7D,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,oBAAoB,OAAO;YAC9C,wCAAwC;YACxC,IAAI,YAAY,QAAQ,IAAI,OAAO;YACnC,IAAI,UAAU,OAAO;YACrB,OAAO,sBAAsB;QACjC,CAAC;QACD,mDAAmD;QACnD,MAAM,mBAAmB,CAAC;YACtB,mDAAmD;YACnD,MAAM,WAAW,CAAA,GAAA,yNAAA,CAAA,eAAY,AAAD,EAAE;YAC9B,OAAO,SAAS,EAAE;YAClB,IAAI,CAAC,oBAAoB;gBACrB,OAAO;YACX;YACA,OAAO;gBACH,cAAc;gBACd,WAAW;gBACX,GAAG,QAAQ;YACf;QACJ;QACA,MAAM,aAAa;QACnB,MAAM,cAAc;QACpB,MAAM,iBAAiB,iBAAiB;QACxC,MAAM,iBAAiB,eAAe,cAAc,iBAAiB,iBAAiB;QACtF,wEAAwE;QACxE,qEAAqE;QACrE,sDAAsD;QACtD,IAAI,oBAAoB;QACxB,OAAO;YACH;gBACI,MAAM,cAAc,iBAAiB;gBACrC,MAAM,gBAAgB,QAAQ,aAAa;gBAC3C,IAAI,eAAe;oBACf,iEAAiE;oBACjE,0DAA0D;oBAC1D,uEAAuE;oBACvE,+DAA+D;oBAC/D,uCAAuC;oBACvC,kEAAkE;oBAClE,gEAAgE;oBAChE,oEAAoE;oBACpE,mEAAmE;oBACnE,mCAAmC;oBACnC,kBAAkB,IAAI,GAAG,YAAY,IAAI;oBACzC,kBAAkB,SAAS,GAAG,YAAY,SAAS;oBACnD,kBAAkB,YAAY,GAAG,YAAY,YAAY;oBACzD,kBAAkB,KAAK,GAAG,YAAY,KAAK;oBAC3C,OAAO;gBACX,OAAO;oBACH,oBAAoB;oBACpB,OAAO;gBACX;YACJ;YACA,IAAI;SACP;IACL,uDAAuD;IACvD,GAAG;QACC;QACA;KACH;IACD,gDAAgD;IAChD,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,uBAAoB,AAAD,EAAE,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAW,eAAe,KAAK,CAAC,SAAS;YAClF,IAAI,CAAC,QAAQ,MAAM,UAAU;QACjC,IACJ;QACI;QACA;KACH,GAAG,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE;IAClC,MAAM,iBAAiB,CAAC,kBAAkB,OAAO;IACjD,MAAM,iBAAiB,kBAAkB,CAAC,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,GAAG;IACnF,MAAM,aAAa,OAAO,IAAI;IAC9B,MAAM,OAAO,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,cAAc,YAAY,CAAA,GAAA,0NAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,IAAI,YAAY,WAAW;IAC1G,MAAM,QAAQ,OAAO,KAAK;IAC1B,0FAA0F;IAC1F,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,eAAe,mBAAmB,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,cAAc,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,aAAa,OAAO,IAAI,OAAO,aAAa,OAAO,GAAG,aAAa;IACrJ,iEAAiE;IACjE,wFAAwF;IACxF,8DAA8D;IAC9D,MAAM,8BAA8B,CAAC;QACjC,2FAA2F;QAC3F,IAAI,kBAAkB,CAAC,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,QAAQ,OAAO;QACpD,6DAA6D;QAC7D,IAAI,kBAAkB,CAAC,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,oBAAoB,OAAO;QAChE,wCAAwC;QACxC,IAAI,YAAY,QAAQ,IAAI,OAAO;QACnC,qEAAqE;QACrE,kEAAkE;QAClE,kEAAkE;QAClE,IAAI,UAAU,OAAO,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,QAAQ,QAAQ;QACnD,+DAA+D;QAC/D,oEAAoE;QACpE,OAAO,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,SAAS;IAClC,CAAC;IACD,wCAAwC;IACxC,oFAAoF;IACpF,MAAM,yBAAyB,CAAC,CAAC,CAAC,OAAO,WAAW,kBAAkB,2BAA2B;IACjG,MAAM,eAAe,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,OAAO,YAAY,IAAI,yBAAyB,OAAO,YAAY;IACtG,MAAM,YAAY,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,OAAO,SAAS,IAAI,yBAAyB,OAAO,SAAS;IAC7F,2EAA2E;IAC3E,sDAAsD;IACtD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAClC,MAAM,iBAAiB,WAAW,OAAO;QACzC,IAAI,CAAC,OAAO,CAAC,kBAAkB,aAAa,OAAO,IAAI,YAAY,QAAQ,IAAI;YAC3E,OAAO;QACX;QACA,IAAI;QACJ,IAAI;QACJ,IAAI,UAAU;QACd,MAAM,OAAO,kBAAkB,CAAC;QAChC,uEAAuE;QACvE,mCAAmC;QACnC,MAAM,wBAAwB,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,MAAM;QACzD;;;;;;;;;;MAUF,GAAG,MAAM,oBAAoB;YACvB,IAAI,4NAAA,CAAA,kBAAe,EAAE;gBACjB,OAAO,CAAC,aAAa,OAAO,IAAI,QAAQ,OAAO,OAAO,IAAI,kBAAkB,OAAO;YACvF;YACA,OAAO,QAAQ,OAAO,OAAO;QACjC;QACA,oDAAoD;QACpD,MAAM,aAAa;YACf,cAAc;YACd,WAAW;QACf;QACA,MAAM,8BAA8B;YAChC,SAAS;QACb;QACA,MAAM,eAAe;YACjB,2DAA2D;YAC3D,MAAM,cAAc,KAAK,CAAC,IAAI;YAC9B,IAAI,eAAe,WAAW,CAAC,EAAE,KAAK,SAAS;gBAC3C,OAAO,KAAK,CAAC,IAAI;YACrB;QACJ;QACA,qEAAqE;QACrE,MAAM,eAAe;YACjB,cAAc;QAClB;QACA,0EAA0E;QAC1E,8CAA8C;QAC9C,IAAI,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,WAAW,IAAI,GAAG;YAChC,aAAa,SAAS,GAAG;QAC7B;QACA,IAAI;YACA,IAAI,uBAAuB;gBACvB,SAAS;gBACT,mEAAmE;gBACnE,qCAAqC;gBACrC,IAAI,OAAO,cAAc,IAAI,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,WAAW,IAAI,GAAG;oBACzD,WAAW;wBACP,IAAI,WAAW,qBAAqB;4BAChC,YAAY,aAAa,CAAC,KAAK;wBACnC;oBACJ,GAAG,OAAO,cAAc;gBAC5B;gBACA,4CAA4C;gBAC5C,uCAAuC;gBACvC,KAAK,CAAC,IAAI,GAAG;oBACT,eAAe;oBACf,CAAA,GAAA,yNAAA,CAAA,eAAY,AAAD;iBACd;YACL;YACA,gEAAgE;YAChE,mBAAmB;;YAEnB,CAAC,SAAS,QAAQ,GAAG,KAAK,CAAC,IAAI;YAC/B,UAAU,MAAM;YAChB,IAAI,uBAAuB;gBACvB,0DAA0D;gBAC1D,0BAA0B;gBAC1B,WAAW,cAAc,OAAO,gBAAgB;YACpD;YACA,uEAAuE;YACvE,uEAAuE;YACvE,qDAAqD;YACrD,mCAAmC;YACnC,oDAAoD;YACpD,iDAAiD;YACjD,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS;gBAC1C,IAAI,uBAAuB;oBACvB,IAAI,qBAAqB;wBACrB,YAAY,WAAW,CAAC;oBAC5B;gBACJ;gBACA,OAAO;YACX;YACA,eAAe;YACf,WAAW,KAAK,GAAG,sNAAA,CAAA,YAAW;YAC9B,iFAAiF;YACjF,UAAU;YACV,8BAA8B;YAC9B,yBAAyB;YACzB,UAAU;YACV,8BAA8B;YAC9B,qBAAqB;YACrB,UAAU;YACV,8BAA8B;YAC9B,oCAAoC;YACpC,gFAAgF;YAChF,4EAA4E;YAC5E,MAAM,eAAe,QAAQ,CAAC,IAAI;YAClC,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,iBAAiB,SAAS;YAC7C,CAAC,WAAW,YAAY,CAAC,EAAE,IAAI,SAAS;YACxC,WAAW,YAAY,CAAC,EAAE,IAAI,SAAS;YACvC,YAAY,CAAC,EAAE,KAAK,CAAC,GAAG;gBACpB;gBACA,IAAI,uBAAuB;oBACvB,IAAI,qBAAqB;wBACrB,YAAY,WAAW,CAAC;oBAC5B;gBACJ;gBACA,OAAO;YACX;YACA,gEAAgE;YAChE,uCAAuC;YACvC,MAAM,YAAY,WAAW,IAAI;YACjC,0CAA0C;YAC1C,8EAA8E;YAC9E,WAAW,IAAI,GAAG,QAAQ,WAAW,WAAW,YAAY;YAC5D,gEAAgE;YAChE,IAAI,uBAAuB;gBACvB,IAAI,qBAAqB;oBACrB,YAAY,SAAS,CAAC,SAAS,KAAK;gBACxC;YACJ;QACJ,EAAE,OAAO,KAAK;YACV;YACA,MAAM,gBAAgB;YACtB,MAAM,EAAE,kBAAkB,EAAE,GAAG;YAC/B,qEAAqE;YACrE,IAAI,CAAC,cAAc,QAAQ,IAAI;gBAC3B,yDAAyD;gBACzD,WAAW,KAAK,GAAG;gBACnB,gEAAgE;gBAChE,gBAAgB;gBAChB,IAAI,yBAAyB,qBAAqB;oBAC9C,cAAc,OAAO,CAAC,KAAK,KAAK;oBAChC,IAAI,uBAAuB,QAAQ,CAAA,GAAA,uNAAA,CAAA,aAAY,AAAD,EAAE,uBAAuB,mBAAmB,MAAM;wBAC5F,IAAI,CAAC,YAAY,iBAAiB,IAAI,CAAC,YAAY,qBAAqB,IAAI,YAAY;4BACpF,uDAAuD;4BACvD,8BAA8B;4BAC9B,kDAAkD;4BAClD,cAAc,YAAY,CAAC,KAAK,KAAK,eAAe,CAAC;gCACjD,MAAM,eAAe,kBAAkB,CAAC,IAAI;gCAC5C,IAAI,gBAAgB,YAAY,CAAC,EAAE,EAAE;oCACjC,YAAY,CAAC,EAAE,CAAC,+LAAA,CAAA,mBAAgB,CAAC,sBAAsB,EAAE;gCAC7D;4BACJ,GAAG;gCACC,YAAY,CAAC,KAAK,UAAU,IAAI,CAAC,IAAI;gCACrC,QAAQ;4BACZ;wBACJ;oBACJ;gBACJ;YACJ;QACJ;QACA,2BAA2B;QAC3B,UAAU;QACV,mCAAmC;QACnC;QACA,OAAO;IACX,GACA,sEAAsE;IACtE,kBAAkB;IAClB,EAAE;IACF,SAAS;IACT,2DAA2D;IAC3D,oDAAoD;IACpD,4DAA4D;IAC5D,4CAA4C;IAC5C,mDAAmD;IACnD,uDAAuD;IACvD;QACI;QACA;KACH;IACD,uEAAuE;IACvE,wDAAwD;IACxD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,GAAG;QACA,OAAO,CAAA,GAAA,2NAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,OAAO,KAAK;IACpD,GACA,EAAE;IACF,+BAA+B;IAC/B,CAAA,GAAA,sOAAA,CAAA,4BAAyB,AAAD,EAAE;QACtB,WAAW,OAAO,GAAG;QACrB,UAAU,OAAO,GAAG;QACpB,wEAAwE;QACxE,kCAAkC;QAClC,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,aAAa;YAC5B,aAAa,OAAO,GAAG;QAC3B;IACJ;IACA,gCAAgC;IAChC,CAAA,GAAA,sOAAA,CAAA,4BAAyB,AAAD,EAAE;QACtB,IAAI,CAAC,KAAK;QACV,MAAM,iBAAiB,WAAW,IAAI,CAAC,sNAAA,CAAA,YAAW,EAAE;QACpD,IAAI,yBAAyB;QAC7B,IAAI,YAAY,iBAAiB,EAAE;YAC/B,MAAM,UAAU,KAAK,GAAG;YACxB,yBAAyB,UAAU,YAAY,qBAAqB;QACxE;QACA,mEAAmE;QACnE,iCAAiC;QACjC,MAAM,eAAe,CAAC,MAAM,OAAO,CAAC,CAAC;YACjC,IAAI,QAAQ,+LAAA,CAAA,mBAAgB,CAAC,WAAW,EAAE;gBACtC,MAAM,MAAM,KAAK,GAAG;gBACpB,IAAI,YAAY,iBAAiB,IAAI,MAAM,0BAA0B,YAAY;oBAC7E,yBAAyB,MAAM,YAAY,qBAAqB;oBAChE;gBACJ;YACJ,OAAO,IAAI,QAAQ,+LAAA,CAAA,mBAAgB,CAAC,eAAe,EAAE;gBACjD,IAAI,YAAY,qBAAqB,IAAI,YAAY;oBACjD;gBACJ;YACJ,OAAO,IAAI,QAAQ,+LAAA,CAAA,mBAAgB,CAAC,YAAY,EAAE;gBAC9C,OAAO;YACX,OAAO,IAAI,QAAQ,+LAAA,CAAA,mBAAgB,CAAC,sBAAsB,EAAE;gBACxD,OAAO,WAAW;YACtB;YACA;QACJ;QACA,MAAM,cAAc,CAAA,GAAA,kKAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,oBAAoB;QAC/D,+DAA+D;QAC/D,aAAa,OAAO,GAAG;QACvB,OAAO,OAAO,GAAG;QACjB,kBAAkB,OAAO,GAAG;QAC5B,sCAAsC;QACtC,SAAS;YACL,IAAI;QACR;QACA,yBAAyB;QACzB,IAAI,6BAA6B;YAC7B,8EAA8E;YAC9E,gDAAgD;YAChD,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;gBACb,IAAI,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,SAAS,sNAAA,CAAA,YAAS,EAAE;oBAClC,0BAA0B;oBAC1B;gBACJ,OAAO;oBACH,mEAAmE;oBACnE,aAAa;oBACb,CAAA,GAAA,gNAAA,CAAA,MAAG,AAAD,EAAE;gBACR;YACJ;QACJ;QACA,OAAO;YACH,wBAAwB;YACxB,aAAa,OAAO,GAAG;YACvB;QACJ;IACJ,GAAG;QACC;KACH;IACD,UAAU;IACV,CAAA,GAAA,sOAAA,CAAA,4BAAyB,AAAD,EAAE;QACtB,IAAI;QACJ,SAAS;YACL,0BAA0B;YAC1B,sEAAsE;YACtE,MAAM,WAAW,CAAA,GAAA,uNAAA,CAAA,aAAY,AAAD,EAAE,mBAAmB,gBAAgB,WAAW,IAAI,IAAI;YACpF,sEAAsE;YACtE,mDAAmD;YACnD,gEAAgE;YAChE,IAAI,YAAY,UAAU,CAAC,GAAG;gBAC1B,QAAQ,WAAW,SAAS;YAChC;QACJ;QACA,SAAS;YACL,+BAA+B;YAC/B,qEAAqE;YACrE,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,qBAAqB,YAAY,SAAS,EAAE,KAAK,CAAC,sBAAsB,YAAY,QAAQ,EAAE,GAAG;gBACvH,WAAW,aAAa,IAAI,CAAC;YACjC,OAAO;gBACH,6CAA6C;gBAC7C;YACJ;QACJ;QACA;QACA,OAAO;YACH,IAAI,OAAO;gBACP,aAAa;gBACb,QAAQ,CAAC;YACb;QACJ;IACJ,GAAG;QACC;QACA;QACA;QACA;KACH;IACD,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;IACd,4DAA4D;IAC5D,gFAAgF;IAChF,2EAA2E;IAC3E,yBAAyB;IACzB,IAAI,YAAY,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,SAAS,KAAK;QACxC,4EAA4E;QAC5E,0EAA0E;QAC1E,4CAA4C;QAC5C,IAAI,CAAC,4NAAA,CAAA,kBAAe,IAAI,sNAAA,CAAA,YAAS,EAAE;YAC/B,MAAM,IAAI,MAAM;QACpB;QACA,qEAAqE;QACrE,WAAW,OAAO,GAAG;QACrB,UAAU,OAAO,GAAG;QACpB,aAAa,OAAO,GAAG;QACvB,MAAM,MAAM,OAAO,CAAC,IAAI;QACxB,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,MAAM;YACrB,MAAM,UAAU,YAAY;YAC5B,IAAI;QACR;QACA,IAAI,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,QAAQ;YACtB,MAAM,UAAU,WAAW;YAC3B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,cAAa,AAAD,EAAE,eAAe;gBAC9B,QAAQ,MAAM,GAAG;gBACjB,QAAQ,KAAK,GAAG;YACpB;YACA,IAAI;QACR,OAAO;YACH,MAAM;QACV;IACJ;IACA,MAAM,cAAc;QAChB,QAAQ;QACR,IAAI,QAAQ;YACR,kBAAkB,IAAI,GAAG;YACzB,OAAO;QACX;QACA,IAAI,SAAS;YACT,kBAAkB,KAAK,GAAG;YAC1B,OAAO;QACX;QACA,IAAI,gBAAgB;YAChB,kBAAkB,YAAY,GAAG;YACjC,OAAO;QACX;QACA,IAAI,aAAa;YACb,kBAAkB,SAAS,GAAG;YAC9B,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,MAAM,YAAY,mNAAA,CAAA,SAAQ,CAAC,cAAc,CAAC,sNAAA,CAAA,YAAW,EAAE,gBAAgB;IACnE,OAAO,0NAAA,CAAA,gBAAa;AACxB;AACA;;;;;;;;;;;;;;CAcC,GAAG,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,WAAQ,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3452, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/dequal/lite/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nexport function dequal(foo, bar) {\n\tvar ctor, len;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAElC,SAAS,OAAO,GAAG,EAAE,GAAG;IAC9B,IAAI,MAAM;IACV,IAAI,QAAQ,KAAK,OAAO;IAExB,IAAI,OAAO,OAAO,CAAC,OAAK,IAAI,WAAW,MAAM,IAAI,WAAW,EAAE;QAC7D,IAAI,SAAS,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO;QACvD,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;QAE3D,IAAI,SAAS,OAAO;YACnB,IAAI,CAAC,MAAI,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAO,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YAC1C;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;YACrC,MAAM;YACN,IAAK,QAAQ,IAAK;gBACjB,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,OAAO;gBACjE,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;YAC7D;YACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;QACpC;IACD;IAEA,OAAO,QAAQ,OAAO,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3485, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/chalk/source/vendor/ansi-styles/index.js"], "sourcesContent": ["const ANSI_BACKGROUND_OFFSET = 10;\n\nconst wrapAnsi16 = (offset = 0) => code => `\\u001B[${code + offset}m`;\n\nconst wrapAnsi256 = (offset = 0) => code => `\\u001B[${38 + offset};5;${code}m`;\n\nconst wrapAnsi16m = (offset = 0) => (red, green, blue) => `\\u001B[${38 + offset};2;${red};${green};${blue}m`;\n\nconst styles = {\n\tmodifier: {\n\t\treset: [0, 0],\n\t\t// 21 isn't widely supported and 22 does the same thing\n\t\tbold: [1, 22],\n\t\tdim: [2, 22],\n\t\titalic: [3, 23],\n\t\tunderline: [4, 24],\n\t\toverline: [53, 55],\n\t\tinverse: [7, 27],\n\t\thidden: [8, 28],\n\t\tstrikethrough: [9, 29],\n\t},\n\tcolor: {\n\t\tblack: [30, 39],\n\t\tred: [31, 39],\n\t\tgreen: [32, 39],\n\t\tyellow: [33, 39],\n\t\tblue: [34, 39],\n\t\tmagenta: [35, 39],\n\t\tcyan: [36, 39],\n\t\twhite: [37, 39],\n\n\t\t// Bright color\n\t\tblackBright: [90, 39],\n\t\tgray: [90, 39], // Alias of `blackBright`\n\t\tgrey: [90, 39], // Alias of `blackBright`\n\t\tredBright: [91, 39],\n\t\tgreenBright: [92, 39],\n\t\tyellowBright: [93, 39],\n\t\tblueBright: [94, 39],\n\t\tmagentaBright: [95, 39],\n\t\tcyanBright: [96, 39],\n\t\twhiteBright: [97, 39],\n\t},\n\tbgColor: {\n\t\tbgBlack: [40, 49],\n\t\tbgRed: [41, 49],\n\t\tbgGreen: [42, 49],\n\t\tbgYellow: [43, 49],\n\t\tbgBlue: [44, 49],\n\t\tbgMagenta: [45, 49],\n\t\tbgCyan: [46, 49],\n\t\tbgWhite: [47, 49],\n\n\t\t// Bright color\n\t\tbgBlackBright: [100, 49],\n\t\tbgGray: [100, 49], // Alias of `bgBlackBright`\n\t\tbgGrey: [100, 49], // Alias of `bgBlackBright`\n\t\tbgRedBright: [101, 49],\n\t\tbgGreenBright: [102, 49],\n\t\tbgYellowBright: [103, 49],\n\t\tbgBlueBright: [104, 49],\n\t\tbgMagentaBright: [105, 49],\n\t\tbgCyanBright: [106, 49],\n\t\tbgWhiteBright: [107, 49],\n\t},\n};\n\nexport const modifierNames = Object.keys(styles.modifier);\nexport const foregroundColorNames = Object.keys(styles.color);\nexport const backgroundColorNames = Object.keys(styles.bgColor);\nexport const colorNames = [...foregroundColorNames, ...backgroundColorNames];\n\nfunction assembleStyles() {\n\tconst codes = new Map();\n\n\tfor (const [groupName, group] of Object.entries(styles)) {\n\t\tfor (const [styleName, style] of Object.entries(group)) {\n\t\t\tstyles[styleName] = {\n\t\t\t\topen: `\\u001B[${style[0]}m`,\n\t\t\t\tclose: `\\u001B[${style[1]}m`,\n\t\t\t};\n\n\t\t\tgroup[styleName] = styles[styleName];\n\n\t\t\tcodes.set(style[0], style[1]);\n\t\t}\n\n\t\tObject.defineProperty(styles, groupName, {\n\t\t\tvalue: group,\n\t\t\tenumerable: false,\n\t\t});\n\t}\n\n\tObject.defineProperty(styles, 'codes', {\n\t\tvalue: codes,\n\t\tenumerable: false,\n\t});\n\n\tstyles.color.close = '\\u001B[39m';\n\tstyles.bgColor.close = '\\u001B[49m';\n\n\tstyles.color.ansi = wrapAnsi16();\n\tstyles.color.ansi256 = wrapAnsi256();\n\tstyles.color.ansi16m = wrapAnsi16m();\n\tstyles.bgColor.ansi = wrapAnsi16(ANSI_BACKGROUND_OFFSET);\n\tstyles.bgColor.ansi256 = wrapAnsi256(ANSI_BACKGROUND_OFFSET);\n\tstyles.bgColor.ansi16m = wrapAnsi16m(ANSI_BACKGROUND_OFFSET);\n\n\t// From https://github.com/Qix-/color-convert/blob/3f0e0d4e92e235796ccb17f6e85c72094a651f49/conversions.js\n\tObject.defineProperties(styles, {\n\t\trgbToAnsi256: {\n\t\t\tvalue(red, green, blue) {\n\t\t\t\t// We use the extended greyscale palette here, with the exception of\n\t\t\t\t// black and white. normal palette only has 4 greyscale shades.\n\t\t\t\tif (red === green && green === blue) {\n\t\t\t\t\tif (red < 8) {\n\t\t\t\t\t\treturn 16;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (red > 248) {\n\t\t\t\t\t\treturn 231;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Math.round(((red - 8) / 247) * 24) + 232;\n\t\t\t\t}\n\n\t\t\t\treturn 16\n\t\t\t\t\t+ (36 * Math.round(red / 255 * 5))\n\t\t\t\t\t+ (6 * Math.round(green / 255 * 5))\n\t\t\t\t\t+ Math.round(blue / 255 * 5);\n\t\t\t},\n\t\t\tenumerable: false,\n\t\t},\n\t\thexToRgb: {\n\t\t\tvalue(hex) {\n\t\t\t\tconst matches = /[a-f\\d]{6}|[a-f\\d]{3}/i.exec(hex.toString(16));\n\t\t\t\tif (!matches) {\n\t\t\t\t\treturn [0, 0, 0];\n\t\t\t\t}\n\n\t\t\t\tlet [colorString] = matches;\n\n\t\t\t\tif (colorString.length === 3) {\n\t\t\t\t\tcolorString = [...colorString].map(character => character + character).join('');\n\t\t\t\t}\n\n\t\t\t\tconst integer = Number.parseInt(colorString, 16);\n\n\t\t\t\treturn [\n\t\t\t\t\t/* eslint-disable no-bitwise */\n\t\t\t\t\t(integer >> 16) & 0xFF,\n\t\t\t\t\t(integer >> 8) & 0xFF,\n\t\t\t\t\tinteger & 0xFF,\n\t\t\t\t\t/* eslint-enable no-bitwise */\n\t\t\t\t];\n\t\t\t},\n\t\t\tenumerable: false,\n\t\t},\n\t\thexToAnsi256: {\n\t\t\tvalue: hex => styles.rgbToAnsi256(...styles.hexToRgb(hex)),\n\t\t\tenumerable: false,\n\t\t},\n\t\tansi256ToAnsi: {\n\t\t\tvalue(code) {\n\t\t\t\tif (code < 8) {\n\t\t\t\t\treturn 30 + code;\n\t\t\t\t}\n\n\t\t\t\tif (code < 16) {\n\t\t\t\t\treturn 90 + (code - 8);\n\t\t\t\t}\n\n\t\t\t\tlet red;\n\t\t\t\tlet green;\n\t\t\t\tlet blue;\n\n\t\t\t\tif (code >= 232) {\n\t\t\t\t\tred = (((code - 232) * 10) + 8) / 255;\n\t\t\t\t\tgreen = red;\n\t\t\t\t\tblue = red;\n\t\t\t\t} else {\n\t\t\t\t\tcode -= 16;\n\n\t\t\t\t\tconst remainder = code % 36;\n\n\t\t\t\t\tred = Math.floor(code / 36) / 5;\n\t\t\t\t\tgreen = Math.floor(remainder / 6) / 5;\n\t\t\t\t\tblue = (remainder % 6) / 5;\n\t\t\t\t}\n\n\t\t\t\tconst value = Math.max(red, green, blue) * 2;\n\n\t\t\t\tif (value === 0) {\n\t\t\t\t\treturn 30;\n\t\t\t\t}\n\n\t\t\t\t// eslint-disable-next-line no-bitwise\n\t\t\t\tlet result = 30 + ((Math.round(blue) << 2) | (Math.round(green) << 1) | Math.round(red));\n\n\t\t\t\tif (value === 2) {\n\t\t\t\t\tresult += 60;\n\t\t\t\t}\n\n\t\t\t\treturn result;\n\t\t\t},\n\t\t\tenumerable: false,\n\t\t},\n\t\trgbToAnsi: {\n\t\t\tvalue: (red, green, blue) => styles.ansi256ToAnsi(styles.rgbToAnsi256(red, green, blue)),\n\t\t\tenumerable: false,\n\t\t},\n\t\thexToAnsi: {\n\t\t\tvalue: hex => styles.ansi256ToAnsi(styles.hexToAnsi256(hex)),\n\t\t\tenumerable: false,\n\t\t},\n\t});\n\n\treturn styles;\n}\n\nconst ansiStyles = assembleStyles();\n\nexport default ansiStyles;\n"], "names": [], "mappings": ";;;;;;;AAAA,MAAM,yBAAyB;AAE/B,MAAM,aAAa,CAAC,SAAS,CAAC,GAAK,CAAA,OAAQ,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,CAAC;AAErE,MAAM,cAAc,CAAC,SAAS,CAAC,GAAK,CAAA,OAAQ,CAAC,OAAO,EAAE,KAAK,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;AAE9E,MAAM,cAAc,CAAC,SAAS,CAAC,GAAK,CAAC,KAAK,OAAO,OAAS,CAAC,OAAO,EAAE,KAAK,OAAO,GAAG,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;AAE5G,MAAM,SAAS;IACd,UAAU;QACT,OAAO;YAAC;YAAG;SAAE;QACb,uDAAuD;QACvD,MAAM;YAAC;YAAG;SAAG;QACb,KAAK;YAAC;YAAG;SAAG;QACZ,QAAQ;YAAC;YAAG;SAAG;QACf,WAAW;YAAC;YAAG;SAAG;QAClB,UAAU;YAAC;YAAI;SAAG;QAClB,SAAS;YAAC;YAAG;SAAG;QAChB,QAAQ;YAAC;YAAG;SAAG;QACf,eAAe;YAAC;YAAG;SAAG;IACvB;IACA,OAAO;QACN,OAAO;YAAC;YAAI;SAAG;QACf,KAAK;YAAC;YAAI;SAAG;QACb,OAAO;YAAC;YAAI;SAAG;QACf,QAAQ;YAAC;YAAI;SAAG;QAChB,MAAM;YAAC;YAAI;SAAG;QACd,SAAS;YAAC;YAAI;SAAG;QACjB,MAAM;YAAC;YAAI;SAAG;QACd,OAAO;YAAC;YAAI;SAAG;QAEf,eAAe;QACf,aAAa;YAAC;YAAI;SAAG;QACrB,MAAM;YAAC;YAAI;SAAG;QACd,MAAM;YAAC;YAAI;SAAG;QACd,WAAW;YAAC;YAAI;SAAG;QACnB,aAAa;YAAC;YAAI;SAAG;QACrB,cAAc;YAAC;YAAI;SAAG;QACtB,YAAY;YAAC;YAAI;SAAG;QACpB,eAAe;YAAC;YAAI;SAAG;QACvB,YAAY;YAAC;YAAI;SAAG;QACpB,aAAa;YAAC;YAAI;SAAG;IACtB;IACA,SAAS;QACR,SAAS;YAAC;YAAI;SAAG;QACjB,OAAO;YAAC;YAAI;SAAG;QACf,SAAS;YAAC;YAAI;SAAG;QACjB,UAAU;YAAC;YAAI;SAAG;QAClB,QAAQ;YAAC;YAAI;SAAG;QAChB,WAAW;YAAC;YAAI;SAAG;QACnB,QAAQ;YAAC;YAAI;SAAG;QAChB,SAAS;YAAC;YAAI;SAAG;QAEjB,eAAe;QACf,eAAe;YAAC;YAAK;SAAG;QACxB,QAAQ;YAAC;YAAK;SAAG;QACjB,QAAQ;YAAC;YAAK;SAAG;QACjB,aAAa;YAAC;YAAK;SAAG;QACtB,eAAe;YAAC;YAAK;SAAG;QACxB,gBAAgB;YAAC;YAAK;SAAG;QACzB,cAAc;YAAC;YAAK;SAAG;QACvB,iBAAiB;YAAC;YAAK;SAAG;QAC1B,cAAc;YAAC;YAAK;SAAG;QACvB,eAAe;YAAC;YAAK;SAAG;IACzB;AACD;AAEO,MAAM,gBAAgB,OAAO,IAAI,CAAC,OAAO,QAAQ;AACjD,MAAM,uBAAuB,OAAO,IAAI,CAAC,OAAO,KAAK;AACrD,MAAM,uBAAuB,OAAO,IAAI,CAAC,OAAO,OAAO;AACvD,MAAM,aAAa;OAAI;OAAyB;CAAqB;AAE5E,SAAS;IACR,MAAM,QAAQ,IAAI;IAElB,KAAK,MAAM,CAAC,WAAW,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QACxD,KAAK,MAAM,CAAC,WAAW,MAAM,IAAI,OAAO,OAAO,CAAC,OAAQ;YACvD,MAAM,CAAC,UAAU,GAAG;gBACnB,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC3B,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7B;YAEA,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU;YAEpC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;QAC7B;QAEA,OAAO,cAAc,CAAC,QAAQ,WAAW;YACxC,OAAO;YACP,YAAY;QACb;IACD;IAEA,OAAO,cAAc,CAAC,QAAQ,SAAS;QACtC,OAAO;QACP,YAAY;IACb;IAEA,OAAO,KAAK,CAAC,KAAK,GAAG;IACrB,OAAO,OAAO,CAAC,KAAK,GAAG;IAEvB,OAAO,KAAK,CAAC,IAAI,GAAG;IACpB,OAAO,KAAK,CAAC,OAAO,GAAG;IACvB,OAAO,KAAK,CAAC,OAAO,GAAG;IACvB,OAAO,OAAO,CAAC,IAAI,GAAG,WAAW;IACjC,OAAO,OAAO,CAAC,OAAO,GAAG,YAAY;IACrC,OAAO,OAAO,CAAC,OAAO,GAAG,YAAY;IAErC,0GAA0G;IAC1G,OAAO,gBAAgB,CAAC,QAAQ;QAC/B,cAAc;YACb,OAAM,GAAG,EAAE,KAAK,EAAE,IAAI;gBACrB,oEAAoE;gBACpE,+DAA+D;gBAC/D,IAAI,QAAQ,SAAS,UAAU,MAAM;oBACpC,IAAI,MAAM,GAAG;wBACZ,OAAO;oBACR;oBAEA,IAAI,MAAM,KAAK;wBACd,OAAO;oBACR;oBAEA,OAAO,KAAK,KAAK,CAAC,AAAC,CAAC,MAAM,CAAC,IAAI,MAAO,MAAM;gBAC7C;gBAEA,OAAO,KACH,KAAK,KAAK,KAAK,CAAC,MAAM,MAAM,KAC5B,IAAI,KAAK,KAAK,CAAC,QAAQ,MAAM,KAC9B,KAAK,KAAK,CAAC,OAAO,MAAM;YAC5B;YACA,YAAY;QACb;QACA,UAAU;YACT,OAAM,GAAG;gBACR,MAAM,UAAU,yBAAyB,IAAI,CAAC,IAAI,QAAQ,CAAC;gBAC3D,IAAI,CAAC,SAAS;oBACb,OAAO;wBAAC;wBAAG;wBAAG;qBAAE;gBACjB;gBAEA,IAAI,CAAC,YAAY,GAAG;gBAEpB,IAAI,YAAY,MAAM,KAAK,GAAG;oBAC7B,cAAc;2BAAI;qBAAY,CAAC,GAAG,CAAC,CAAA,YAAa,YAAY,WAAW,IAAI,CAAC;gBAC7E;gBAEA,MAAM,UAAU,OAAO,QAAQ,CAAC,aAAa;gBAE7C,OAAO;oBACN,6BAA6B,GAC7B,AAAC,WAAW,KAAM;oBACjB,WAAW,IAAK;oBACjB,UAAU;iBAEV;YACF;YACA,YAAY;QACb;QACA,cAAc;YACb,OAAO,CAAA,MAAO,OAAO,YAAY,IAAI,OAAO,QAAQ,CAAC;YACrD,YAAY;QACb;QACA,eAAe;YACd,OAAM,IAAI;gBACT,IAAI,OAAO,GAAG;oBACb,OAAO,KAAK;gBACb;gBAEA,IAAI,OAAO,IAAI;oBACd,OAAO,KAAK,CAAC,OAAO,CAAC;gBACtB;gBAEA,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBAEJ,IAAI,QAAQ,KAAK;oBAChB,MAAM,CAAC,AAAC,CAAC,OAAO,GAAG,IAAI,KAAM,CAAC,IAAI;oBAClC,QAAQ;oBACR,OAAO;gBACR,OAAO;oBACN,QAAQ;oBAER,MAAM,YAAY,OAAO;oBAEzB,MAAM,KAAK,KAAK,CAAC,OAAO,MAAM;oBAC9B,QAAQ,KAAK,KAAK,CAAC,YAAY,KAAK;oBACpC,OAAO,AAAC,YAAY,IAAK;gBAC1B;gBAEA,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,OAAO,QAAQ;gBAE3C,IAAI,UAAU,GAAG;oBAChB,OAAO;gBACR;gBAEA,sCAAsC;gBACtC,IAAI,SAAS,KAAK,CAAC,AAAC,KAAK,KAAK,CAAC,SAAS,IAAM,KAAK,KAAK,CAAC,UAAU,IAAK,KAAK,KAAK,CAAC,IAAI;gBAEvF,IAAI,UAAU,GAAG;oBAChB,UAAU;gBACX;gBAEA,OAAO;YACR;YACA,YAAY;QACb;QACA,WAAW;YACV,OAAO,CAAC,KAAK,OAAO,OAAS,OAAO,aAAa,CAAC,OAAO,YAAY,CAAC,KAAK,OAAO;YAClF,YAAY;QACb;QACA,WAAW;YACV,OAAO,CAAA,MAAO,OAAO,aAAa,CAAC,OAAO,YAAY,CAAC;YACvD,YAAY;QACb;IACD;IAEA,OAAO;AACR;AAEA,MAAM,aAAa;uCAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3824, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/chalk/source/vendor/supports-color/index.js"], "sourcesContent": ["import process from 'node:process';\nimport os from 'node:os';\nimport tty from 'node:tty';\n\n// From: https://github.com/sindresorhus/has-flag/blob/main/index.js\n/// function hasFlag(flag, argv = globalThis.Deno?.args ?? process.argv) {\nfunction hasFlag(flag, argv = globalThis.Deno ? globalThis.Deno.args : process.argv) {\n\tconst prefix = flag.startsWith('-') ? '' : (flag.length === 1 ? '-' : '--');\n\tconst position = argv.indexOf(prefix + flag);\n\tconst terminatorPosition = argv.indexOf('--');\n\treturn position !== -1 && (terminatorPosition === -1 || position < terminatorPosition);\n}\n\nconst {env} = process;\n\nlet flagForceColor;\nif (\n\thasFlag('no-color')\n\t|| hasFlag('no-colors')\n\t|| hasFlag('color=false')\n\t|| hasFlag('color=never')\n) {\n\tflagForceColor = 0;\n} else if (\n\thasFlag('color')\n\t|| hasFlag('colors')\n\t|| hasFlag('color=true')\n\t|| hasFlag('color=always')\n) {\n\tflagForceColor = 1;\n}\n\nfunction envForceColor() {\n\tif ('FORCE_COLOR' in env) {\n\t\tif (env.FORCE_COLOR === 'true') {\n\t\t\treturn 1;\n\t\t}\n\n\t\tif (env.FORCE_COLOR === 'false') {\n\t\t\treturn 0;\n\t\t}\n\n\t\treturn env.FORCE_COLOR.length === 0 ? 1 : Math.min(Number.parseInt(env.FORCE_COLOR, 10), 3);\n\t}\n}\n\nfunction translateLevel(level) {\n\tif (level === 0) {\n\t\treturn false;\n\t}\n\n\treturn {\n\t\tlevel,\n\t\thasBasic: true,\n\t\thas256: level >= 2,\n\t\thas16m: level >= 3,\n\t};\n}\n\nfunction _supportsColor(haveStream, {streamIsTTY, sniffFlags = true} = {}) {\n\tconst noFlagForceColor = envForceColor();\n\tif (noFlagForceColor !== undefined) {\n\t\tflagForceColor = noFlagForceColor;\n\t}\n\n\tconst forceColor = sniffFlags ? flagForceColor : noFlagForceColor;\n\n\tif (forceColor === 0) {\n\t\treturn 0;\n\t}\n\n\tif (sniffFlags) {\n\t\tif (hasFlag('color=16m')\n\t\t\t|| hasFlag('color=full')\n\t\t\t|| hasFlag('color=truecolor')) {\n\t\t\treturn 3;\n\t\t}\n\n\t\tif (hasFlag('color=256')) {\n\t\t\treturn 2;\n\t\t}\n\t}\n\n\t// Check for Azure DevOps pipelines.\n\t// Has to be above the `!streamIsTTY` check.\n\tif ('TF_BUILD' in env && 'AGENT_NAME' in env) {\n\t\treturn 1;\n\t}\n\n\tif (haveStream && !streamIsTTY && forceColor === undefined) {\n\t\treturn 0;\n\t}\n\n\tconst min = forceColor || 0;\n\n\tif (env.TERM === 'dumb') {\n\t\treturn min;\n\t}\n\n\tif (process.platform === 'win32') {\n\t\t// Windows 10 build 10586 is the first Windows release that supports 256 colors.\n\t\t// Windows 10 build 14931 is the first release that supports 16m/TrueColor.\n\t\tconst osRelease = os.release().split('.');\n\t\tif (\n\t\t\tNumber(osRelease[0]) >= 10\n\t\t\t&& Number(osRelease[2]) >= 10_586\n\t\t) {\n\t\t\treturn Number(osRelease[2]) >= 14_931 ? 3 : 2;\n\t\t}\n\n\t\treturn 1;\n\t}\n\n\tif ('CI' in env) {\n\t\tif (['GITHUB_ACTIONS', 'GITEA_ACTIONS', 'CIRCLECI'].some(key => key in env)) {\n\t\t\treturn 3;\n\t\t}\n\n\t\tif (['TRAVIS', 'APPVEYOR', 'GITLAB_CI', 'BUILDKITE', 'DRONE'].some(sign => sign in env) || env.CI_NAME === 'codeship') {\n\t\t\treturn 1;\n\t\t}\n\n\t\treturn min;\n\t}\n\n\tif ('TEAMCITY_VERSION' in env) {\n\t\treturn /^(9\\.(0*[1-9]\\d*)\\.|\\d{2,}\\.)/.test(env.TEAMCITY_VERSION) ? 1 : 0;\n\t}\n\n\tif (env.COLORTERM === 'truecolor') {\n\t\treturn 3;\n\t}\n\n\tif (env.TERM === 'xterm-kitty') {\n\t\treturn 3;\n\t}\n\n\tif ('TERM_PROGRAM' in env) {\n\t\tconst version = Number.parseInt((env.TERM_PROGRAM_VERSION || '').split('.')[0], 10);\n\n\t\tswitch (env.TERM_PROGRAM) {\n\t\t\tcase 'iTerm.app': {\n\t\t\t\treturn version >= 3 ? 3 : 2;\n\t\t\t}\n\n\t\t\tcase 'Apple_Terminal': {\n\t\t\t\treturn 2;\n\t\t\t}\n\t\t\t// No default\n\t\t}\n\t}\n\n\tif (/-256(color)?$/i.test(env.TERM)) {\n\t\treturn 2;\n\t}\n\n\tif (/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(env.TERM)) {\n\t\treturn 1;\n\t}\n\n\tif ('COLORTERM' in env) {\n\t\treturn 1;\n\t}\n\n\treturn min;\n}\n\nexport function createSupportsColor(stream, options = {}) {\n\tconst level = _supportsColor(stream, {\n\t\tstreamIsTTY: stream && stream.isTTY,\n\t\t...options,\n\t});\n\n\treturn translateLevel(level);\n}\n\nconst supportsColor = {\n\tstdout: createSupportsColor({isTTY: tty.isatty(1)}),\n\tstderr: createSupportsColor({isTTY: tty.isatty(2)}),\n};\n\nexport default supportsColor;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,oEAAoE;AACpE,0EAA0E;AAC1E,SAAS,QAAQ,IAAI,EAAE,OAAO,WAAW,IAAI,GAAG,WAAW,IAAI,CAAC,IAAI,GAAG,uHAAA,CAAA,UAAO,CAAC,IAAI;IAClF,MAAM,SAAS,KAAK,UAAU,CAAC,OAAO,KAAM,KAAK,MAAM,KAAK,IAAI,MAAM;IACtE,MAAM,WAAW,KAAK,OAAO,CAAC,SAAS;IACvC,MAAM,qBAAqB,KAAK,OAAO,CAAC;IACxC,OAAO,aAAa,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,WAAW,kBAAkB;AACtF;AAEA,MAAM,EAAC,GAAG,EAAC,GAAG,uHAAA,CAAA,UAAO;AAErB,IAAI;AACJ,IACC,QAAQ,eACL,QAAQ,gBACR,QAAQ,kBACR,QAAQ,gBACV;IACD,iBAAiB;AAClB,OAAO,IACN,QAAQ,YACL,QAAQ,aACR,QAAQ,iBACR,QAAQ,iBACV;IACD,iBAAiB;AAClB;AAEA,SAAS;IACR,IAAI,iBAAiB,KAAK;QACzB,IAAI,IAAI,WAAW,KAAK,QAAQ;YAC/B,OAAO;QACR;QAEA,IAAI,IAAI,WAAW,KAAK,SAAS;YAChC,OAAO;QACR;QAEA,OAAO,IAAI,WAAW,CAAC,MAAM,KAAK,IAAI,IAAI,KAAK,GAAG,CAAC,OAAO,QAAQ,CAAC,IAAI,WAAW,EAAE,KAAK;IAC1F;AACD;AAEA,SAAS,eAAe,KAAK;IAC5B,IAAI,UAAU,GAAG;QAChB,OAAO;IACR;IAEA,OAAO;QACN;QACA,UAAU;QACV,QAAQ,SAAS;QACjB,QAAQ,SAAS;IAClB;AACD;AAEA,SAAS,eAAe,UAAU,EAAE,EAAC,WAAW,EAAE,aAAa,IAAI,EAAC,GAAG,CAAC,CAAC;IACxE,MAAM,mBAAmB;IACzB,IAAI,qBAAqB,WAAW;QACnC,iBAAiB;IAClB;IAEA,MAAM,aAAa,aAAa,iBAAiB;IAEjD,IAAI,eAAe,GAAG;QACrB,OAAO;IACR;IAEA,IAAI,YAAY;QACf,IAAI,QAAQ,gBACR,QAAQ,iBACR,QAAQ,oBAAoB;YAC/B,OAAO;QACR;QAEA,IAAI,QAAQ,cAAc;YACzB,OAAO;QACR;IACD;IAEA,oCAAoC;IACpC,4CAA4C;IAC5C,IAAI,cAAc,OAAO,gBAAgB,KAAK;QAC7C,OAAO;IACR;IAEA,IAAI,cAAc,CAAC,eAAe,eAAe,WAAW;QAC3D,OAAO;IACR;IAEA,MAAM,MAAM,cAAc;IAE1B,IAAI,IAAI,IAAI,KAAK,QAAQ;QACxB,OAAO;IACR;IAEA,IAAI,uHAAA,CAAA,UAAO,CAAC,QAAQ,KAAK,SAAS;QACjC,gFAAgF;QAChF,2EAA2E;QAC3E,MAAM,YAAY,6GAAA,CAAA,UAAE,CAAC,OAAO,GAAG,KAAK,CAAC;QACrC,IACC,OAAO,SAAS,CAAC,EAAE,KAAK,MACrB,OAAO,SAAS,CAAC,EAAE,KAAK,QAC1B;YACD,OAAO,OAAO,SAAS,CAAC,EAAE,KAAK,SAAS,IAAI;QAC7C;QAEA,OAAO;IACR;IAEA,IAAI,QAAQ,KAAK;QAChB,IAAI;YAAC;YAAkB;YAAiB;SAAW,CAAC,IAAI,CAAC,CAAA,MAAO,OAAO,MAAM;YAC5E,OAAO;QACR;QAEA,IAAI;YAAC;YAAU;YAAY;YAAa;YAAa;SAAQ,CAAC,IAAI,CAAC,CAAA,OAAQ,QAAQ,QAAQ,IAAI,OAAO,KAAK,YAAY;YACtH,OAAO;QACR;QAEA,OAAO;IACR;IAEA,IAAI,sBAAsB,KAAK;QAC9B,OAAO,gCAAgC,IAAI,CAAC,IAAI,gBAAgB,IAAI,IAAI;IACzE;IAEA,IAAI,IAAI,SAAS,KAAK,aAAa;QAClC,OAAO;IACR;IAEA,IAAI,IAAI,IAAI,KAAK,eAAe;QAC/B,OAAO;IACR;IAEA,IAAI,kBAAkB,KAAK;QAC1B,MAAM,UAAU,OAAO,QAAQ,CAAC,CAAC,IAAI,oBAAoB,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;QAEhF,OAAQ,IAAI,YAAY;YACvB,KAAK;gBAAa;oBACjB,OAAO,WAAW,IAAI,IAAI;gBAC3B;YAEA,KAAK;gBAAkB;oBACtB,OAAO;gBACR;QAED;IACD;IAEA,IAAI,iBAAiB,IAAI,CAAC,IAAI,IAAI,GAAG;QACpC,OAAO;IACR;IAEA,IAAI,8DAA8D,IAAI,CAAC,IAAI,IAAI,GAAG;QACjF,OAAO;IACR;IAEA,IAAI,eAAe,KAAK;QACvB,OAAO;IACR;IAEA,OAAO;AACR;AAEO,SAAS,oBAAoB,MAAM,EAAE,UAAU,CAAC,CAAC;IACvD,MAAM,QAAQ,eAAe,QAAQ;QACpC,aAAa,UAAU,OAAO,KAAK;QACnC,GAAG,OAAO;IACX;IAEA,OAAO,eAAe;AACvB;AAEA,MAAM,gBAAgB;IACrB,QAAQ,oBAAoB;QAAC,OAAO,+GAAA,CAAA,UAAG,CAAC,MAAM,CAAC;IAAE;IACjD,QAAQ,oBAAoB;QAAC,OAAO,+GAAA,CAAA,UAAG,CAAC,MAAM,CAAC;IAAE;AAClD;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3983, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/chalk/source/utilities.js"], "sourcesContent": ["// TODO: When targeting Node.js 16, use `String.prototype.replaceAll`.\nexport function stringReplaceAll(string, substring, replacer) {\n\tlet index = string.indexOf(substring);\n\tif (index === -1) {\n\t\treturn string;\n\t}\n\n\tconst substringLength = substring.length;\n\tlet endIndex = 0;\n\tlet returnValue = '';\n\tdo {\n\t\treturnValue += string.slice(endIndex, index) + substring + replacer;\n\t\tendIndex = index + substringLength;\n\t\tindex = string.indexOf(substring, endIndex);\n\t} while (index !== -1);\n\n\treturnValue += string.slice(endIndex);\n\treturn returnValue;\n}\n\nexport function stringEncaseCRLFWithFirstIndex(string, prefix, postfix, index) {\n\tlet endIndex = 0;\n\tlet returnValue = '';\n\tdo {\n\t\tconst gotCR = string[index - 1] === '\\r';\n\t\treturnValue += string.slice(endIndex, (gotCR ? index - 1 : index)) + prefix + (gotCR ? '\\r\\n' : '\\n') + postfix;\n\t\tendIndex = index + 1;\n\t\tindex = string.indexOf('\\n', endIndex);\n\t} while (index !== -1);\n\n\treturnValue += string.slice(endIndex);\n\treturn returnValue;\n}\n"], "names": [], "mappings": "AAAA,sEAAsE;;;;;AAC/D,SAAS,iBAAiB,MAAM,EAAE,SAAS,EAAE,QAAQ;IAC3D,IAAI,QAAQ,OAAO,OAAO,CAAC;IAC3B,IAAI,UAAU,CAAC,GAAG;QACjB,OAAO;IACR;IAEA,MAAM,kBAAkB,UAAU,MAAM;IACxC,IAAI,WAAW;IACf,IAAI,cAAc;IAClB,GAAG;QACF,eAAe,OAAO,KAAK,CAAC,UAAU,SAAS,YAAY;QAC3D,WAAW,QAAQ;QACnB,QAAQ,OAAO,OAAO,CAAC,WAAW;IACnC,QAAS,UAAU,CAAC,EAAG;IAEvB,eAAe,OAAO,KAAK,CAAC;IAC5B,OAAO;AACR;AAEO,SAAS,+BAA+B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;IAC5E,IAAI,WAAW;IACf,IAAI,cAAc;IAClB,GAAG;QACF,MAAM,QAAQ,MAAM,CAAC,QAAQ,EAAE,KAAK;QACpC,eAAe,OAAO,KAAK,CAAC,UAAW,QAAQ,QAAQ,IAAI,SAAU,SAAS,CAAC,QAAQ,SAAS,IAAI,IAAI;QACxG,WAAW,QAAQ;QACnB,QAAQ,OAAO,OAAO,CAAC,MAAM;IAC9B,QAAS,UAAU,CAAC,EAAG;IAEvB,eAAe,OAAO,KAAK,CAAC;IAC5B,OAAO;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4022, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/chalk/source/index.js"], "sourcesContent": ["import ansiStyles from '#ansi-styles';\nimport supportsColor from '#supports-color';\nimport { // eslint-disable-line import/order\n\tstringReplaceAll,\n\tstringEncaseCRLFWithFirstIndex,\n} from './utilities.js';\n\nconst {stdout: stdoutColor, stderr: stderrColor} = supportsColor;\n\nconst GENERATOR = Symbol('GENERATOR');\nconst STYLER = Symbol('STYLER');\nconst IS_EMPTY = Symbol('IS_EMPTY');\n\n// `supportsColor.level` → `ansiStyles.color[name]` mapping\nconst levelMapping = [\n\t'ansi',\n\t'ansi',\n\t'ansi256',\n\t'ansi16m',\n];\n\nconst styles = Object.create(null);\n\nconst applyOptions = (object, options = {}) => {\n\tif (options.level && !(Number.isInteger(options.level) && options.level >= 0 && options.level <= 3)) {\n\t\tthrow new Error('The `level` option should be an integer from 0 to 3');\n\t}\n\n\t// Detect level if not set manually\n\tconst colorLevel = stdoutColor ? stdoutColor.level : 0;\n\tobject.level = options.level === undefined ? colorLevel : options.level;\n};\n\nexport class Chalk {\n\tconstructor(options) {\n\t\t// eslint-disable-next-line no-constructor-return\n\t\treturn chalkFactory(options);\n\t}\n}\n\nconst chalkFactory = options => {\n\tconst chalk = (...strings) => strings.join(' ');\n\tapplyOptions(chalk, options);\n\n\tObject.setPrototypeOf(chalk, createChalk.prototype);\n\n\treturn chalk;\n};\n\nfunction createChalk(options) {\n\treturn chalkFactory(options);\n}\n\nObject.setPrototypeOf(createChalk.prototype, Function.prototype);\n\nfor (const [styleName, style] of Object.entries(ansiStyles)) {\n\tstyles[styleName] = {\n\t\tget() {\n\t\t\tconst builder = createBuilder(this, createStyler(style.open, style.close, this[STYLER]), this[IS_EMPTY]);\n\t\t\tObject.defineProperty(this, styleName, {value: builder});\n\t\t\treturn builder;\n\t\t},\n\t};\n}\n\nstyles.visible = {\n\tget() {\n\t\tconst builder = createBuilder(this, this[STYLER], true);\n\t\tObject.defineProperty(this, 'visible', {value: builder});\n\t\treturn builder;\n\t},\n};\n\nconst getModelAnsi = (model, level, type, ...arguments_) => {\n\tif (model === 'rgb') {\n\t\tif (level === 'ansi16m') {\n\t\t\treturn ansiStyles[type].ansi16m(...arguments_);\n\t\t}\n\n\t\tif (level === 'ansi256') {\n\t\t\treturn ansiStyles[type].ansi256(ansiStyles.rgbToAnsi256(...arguments_));\n\t\t}\n\n\t\treturn ansiStyles[type].ansi(ansiStyles.rgbToAnsi(...arguments_));\n\t}\n\n\tif (model === 'hex') {\n\t\treturn getModelAnsi('rgb', level, type, ...ansiStyles.hexToRgb(...arguments_));\n\t}\n\n\treturn ansiStyles[type][model](...arguments_);\n};\n\nconst usedModels = ['rgb', 'hex', 'ansi256'];\n\nfor (const model of usedModels) {\n\tstyles[model] = {\n\t\tget() {\n\t\t\tconst {level} = this;\n\t\t\treturn function (...arguments_) {\n\t\t\t\tconst styler = createStyler(getModelAnsi(model, levelMapping[level], 'color', ...arguments_), ansiStyles.color.close, this[STYLER]);\n\t\t\t\treturn createBuilder(this, styler, this[IS_EMPTY]);\n\t\t\t};\n\t\t},\n\t};\n\n\tconst bgModel = 'bg' + model[0].toUpperCase() + model.slice(1);\n\tstyles[bgModel] = {\n\t\tget() {\n\t\t\tconst {level} = this;\n\t\t\treturn function (...arguments_) {\n\t\t\t\tconst styler = createStyler(getModelAnsi(model, levelMapping[level], 'bgColor', ...arguments_), ansiStyles.bgColor.close, this[STYLER]);\n\t\t\t\treturn createBuilder(this, styler, this[IS_EMPTY]);\n\t\t\t};\n\t\t},\n\t};\n}\n\nconst proto = Object.defineProperties(() => {}, {\n\t...styles,\n\tlevel: {\n\t\tenumerable: true,\n\t\tget() {\n\t\t\treturn this[GENERATOR].level;\n\t\t},\n\t\tset(level) {\n\t\t\tthis[GENERATOR].level = level;\n\t\t},\n\t},\n});\n\nconst createStyler = (open, close, parent) => {\n\tlet openAll;\n\tlet closeAll;\n\tif (parent === undefined) {\n\t\topenAll = open;\n\t\tcloseAll = close;\n\t} else {\n\t\topenAll = parent.openAll + open;\n\t\tcloseAll = close + parent.closeAll;\n\t}\n\n\treturn {\n\t\topen,\n\t\tclose,\n\t\topenAll,\n\t\tcloseAll,\n\t\tparent,\n\t};\n};\n\nconst createBuilder = (self, _styler, _isEmpty) => {\n\t// Single argument is hot path, implicit coercion is faster than anything\n\t// eslint-disable-next-line no-implicit-coercion\n\tconst builder = (...arguments_) => applyStyle(builder, (arguments_.length === 1) ? ('' + arguments_[0]) : arguments_.join(' '));\n\n\t// We alter the prototype because we must return a function, but there is\n\t// no way to create a function with a different prototype\n\tObject.setPrototypeOf(builder, proto);\n\n\tbuilder[GENERATOR] = self;\n\tbuilder[STYLER] = _styler;\n\tbuilder[IS_EMPTY] = _isEmpty;\n\n\treturn builder;\n};\n\nconst applyStyle = (self, string) => {\n\tif (self.level <= 0 || !string) {\n\t\treturn self[IS_EMPTY] ? '' : string;\n\t}\n\n\tlet styler = self[STYLER];\n\n\tif (styler === undefined) {\n\t\treturn string;\n\t}\n\n\tconst {openAll, closeAll} = styler;\n\tif (string.includes('\\u001B')) {\n\t\twhile (styler !== undefined) {\n\t\t\t// Replace any instances already present with a re-opening code\n\t\t\t// otherwise only the part of the string until said closing code\n\t\t\t// will be colored, and the rest will simply be 'plain'.\n\t\t\tstring = stringReplaceAll(string, styler.close, styler.open);\n\n\t\t\tstyler = styler.parent;\n\t\t}\n\t}\n\n\t// We can move both next actions out of loop, because remaining actions in loop won't have\n\t// any/visible effect on parts we add here. Close the styling before a linebreak and reopen\n\t// after next line to fix a bleed issue on macOS: https://github.com/chalk/chalk/pull/92\n\tconst lfIndex = string.indexOf('\\n');\n\tif (lfIndex !== -1) {\n\t\tstring = stringEncaseCRLFWithFirstIndex(string, closeAll, openAll, lfIndex);\n\t}\n\n\treturn openAll + string + closeAll;\n};\n\nObject.defineProperties(createChalk.prototype, styles);\n\nconst chalk = createChalk();\nexport const chalkStderr = createChalk({level: stderrColor ? stderrColor.level : 0});\n\nexport {\n\tmodifierNames,\n\tforegroundColorNames,\n\tbackgroundColorNames,\n\tcolorNames,\n\n\t// TODO: Remove these aliases in the next major version\n\tmodifierNames as modifiers,\n\tforegroundColorNames as foregroundColors,\n\tbackgroundColorNames as backgroundColors,\n\tcolorNames as colors,\n} from './vendor/ansi-styles/index.js';\n\nexport {\n\tstdoutColor as supportsColor,\n\tstderrColor as supportsColorStderr,\n};\n\nexport default chalk;\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAKA,MAAM,EAAC,QAAQ,WAAW,EAAE,QAAQ,WAAW,EAAC,GAAG,uKAAA,CAAA,UAAa;AAEhE,MAAM,YAAY,OAAO;AACzB,MAAM,SAAS,OAAO;AACtB,MAAM,WAAW,OAAO;AAExB,2DAA2D;AAC3D,MAAM,eAAe;IACpB;IACA;IACA;IACA;CACA;AAED,MAAM,SAAS,OAAO,MAAM,CAAC;AAE7B,MAAM,eAAe,CAAC,QAAQ,UAAU,CAAC,CAAC;IACzC,IAAI,QAAQ,KAAK,IAAI,CAAC,CAAC,OAAO,SAAS,CAAC,QAAQ,KAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,KAAK,IAAI,CAAC,GAAG;QACpG,MAAM,IAAI,MAAM;IACjB;IAEA,mCAAmC;IACnC,MAAM,aAAa,cAAc,YAAY,KAAK,GAAG;IACrD,OAAO,KAAK,GAAG,QAAQ,KAAK,KAAK,YAAY,aAAa,QAAQ,KAAK;AACxE;AAEO,MAAM;IACZ,YAAY,OAAO,CAAE;QACpB,iDAAiD;QACjD,OAAO,aAAa;IACrB;AACD;AAEA,MAAM,eAAe,CAAA;IACpB,MAAM,QAAQ,CAAC,GAAG,UAAY,QAAQ,IAAI,CAAC;IAC3C,aAAa,OAAO;IAEpB,OAAO,cAAc,CAAC,OAAO,YAAY,SAAS;IAElD,OAAO;AACR;AAEA,SAAS,YAAY,OAAO;IAC3B,OAAO,aAAa;AACrB;AAEA,OAAO,cAAc,CAAC,YAAY,SAAS,EAAE,SAAS,SAAS;AAE/D,KAAK,MAAM,CAAC,WAAW,MAAM,IAAI,OAAO,OAAO,CAAC,oKAAA,CAAA,UAAU,EAAG;IAC5D,MAAM,CAAC,UAAU,GAAG;QACnB;YACC,MAAM,UAAU,cAAc,IAAI,EAAE,aAAa,MAAM,IAAI,EAAE,MAAM,KAAK,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS;YACvG,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW;gBAAC,OAAO;YAAO;YACtD,OAAO;QACR;IACD;AACD;AAEA,OAAO,OAAO,GAAG;IAChB;QACC,MAAM,UAAU,cAAc,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE;QAClD,OAAO,cAAc,CAAC,IAAI,EAAE,WAAW;YAAC,OAAO;QAAO;QACtD,OAAO;IACR;AACD;AAEA,MAAM,eAAe,CAAC,OAAO,OAAO,MAAM,GAAG;IAC5C,IAAI,UAAU,OAAO;QACpB,IAAI,UAAU,WAAW;YACxB,OAAO,oKAAA,CAAA,UAAU,CAAC,KAAK,CAAC,OAAO,IAAI;QACpC;QAEA,IAAI,UAAU,WAAW;YACxB,OAAO,oKAAA,CAAA,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,oKAAA,CAAA,UAAU,CAAC,YAAY,IAAI;QAC5D;QAEA,OAAO,oKAAA,CAAA,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,oKAAA,CAAA,UAAU,CAAC,SAAS,IAAI;IACtD;IAEA,IAAI,UAAU,OAAO;QACpB,OAAO,aAAa,OAAO,OAAO,SAAS,oKAAA,CAAA,UAAU,CAAC,QAAQ,IAAI;IACnE;IAEA,OAAO,oKAAA,CAAA,UAAU,CAAC,KAAK,CAAC,MAAM,IAAI;AACnC;AAEA,MAAM,aAAa;IAAC;IAAO;IAAO;CAAU;AAE5C,KAAK,MAAM,SAAS,WAAY;IAC/B,MAAM,CAAC,MAAM,GAAG;QACf;YACC,MAAM,EAAC,KAAK,EAAC,GAAG,IAAI;YACpB,OAAO,SAAU,GAAG,UAAU;gBAC7B,MAAM,SAAS,aAAa,aAAa,OAAO,YAAY,CAAC,MAAM,EAAE,YAAY,aAAa,oKAAA,CAAA,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO;gBAClI,OAAO,cAAc,IAAI,EAAE,QAAQ,IAAI,CAAC,SAAS;YAClD;QACD;IACD;IAEA,MAAM,UAAU,OAAO,KAAK,CAAC,EAAE,CAAC,WAAW,KAAK,MAAM,KAAK,CAAC;IAC5D,MAAM,CAAC,QAAQ,GAAG;QACjB;YACC,MAAM,EAAC,KAAK,EAAC,GAAG,IAAI;YACpB,OAAO,SAAU,GAAG,UAAU;gBAC7B,MAAM,SAAS,aAAa,aAAa,OAAO,YAAY,CAAC,MAAM,EAAE,cAAc,aAAa,oKAAA,CAAA,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO;gBACtI,OAAO,cAAc,IAAI,EAAE,QAAQ,IAAI,CAAC,SAAS;YAClD;QACD;IACD;AACD;AAEA,MAAM,QAAQ,OAAO,gBAAgB,CAAC,KAAO,GAAG;IAC/C,GAAG,MAAM;IACT,OAAO;QACN,YAAY;QACZ;YACC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK;QAC7B;QACA,KAAI,KAAK;YACR,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG;QACzB;IACD;AACD;AAEA,MAAM,eAAe,CAAC,MAAM,OAAO;IAClC,IAAI;IACJ,IAAI;IACJ,IAAI,WAAW,WAAW;QACzB,UAAU;QACV,WAAW;IACZ,OAAO;QACN,UAAU,OAAO,OAAO,GAAG;QAC3B,WAAW,QAAQ,OAAO,QAAQ;IACnC;IAEA,OAAO;QACN;QACA;QACA;QACA;QACA;IACD;AACD;AAEA,MAAM,gBAAgB,CAAC,MAAM,SAAS;IACrC,yEAAyE;IACzE,gDAAgD;IAChD,MAAM,UAAU,CAAC,GAAG,aAAe,WAAW,SAAS,AAAC,WAAW,MAAM,KAAK,IAAM,KAAK,UAAU,CAAC,EAAE,GAAI,WAAW,IAAI,CAAC;IAE1H,yEAAyE;IACzE,yDAAyD;IACzD,OAAO,cAAc,CAAC,SAAS;IAE/B,OAAO,CAAC,UAAU,GAAG;IACrB,OAAO,CAAC,OAAO,GAAG;IAClB,OAAO,CAAC,SAAS,GAAG;IAEpB,OAAO;AACR;AAEA,MAAM,aAAa,CAAC,MAAM;IACzB,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,QAAQ;QAC/B,OAAO,IAAI,CAAC,SAAS,GAAG,KAAK;IAC9B;IAEA,IAAI,SAAS,IAAI,CAAC,OAAO;IAEzB,IAAI,WAAW,WAAW;QACzB,OAAO;IACR;IAEA,MAAM,EAAC,OAAO,EAAE,QAAQ,EAAC,GAAG;IAC5B,IAAI,OAAO,QAAQ,CAAC,WAAW;QAC9B,MAAO,WAAW,UAAW;YAC5B,+DAA+D;YAC/D,gEAAgE;YAChE,wDAAwD;YACxD,SAAS,CAAA,GAAA,4IAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,OAAO,KAAK,EAAE,OAAO,IAAI;YAE3D,SAAS,OAAO,MAAM;QACvB;IACD;IAEA,0FAA0F;IAC1F,2FAA2F;IAC3F,wFAAwF;IACxF,MAAM,UAAU,OAAO,OAAO,CAAC;IAC/B,IAAI,YAAY,CAAC,GAAG;QACnB,SAAS,CAAA,GAAA,4IAAA,CAAA,iCAA8B,AAAD,EAAE,QAAQ,UAAU,SAAS;IACpE;IAEA,OAAO,UAAU,SAAS;AAC3B;AAEA,OAAO,gBAAgB,CAAC,YAAY,SAAS,EAAE;AAE/C,MAAM,QAAQ;AACP,MAAM,cAAc,YAAY;IAAC,OAAO,cAAc,YAAY,KAAK,GAAG;AAAC;;;uCAoBnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4215, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/utils.ts"], "sourcesContent": ["import type {\n  DefaultError,\n  Enabled,\n  FetchStatus,\n  MutationKey,\n  MutationStatus,\n  QueryFunction,\n  QueryKey,\n  QueryOptions,\n  StaleTime,\n  StaleTimeFunction,\n} from './types'\nimport type { Mutation } from './mutation'\nimport type { FetchOptions, Query } from './query'\n\n// TYPES\n\nexport interface QueryFilters<TQueryKey extends QueryKey = QueryKey> {\n  /**\n   * Filter to active queries, inactive queries or all queries\n   */\n  type?: QueryTypeFilter\n  /**\n   * Match query key exactly\n   */\n  exact?: boolean\n  /**\n   * Include queries matching this predicate function\n   */\n  predicate?: (query: Query) => boolean\n  /**\n   * Include queries matching this query key\n   */\n  queryKey?: TQueryKey\n  /**\n   * Include or exclude stale queries\n   */\n  stale?: boolean\n  /**\n   * Include queries matching their fetchStatus\n   */\n  fetchStatus?: FetchStatus\n}\n\nexport interface MutationFilters<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> {\n  /**\n   * Match mutation key exactly\n   */\n  exact?: boolean\n  /**\n   * Include mutations matching this predicate function\n   */\n  predicate?: (\n    mutation: Mutation<TData, TError, TVariables, TContext>,\n  ) => boolean\n  /**\n   * Include mutations matching this mutation key\n   */\n  mutationKey?: MutationKey\n  /**\n   * Filter by mutation status\n   */\n  status?: MutationStatus\n}\n\nexport type Updater<TInput, TOutput> = TOutput | ((input: TInput) => TOutput)\n\nexport type QueryTypeFilter = 'all' | 'active' | 'inactive'\n\n// UTILS\n\nexport const isServer = typeof window === 'undefined' || 'Deno' in globalThis\n\nexport function noop(): void\nexport function noop(): undefined\nexport function noop() {}\n\nexport function functionalUpdate<TInput, TOutput>(\n  updater: Updater<TInput, TOutput>,\n  input: TInput,\n): TOutput {\n  return typeof updater === 'function'\n    ? (updater as (_: TInput) => TOutput)(input)\n    : updater\n}\n\nexport function isValidTimeout(value: unknown): value is number {\n  return typeof value === 'number' && value >= 0 && value !== Infinity\n}\n\nexport function timeUntilStale(updatedAt: number, staleTime?: number): number {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0)\n}\n\nexport function resolveStaleTime<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  staleTime:\n    | undefined\n    | StaleTimeFunction<TQueryFnData, TError, TData, TQueryKey>,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n): StaleTime | undefined {\n  return typeof staleTime === 'function' ? staleTime(query) : staleTime\n}\n\nexport function resolveEnabled<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  enabled: undefined | Enabled<TQueryFnData, TError, TData, TQueryKey>,\n  query: Query<TQueryFnData, TError, TData, TQueryKey>,\n): boolean | undefined {\n  return typeof enabled === 'function' ? enabled(query) : enabled\n}\n\nexport function matchQuery(\n  filters: QueryFilters,\n  query: Query<any, any, any, any>,\n): boolean {\n  const {\n    type = 'all',\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale,\n  } = filters\n\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false\n    }\n  }\n\n  if (type !== 'all') {\n    const isActive = query.isActive()\n    if (type === 'active' && !isActive) {\n      return false\n    }\n    if (type === 'inactive' && isActive) {\n      return false\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false\n  }\n\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false\n  }\n\n  if (predicate && !predicate(query)) {\n    return false\n  }\n\n  return true\n}\n\nexport function matchMutation(\n  filters: MutationFilters,\n  mutation: Mutation<any, any>,\n): boolean {\n  const { exact, status, predicate, mutationKey } = filters\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false\n    }\n  }\n\n  if (status && mutation.state.status !== status) {\n    return false\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hashQueryKeyByOptions<TQueryKey extends QueryKey = QueryKey>(\n  queryKey: TQueryKey,\n  options?: Pick<QueryOptions<any, any, any, any>, 'queryKeyHashFn'>,\n): string {\n  const hashFn = options?.queryKeyHashFn || hashKey\n  return hashFn(queryKey)\n}\n\n/**\n * Default query & mutation keys hash function.\n * Hashes the value into a stable hash.\n */\nexport function hashKey(queryKey: QueryKey | MutationKey): string {\n  return JSON.stringify(queryKey, (_, val) =>\n    isPlainObject(val)\n      ? Object.keys(val)\n          .sort()\n          .reduce((result, key) => {\n            result[key] = val[key]\n            return result\n          }, {} as any)\n      : val,\n  )\n}\n\n/**\n * Checks if key `b` partially matches with key `a`.\n */\nexport function partialMatchKey(a: QueryKey, b: QueryKey): boolean\nexport function partialMatchKey(a: any, b: any): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return Object.keys(b).every((key) => partialMatchKey(a[key], b[key]))\n  }\n\n  return false\n}\n\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\nexport function replaceEqualDeep<T>(a: unknown, b: T): T\nexport function replaceEqualDeep(a: any, b: any): any {\n  if (a === b) {\n    return a\n  }\n\n  const array = isPlainArray(a) && isPlainArray(b)\n\n  if (array || (isPlainObject(a) && isPlainObject(b))) {\n    const aItems = array ? a : Object.keys(a)\n    const aSize = aItems.length\n    const bItems = array ? b : Object.keys(b)\n    const bSize = bItems.length\n    const copy: any = array ? [] : {}\n    const aItemsSet = new Set(aItems)\n\n    let equalItems = 0\n\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i]\n      if (\n        ((!array && aItemsSet.has(key)) || array) &&\n        a[key] === undefined &&\n        b[key] === undefined\n      ) {\n        copy[key] = undefined\n        equalItems++\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key])\n        if (copy[key] === a[key] && a[key] !== undefined) {\n          equalItems++\n        }\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy\n  }\n\n  return b\n}\n\n/**\n * Shallow compare objects.\n */\nexport function shallowEqualObjects<T extends Record<string, any>>(\n  a: T,\n  b: T | undefined,\n): boolean {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false\n  }\n\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nexport function isPlainArray(value: unknown) {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\n// eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\nexport function isPlainObject(o: any): o is Object {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has no constructor\n  const ctor = o.constructor\n  if (ctor === undefined) {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Handles Objects created by Object.create(<arbitrary prototype>)\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function sleep(timeout: number): Promise<void> {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout)\n  })\n}\n\nexport function replaceData<\n  TData,\n  TOptions extends QueryOptions<any, any, any, any>,\n>(prevData: TData | undefined, data: TData, options: TOptions): TData {\n  if (typeof options.structuralSharing === 'function') {\n    return options.structuralSharing(prevData, data) as TData\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== 'production') {\n      try {\n        return replaceEqualDeep(prevData, data)\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`,\n        )\n\n        // Prevent the replaceEqualDeep from being called again down below.\n        throw error\n      }\n    }\n    // Structurally share data between prev and new data if needed\n    return replaceEqualDeep(prevData, data)\n  }\n  return data\n}\n\nexport function keepPreviousData<T>(\n  previousData: T | undefined,\n): T | undefined {\n  return previousData\n}\n\nexport function addToEnd<T>(items: Array<T>, item: T, max = 0): Array<T> {\n  const newItems = [...items, item]\n  return max && newItems.length > max ? newItems.slice(1) : newItems\n}\n\nexport function addToStart<T>(items: Array<T>, item: T, max = 0): Array<T> {\n  const newItems = [item, ...items]\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems\n}\n\nexport const skipToken = Symbol()\nexport type SkipToken = typeof skipToken\n\nexport function ensureQueryFn<\n  TQueryFnData = unknown,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: {\n    queryFn?: QueryFunction<TQueryFnData, TQueryKey> | SkipToken\n    queryHash?: string\n  },\n  fetchOptions?: FetchOptions<TQueryFnData>,\n): QueryFunction<TQueryFnData, TQueryKey> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`,\n      )\n    }\n  }\n\n  // if we attempt to retry a fetch that was triggered from an initialPromise\n  // when we don't have a queryFn yet, we can't retry, so we just return the already rejected initialPromise\n  // if an observer has already mounted, we will be able to retry with that queryFn\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise!\n  }\n\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () =>\n      Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`))\n  }\n\n  return options.queryFn\n}\n\nexport function shouldThrowError<T extends (...args: Array<any>) => boolean>(\n  throwOnError: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow throwOnError function to override throwing behavior on a per-error basis\n  if (typeof throwOnError === 'function') {\n    return throwOnError(...params)\n  }\n\n  return !!throwOnError\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA4EO,IAAM,WAAW,OAAO,WAAW,eAAe,UAAU;AAI5D,SAAS,OAAO,CAAC;AAEjB,SAAS,iBACd,OAAA,EACA,KAAA,EACS;IACT,OAAO,OAAO,YAAY,aACrB,QAAmC,KAAK,IACzC;AACN;AAEO,SAAS,eAAe,KAAA,EAAiC;IAC9D,OAAO,OAAO,UAAU,YAAY,SAAS,KAAK,UAAU;AAC9D;AAEO,SAAS,eAAe,SAAA,EAAmB,SAAA,EAA4B;IAC5E,OAAO,KAAK,GAAA,CAAI,YAAA,CAAa,aAAa,CAAA,IAAK,KAAK,GAAA,CAAI,GAAG,CAAC;AAC9D;AAEO,SAAS,iBAMd,SAAA,EAGA,KAAA,EACuB;IACvB,OAAO,OAAO,cAAc,aAAa,UAAU,KAAK,IAAI;AAC9D;AAEO,SAAS,eAMd,OAAA,EACA,KAAA,EACqB;IACrB,OAAO,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AAC1D;AAEO,SAAS,WACd,OAAA,EACA,KAAA,EACS;IACT,MAAM,EACJ,OAAO,KAAA,EACP,KAAA,EACA,WAAA,EACA,SAAA,EACA,QAAA,EACA,KAAA,EACF,GAAI;IAEJ,IAAI,UAAU;QACZ,IAAI,OAAO;YACT,IAAI,MAAM,SAAA,KAAc,sBAAsB,UAAU,MAAM,OAAO,GAAG;gBACtE,OAAO;YACT;QACF,OAAA,IAAW,CAAC,gBAAgB,MAAM,QAAA,EAAU,QAAQ,GAAG;YACrD,OAAO;QACT;IACF;IAEA,IAAI,SAAS,OAAO;QAClB,MAAM,WAAW,MAAM,QAAA,CAAS;QAChC,IAAI,SAAS,YAAY,CAAC,UAAU;YAClC,OAAO;QACT;QACA,IAAI,SAAS,cAAc,UAAU;YACnC,OAAO;QACT;IACF;IAEA,IAAI,OAAO,UAAU,aAAa,MAAM,OAAA,CAAQ,MAAM,OAAO;QAC3D,OAAO;IACT;IAEA,IAAI,eAAe,gBAAgB,MAAM,KAAA,CAAM,WAAA,EAAa;QAC1D,OAAO;IACT;IAEA,IAAI,aAAa,CAAC,UAAU,KAAK,GAAG;QAClC,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAAS,cACd,OAAA,EACA,QAAA,EACS;IACT,MAAM,EAAE,KAAA,EAAO,MAAA,EAAQ,SAAA,EAAW,WAAA,CAAY,CAAA,GAAI;IAClD,IAAI,aAAa;QACf,IAAI,CAAC,SAAS,OAAA,CAAQ,WAAA,EAAa;YACjC,OAAO;QACT;QACA,IAAI,OAAO;YACT,IAAI,QAAQ,SAAS,OAAA,CAAQ,WAAW,MAAM,QAAQ,WAAW,GAAG;gBAClE,OAAO;YACT;QACF,OAAA,IAAW,CAAC,gBAAgB,SAAS,OAAA,CAAQ,WAAA,EAAa,WAAW,GAAG;YACtE,OAAO;QACT;IACF;IAEA,IAAI,UAAU,SAAS,KAAA,CAAM,MAAA,KAAW,QAAQ;QAC9C,OAAO;IACT;IAEA,IAAI,aAAa,CAAC,UAAU,QAAQ,GAAG;QACrC,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAAS,sBACd,QAAA,EACA,OAAA,EACQ;IACR,MAAM,SAAS,SAAS,kBAAkB;IAC1C,OAAO,OAAO,QAAQ;AACxB;AAMO,SAAS,QAAQ,QAAA,EAA0C;IAChE,OAAO,KAAK,SAAA,CAAU,UAAU,CAAC,GAAG,MAClC,cAAc,GAAG,IACb,OAAO,IAAA,CAAK,GAAG,EACZ,IAAA,CAAK,EACL,MAAA,CAAO,CAAC,QAAQ,QAAQ;YACvB,MAAA,CAAO,GAAG,CAAA,GAAI,GAAA,CAAI,GAAG,CAAA;YACrB,OAAO;QACT,GAAG,CAAC,CAAQ,IACd;AAER;AAMO,SAAS,gBAAgB,CAAA,EAAQ,CAAA,EAAiB;IACvD,IAAI,MAAM,GAAG;QACX,OAAO;IACT;IAEA,IAAI,OAAO,MAAM,OAAO,GAAG;QACzB,OAAO;IACT;IAEA,IAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;QAC5D,OAAO,OAAO,IAAA,CAAK,CAAC,EAAE,KAAA,CAAM,CAAC,MAAQ,gBAAgB,CAAA,CAAE,GAAG,CAAA,EAAG,CAAA,CAAE,GAAG,CAAC,CAAC;IACtE;IAEA,OAAO;AACT;AAQO,SAAS,iBAAiB,CAAA,EAAQ,CAAA,EAAa;IACpD,IAAI,MAAM,GAAG;QACX,OAAO;IACT;IAEA,MAAM,QAAQ,aAAa,CAAC,KAAK,aAAa,CAAC;IAE/C,IAAI,SAAU,cAAc,CAAC,KAAK,cAAc,CAAC,GAAI;QACnD,MAAM,SAAS,QAAQ,IAAI,OAAO,IAAA,CAAK,CAAC;QACxC,MAAM,QAAQ,OAAO,MAAA;QACrB,MAAM,SAAS,QAAQ,IAAI,OAAO,IAAA,CAAK,CAAC;QACxC,MAAM,QAAQ,OAAO,MAAA;QACrB,MAAM,OAAY,QAAQ,CAAC,CAAA,GAAI,CAAC;QAChC,MAAM,YAAY,IAAI,IAAI,MAAM;QAEhC,IAAI,aAAa;QAEjB,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,MAAM,QAAQ,IAAI,MAAA,CAAO,CAAC,CAAA;YAChC,IAAA,CACI,CAAC,SAAS,UAAU,GAAA,CAAI,GAAG,KAAM,KAAA,KACnC,CAAA,CAAE,GAAG,CAAA,KAAM,KAAA,KACX,CAAA,CAAE,GAAG,CAAA,KAAM,KAAA,GACX;gBACA,IAAA,CAAK,GAAG,CAAA,GAAI,KAAA;gBACZ;YACF,OAAO;gBACL,IAAA,CAAK,GAAG,CAAA,GAAI,iBAAiB,CAAA,CAAE,GAAG,CAAA,EAAG,CAAA,CAAE,GAAG,CAAC;gBAC3C,IAAI,IAAA,CAAK,GAAG,CAAA,KAAM,CAAA,CAAE,GAAG,CAAA,IAAK,CAAA,CAAE,GAAG,CAAA,KAAM,KAAA,GAAW;oBAChD;gBACF;YACF;QACF;QAEA,OAAO,UAAU,SAAS,eAAe,QAAQ,IAAI;IACvD;IAEA,OAAO;AACT;AAKO,SAAS,oBACd,CAAA,EACA,CAAA,EACS;IACT,IAAI,CAAC,KAAK,OAAO,IAAA,CAAK,CAAC,EAAE,MAAA,KAAW,OAAO,IAAA,CAAK,CAAC,EAAE,MAAA,EAAQ;QACzD,OAAO;IACT;IAEA,IAAA,MAAW,OAAO,EAAG;QACnB,IAAI,CAAA,CAAE,GAAG,CAAA,KAAM,CAAA,CAAE,GAAG,CAAA,EAAG;YACrB,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEO,SAAS,aAAa,KAAA,EAAgB;IAC3C,OAAO,MAAM,OAAA,CAAQ,KAAK,KAAK,MAAM,MAAA,KAAW,OAAO,IAAA,CAAK,KAAK,EAAE,MAAA;AACrE;AAIO,SAAS,cAAc,CAAA,EAAqB;IACjD,IAAI,CAAC,mBAAmB,CAAC,GAAG;QAC1B,OAAO;IACT;IAGA,MAAM,OAAO,EAAE,WAAA;IACf,IAAI,SAAS,KAAA,GAAW;QACtB,OAAO;IACT;IAGA,MAAM,OAAO,KAAK,SAAA;IAClB,IAAI,CAAC,mBAAmB,IAAI,GAAG;QAC7B,OAAO;IACT;IAGA,IAAI,CAAC,KAAK,cAAA,CAAe,eAAe,GAAG;QACzC,OAAO;IACT;IAGA,IAAI,OAAO,cAAA,CAAe,CAAC,MAAM,OAAO,SAAA,EAAW;QACjD,OAAO;IACT;IAGA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAiB;IAC3C,OAAO,OAAO,SAAA,CAAU,QAAA,CAAS,IAAA,CAAK,CAAC,MAAM;AAC/C;AAEO,SAAS,MAAM,OAAA,EAAgC;IACpD,OAAO,IAAI,QAAQ,CAAC,YAAY;QAC9B,WAAW,SAAS,OAAO;IAC7B,CAAC;AACH;AAEO,SAAS,YAGd,QAAA,EAA6B,IAAA,EAAa,OAAA,EAA0B;IACpE,IAAI,OAAO,QAAQ,iBAAA,KAAsB,YAAY;QACnD,OAAO,QAAQ,iBAAA,CAAkB,UAAU,IAAI;IACjD,OAAA,IAAW,QAAQ,iBAAA,KAAsB,OAAO;QAC9C,IAAI,QAAQ,IAAI,aAAa,WAAc;YACzC,IAAI;gBACF,OAAO,iBAAiB,UAAU,IAAI;YACxC,EAAA,OAAS,OAAO;gBACd,QAAQ,KAAA,CACN,CAAA,uJAAA,EAA0J,QAAQ,SAAS,CAAA,GAAA,EAAM,KAAK,EAAA;gBAIxL,MAAM;YACR;QACF;QAEA,OAAO,iBAAiB,UAAU,IAAI;IACxC;IACA,OAAO;AACT;AAEO,SAAS,iBACd,YAAA,EACe;IACf,OAAO;AACT;AAEO,SAAS,SAAY,KAAA,EAAiB,IAAA,EAAS,MAAM,CAAA,EAAa;IACvE,MAAM,WAAW,CAAC;WAAG;QAAO,IAAI;KAAA;IAChC,OAAO,OAAO,SAAS,MAAA,GAAS,MAAM,SAAS,KAAA,CAAM,CAAC,IAAI;AAC5D;AAEO,SAAS,WAAc,KAAA,EAAiB,IAAA,EAAS,MAAM,CAAA,EAAa;IACzE,MAAM,WAAW;QAAC,MAAM;WAAG,KAAK;KAAA;IAChC,OAAO,OAAO,SAAS,MAAA,GAAS,MAAM,SAAS,KAAA,CAAM,GAAG,CAAA,CAAE,IAAI;AAChE;AAEO,IAAM,YAAY,OAAO;AAGzB,SAAS,cAId,OAAA,EAIA,YAAA,EACwC;IACxC,IAAI,QAAQ,IAAI,aAAa,WAAc;QACzC,IAAI,QAAQ,OAAA,KAAY,WAAW;YACjC,QAAQ,KAAA,CACN,CAAA,sGAAA,EAAyG,QAAQ,SAAS,CAAA,CAAA,CAAA;QAE9H;IACF;IAKA,IAAI,CAAC,QAAQ,OAAA,IAAW,cAAc,gBAAgB;QACpD,OAAO,IAAM,aAAa,cAAA;IAC5B;IAEA,IAAI,CAAC,QAAQ,OAAA,IAAW,QAAQ,OAAA,KAAY,WAAW;QACrD,OAAO,IACL,QAAQ,MAAA,CAAO,IAAI,MAAM,CAAA,kBAAA,EAAqB,QAAQ,SAAS,CAAA,CAAA,CAAG,CAAC;IACvE;IAEA,OAAO,QAAQ,OAAA;AACjB;AAEO,SAAS,iBACd,YAAA,EACA,MAAA,EACS;IAET,IAAI,OAAO,iBAAiB,YAAY;QACtC,OAAO,aAAa,GAAG,MAAM;IAC/B;IAEA,OAAO,CAAC,CAAC;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4467, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/notifyManager.ts"], "sourcesContent": ["// TYPES\n\ntype NotifyCallback = () => void\n\ntype NotifyFunction = (callback: () => void) => void\n\ntype BatchNotifyFunction = (callback: () => void) => void\n\ntype BatchCallsCallback<T extends Array<unknown>> = (...args: T) => void\n\ntype ScheduleFunction = (callback: () => void) => void\n\nexport const defaultScheduler: ScheduleFunction = (cb) => setTimeout(cb, 0)\n\nexport function createNotifyManager() {\n  let queue: Array<NotifyCallback> = []\n  let transactions = 0\n  let notifyFn: NotifyFunction = (callback) => {\n    callback()\n  }\n  let batchNotifyFn: BatchNotifyFunction = (callback: () => void) => {\n    callback()\n  }\n  let scheduleFn = defaultScheduler\n\n  const schedule = (callback: NotifyCallback): void => {\n    if (transactions) {\n      queue.push(callback)\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback)\n      })\n    }\n  }\n  const flush = (): void => {\n    const originalQueue = queue\n    queue = []\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback)\n          })\n        })\n      })\n    }\n  }\n\n  return {\n    batch: <T>(callback: () => T): T => {\n      let result\n      transactions++\n      try {\n        result = callback()\n      } finally {\n        transactions--\n        if (!transactions) {\n          flush()\n        }\n      }\n      return result\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: <T extends Array<unknown>>(\n      callback: BatchCallsCallback<T>,\n    ): BatchCallsCallback<T> => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args)\n        })\n      }\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn: NotifyFunction) => {\n      notifyFn = fn\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn: BatchNotifyFunction) => {\n      batchNotifyFn = fn\n    },\n    setScheduler: (fn: ScheduleFunction) => {\n      scheduleFn = fn\n    },\n  } as const\n}\n\n// SINGLETON\nexport const notifyManager = createNotifyManager()\n"], "names": [], "mappings": ";;;;;;AAYO,IAAM,mBAAqC,CAAC,KAAO,WAAW,IAAI,CAAC;AAEnE,SAAS,sBAAsB;IACpC,IAAI,QAA+B,CAAC,CAAA;IACpC,IAAI,eAAe;IACnB,IAAI,WAA2B,CAAC,aAAa;QAC3C,SAAS;IACX;IACA,IAAI,gBAAqC,CAAC,aAAyB;QACjE,SAAS;IACX;IACA,IAAI,aAAa;IAEjB,MAAM,WAAW,CAAC,aAAmC;QACnD,IAAI,cAAc;YAChB,MAAM,IAAA,CAAK,QAAQ;QACrB,OAAO;YACL,WAAW,MAAM;gBACf,SAAS,QAAQ;YACnB,CAAC;QACH;IACF;IACA,MAAM,QAAQ,MAAY;QACxB,MAAM,gBAAgB;QACtB,QAAQ,CAAC,CAAA;QACT,IAAI,cAAc,MAAA,EAAQ;YACxB,WAAW,MAAM;gBACf,cAAc,MAAM;oBAClB,cAAc,OAAA,CAAQ,CAAC,aAAa;wBAClC,SAAS,QAAQ;oBACnB,CAAC;gBACH,CAAC;YACH,CAAC;QACH;IACF;IAEA,OAAO;QACL,OAAO,CAAI,aAAyB;YAClC,IAAI;YACJ;YACA,IAAI;gBACF,SAAS,SAAS;YACpB,SAAE;gBACA;gBACA,IAAI,CAAC,cAAc;oBACjB,MAAM;gBACR;YACF;YACA,OAAO;QACT;QAAA;;KAAA,GAIA,YAAY,CACV,aAC0B;YAC1B,OAAO,CAAA,GAAI,SAAS;gBAClB,SAAS,MAAM;oBACb,SAAS,GAAG,IAAI;gBAClB,CAAC;YACH;QACF;QACA;QAAA;;;KAAA,GAKA,mBAAmB,CAAC,OAAuB;YACzC,WAAW;QACb;QAAA;;;KAAA,GAKA,wBAAwB,CAAC,OAA4B;YACnD,gBAAgB;QAClB;QACA,cAAc,CAAC,OAAyB;YACtC,aAAa;QACf;IACF;AACF;AAGO,IAAM,gBAAgB,oBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4556, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/subscribable.ts"], "sourcesContent": ["export class Subscribable<TListener extends Function> {\n  protected listeners = new Set<TListener>()\n\n  constructor() {\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  subscribe(listener: TListener): () => void {\n    this.listeners.add(listener)\n\n    this.onSubscribe()\n\n    return () => {\n      this.listeners.delete(listener)\n      this.onUnsubscribe()\n    }\n  }\n\n  hasListeners(): boolean {\n    return this.listeners.size > 0\n  }\n\n  protected onSubscribe(): void {\n    // Do nothing\n  }\n\n  protected onUnsubscribe(): void {\n    // Do nothing\n  }\n}\n"], "names": [], "mappings": ";;;;AAAO,IAAM,eAAN,MAA+C;IAGpD,aAAc;QAFd,IAAA,CAAU,SAAA,GAAY,aAAA,GAAA,IAAI,IAAe;QAGvC,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAI;IAC3C;IAEA,UAAU,QAAA,EAAiC;QACzC,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,QAAQ;QAE3B,IAAA,CAAK,WAAA,CAAY;QAEjB,OAAO,MAAM;YACX,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,QAAQ;YAC9B,IAAA,CAAK,aAAA,CAAc;QACrB;IACF;IAEA,eAAwB;QACtB,OAAO,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO;IAC/B;IAEU,cAAoB,CAE9B;IAEU,gBAAsB,CAEhC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4587, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/focusManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype Listener = (focused: boolean) => void\n\ntype SetupFn = (\n  setFocused: (focused?: boolean) => void,\n) => (() => void) | undefined\n\nexport class FocusManager extends Subscribable<Listener> {\n  #focused?: boolean\n  #cleanup?: () => void\n\n  #setup: SetupFn\n\n  constructor() {\n    super()\n    this.#setup = (onFocus) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus()\n        // Listen to visibilitychange\n        window.addEventListener('visibilitychange', listener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener)\n        }\n      }\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.()\n      this.#cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.#setup = setup\n    this.#cleanup?.()\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === 'boolean') {\n        this.setFocused(focused)\n      } else {\n        this.onFocus()\n      }\n    })\n  }\n\n  setFocused(focused?: boolean): void {\n    const changed = this.#focused !== focused\n    if (changed) {\n      this.#focused = focused\n      this.onFocus()\n    }\n  }\n\n  onFocus(): void {\n    const isFocused = this.isFocused()\n    this.listeners.forEach((listener) => {\n      listener(isFocused)\n    })\n  }\n\n  isFocused(): boolean {\n    if (typeof this.#focused === 'boolean') {\n      return this.#focused\n    }\n\n    // document global can be unavailable in react native\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    return globalThis.document?.visibilityState !== 'hidden'\n  }\n}\n\nexport const focusManager = new FocusManager()\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,gBAAgB;;;AAQlB,IAAM,eAAN,6LAA2B,eAAA,CAAuB;KACvD,OAAA,CAAA;KACA,OAAA,CAAA;KAEA,KAAA,CAAA;IAEA,aAAc;QACZ,KAAA,CAAM;QACN,IAAA,EAAK,KAAA,GAAS,CAAC,YAAY;YAGzB,IAAI,yKAAC,WAAA,IAAY,OAAO,gBAAA,EAAkB;gBACxC,MAAM,WAAW,IAAM,QAAQ;gBAE/B,OAAO,gBAAA,CAAiB,oBAAoB,UAAU,KAAK;gBAE3D,OAAO,MAAM;oBAEX,OAAO,mBAAA,CAAoB,oBAAoB,QAAQ;gBACzD;YACF;YACA;QACF;IACF;IAEU,cAAoB;QAC5B,IAAI,CAAC,IAAA,EAAK,OAAA,EAAU;YAClB,IAAA,CAAK,gBAAA,CAAiB,IAAA,EAAK,KAAM;QACnC;IACF;IAEU,gBAAgB;QACxB,IAAI,CAAC,IAAA,CAAK,YAAA,CAAa,GAAG;YACxB,IAAA,EAAK,OAAA,GAAW;YAChB,IAAA,EAAK,OAAA,GAAW,KAAA;QAClB;IACF;IAEA,iBAAiB,KAAA,EAAsB;QACrC,IAAA,EAAK,KAAA,GAAS;QACd,IAAA,EAAK,OAAA,GAAW;QAChB,IAAA,EAAK,OAAA,GAAW,MAAM,CAAC,YAAY;YACjC,IAAI,OAAO,YAAY,WAAW;gBAChC,IAAA,CAAK,UAAA,CAAW,OAAO;YACzB,OAAO;gBACL,IAAA,CAAK,OAAA,CAAQ;YACf;QACF,CAAC;IACH;IAEA,WAAW,OAAA,EAAyB;QAClC,MAAM,UAAU,IAAA,EAAK,OAAA,KAAa;QAClC,IAAI,SAAS;YACX,IAAA,EAAK,OAAA,GAAW;YAChB,IAAA,CAAK,OAAA,CAAQ;QACf;IACF;IAEA,UAAgB;QACd,MAAM,YAAY,IAAA,CAAK,SAAA,CAAU;QACjC,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;YACnC,SAAS,SAAS;QACpB,CAAC;IACH;IAEA,YAAqB;QACnB,IAAI,OAAO,IAAA,EAAK,OAAA,KAAa,WAAW;YACtC,OAAO,IAAA,EAAK,OAAA;QACd;QAIA,OAAO,WAAW,QAAA,EAAU,oBAAoB;IAClD;AACF;AAEO,IAAM,eAAe,IAAI,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4664, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/onlineManager.ts"], "sourcesContent": ["import { Subscribable } from './subscribable'\nimport { isServer } from './utils'\n\ntype Listener = (online: boolean) => void\ntype SetupFn = (setOnline: Listener) => (() => void) | undefined\n\nexport class OnlineManager extends Subscribable<Listener> {\n  #online = true\n  #cleanup?: () => void\n\n  #setup: SetupFn\n\n  constructor() {\n    super()\n    this.#setup = (onOnline) => {\n      // addEventListener does not exist in React Native, but window does\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true)\n        const offlineListener = () => onOnline(false)\n        // Listen to online\n        window.addEventListener('online', onlineListener, false)\n        window.addEventListener('offline', offlineListener, false)\n\n        return () => {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', onlineListener)\n          window.removeEventListener('offline', offlineListener)\n        }\n      }\n\n      return\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup)\n    }\n  }\n\n  protected onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.()\n      this.#cleanup = undefined\n    }\n  }\n\n  setEventListener(setup: SetupFn): void {\n    this.#setup = setup\n    this.#cleanup?.()\n    this.#cleanup = setup(this.setOnline.bind(this))\n  }\n\n  setOnline(online: boolean): void {\n    const changed = this.#online !== online\n\n    if (changed) {\n      this.#online = online\n      this.listeners.forEach((listener) => {\n        listener(online)\n      })\n    }\n  }\n\n  isOnline(): boolean {\n    return this.#online\n  }\n}\n\nexport const onlineManager = new OnlineManager()\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,gBAAgB;;;AAKlB,IAAM,gBAAN,6LAA4B,eAAA,CAAuB;KACxD,MAAA,GAAU,KAAA;KACV,OAAA,CAAA;KAEA,KAAA,CAAA;IAEA,aAAc;QACZ,KAAA,CAAM;QACN,IAAA,EAAK,KAAA,GAAS,CAAC,aAAa;YAG1B,IAAI,yKAAC,WAAA,IAAY,OAAO,gBAAA,EAAkB;gBACxC,MAAM,iBAAiB,IAAM,SAAS,IAAI;gBAC1C,MAAM,kBAAkB,IAAM,SAAS,KAAK;gBAE5C,OAAO,gBAAA,CAAiB,UAAU,gBAAgB,KAAK;gBACvD,OAAO,gBAAA,CAAiB,WAAW,iBAAiB,KAAK;gBAEzD,OAAO,MAAM;oBAEX,OAAO,mBAAA,CAAoB,UAAU,cAAc;oBACnD,OAAO,mBAAA,CAAoB,WAAW,eAAe;gBACvD;YACF;YAEA;QACF;IACF;IAEU,cAAoB;QAC5B,IAAI,CAAC,IAAA,EAAK,OAAA,EAAU;YAClB,IAAA,CAAK,gBAAA,CAAiB,IAAA,EAAK,KAAM;QACnC;IACF;IAEU,gBAAgB;QACxB,IAAI,CAAC,IAAA,CAAK,YAAA,CAAa,GAAG;YACxB,IAAA,EAAK,OAAA,GAAW;YAChB,IAAA,EAAK,OAAA,GAAW,KAAA;QAClB;IACF;IAEA,iBAAiB,KAAA,EAAsB;QACrC,IAAA,EAAK,KAAA,GAAS;QACd,IAAA,EAAK,OAAA,GAAW;QAChB,IAAA,EAAK,OAAA,GAAW,MAAM,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAI,CAAC;IACjD;IAEA,UAAU,MAAA,EAAuB;QAC/B,MAAM,UAAU,IAAA,EAAK,MAAA,KAAY;QAEjC,IAAI,SAAS;YACX,IAAA,EAAK,MAAA,GAAU;YACf,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;gBACnC,SAAS,MAAM;YACjB,CAAC;QACH;IACF;IAEA,WAAoB;QAClB,OAAO,IAAA,EAAK,MAAA;IACd;AACF;AAEO,IAAM,gBAAgB,IAAI,cAAc", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4731, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/thenable.ts"], "sourcesContent": ["/**\n * Thenable types which matches <PERSON><PERSON>'s types for promises\n *\n * <PERSON><PERSON> seemingly uses `.status`, `.value` and `.reason` properties on a promises to optimistically unwrap data from promises\n *\n * @see https://github.com/facebook/react/blob/main/packages/shared/ReactTypes.js#L112-L138\n * @see https://github.com/facebook/react/blob/4f604941569d2e8947ce1460a0b2997e835f37b9/packages/react-debug-tools/src/ReactDebugHooks.js#L224-L227\n */\n\nimport { noop } from './utils'\n\ninterface Fulfilled<T> {\n  status: 'fulfilled'\n  value: T\n}\ninterface Rejected {\n  status: 'rejected'\n  reason: unknown\n}\ninterface Pending<T> {\n  status: 'pending'\n\n  /**\n   * Resolve the promise with a value.\n   * Will remove the `resolve` and `reject` properties from the promise.\n   */\n  resolve: (value: T) => void\n  /**\n   * Reject the promise with a reason.\n   * Will remove the `resolve` and `reject` properties from the promise.\n   */\n  reject: (reason: unknown) => void\n}\n\nexport type FulfilledThenable<T> = Promise<T> & Fulfilled<T>\nexport type RejectedThenable<T> = Promise<T> & Rejected\nexport type PendingThenable<T> = Promise<T> & Pending<T>\n\nexport type Thenable<T> =\n  | FulfilledThenable<T>\n  | RejectedThenable<T>\n  | PendingThenable<T>\n\nexport function pendingThenable<T>(): PendingThenable<T> {\n  let resolve: Pending<T>['resolve']\n  let reject: Pending<T>['reject']\n  // this could use `Promise.withResolvers()` in the future\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve\n    reject = _reject\n  }) as PendingThenable<T>\n\n  thenable.status = 'pending'\n  thenable.catch(() => {\n    // prevent unhandled rejection errors\n  })\n\n  function finalize(data: Fulfilled<T> | Rejected) {\n    Object.assign(thenable, data)\n\n    // clear pending props props to avoid calling them twice\n    delete (thenable as Partial<PendingThenable<T>>).resolve\n    delete (thenable as Partial<PendingThenable<T>>).reject\n  }\n\n  thenable.resolve = (value) => {\n    finalize({\n      status: 'fulfilled',\n      value,\n    })\n\n    resolve(value)\n  }\n  thenable.reject = (reason) => {\n    finalize({\n      status: 'rejected',\n      reason,\n    })\n\n    reject(reason)\n  }\n\n  return thenable\n}\n\n/**\n * This function takes a Promise-like input and detects whether the data\n * is synchronously available or not.\n *\n * It does not inspect .status, .value or .reason properties of the promise,\n * as those are not always available, and the .status of React's promises\n * should not be considered part of the public API.\n */\nexport function tryResolveSync(promise: Promise<unknown> | Thenable<unknown>) {\n  let data: unknown\n\n  promise\n    .then((result) => {\n      data = result\n      return result\n    }, noop)\n    // .catch can be unavailable on certain kinds of thenable's\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    ?.catch(noop)\n\n  if (data !== undefined) {\n    return { data }\n  }\n\n  return undefined\n}\n"], "names": [], "mappings": ";;;;;AASA,SAAS,YAAY;;AAkCd,SAAS,kBAAyC;IACvD,IAAI;IACJ,IAAI;IAEJ,MAAM,WAAW,IAAI,QAAQ,CAAC,UAAU,YAAY;QAClD,UAAU;QACV,SAAS;IACX,CAAC;IAED,SAAS,MAAA,GAAS;IAClB,SAAS,KAAA,CAAM,KAEf,CAFqB,AAEpB;IAED,SAAS,SAAS,IAAA,EAA+B;QAC/C,OAAO,MAAA,CAAO,UAAU,IAAI;QAG5B,OAAQ,SAAyC,OAAA;QACjD,OAAQ,SAAyC,MAAA;IACnD;IAEA,SAAS,OAAA,GAAU,CAAC,UAAU;QAC5B,SAAS;YACP,QAAQ;YACR;QACF,CAAC;QAED,QAAQ,KAAK;IACf;IACA,SAAS,MAAA,GAAS,CAAC,WAAW;QAC5B,SAAS;YACP,QAAQ;YACR;QACF,CAAC;QAED,OAAO,MAAM;IACf;IAEA,OAAO;AACT;AAUO,SAAS,eAAe,OAAA,EAA+C;IAC5E,IAAI;IAEJ,QACG,IAAA,CAAK,CAAC,WAAW;QAChB,OAAO;QACP,OAAO;IACT,2KAAG,OAAI,GAGL,8KAAM,OAAI;IAEd,IAAI,SAAS,KAAA,GAAW;QACtB,OAAO;YAAE;QAAK;IAChB;IAEA,OAAO,KAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4789, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/retryer.ts"], "sourcesContent": ["import { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { pendingThenable } from './thenable'\nimport { isServer, sleep } from './utils'\nimport type { CancelOptions, DefaultError, NetworkMode } from './types'\n\n// TYPES\n\ninterface RetryerConfig<TData = unknown, TError = DefaultError> {\n  fn: () => TData | Promise<TData>\n  initialPromise?: Promise<TData>\n  abort?: () => void\n  onError?: (error: TError) => void\n  onSuccess?: (data: TData) => void\n  onFail?: (failureCount: number, error: TError) => void\n  onPause?: () => void\n  onContinue?: () => void\n  retry?: RetryValue<TError>\n  retryDelay?: RetryDelayValue<TError>\n  networkMode: NetworkMode | undefined\n  canRun: () => boolean\n}\n\nexport interface Retryer<TData = unknown> {\n  promise: Promise<TData>\n  cancel: (cancelOptions?: CancelOptions) => void\n  continue: () => Promise<unknown>\n  cancelRetry: () => void\n  continueRetry: () => void\n  canStart: () => boolean\n  start: () => Promise<TData>\n}\n\nexport type RetryValue<TError> = boolean | number | ShouldRetryFunction<TError>\n\ntype ShouldRetryFunction<TError = DefaultError> = (\n  failureCount: number,\n  error: TError,\n) => boolean\n\nexport type RetryDelayValue<TError> = number | RetryDelayFunction<TError>\n\ntype RetryDelayFunction<TError = DefaultError> = (\n  failureCount: number,\n  error: TError,\n) => number\n\nfunction defaultRetryDelay(failureCount: number) {\n  return Math.min(1000 * 2 ** failureCount, 30000)\n}\n\nexport function canFetch(networkMode: NetworkMode | undefined): boolean {\n  return (networkMode ?? 'online') === 'online'\n    ? onlineManager.isOnline()\n    : true\n}\n\nexport class CancelledError extends Error {\n  revert?: boolean\n  silent?: boolean\n  constructor(options?: CancelOptions) {\n    super('CancelledError')\n    this.revert = options?.revert\n    this.silent = options?.silent\n  }\n}\n\nexport function isCancelledError(value: any): value is CancelledError {\n  return value instanceof CancelledError\n}\n\nexport function createRetryer<TData = unknown, TError = DefaultError>(\n  config: RetryerConfig<TData, TError>,\n): Retryer<TData> {\n  let isRetryCancelled = false\n  let failureCount = 0\n  let isResolved = false\n  let continueFn: ((value?: unknown) => void) | undefined\n\n  const thenable = pendingThenable<TData>()\n\n  const cancel = (cancelOptions?: CancelOptions): void => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions))\n\n      config.abort?.()\n    }\n  }\n  const cancelRetry = () => {\n    isRetryCancelled = true\n  }\n\n  const continueRetry = () => {\n    isRetryCancelled = false\n  }\n\n  const canContinue = () =>\n    focusManager.isFocused() &&\n    (config.networkMode === 'always' || onlineManager.isOnline()) &&\n    config.canRun()\n\n  const canStart = () => canFetch(config.networkMode) && config.canRun()\n\n  const resolve = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onSuccess?.(value)\n      continueFn?.()\n      thenable.resolve(value)\n    }\n  }\n\n  const reject = (value: any) => {\n    if (!isResolved) {\n      isResolved = true\n      config.onError?.(value)\n      continueFn?.()\n      thenable.reject(value)\n    }\n  }\n\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value)\n        }\n      }\n      config.onPause?.()\n    }).then(() => {\n      continueFn = undefined\n      if (!isResolved) {\n        config.onContinue?.()\n      }\n    })\n  }\n\n  // Create loop function\n  const run = () => {\n    // Do nothing if already resolved\n    if (isResolved) {\n      return\n    }\n\n    let promiseOrValue: any\n\n    // we can re-use config.initialPromise on the first call of run()\n    const initialPromise =\n      failureCount === 0 ? config.initialPromise : undefined\n\n    // Execute query\n    try {\n      promiseOrValue = initialPromise ?? config.fn()\n    } catch (error) {\n      promiseOrValue = Promise.reject(error)\n    }\n\n    Promise.resolve(promiseOrValue)\n      .then(resolve)\n      .catch((error) => {\n        // Stop if the fetch is already resolved\n        if (isResolved) {\n          return\n        }\n\n        // Do we need to retry the request?\n        const retry = config.retry ?? (isServer ? 0 : 3)\n        const retryDelay = config.retryDelay ?? defaultRetryDelay\n        const delay =\n          typeof retryDelay === 'function'\n            ? retryDelay(failureCount, error)\n            : retryDelay\n        const shouldRetry =\n          retry === true ||\n          (typeof retry === 'number' && failureCount < retry) ||\n          (typeof retry === 'function' && retry(failureCount, error))\n\n        if (isRetryCancelled || !shouldRetry) {\n          // We are done if the query does not need to be retried\n          reject(error)\n          return\n        }\n\n        failureCount++\n\n        // Notify on fail\n        config.onFail?.(failureCount, error)\n\n        // Delay\n        sleep(delay)\n          // Pause if the document is not visible or when the device is offline\n          .then(() => {\n            return canContinue() ? undefined : pause()\n          })\n          .then(() => {\n            if (isRetryCancelled) {\n              reject(error)\n            } else {\n              run()\n            }\n          })\n      })\n  }\n\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.()\n      return thenable\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      // Start loop\n      if (canStart()) {\n        run()\n      } else {\n        pause().then(run)\n      }\n      return thenable\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,qBAAqB;AAC9B,SAAS,uBAAuB;AAChC,SAAS,UAAU,aAAa;;;;;AA4ChC,SAAS,kBAAkB,YAAA,EAAsB;IAC/C,OAAO,KAAK,GAAA,CAAI,MAAO,KAAK,cAAc,GAAK;AACjD;AAEO,SAAS,SAAS,WAAA,EAA+C;IACtE,OAAA,CAAQ,eAAe,QAAA,MAAc,2LACjC,gBAAA,CAAc,QAAA,CAAS,IACvB;AACN;AAEO,IAAM,iBAAN,cAA6B,MAAM;IAGxC,YAAY,OAAA,CAAyB;QACnC,KAAA,CAAM,gBAAgB;QACtB,IAAA,CAAK,MAAA,GAAS,SAAS;QACvB,IAAA,CAAK,MAAA,GAAS,SAAS;IACzB;AACF;AAEO,SAAS,iBAAiB,KAAA,EAAqC;IACpE,OAAO,iBAAiB;AAC1B;AAEO,SAAS,cACd,MAAA,EACgB;IAChB,IAAI,mBAAmB;IACvB,IAAI,eAAe;IACnB,IAAI,aAAa;IACjB,IAAI;IAEJ,MAAM,0LAAW,kBAAA,CAAuB;IAExC,MAAM,SAAS,CAAC,kBAAwC;QACtD,IAAI,CAAC,YAAY;YACf,OAAO,IAAI,eAAe,aAAa,CAAC;YAExC,OAAO,KAAA,GAAQ;QACjB;IACF;IACA,MAAM,cAAc,MAAM;QACxB,mBAAmB;IACrB;IAEA,MAAM,gBAAgB,MAAM;QAC1B,mBAAmB;IACrB;IAEA,MAAM,cAAc,IAClB,8LAAA,CAAa,SAAA,CAAU,KAAA,CACtB,OAAO,WAAA,KAAgB,4LAAY,gBAAA,CAAc,QAAA,CAAS,CAAA,KAC3D,OAAO,MAAA,CAAO;IAEhB,MAAM,WAAW,IAAM,SAAS,OAAO,WAAW,KAAK,OAAO,MAAA,CAAO;IAErE,MAAM,UAAU,CAAC,UAAe;QAC9B,IAAI,CAAC,YAAY;YACf,aAAa;YACb,OAAO,SAAA,GAAY,KAAK;YACxB,aAAa;YACb,SAAS,OAAA,CAAQ,KAAK;QACxB;IACF;IAEA,MAAM,SAAS,CAAC,UAAe;QAC7B,IAAI,CAAC,YAAY;YACf,aAAa;YACb,OAAO,OAAA,GAAU,KAAK;YACtB,aAAa;YACb,SAAS,MAAA,CAAO,KAAK;QACvB;IACF;IAEA,MAAM,QAAQ,MAAM;QAClB,OAAO,IAAI,QAAQ,CAAC,oBAAoB;YACtC,aAAa,CAAC,UAAU;gBACtB,IAAI,cAAc,YAAY,GAAG;oBAC/B,gBAAgB,KAAK;gBACvB;YACF;YACA,OAAO,OAAA,GAAU;QACnB,CAAC,EAAE,IAAA,CAAK,MAAM;YACZ,aAAa,KAAA;YACb,IAAI,CAAC,YAAY;gBACf,OAAO,UAAA,GAAa;YACtB;QACF,CAAC;IACH;IAGA,MAAM,MAAM,MAAM;QAEhB,IAAI,YAAY;YACd;QACF;QAEA,IAAI;QAGJ,MAAM,iBACJ,iBAAiB,IAAI,OAAO,cAAA,GAAiB,KAAA;QAG/C,IAAI;YACF,iBAAiB,kBAAkB,OAAO,EAAA,CAAG;QAC/C,EAAA,OAAS,OAAO;YACd,iBAAiB,QAAQ,MAAA,CAAO,KAAK;QACvC;QAEA,QAAQ,OAAA,CAAQ,cAAc,EAC3B,IAAA,CAAK,OAAO,EACZ,KAAA,CAAM,CAAC,UAAU;YAEhB,IAAI,YAAY;gBACd;YACF;YAGA,MAAM,QAAQ,OAAO,KAAA,IAAA,yKAAU,WAAA,GAAW,IAAI,CAAA;YAC9C,MAAM,aAAa,OAAO,UAAA,IAAc;YACxC,MAAM,QACJ,OAAO,eAAe,aAClB,WAAW,cAAc,KAAK,IAC9B;YACN,MAAM,cACJ,UAAU,QACT,OAAO,UAAU,YAAY,eAAe,SAC5C,OAAO,UAAU,cAAc,MAAM,cAAc,KAAK;YAE3D,IAAI,oBAAoB,CAAC,aAAa;gBAEpC,OAAO,KAAK;gBACZ;YACF;YAEA;YAGA,OAAO,MAAA,GAAS,cAAc,KAAK;YAGnC,CAAA,GAAA,uKAAA,CAAA,QAAA,EAAM,KAAK,EAER,IAAA,CAAK,MAAM;gBACV,OAAO,YAAY,IAAI,KAAA,IAAY,MAAM;YAC3C,CAAC,EACA,IAAA,CAAK,MAAM;gBACV,IAAI,kBAAkB;oBACpB,OAAO,KAAK;gBACd,OAAO;oBACL,IAAI;gBACN;YACF,CAAC;QACL,CAAC;IACL;IAEA,OAAO;QACL,SAAS;QACT;QACA,UAAU,MAAM;YACd,aAAa;YACb,OAAO;QACT;QACA;QACA;QACA;QACA,OAAO,MAAM;YAEX,IAAI,SAAS,GAAG;gBACd,IAAI;YACN,OAAO;gBACL,MAAM,EAAE,IAAA,CAAK,GAAG;YAClB;YACA,OAAO;QACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4935, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/removable.ts"], "sourcesContent": ["import { isServer, isValidTimeout } from './utils'\n\nexport abstract class Removable {\n  gcTime!: number\n  #gcTimeout?: ReturnType<typeof setTimeout>\n\n  destroy(): void {\n    this.clearGcTimeout()\n  }\n\n  protected scheduleGc(): void {\n    this.clearGcTimeout()\n\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove()\n      }, this.gcTime)\n    }\n  }\n\n  protected updateGcTime(newGcTime: number | undefined): void {\n    // Default to 5 minutes (Infinity for server-side) if no gcTime is set\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (isServer ? Infinity : 5 * 60 * 1000),\n    )\n  }\n\n  protected clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout)\n      this.#gcTimeout = undefined\n    }\n  }\n\n  protected abstract optionalRemove(): void\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,UAAU,sBAAsB;;AAElC,IAAe,YAAf,MAAyB;KAE9B,SAAA,CAAA;IAEA,UAAgB;QACd,IAAA,CAAK,cAAA,CAAe;IACtB;IAEU,aAAmB;QAC3B,IAAA,CAAK,cAAA,CAAe;QAEpB,gLAAI,iBAAA,EAAe,IAAA,CAAK,MAAM,GAAG;YAC/B,IAAA,EAAK,SAAA,GAAa,WAAW,MAAM;gBACjC,IAAA,CAAK,cAAA,CAAe;YACtB,GAAG,IAAA,CAAK,MAAM;QAChB;IACF;IAEU,aAAa,SAAA,EAAqC;QAE1D,IAAA,CAAK,MAAA,GAAS,KAAK,GAAA,CACjB,IAAA,CAAK,MAAA,IAAU,GACf,aAAA,yKAAc,WAAA,GAAW,WAAW,IAAI,KAAK,GAAA;IAEjD;IAEU,iBAAiB;QACzB,IAAI,IAAA,EAAK,SAAA,EAAY;YACnB,aAAa,IAAA,EAAK,SAAU;YAC5B,IAAA,EAAK,SAAA,GAAa,KAAA;QACpB;IACF;AAGF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4972, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/query.ts"], "sourcesContent": ["import {\n  ensureQueryFn,\n  noop,\n  replaceData,\n  resolveEnabled,\n  resolveStaleTime,\n  skipToken,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport { canFetch, createRetryer, isCancelledError } from './retryer'\nimport { Removable } from './removable'\nimport type { QueryCache } from './queryCache'\nimport type { QueryClient } from './queryClient'\nimport type {\n  CancelOptions,\n  DefaultError,\n  FetchStatus,\n  InitialDataFunction,\n  OmitKeyof,\n  QueryFunctionContext,\n  QueryKey,\n  QueryMeta,\n  QueryOptions,\n  QueryStatus,\n  SetDataOptions,\n  StaleTime,\n} from './types'\nimport type { QueryObserver } from './queryObserver'\nimport type { Retryer } from './retryer'\n\n// TYPES\n\ninterface QueryConfig<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  client: QueryClient\n  queryKey: TQ<PERSON>y<PERSON>ey\n  queryHash: string\n  options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state?: QueryState<TData, TError>\n}\n\nexport interface QueryState<TData = unknown, TError = DefaultError> {\n  data: TData | undefined\n  dataUpdateCount: number\n  dataUpdatedAt: number\n  error: TError | null\n  errorUpdateCount: number\n  errorUpdatedAt: number\n  fetchFailureCount: number\n  fetchFailureReason: TError | null\n  fetchMeta: FetchMeta | null\n  isInvalidated: boolean\n  status: QueryStatus\n  fetchStatus: FetchStatus\n}\n\nexport interface FetchContext<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  fetchFn: () => unknown | Promise<unknown>\n  fetchOptions?: FetchOptions\n  signal: AbortSignal\n  options: QueryOptions<TQueryFnData, TError, TData, any>\n  client: QueryClient\n  queryKey: TQueryKey\n  state: QueryState<TData, TError>\n}\n\nexport interface QueryBehavior<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> {\n  onFetch: (\n    context: FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n    query: Query,\n  ) => void\n}\n\nexport type FetchDirection = 'forward' | 'backward'\n\nexport interface FetchMeta {\n  fetchMore?: { direction: FetchDirection }\n}\n\nexport interface FetchOptions<TData = unknown> {\n  cancelRefetch?: boolean\n  meta?: FetchMeta\n  initialPromise?: Promise<TData>\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError\n}\n\ninterface FetchAction {\n  type: 'fetch'\n  meta?: FetchMeta\n}\n\ninterface SuccessAction<TData> {\n  data: TData | undefined\n  type: 'success'\n  dataUpdatedAt?: number\n  manual?: boolean\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface InvalidateAction {\n  type: 'invalidate'\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\ninterface SetStateAction<TData, TError> {\n  type: 'setState'\n  state: Partial<QueryState<TData, TError>>\n  setStateOptions?: SetStateOptions\n}\n\nexport type Action<TData, TError> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | FetchAction\n  | InvalidateAction\n  | PauseAction\n  | SetStateAction<TData, TError>\n  | SuccessAction<TData>\n\nexport interface SetStateOptions {\n  meta?: any\n}\n\n// CLASS\n\nexport class Query<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Removable {\n  queryKey: TQueryKey\n  queryHash: string\n  options!: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  state: QueryState<TData, TError>\n\n  #initialState: QueryState<TData, TError>\n  #revertState?: QueryState<TData, TError>\n  #cache: QueryCache\n  #client: QueryClient\n  #retryer?: Retryer<TData>\n  observers: Array<QueryObserver<any, any, any, any, any>>\n  #defaultOptions?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>\n  #abortSignalConsumed: boolean\n\n  constructor(config: QueryConfig<TQueryFnData, TError, TData, TQueryKey>) {\n    super()\n\n    this.#abortSignalConsumed = false\n    this.#defaultOptions = config.defaultOptions\n    this.setOptions(config.options)\n    this.observers = []\n    this.#client = config.client\n    this.#cache = this.#client.getQueryCache()\n    this.queryKey = config.queryKey\n    this.queryHash = config.queryHash\n    this.#initialState = getDefaultState(this.options)\n    this.state = config.state ?? this.#initialState\n    this.scheduleGc()\n  }\n  get meta(): QueryMeta | undefined {\n    return this.options.meta\n  }\n\n  get promise(): Promise<TData> | undefined {\n    return this.#retryer?.promise\n  }\n\n  setOptions(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): void {\n    this.options = { ...this.#defaultOptions, ...options }\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  protected optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === 'idle') {\n      this.#cache.remove(this)\n    }\n  }\n\n  setData(\n    newData: TData,\n    options?: SetDataOptions & { manual: boolean },\n  ): TData {\n    const data = replaceData(this.state.data, newData, this.options)\n\n    // Set data and mark it as cached\n    this.#dispatch({\n      data,\n      type: 'success',\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual,\n    })\n\n    return data\n  }\n\n  setState(\n    state: Partial<QueryState<TData, TError>>,\n    setStateOptions?: SetStateOptions,\n  ): void {\n    this.#dispatch({ type: 'setState', state, setStateOptions })\n  }\n\n  cancel(options?: CancelOptions): Promise<void> {\n    const promise = this.#retryer?.promise\n    this.#retryer?.cancel(options)\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve()\n  }\n\n  destroy(): void {\n    super.destroy()\n\n    this.cancel({ silent: true })\n  }\n\n  reset(): void {\n    this.destroy()\n    this.setState(this.#initialState)\n  }\n\n  isActive(): boolean {\n    return this.observers.some(\n      (observer) => resolveEnabled(observer.options.enabled, this) !== false,\n    )\n  }\n\n  isDisabled(): boolean {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive()\n    }\n    // if a query has no observers, it should still be considered disabled if it never attempted a fetch\n    return (\n      this.options.queryFn === skipToken ||\n      this.state.dataUpdateCount + this.state.errorUpdateCount === 0\n    )\n  }\n\n  isStatic(): boolean {\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) =>\n          resolveStaleTime(observer.options.staleTime, this) === 'static',\n      )\n    }\n\n    return false\n  }\n\n  isStale(): boolean {\n    // check observers first, their `isStale` has the source of truth\n    // calculated with `isStaleByTime` and it takes `enabled` into account\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale,\n      )\n    }\n\n    return this.state.data === undefined || this.state.isInvalidated\n  }\n\n  isStaleByTime(staleTime: StaleTime = 0): boolean {\n    // no data is always stale\n    if (this.state.data === undefined) {\n      return true\n    }\n    // static is never stale\n    if (staleTime === 'static') {\n      return false\n    }\n    // if the query is invalidated, it is stale\n    if (this.state.isInvalidated) {\n      return true\n    }\n\n    return !timeUntilStale(this.state.dataUpdatedAt, staleTime)\n  }\n\n  onFocus(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  onOnline(): void {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect())\n\n    observer?.refetch({ cancelRefetch: false })\n\n    // Continue fetch if currently paused\n    this.#retryer?.continue()\n  }\n\n  addObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer)\n\n      // Stop the query from being garbage collected\n      this.clearGcTimeout()\n\n      this.#cache.notify({ type: 'observerAdded', query: this, observer })\n    }\n  }\n\n  removeObserver(observer: QueryObserver<any, any, any, any, any>): void {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer)\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true })\n          } else {\n            this.#retryer.cancelRetry()\n          }\n        }\n\n        this.scheduleGc()\n      }\n\n      this.#cache.notify({ type: 'observerRemoved', query: this, observer })\n    }\n  }\n\n  getObserversCount(): number {\n    return this.observers.length\n  }\n\n  invalidate(): void {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: 'invalidate' })\n    }\n  }\n\n  fetch(\n    options?: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    fetchOptions?: FetchOptions<TQueryFnData>,\n  ): Promise<TData> {\n    if (this.state.fetchStatus !== 'idle') {\n      if (this.state.data !== undefined && fetchOptions?.cancelRefetch) {\n        // Silently cancel current fetch if the user wants to cancel refetch\n        this.cancel({ silent: true })\n      } else if (this.#retryer) {\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        this.#retryer.continueRetry()\n        // Return current promise if we are already fetching\n        return this.#retryer.promise\n      }\n    }\n\n    // Update config if passed, otherwise the config from the last execution is used\n    if (options) {\n      this.setOptions(options)\n    }\n\n    // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn)\n      if (observer) {\n        this.setOptions(observer.options)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`,\n        )\n      }\n    }\n\n    const abortController = new AbortController()\n\n    // Adds an enumerable signal property to the object that\n    // which sets abortSignalConsumed to true when the signal\n    // is read.\n    const addSignalProperty = (object: unknown) => {\n      Object.defineProperty(object, 'signal', {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true\n          return abortController.signal\n        },\n      })\n    }\n\n    // Create fetch function\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions)\n\n      // Create query function context\n      const createQueryFnContext = (): QueryFunctionContext<TQueryKey> => {\n        const queryFnContext: OmitKeyof<\n          QueryFunctionContext<TQueryKey>,\n          'signal'\n        > = {\n          client: this.#client,\n          queryKey: this.queryKey,\n          meta: this.meta,\n        }\n        addSignalProperty(queryFnContext)\n        return queryFnContext as QueryFunctionContext<TQueryKey>\n      }\n\n      const queryFnContext = createQueryFnContext()\n\n      this.#abortSignalConsumed = false\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this as unknown as Query,\n        )\n      }\n\n      return queryFn(queryFnContext)\n    }\n\n    // Trigger behavior hook\n    const createFetchContext = (): FetchContext<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey\n    > => {\n      const context: OmitKeyof<\n        FetchContext<TQueryFnData, TError, TData, TQueryKey>,\n        'signal'\n      > = {\n        fetchOptions,\n        options: this.options,\n        queryKey: this.queryKey,\n        client: this.#client,\n        state: this.state,\n        fetchFn,\n      }\n\n      addSignalProperty(context)\n      return context as FetchContext<TQueryFnData, TError, TData, TQueryKey>\n    }\n\n    const context = createFetchContext()\n\n    this.options.behavior?.onFetch(context, this as unknown as Query)\n\n    // Store state in case the current fetch needs to be reverted\n    this.#revertState = this.state\n\n    // Set to fetching state if not already in it\n    if (\n      this.state.fetchStatus === 'idle' ||\n      this.state.fetchMeta !== context.fetchOptions?.meta\n    ) {\n      this.#dispatch({ type: 'fetch', meta: context.fetchOptions?.meta })\n    }\n\n    const onError = (error: TError | { silent?: boolean }) => {\n      // Optimistically update state if needed\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: 'error',\n          error: error as TError,\n        })\n      }\n\n      if (!isCancelledError(error)) {\n        // Notify cache callback\n        this.#cache.config.onError?.(\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error as any,\n          this as Query<any, any, any, any>,\n        )\n      }\n\n      // Schedule query gc after fetching\n      this.scheduleGc()\n    }\n\n    // Try to fetch the data\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise as\n        | Promise<TData>\n        | undefined,\n      fn: context.fetchFn as () => Promise<TData>,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === undefined) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`,\n            )\n          }\n          onError(new Error(`${this.queryHash} data is undefined`) as any)\n          return\n        }\n\n        try {\n          this.setData(data)\n        } catch (error) {\n          onError(error as TError)\n          return\n        }\n\n        // Notify cache callback\n        this.#cache.config.onSuccess?.(data, this as Query<any, any, any, any>)\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error as any,\n          this as Query<any, any, any, any>,\n        )\n\n        // Schedule query gc after fetching\n        this.scheduleGc()\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.#dispatch({ type: 'pause' })\n      },\n      onContinue: () => {\n        this.#dispatch({ type: 'continue' })\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true,\n    })\n\n    return this.#retryer.start()\n  }\n\n  #dispatch(action: Action<TData, TError>): void {\n    const reducer = (\n      state: QueryState<TData, TError>,\n    ): QueryState<TData, TError> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            fetchStatus: 'paused',\n          }\n        case 'continue':\n          return {\n            ...state,\n            fetchStatus: 'fetching',\n          }\n        case 'fetch':\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null,\n          }\n        case 'success':\n          // If fetching ends successfully, we don't need revertState as a fallback anymore.\n          this.#revertState = undefined\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: 'success',\n            ...(!action.manual && {\n              fetchStatus: 'idle',\n              fetchFailureCount: 0,\n              fetchFailureReason: null,\n            }),\n          }\n        case 'error':\n          const error = action.error\n\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: 'idle' }\n          }\n\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: 'idle',\n            status: 'error',\n          }\n        case 'invalidate':\n          return {\n            ...state,\n            isInvalidated: true,\n          }\n        case 'setState':\n          return {\n            ...state,\n            ...action.state,\n          }\n      }\n    }\n\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate()\n      })\n\n      this.#cache.notify({ query: this, type: 'updated', action })\n    })\n  }\n}\n\nexport function fetchState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  data: TData | undefined,\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? 'fetching' : 'paused',\n    ...(data === undefined &&\n      ({\n        error: null,\n        status: 'pending',\n      } as const)),\n  } as const\n}\n\nfunction getDefaultState<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryKey extends QueryKey,\n>(\n  options: QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): QueryState<TData, TError> {\n  const data =\n    typeof options.initialData === 'function'\n      ? (options.initialData as InitialDataFunction<TData>)()\n      : options.initialData\n\n  const hasData = data !== undefined\n\n  const initialDataUpdatedAt = hasData\n    ? typeof options.initialDataUpdatedAt === 'function'\n      ? (options.initialDataUpdatedAt as () => number | undefined)()\n      : options.initialDataUpdatedAt\n    : 0\n\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? (initialDataUpdatedAt ?? Date.now()) : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? 'success' : 'pending',\n    fetchStatus: 'idle',\n  }\n}\n"], "names": ["queryFnContext", "context"], "mappings": ";;;;;AAAA;AASA,SAAS,qBAAqB;AAC9B,SAAS,UAAU,eAAe,wBAAwB;AAC1D,SAAS,iBAAiB;;;;;AAmJnB,IAAM,QAAN,0LAKG,YAAA,CAAU;KAMlB,YAAA,CAAA;KACA,WAAA,CAAA;KACA,KAAA,CAAA;KACA,MAAA,CAAA;KACA,OAAA,CAAA;IAEA,eAAA,CAAA;KACA,mBAAA,CAAA;IAEA,YAAY,MAAA,CAA6D;QACvE,KAAA,CAAM;QAEN,IAAA,EAAK,mBAAA,GAAuB;QAC5B,IAAA,EAAK,cAAA,GAAkB,OAAO,cAAA;QAC9B,IAAA,CAAK,UAAA,CAAW,OAAO,OAAO;QAC9B,IAAA,CAAK,SAAA,GAAY,CAAC,CAAA;QAClB,IAAA,EAAK,MAAA,GAAU,OAAO,MAAA;QACtB,IAAA,CAAK,MAAA,GAAS,IAAA,EAAK,MAAA,CAAQ,aAAA,CAAc;QACzC,IAAA,CAAK,QAAA,GAAW,OAAO,QAAA;QACvB,IAAA,CAAK,SAAA,GAAY,OAAO,SAAA;QACxB,IAAA,EAAK,YAAA,GAAgB,gBAAgB,IAAA,CAAK,OAAO;QACjD,IAAA,CAAK,KAAA,GAAQ,OAAO,KAAA,IAAS,IAAA,CAAK,aAAA;QAClC,IAAA,CAAK,UAAA,CAAW;IAClB;IACA,IAAI,OAA8B;QAChC,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA;IACtB;IAEA,IAAI,UAAsC;QACxC,OAAO,IAAA,EAAK,OAAA,EAAU;IACxB;IAEA,WACE,OAAA,EACM;QACN,IAAA,CAAK,OAAA,GAAU;YAAE,GAAG,IAAA,EAAK,cAAA;YAAiB,GAAG,OAAA;QAAQ;QAErD,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,MAAM;IACvC;IAEU,iBAAiB;QACzB,IAAI,CAAC,IAAA,CAAK,SAAA,CAAU,MAAA,IAAU,IAAA,CAAK,KAAA,CAAM,WAAA,KAAgB,QAAQ;YAC/D,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO,IAAI;QACzB;IACF;IAEA,QACE,OAAA,EACA,OAAA,EACO;QACP,MAAM,OAAO,0LAAA,EAAY,IAAA,CAAK,KAAA,CAAM,IAAA,EAAM,SAAS,IAAA,CAAK,OAAO;QAG/D,IAAA,EAAK,QAAA,CAAU;YACb;YACA,MAAM;YACN,eAAe,SAAS;YACxB,QAAQ,SAAS;QACnB,CAAC;QAED,OAAO;IACT;IAEA,SACE,KAAA,EACA,eAAA,EACM;QACN,IAAA,EAAK,QAAA,CAAU;YAAE,MAAM;YAAY;YAAO;QAAgB,CAAC;IAC7D;IAEA,OAAO,OAAA,EAAwC;QAC7C,MAAM,UAAU,IAAA,EAAK,OAAA,EAAU;QAC/B,IAAA,EAAK,OAAA,EAAU,OAAO,OAAO;QAC7B,OAAO,UAAU,QAAQ,IAAA,yKAAK,OAAI,EAAE,KAAA,yKAAM,OAAI,IAAI,QAAQ,OAAA,CAAQ;IACpE;IAEA,UAAgB;QACd,KAAA,CAAM,QAAQ;QAEd,IAAA,CAAK,MAAA,CAAO;YAAE,QAAQ;QAAK,CAAC;IAC9B;IAEA,QAAc;QACZ,IAAA,CAAK,OAAA,CAAQ;QACb,IAAA,CAAK,QAAA,CAAS,IAAA,EAAK,YAAa;IAClC;IAEA,WAAoB;QAClB,OAAO,IAAA,CAAK,SAAA,CAAU,IAAA,CACpB,CAAC,WAAa,6LAAA,EAAe,SAAS,OAAA,CAAQ,OAAA,EAAS,IAAI,MAAM;IAErE;IAEA,aAAsB;QACpB,IAAI,IAAA,CAAK,iBAAA,CAAkB,IAAI,GAAG;YAChC,OAAO,CAAC,IAAA,CAAK,QAAA,CAAS;QACxB;QAEA,OACE,IAAA,CAAK,OAAA,CAAQ,OAAA,6KAAY,YAAA,IACzB,IAAA,CAAK,KAAA,CAAM,eAAA,GAAkB,IAAA,CAAK,KAAA,CAAM,gBAAA,KAAqB;IAEjE;IAEA,WAAoB;QAClB,IAAI,IAAA,CAAK,iBAAA,CAAkB,IAAI,GAAG;YAChC,OAAO,IAAA,CAAK,SAAA,CAAU,IAAA,CACpB,CAAC,uLACC,mBAAA,EAAiB,SAAS,OAAA,CAAQ,SAAA,EAAW,IAAI,MAAM;QAE7D;QAEA,OAAO;IACT;IAEA,UAAmB;QAGjB,IAAI,IAAA,CAAK,iBAAA,CAAkB,IAAI,GAAG;YAChC,OAAO,IAAA,CAAK,SAAA,CAAU,IAAA,CACpB,CAAC,WAAa,SAAS,gBAAA,CAAiB,EAAE,OAAA;QAE9C;QAEA,OAAO,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,KAAA,KAAa,IAAA,CAAK,KAAA,CAAM,aAAA;IACrD;IAEA,cAAc,YAAuB,CAAA,EAAY;QAE/C,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,KAAA,GAAW;YACjC,OAAO;QACT;QAEA,IAAI,cAAc,UAAU;YAC1B,OAAO;QACT;QAEA,IAAI,IAAA,CAAK,KAAA,CAAM,aAAA,EAAe;YAC5B,OAAO;QACT;QAEA,OAAO,KAAC,yLAAA,EAAe,IAAA,CAAK,KAAA,CAAM,aAAA,EAAe,SAAS;IAC5D;IAEA,UAAgB;QACd,MAAM,WAAW,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,CAAC,IAAM,EAAE,wBAAA,CAAyB,CAAC;QAExE,UAAU,QAAQ;YAAE,eAAe;QAAM,CAAC;QAG1C,IAAA,EAAK,OAAA,EAAU,SAAS;IAC1B;IAEA,WAAiB;QACf,MAAM,WAAW,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,CAAC,IAAM,EAAE,sBAAA,CAAuB,CAAC;QAEtE,UAAU,QAAQ;YAAE,eAAe;QAAM,CAAC;QAG1C,IAAA,EAAK,OAAA,EAAU,SAAS;IAC1B;IAEA,YAAY,QAAA,EAAwD;QAClE,IAAI,CAAC,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,QAAQ,GAAG;YACtC,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,QAAQ;YAG5B,IAAA,CAAK,cAAA,CAAe;YAEpB,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO;gBAAE,MAAM;gBAAiB,OAAO,IAAA;gBAAM;YAAS,CAAC;QACrE;IACF;IAEA,eAAe,QAAA,EAAwD;QACrE,IAAI,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,QAAQ,GAAG;YACrC,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,CAAC,IAAM,MAAM,QAAQ;YAE5D,IAAI,CAAC,IAAA,CAAK,SAAA,CAAU,MAAA,EAAQ;gBAG1B,IAAI,IAAA,EAAK,OAAA,EAAU;oBACjB,IAAI,IAAA,EAAK,mBAAA,EAAsB;wBAC7B,IAAA,EAAK,OAAA,CAAS,MAAA,CAAO;4BAAE,QAAQ;wBAAK,CAAC;oBACvC,OAAO;wBACL,IAAA,EAAK,OAAA,CAAS,WAAA,CAAY;oBAC5B;gBACF;gBAEA,IAAA,CAAK,UAAA,CAAW;YAClB;YAEA,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO;gBAAE,MAAM;gBAAmB,OAAO,IAAA;gBAAM;YAAS,CAAC;QACvE;IACF;IAEA,oBAA4B;QAC1B,OAAO,IAAA,CAAK,SAAA,CAAU,MAAA;IACxB;IAEA,aAAmB;QACjB,IAAI,CAAC,IAAA,CAAK,KAAA,CAAM,aAAA,EAAe;YAC7B,IAAA,EAAK,QAAA,CAAU;gBAAE,MAAM;YAAa,CAAC;QACvC;IACF;IAEA,MACE,OAAA,EACA,YAAA,EACgB;QAChB,IAAI,IAAA,CAAK,KAAA,CAAM,WAAA,KAAgB,QAAQ;YACrC,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,KAAS,KAAA,KAAa,cAAc,eAAe;gBAEhE,IAAA,CAAK,MAAA,CAAO;oBAAE,QAAQ;gBAAK,CAAC;YAC9B,OAAA,IAAW,IAAA,EAAK,OAAA,EAAU;gBAExB,IAAA,CAAK,QAAA,CAAS,aAAA,CAAc;gBAE5B,OAAO,IAAA,EAAK,OAAA,CAAS,OAAA;YACvB;QACF;QAGA,IAAI,SAAS;YACX,IAAA,CAAK,UAAA,CAAW,OAAO;QACzB;QAIA,IAAI,CAAC,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS;YACzB,MAAM,WAAW,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,CAAC,IAAM,EAAE,OAAA,CAAQ,OAAO;YAC7D,IAAI,UAAU;gBACZ,IAAA,CAAK,UAAA,CAAW,SAAS,OAAO;YAClC;QACF;QAEA,IAAI,QAAQ,IAAI,aAAa,WAAc;YACzC,IAAI,CAAC,MAAM,OAAA,CAAQ,IAAA,CAAK,OAAA,CAAQ,QAAQ,GAAG;gBACzC,QAAQ,KAAA,CACN,CAAA,mIAAA,CAAA;YAEJ;QACF;QAEA,MAAM,kBAAkB,IAAI,gBAAgB;QAK5C,MAAM,oBAAoB,CAAC,WAAoB;YAC7C,OAAO,cAAA,CAAe,QAAQ,UAAU;gBACtC,YAAY;gBACZ,KAAK,MAAM;oBACT,IAAA,EAAK,mBAAA,GAAuB;oBAC5B,OAAO,gBAAgB,MAAA;gBACzB;YACF,CAAC;QACH;QAGA,MAAM,UAAU,MAAM;YACpB,MAAM,WAAU,2LAAA,EAAc,IAAA,CAAK,OAAA,EAAS,YAAY;YAGxD,MAAM,uBAAuB,MAAuC;gBAClE,MAAMA,kBAGF;oBACF,QAAQ,IAAA,CAAK,OAAA;oBACb,UAAU,IAAA,CAAK,QAAA;oBACf,MAAM,IAAA,CAAK,IAAA;gBACb;gBACA,kBAAkBA,eAAc;gBAChC,OAAOA;YACT;YAEA,MAAM,iBAAiB,qBAAqB;YAE5C,IAAA,CAAK,oBAAA,GAAuB;YAC5B,IAAI,IAAA,CAAK,OAAA,CAAQ,SAAA,EAAW;gBAC1B,OAAO,IAAA,CAAK,OAAA,CAAQ,SAAA,CAClB,SACA,gBACA,IAAA;YAEJ;YAEA,OAAO,QAAQ,cAAc;QAC/B;QAGA,MAAM,qBAAqB,MAKtB;YACH,MAAMC,WAGF;gBACF;gBACA,SAAS,IAAA,CAAK,OAAA;gBACd,UAAU,IAAA,CAAK,QAAA;gBACf,QAAQ,IAAA,EAAK,MAAA;gBACb,OAAO,IAAA,CAAK,KAAA;gBACZ;YACF;YAEA,kBAAkBA,QAAO;YACzB,OAAOA;QACT;QAEA,MAAM,UAAU,mBAAmB;QAEnC,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,QAAQ,SAAS,IAAwB;QAGhE,IAAA,EAAK,WAAA,GAAe,IAAA,CAAK,KAAA;QAGzB,IACE,IAAA,CAAK,KAAA,CAAM,WAAA,KAAgB,UAC3B,IAAA,CAAK,KAAA,CAAM,SAAA,KAAc,QAAQ,YAAA,EAAc,MAC/C;YACA,IAAA,EAAK,QAAA,CAAU;gBAAE,MAAM;gBAAS,MAAM,QAAQ,YAAA,EAAc;YAAK,CAAC;QACpE;QAEA,MAAM,UAAU,CAAC,UAAyC;YAExD,IAAI,CAAA,KAAE,6LAAA,EAAiB,KAAK,KAAK,MAAM,MAAA,GAAS;gBAC9C,IAAA,EAAK,QAAA,CAAU;oBACb,MAAM;oBACN;gBACF,CAAC;YACH;YAEA,IAAI,+KAAC,mBAAA,EAAiB,KAAK,GAAG;gBAE5B,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO,OAAA,GACjB,OACA,IAAA;gBAEF,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO,SAAA,GACjB,IAAA,CAAK,KAAA,CAAM,IAAA,EACX,OACA,IAAA;YAEJ;YAGA,IAAA,CAAK,UAAA,CAAW;QAClB;QAGA,IAAA,EAAK,OAAA,iLAAW,gBAAA,EAAc;YAC5B,gBAAgB,cAAc;YAG9B,IAAI,QAAQ,OAAA;YACZ,OAAO,gBAAgB,KAAA,CAAM,IAAA,CAAK,eAAe;YACjD,WAAW,CAAC,SAAS;gBACnB,IAAI,SAAS,KAAA,GAAW;oBACtB,IAAI,QAAQ,IAAI,aAAa,WAAc;wBACzC,QAAQ,KAAA,CACN,CAAA,sIAAA,EAAyI,IAAA,CAAK,SAAS,EAAA;oBAE3J;oBACA,QAAQ,IAAI,MAAM,GAAG,IAAA,CAAK,SAAS,CAAA,kBAAA,CAAoB,CAAQ;oBAC/D;gBACF;gBAEA,IAAI;oBACF,IAAA,CAAK,OAAA,CAAQ,IAAI;gBACnB,EAAA,OAAS,OAAO;oBACd,QAAQ,KAAe;oBACvB;gBACF;gBAGA,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO,SAAA,GAAY,MAAM,IAAiC;gBACtE,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO,SAAA,GACjB,MACA,IAAA,CAAK,KAAA,CAAM,KAAA,EACX,IAAA;gBAIF,IAAA,CAAK,UAAA,CAAW;YAClB;YACA;YACA,QAAQ,CAAC,cAAc,UAAU;gBAC/B,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;oBAAU;oBAAc;gBAAM,CAAC;YACxD;YACA,SAAS,MAAM;gBACb,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;gBAAQ,CAAC;YAClC;YACA,YAAY,MAAM;gBAChB,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;gBAAW,CAAC;YACrC;YACA,OAAO,QAAQ,OAAA,CAAQ,KAAA;YACvB,YAAY,QAAQ,OAAA,CAAQ,UAAA;YAC5B,aAAa,QAAQ,OAAA,CAAQ,WAAA;YAC7B,QAAQ,IAAM;QAChB,CAAC;QAED,OAAO,IAAA,EAAK,OAAA,CAAS,KAAA,CAAM;IAC7B;KAEA,QAAA,CAAU,MAAA,EAAqC;QAC7C,MAAM,UAAU,CACd,UAC8B;YAC9B,OAAQ,OAAO,IAAA,EAAM;gBACnB,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,mBAAmB,OAAO,YAAA;wBAC1B,oBAAoB,OAAO,KAAA;oBAC7B;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,aAAa;oBACf;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,aAAa;oBACf;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,GAAG,WAAW,MAAM,IAAA,EAAM,IAAA,CAAK,OAAO,CAAA;wBACtC,WAAW,OAAO,IAAA,IAAQ;oBAC5B;gBACF,KAAK;oBAEH,IAAA,EAAK,WAAA,GAAe,KAAA;oBACpB,OAAO;wBACL,GAAG,KAAA;wBACH,MAAM,OAAO,IAAA;wBACb,iBAAiB,MAAM,eAAA,GAAkB;wBACzC,eAAe,OAAO,aAAA,IAAiB,KAAK,GAAA,CAAI;wBAChD,OAAO;wBACP,eAAe;wBACf,QAAQ;wBACR,GAAI,CAAC,OAAO,MAAA,IAAU;4BACpB,aAAa;4BACb,mBAAmB;4BACnB,oBAAoB;wBACtB,CAAA;oBACF;gBACF,KAAK;oBACH,MAAM,QAAQ,OAAO,KAAA;oBAErB,kLAAI,mBAAA,EAAiB,KAAK,KAAK,MAAM,MAAA,IAAU,IAAA,CAAK,YAAA,EAAc;wBAChE,OAAO;4BAAE,GAAG,IAAA,EAAK,WAAA;4BAAc,aAAa;wBAAO;oBACrD;oBAEA,OAAO;wBACL,GAAG,KAAA;wBACH;wBACA,kBAAkB,MAAM,gBAAA,GAAmB;wBAC3C,gBAAgB,KAAK,GAAA,CAAI;wBACzB,mBAAmB,MAAM,iBAAA,GAAoB;wBAC7C,oBAAoB;wBACpB,aAAa;wBACb,QAAQ;oBACV;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,eAAe;oBACjB;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,GAAG,OAAO,KAAA;oBACZ;YACJ;QACF;QAEA,IAAA,CAAK,KAAA,GAAQ,QAAQ,IAAA,CAAK,KAAK;QAE/B,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;gBACnC,SAAS,aAAA,CAAc;YACzB,CAAC;YAED,IAAA,EAAK,KAAA,CAAO,MAAA,CAAO;gBAAE,OAAO,IAAA;gBAAM,MAAM;gBAAW;YAAO,CAAC;QAC7D,CAAC;IACH;AACF;AAEO,SAAS,WAMd,IAAA,EACA,OAAA,EACA;IACA,OAAO;QACL,mBAAmB;QACnB,oBAAoB;QACpB,2LAAa,WAAA,EAAS,QAAQ,WAAW,IAAI,aAAa;QAC1D,GAAI,SAAS,KAAA,KACV;YACC,OAAO;YACP,QAAQ;QACV,CAAA;IACJ;AACF;AAEA,SAAS,gBAMP,OAAA,EAC2B;IAC3B,MAAM,OACJ,OAAO,QAAQ,WAAA,KAAgB,aAC1B,QAAQ,WAAA,CAA2C,IACpD,QAAQ,WAAA;IAEd,MAAM,UAAU,SAAS,KAAA;IAEzB,MAAM,uBAAuB,UACzB,OAAO,QAAQ,oBAAA,KAAyB,aACrC,QAAQ,oBAAA,CAAkD,IAC3D,QAAQ,oBAAA,GACV;IAEJ,OAAO;QACL;QACA,iBAAiB;QACjB,eAAe,UAAW,wBAAwB,KAAK,GAAA,CAAI,IAAK;QAChE,OAAO;QACP,kBAAkB;QAClB,gBAAgB;QAChB,mBAAmB;QACnB,oBAAoB;QACpB,WAAW;QACX,eAAe;QACf,QAAQ,UAAU,YAAY;QAC9B,aAAa;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5403, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/queryCache.ts"], "sourcesContent": ["import { hashQueryKeyByOptions, matchQuery } from './utils'\nimport { Query } from './query'\nimport { notifyManager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport type { QueryFilters } from './utils'\nimport type { Action, QueryState } from './query'\nimport type {\n  DefaultError,\n  NotifyEvent,\n  QueryKey,\n  QueryOptions,\n  WithRequired,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { QueryObserver } from './queryObserver'\n\n// TYPES\n\ninterface QueryCacheConfig {\n  onError?: (\n    error: DefaultError,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n  onSuccess?: (data: unknown, query: Query<unknown, unknown, unknown>) => void\n  onSettled?: (\n    data: unknown | undefined,\n    error: DefaultError | null,\n    query: Query<unknown, unknown, unknown>,\n  ) => void\n}\n\ninterface NotifyEventQueryAdded extends NotifyEvent {\n  type: 'added'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryRemoved extends NotifyEvent {\n  type: 'removed'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryUpdated extends NotifyEvent {\n  type: 'updated'\n  query: Query<any, any, any, any>\n  action: Action<any, any>\n}\n\ninterface NotifyEventQueryObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverResultsUpdated extends NotifyEvent {\n  type: 'observerResultsUpdated'\n  query: Query<any, any, any, any>\n}\n\ninterface NotifyEventQueryObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  query: Query<any, any, any, any>\n  observer: QueryObserver<any, any, any, any, any>\n}\n\nexport type QueryCacheNotifyEvent =\n  | NotifyEventQueryAdded\n  | NotifyEventQueryRemoved\n  | NotifyEventQueryUpdated\n  | NotifyEventQueryObserverAdded\n  | NotifyEventQueryObserverRemoved\n  | NotifyEventQueryObserverResultsUpdated\n  | NotifyEventQueryObserverOptionsUpdated\n\ntype QueryCacheListener = (event: QueryCacheNotifyEvent) => void\n\nexport interface QueryStore {\n  has: (queryHash: string) => boolean\n  set: (queryHash: string, query: Query) => void\n  get: (queryHash: string) => Query | undefined\n  delete: (queryHash: string) => void\n  values: () => IterableIterator<Query>\n}\n\n// CLASS\n\nexport class QueryCache extends Subscribable<QueryCacheListener> {\n  #queries: QueryStore\n\n  constructor(public config: QueryCacheConfig = {}) {\n    super()\n    this.#queries = new Map<string, Query>()\n  }\n\n  build<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    client: QueryClient,\n    options: WithRequired<\n      QueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n      'queryKey'\n    >,\n    state?: QueryState<TData, TError>,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> {\n    const queryKey = options.queryKey\n    const queryHash =\n      options.queryHash ?? hashQueryKeyByOptions(queryKey, options)\n    let query = this.get<TQueryFnData, TError, TData, TQueryKey>(queryHash)\n\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n      })\n      this.add(query)\n    }\n\n    return query\n  }\n\n  add(query: Query<any, any, any, any>): void {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query)\n\n      this.notify({\n        type: 'added',\n        query,\n      })\n    }\n  }\n\n  remove(query: Query<any, any, any, any>): void {\n    const queryInMap = this.#queries.get(query.queryHash)\n\n    if (queryInMap) {\n      query.destroy()\n\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash)\n      }\n\n      this.notify({ type: 'removed', query })\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query)\n      })\n    })\n  }\n\n  get<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    queryHash: string,\n  ): Query<TQueryFnData, TError, TData, TQueryKey> | undefined {\n    return this.#queries.get(queryHash) as\n      | Query<TQueryFnData, TError, TData, TQueryKey>\n      | undefined\n  }\n\n  getAll(): Array<Query> {\n    return [...this.#queries.values()]\n  }\n\n  find<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData>(\n    filters: WithRequired<QueryFilters, 'queryKey'>,\n  ): Query<TQueryFnData, TError, TData> | undefined {\n    const defaultedFilters = { exact: true, ...filters }\n\n    return this.getAll().find((query) =>\n      matchQuery(defaultedFilters, query),\n    ) as Query<TQueryFnData, TError, TData> | undefined\n  }\n\n  findAll(filters: QueryFilters<any> = {}): Array<Query> {\n    const queries = this.getAll()\n    return Object.keys(filters).length > 0\n      ? queries.filter((query) => matchQuery(filters, query))\n      : queries\n  }\n\n  notify(event: QueryCacheNotifyEvent): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event)\n      })\n    })\n  }\n\n  onFocus(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus()\n      })\n    })\n  }\n\n  onOnline(): void {\n    notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline()\n      })\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,uBAAuB,kBAAkB;AAClD,SAAS,aAAa;AACtB,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;;;;;AAwFtB,IAAM,aAAN,6LAAyB,eAAA,CAAiC;IAG/D,YAAmB,SAA2B,CAAC,CAAA,CAAG;QAChD,KAAA,CAAM;QADW,IAAA,CAAA,MAAA,GAAA;QAEjB,IAAA,EAAK,OAAA,GAAW,aAAA,GAAA,IAAI,IAAmB;IACzC;KALA,OAAA,CAAA;IAOA,MAME,MAAA,EACA,OAAA,EAIA,KAAA,EAC+C;QAC/C,MAAM,WAAW,QAAQ,QAAA;QACzB,MAAM,YACJ,QAAQ,SAAA,gLAAa,wBAAA,EAAsB,UAAU,OAAO;QAC9D,IAAI,QAAQ,IAAA,CAAK,GAAA,CAA4C,SAAS;QAEtE,IAAI,CAAC,OAAO;YACV,QAAQ,4KAAI,QAAA,CAAM;gBAChB;gBACA;gBACA;gBACA,SAAS,OAAO,mBAAA,CAAoB,OAAO;gBAC3C;gBACA,gBAAgB,OAAO,gBAAA,CAAiB,QAAQ;YAClD,CAAC;YACD,IAAA,CAAK,GAAA,CAAI,KAAK;QAChB;QAEA,OAAO;IACT;IAEA,IAAI,KAAA,EAAwC;QAC1C,IAAI,CAAC,IAAA,EAAK,OAAA,CAAS,GAAA,CAAI,MAAM,SAAS,GAAG;YACvC,IAAA,EAAK,OAAA,CAAS,GAAA,CAAI,MAAM,SAAA,EAAW,KAAK;YAExC,IAAA,CAAK,MAAA,CAAO;gBACV,MAAM;gBACN;YACF,CAAC;QACH;IACF;IAEA,OAAO,KAAA,EAAwC;QAC7C,MAAM,aAAa,IAAA,EAAK,OAAA,CAAS,GAAA,CAAI,MAAM,SAAS;QAEpD,IAAI,YAAY;YACd,MAAM,OAAA,CAAQ;YAEd,IAAI,eAAe,OAAO;gBACxB,IAAA,EAAK,OAAA,CAAS,MAAA,CAAO,MAAM,SAAS;YACtC;YAEA,IAAA,CAAK,MAAA,CAAO;gBAAE,MAAM;gBAAW;YAAM,CAAC;QACxC;IACF;IAEA,QAAc;QACZ,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,MAAA,CAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBAC/B,IAAA,CAAK,MAAA,CAAO,KAAK;YACnB,CAAC;QACH,CAAC;IACH;IAEA,IAME,SAAA,EAC2D;QAC3D,OAAO,IAAA,EAAK,OAAA,CAAS,GAAA,CAAI,SAAS;IAGpC;IAEA,SAAuB;QACrB,OAAO,CAAC;eAAG,IAAA,EAAK,OAAA,CAAS,MAAA,CAAO,CAAC;SAAA;IACnC;IAEA,KACE,OAAA,EACgD;QAChD,MAAM,mBAAmB;YAAE,OAAO;YAAM,GAAG,OAAA;QAAQ;QAEnD,OAAO,IAAA,CAAK,MAAA,CAAO,EAAE,IAAA,CAAK,CAAC,oLACzB,aAAA,EAAW,kBAAkB,KAAK;IAEtC;IAEA,QAAQ,UAA6B,CAAC,CAAA,EAAiB;QACrD,MAAM,UAAU,IAAA,CAAK,MAAA,CAAO;QAC5B,OAAO,OAAO,IAAA,CAAK,OAAO,EAAE,MAAA,GAAS,IACjC,QAAQ,MAAA,CAAO,CAAC,SAAU,wLAAA,EAAW,SAAS,KAAK,CAAC,IACpD;IACN;IAEA,OAAO,KAAA,EAAoC;QACzC,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;gBACnC,SAAS,KAAK;YAChB,CAAC;QACH,CAAC;IACH;IAEA,UAAgB;QACd,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,MAAA,CAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBAC/B,MAAM,OAAA,CAAQ;YAChB,CAAC;QACH,CAAC;IACH;IAEA,WAAiB;QACf,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,MAAA,CAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBAC/B,MAAM,QAAA,CAAS;YACjB,CAAC;QACH,CAAC;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5517, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/mutation.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Removable } from './removable'\nimport { createR<PERSON>ry<PERSON> } from './retryer'\nimport type {\n  DefaultError,\n  MutationMeta,\n  MutationOptions,\n  MutationStatus,\n} from './types'\nimport type { MutationCache } from './mutationCache'\nimport type { MutationObserver } from './mutationObserver'\nimport type { <PERSON><PERSON><PERSON> } from './retryer'\n\n// TYPES\n\ninterface MutationConfig<TData, TError, TVariables, TContext> {\n  mutationId: number\n  mutationCache: MutationCache\n  options: MutationOptions<TData, TError, TVariables, TContext>\n  state?: MutationState<TData, TError, TVariables, TContext>\n}\n\nexport interface MutationState<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> {\n  context: TContext | undefined\n  data: TData | undefined\n  error: TError | null\n  failureCount: number\n  failureReason: TError | null\n  isPaused: boolean\n  status: MutationStatus\n  variables: TVariables | undefined\n  submittedAt: number\n}\n\ninterface FailedAction<TError> {\n  type: 'failed'\n  failureCount: number\n  error: TError | null\n}\n\ninterface PendingAction<TVariables, TContext> {\n  type: 'pending'\n  isPaused: boolean\n  variables?: TVariables\n  context?: TContext\n}\n\ninterface SuccessAction<TData> {\n  type: 'success'\n  data: TData\n}\n\ninterface ErrorAction<TError> {\n  type: 'error'\n  error: TError\n}\n\ninterface PauseAction {\n  type: 'pause'\n}\n\ninterface ContinueAction {\n  type: 'continue'\n}\n\nexport type Action<TData, TError, TVariables, TContext> =\n  | ContinueAction\n  | ErrorAction<TError>\n  | FailedAction<TError>\n  | PendingAction<TVariables, TContext>\n  | PauseAction\n  | SuccessAction<TData>\n\n// CLASS\n\nexport class Mutation<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = unknown,\n  TContext = unknown,\n> extends Removable {\n  state: MutationState<TData, TError, TVariables, TContext>\n  options!: MutationOptions<TData, TError, TVariables, TContext>\n  readonly mutationId: number\n\n  #observers: Array<MutationObserver<TData, TError, TVariables, TContext>>\n  #mutationCache: MutationCache\n  #retryer?: Retryer<TData>\n\n  constructor(config: MutationConfig<TData, TError, TVariables, TContext>) {\n    super()\n\n    this.mutationId = config.mutationId\n    this.#mutationCache = config.mutationCache\n    this.#observers = []\n    this.state = config.state || getDefaultState()\n\n    this.setOptions(config.options)\n    this.scheduleGc()\n  }\n\n  setOptions(\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n  ): void {\n    this.options = options\n\n    this.updateGcTime(this.options.gcTime)\n  }\n\n  get meta(): MutationMeta | undefined {\n    return this.options.meta\n  }\n\n  addObserver(observer: MutationObserver<any, any, any, any>): void {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer)\n\n      // Stop the mutation from being garbage collected\n      this.clearGcTimeout()\n\n      this.#mutationCache.notify({\n        type: 'observerAdded',\n        mutation: this,\n        observer,\n      })\n    }\n  }\n\n  removeObserver(observer: MutationObserver<any, any, any, any>): void {\n    this.#observers = this.#observers.filter((x) => x !== observer)\n\n    this.scheduleGc()\n\n    this.#mutationCache.notify({\n      type: 'observerRemoved',\n      mutation: this,\n      observer,\n    })\n  }\n\n  protected optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === 'pending') {\n        this.scheduleGc()\n      } else {\n        this.#mutationCache.remove(this)\n      }\n    }\n  }\n\n  continue(): Promise<unknown> {\n    return (\n      this.#retryer?.continue() ??\n      // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n      this.execute(this.state.variables!)\n    )\n  }\n\n  async execute(variables: TVariables): Promise<TData> {\n    const onContinue = () => {\n      this.#dispatch({ type: 'continue' })\n    }\n\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error('No mutationFn found'))\n        }\n        return this.options.mutationFn(variables)\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: 'failed', failureCount, error })\n      },\n      onPause: () => {\n        this.#dispatch({ type: 'pause' })\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this),\n    })\n\n    const restored = this.state.status === 'pending'\n    const isPaused = !this.#retryer.canStart()\n\n    try {\n      if (restored) {\n        // Dispatch continue action to unpause restored mutation\n        onContinue()\n      } else {\n        this.#dispatch({ type: 'pending', variables, isPaused })\n        // Notify cache callback\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n        const context = await this.options.onMutate?.(variables)\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: 'pending',\n            context,\n            variables,\n            isPaused,\n          })\n        }\n      }\n      const data = await this.#retryer.start()\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSuccess?.(data, variables, this.state.context!)\n\n      // Notify cache callback\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this as Mutation<unknown, unknown, unknown, unknown>,\n      )\n\n      await this.options.onSettled?.(data, null, variables, this.state.context)\n\n      this.#dispatch({ type: 'success', data })\n      return data\n    } catch (error) {\n      try {\n        // Notify cache callback\n        await this.#mutationCache.config.onError?.(\n          error as any,\n          variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onError?.(\n          error as TError,\n          variables,\n          this.state.context,\n        )\n\n        // Notify cache callback\n        await this.#mutationCache.config.onSettled?.(\n          undefined,\n          error as any,\n          this.state.variables,\n          this.state.context,\n          this as Mutation<unknown, unknown, unknown, unknown>,\n        )\n\n        await this.options.onSettled?.(\n          undefined,\n          error as TError,\n          variables,\n          this.state.context,\n        )\n        throw error\n      } finally {\n        this.#dispatch({ type: 'error', error: error as TError })\n      }\n    } finally {\n      this.#mutationCache.runNext(this)\n    }\n  }\n\n  #dispatch(action: Action<TData, TError, TVariables, TContext>): void {\n    const reducer = (\n      state: MutationState<TData, TError, TVariables, TContext>,\n    ): MutationState<TData, TError, TVariables, TContext> => {\n      switch (action.type) {\n        case 'failed':\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error,\n          }\n        case 'pause':\n          return {\n            ...state,\n            isPaused: true,\n          }\n        case 'continue':\n          return {\n            ...state,\n            isPaused: false,\n          }\n        case 'pending':\n          return {\n            ...state,\n            context: action.context,\n            data: undefined,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: 'pending',\n            variables: action.variables,\n            submittedAt: Date.now(),\n          }\n        case 'success':\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: 'success',\n            isPaused: false,\n          }\n        case 'error':\n          return {\n            ...state,\n            data: undefined,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: 'error',\n          }\n      }\n    }\n    this.state = reducer(this.state)\n\n    notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action)\n      })\n      this.#mutationCache.notify({\n        mutation: this,\n        type: 'updated',\n        action,\n      })\n    })\n  }\n}\n\nexport function getDefaultState<\n  TData,\n  TError,\n  TVariables,\n  TContext,\n>(): MutationState<TData, TError, TVariables, TContext> {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined,\n    submittedAt: 0,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,qBAAqB;AAC9B,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB;;;;AA8EvB,IAAM,WAAN,0LAKG,YAAA,CAAU;KAKlB,SAAA,CAAA;KACA,aAAA,CAAA;KACA,OAAA,CAAA;IAEA,YAAY,MAAA,CAA6D;QACvE,KAAA,CAAM;QAEN,IAAA,CAAK,UAAA,GAAa,OAAO,UAAA;QACzB,IAAA,EAAK,aAAA,GAAiB,OAAO,aAAA;QAC7B,IAAA,CAAK,UAAA,GAAa,CAAC,CAAA;QACnB,IAAA,CAAK,KAAA,GAAQ,OAAO,KAAA,IAAS,gBAAgB;QAE7C,IAAA,CAAK,UAAA,CAAW,OAAO,OAAO;QAC9B,IAAA,CAAK,UAAA,CAAW;IAClB;IAEA,WACE,OAAA,EACM;QACN,IAAA,CAAK,OAAA,GAAU;QAEf,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,OAAA,CAAQ,MAAM;IACvC;IAEA,IAAI,OAAiC;QACnC,OAAO,IAAA,CAAK,OAAA,CAAQ,IAAA;IACtB;IAEA,YAAY,QAAA,EAAsD;QAChE,IAAI,CAAC,IAAA,EAAK,SAAA,CAAW,QAAA,CAAS,QAAQ,GAAG;YACvC,IAAA,EAAK,SAAA,CAAW,IAAA,CAAK,QAAQ;YAG7B,IAAA,CAAK,cAAA,CAAe;YAEpB,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO;gBACzB,MAAM;gBACN,UAAU,IAAA;gBACV;YACF,CAAC;QACH;IACF;IAEA,eAAe,QAAA,EAAsD;QACnE,IAAA,CAAK,UAAA,GAAa,IAAA,EAAK,SAAA,CAAW,MAAA,CAAO,CAAC,IAAM,MAAM,QAAQ;QAE9D,IAAA,CAAK,UAAA,CAAW;QAEhB,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO;YACzB,MAAM;YACN,UAAU,IAAA;YACV;QACF,CAAC;IACH;IAEU,iBAAiB;QACzB,IAAI,CAAC,IAAA,CAAK,UAAA,CAAW,MAAA,EAAQ;YAC3B,IAAI,IAAA,CAAK,KAAA,CAAM,MAAA,KAAW,WAAW;gBACnC,IAAA,CAAK,UAAA,CAAW;YAClB,OAAO;gBACL,IAAA,CAAK,cAAA,CAAe,MAAA,CAAO,IAAI;YACjC;QACF;IACF;IAEA,WAA6B;QAC3B,OACE,IAAA,EAAK,OAAA,EAAU,SAAS,KAAA,kGAAA;QAExB,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,KAAA,CAAM,SAAU;IAEtC;IAEA,MAAM,QAAQ,SAAA,EAAuC;QACnD,MAAM,aAAa,MAAM;YACvB,IAAA,EAAK,QAAA,CAAU;gBAAE,MAAM;YAAW,CAAC;QACrC;QAEA,IAAA,EAAK,OAAA,IAAW,6LAAA,EAAc;YAC5B,IAAI,MAAM;gBACR,IAAI,CAAC,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY;oBAC5B,OAAO,QAAQ,MAAA,CAAO,IAAI,MAAM,qBAAqB,CAAC;gBACxD;gBACA,OAAO,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,SAAS;YAC1C;YACA,QAAQ,CAAC,cAAc,UAAU;gBAC/B,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;oBAAU;oBAAc;gBAAM,CAAC;YACxD;YACA,SAAS,MAAM;gBACb,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;gBAAQ,CAAC;YAClC;YACA;YACA,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAA,IAAS;YAC7B,YAAY,IAAA,CAAK,OAAA,CAAQ,UAAA;YACzB,aAAa,IAAA,CAAK,OAAA,CAAQ,WAAA;YAC1B,QAAQ,IAAM,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,IAAI;QAC/C,CAAC;QAED,MAAM,WAAW,IAAA,CAAK,KAAA,CAAM,MAAA,KAAW;QACvC,MAAM,WAAW,CAAC,IAAA,EAAK,OAAA,CAAS,QAAA,CAAS;QAEzC,IAAI;YACF,IAAI,UAAU;gBAEZ,WAAW;YACb,OAAO;gBACL,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;oBAAW;oBAAW;gBAAS,CAAC;gBAEvD,MAAM,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,QAAA,GAC/B,WACA,IAAA;gBAEF,MAAM,UAAU,MAAM,IAAA,CAAK,OAAA,CAAQ,QAAA,GAAW,SAAS;gBACvD,IAAI,YAAY,IAAA,CAAK,KAAA,CAAM,OAAA,EAAS;oBAClC,IAAA,EAAK,QAAA,CAAU;wBACb,MAAM;wBACN;wBACA;wBACA;oBACF,CAAC;gBACH;YACF;YACA,MAAM,OAAO,MAAM,IAAA,EAAK,OAAA,CAAS,KAAA,CAAM;YAGvC,MAAM,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,SAAA,GAC/B,MACA,WACA,IAAA,CAAK,KAAA,CAAM,OAAA,EACX,IAAA;YAGF,MAAM,IAAA,CAAK,OAAA,CAAQ,SAAA,GAAY,MAAM,WAAW,IAAA,CAAK,KAAA,CAAM,OAAQ;YAGnE,MAAM,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,SAAA,GAC/B,MACA,MACA,IAAA,CAAK,KAAA,CAAM,SAAA,EACX,IAAA,CAAK,KAAA,CAAM,OAAA,EACX,IAAA;YAGF,MAAM,IAAA,CAAK,OAAA,CAAQ,SAAA,GAAY,MAAM,MAAM,WAAW,IAAA,CAAK,KAAA,CAAM,OAAO;YAExE,IAAA,EAAK,QAAA,CAAU;gBAAE,MAAM;gBAAW;YAAK,CAAC;YACxC,OAAO;QACT,EAAA,OAAS,OAAO;YACd,IAAI;gBAEF,MAAM,IAAA,CAAK,cAAA,CAAe,MAAA,CAAO,OAAA,GAC/B,OACA,WACA,IAAA,CAAK,KAAA,CAAM,OAAA,EACX,IAAA;gBAGF,MAAM,IAAA,CAAK,OAAA,CAAQ,OAAA,GACjB,OACA,WACA,IAAA,CAAK,KAAA,CAAM,OAAA;gBAIb,MAAM,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,SAAA,GAC/B,KAAA,GACA,OACA,IAAA,CAAK,KAAA,CAAM,SAAA,EACX,IAAA,CAAK,KAAA,CAAM,OAAA,EACX,IAAA;gBAGF,MAAM,IAAA,CAAK,OAAA,CAAQ,SAAA,GACjB,KAAA,GACA,OACA,WACA,IAAA,CAAK,KAAA,CAAM,OAAA;gBAEb,MAAM;YACR,SAAE;gBACA,IAAA,EAAK,QAAA,CAAU;oBAAE,MAAM;oBAAS;gBAAuB,CAAC;YAC1D;QACF,SAAE;YACA,IAAA,EAAK,aAAA,CAAe,OAAA,CAAQ,IAAI;QAClC;IACF;IAEA,SAAA,CAAU,MAAA,EAA2D;QACnE,MAAM,UAAU,CACd,UACuD;YACvD,OAAQ,OAAO,IAAA,EAAM;gBACnB,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,cAAc,OAAO,YAAA;wBACrB,eAAe,OAAO,KAAA;oBACxB;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,UAAU;oBACZ;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,UAAU;oBACZ;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,SAAS,OAAO,OAAA;wBAChB,MAAM,KAAA;wBACN,cAAc;wBACd,eAAe;wBACf,OAAO;wBACP,UAAU,OAAO,QAAA;wBACjB,QAAQ;wBACR,WAAW,OAAO,SAAA;wBAClB,aAAa,KAAK,GAAA,CAAI;oBACxB;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,MAAM,OAAO,IAAA;wBACb,cAAc;wBACd,eAAe;wBACf,OAAO;wBACP,QAAQ;wBACR,UAAU;oBACZ;gBACF,KAAK;oBACH,OAAO;wBACL,GAAG,KAAA;wBACH,MAAM,KAAA;wBACN,OAAO,OAAO,KAAA;wBACd,cAAc,MAAM,YAAA,GAAe;wBACnC,eAAe,OAAO,KAAA;wBACtB,UAAU;wBACV,QAAQ;oBACV;YACJ;QACF;QACA,IAAA,CAAK,KAAA,GAAQ,QAAQ,IAAA,CAAK,KAAK;QAE/B,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,EAAK,SAAA,CAAW,OAAA,CAAQ,CAAC,aAAa;gBACpC,SAAS,gBAAA,CAAiB,MAAM;YAClC,CAAC;YACD,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO;gBACzB,UAAU,IAAA;gBACV,MAAM;gBACN;YACF,CAAC;QACH,CAAC;IACH;AACF;AAEO,SAAS,kBAKwC;IACtD,OAAO;QACL,SAAS,KAAA;QACT,MAAM,KAAA;QACN,OAAO;QACP,cAAc;QACd,eAAe;QACf,UAAU;QACV,QAAQ;QACR,WAAW,KAAA;QACX,aAAa;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5749, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/mutationCache.ts"], "sourcesContent": ["import { notify<PERSON><PERSON><PERSON> } from './notifyManager'\nimport { Mutation } from './mutation'\nimport { matchMutation, noop } from './utils'\nimport { Subscribable } from './subscribable'\nimport type { MutationObserver } from './mutationObserver'\nimport type { DefaultError, MutationOptions, NotifyEvent } from './types'\nimport type { QueryClient } from './queryClient'\nimport type { Action, MutationState } from './mutation'\nimport type { MutationFilters } from './utils'\n\n// TYPES\n\ninterface MutationCacheConfig {\n  onError?: (\n    error: DefaultError,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSuccess?: (\n    data: unknown,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onMutate?: (\n    variables: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n  onSettled?: (\n    data: unknown | undefined,\n    error: DefaultError | null,\n    variables: unknown,\n    context: unknown,\n    mutation: Mutation<unknown, unknown, unknown>,\n  ) => Promise<unknown> | unknown\n}\n\ninterface NotifyEventMutationAdded extends NotifyEvent {\n  type: 'added'\n  mutation: Mutation<any, any, any, any>\n}\ninterface NotifyEventMutationRemoved extends NotifyEvent {\n  type: 'removed'\n  mutation: Mutation<any, any, any, any>\n}\n\ninterface NotifyEventMutationObserverAdded extends NotifyEvent {\n  type: 'observerAdded'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverRemoved extends NotifyEvent {\n  type: 'observerRemoved'\n  mutation: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any>\n}\n\ninterface NotifyEventMutationObserverOptionsUpdated extends NotifyEvent {\n  type: 'observerOptionsUpdated'\n  mutation?: Mutation<any, any, any, any>\n  observer: MutationObserver<any, any, any, any>\n}\n\ninterface NotifyEventMutationUpdated extends NotifyEvent {\n  type: 'updated'\n  mutation: Mutation<any, any, any, any>\n  action: Action<any, any, any, any>\n}\n\nexport type MutationCacheNotifyEvent =\n  | NotifyEventMutationAdded\n  | NotifyEventMutationRemoved\n  | NotifyEventMutationObserverAdded\n  | NotifyEventMutationObserverRemoved\n  | NotifyEventMutationObserverOptionsUpdated\n  | NotifyEventMutationUpdated\n\ntype MutationCacheListener = (event: MutationCacheNotifyEvent) => void\n\n// CLASS\n\nexport class MutationCache extends Subscribable<MutationCacheListener> {\n  #mutations: Set<Mutation<any, any, any, any>>\n  #scopes: Map<string, Array<Mutation<any, any, any, any>>>\n  #mutationId: number\n\n  constructor(public config: MutationCacheConfig = {}) {\n    super()\n    this.#mutations = new Set()\n    this.#scopes = new Map()\n    this.#mutationId = 0\n  }\n\n  build<TData, TError, TVariables, TContext>(\n    client: QueryClient,\n    options: MutationOptions<TData, TError, TVariables, TContext>,\n    state?: MutationState<TData, TError, TVariables, TContext>,\n  ): Mutation<TData, TError, TVariables, TContext> {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state,\n    })\n\n    this.add(mutation)\n\n    return mutation\n  }\n\n  add(mutation: Mutation<any, any, any, any>): void {\n    this.#mutations.add(mutation)\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const scopedMutations = this.#scopes.get(scope)\n      if (scopedMutations) {\n        scopedMutations.push(mutation)\n      } else {\n        this.#scopes.set(scope, [mutation])\n      }\n    }\n    this.notify({ type: 'added', mutation })\n  }\n\n  remove(mutation: Mutation<any, any, any, any>): void {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation)\n      if (typeof scope === 'string') {\n        const scopedMutations = this.#scopes.get(scope)\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation)\n            if (index !== -1) {\n              scopedMutations.splice(index, 1)\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope)\n          }\n        }\n      }\n    }\n\n    // Currently we notify the removal even if the mutation was already removed.\n    // Consider making this an error or not notifying of the removal depending on the desired semantics.\n    this.notify({ type: 'removed', mutation })\n  }\n\n  canRun(mutation: Mutation<any, any, any, any>): boolean {\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const mutationsWithSameScope = this.#scopes.get(scope)\n      const firstPendingMutation = mutationsWithSameScope?.find(\n        (m) => m.state.status === 'pending',\n      )\n      // we can run if there is no current pending mutation (start use-case)\n      // or if WE are the first pending mutation (continue use-case)\n      return !firstPendingMutation || firstPendingMutation === mutation\n    } else {\n      // For unscoped mutations there are never any pending mutations in front of the\n      // current mutation\n      return true\n    }\n  }\n\n  runNext(mutation: Mutation<any, any, any, any>): Promise<unknown> {\n    const scope = scopeFor(mutation)\n    if (typeof scope === 'string') {\n      const foundMutation = this.#scopes\n        .get(scope)\n        ?.find((m) => m !== mutation && m.state.isPaused)\n\n      return foundMutation?.continue() ?? Promise.resolve()\n    } else {\n      return Promise.resolve()\n    }\n  }\n\n  clear(): void {\n    notifyManager.batch(() => {\n      this.#mutations.forEach((mutation) => {\n        this.notify({ type: 'removed', mutation })\n      })\n      this.#mutations.clear()\n      this.#scopes.clear()\n    })\n  }\n\n  getAll(): Array<Mutation> {\n    return Array.from(this.#mutations)\n  }\n\n  find<\n    TData = unknown,\n    TError = DefaultError,\n    TVariables = any,\n    TContext = unknown,\n  >(\n    filters: MutationFilters,\n  ): Mutation<TData, TError, TVariables, TContext> | undefined {\n    const defaultedFilters = { exact: true, ...filters }\n\n    return this.getAll().find((mutation) =>\n      matchMutation(defaultedFilters, mutation),\n    ) as Mutation<TData, TError, TVariables, TContext> | undefined\n  }\n\n  findAll(filters: MutationFilters = {}): Array<Mutation> {\n    return this.getAll().filter((mutation) => matchMutation(filters, mutation))\n  }\n\n  notify(event: MutationCacheNotifyEvent) {\n    notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event)\n      })\n    })\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused)\n\n    return notifyManager.batch(() =>\n      Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(noop)),\n      ),\n    )\n  }\n}\n\nfunction scopeFor(mutation: Mutation<any, any, any, any>) {\n  return mutation.options.scope?.id\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,qBAAqB;AAC9B,SAAS,gBAAgB;AACzB,SAAS,eAAe,YAAY;AACpC,SAAS,oBAAoB;;;;;AAgFtB,IAAM,gBAAN,6LAA4B,eAAA,CAAoC;IAKrE,YAAmB,SAA8B,CAAC,CAAA,CAAG;QACnD,KAAA,CAAM;QADW,IAAA,CAAA,MAAA,GAAA;QAEjB,IAAA,EAAK,SAAA,GAAa,aAAA,GAAA,IAAI,IAAI;QAC1B,IAAA,EAAK,MAAA,GAAU,aAAA,GAAA,IAAI,IAAI;QACvB,IAAA,EAAK,UAAA,GAAc;IACrB;KATA,SAAA,CAAA;IACA,OAAA,CAAA;KACA,UAAA,CAAA;IASA,MACE,MAAA,EACA,OAAA,EACA,KAAA,EAC+C;QAC/C,MAAM,WAAW,+KAAI,WAAA,CAAS;YAC5B,eAAe,IAAA;YACf,YAAY,EAAE,IAAA,EAAK,UAAA;YACnB,SAAS,OAAO,sBAAA,CAAuB,OAAO;YAC9C;QACF,CAAC;QAED,IAAA,CAAK,GAAA,CAAI,QAAQ;QAEjB,OAAO;IACT;IAEA,IAAI,QAAA,EAA8C;QAChD,IAAA,EAAK,SAAA,CAAW,GAAA,CAAI,QAAQ;QAC5B,MAAM,QAAQ,SAAS,QAAQ;QAC/B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,kBAAkB,IAAA,EAAK,MAAA,CAAQ,GAAA,CAAI,KAAK;YAC9C,IAAI,iBAAiB;gBACnB,gBAAgB,IAAA,CAAK,QAAQ;YAC/B,OAAO;gBACL,IAAA,EAAK,MAAA,CAAQ,GAAA,CAAI,OAAO;oBAAC,QAAQ;iBAAC;YACpC;QACF;QACA,IAAA,CAAK,MAAA,CAAO;YAAE,MAAM;YAAS;QAAS,CAAC;IACzC;IAEA,OAAO,QAAA,EAA8C;QACnD,IAAI,IAAA,EAAK,SAAA,CAAW,MAAA,CAAO,QAAQ,GAAG;YACpC,MAAM,QAAQ,SAAS,QAAQ;YAC/B,IAAI,OAAO,UAAU,UAAU;gBAC7B,MAAM,kBAAkB,IAAA,EAAK,MAAA,CAAQ,GAAA,CAAI,KAAK;gBAC9C,IAAI,iBAAiB;oBACnB,IAAI,gBAAgB,MAAA,GAAS,GAAG;wBAC9B,MAAM,QAAQ,gBAAgB,OAAA,CAAQ,QAAQ;wBAC9C,IAAI,UAAU,CAAA,GAAI;4BAChB,gBAAgB,MAAA,CAAO,OAAO,CAAC;wBACjC;oBACF,OAAA,IAAW,eAAA,CAAgB,CAAC,CAAA,KAAM,UAAU;wBAC1C,IAAA,EAAK,MAAA,CAAQ,MAAA,CAAO,KAAK;oBAC3B;gBACF;YACF;QACF;QAIA,IAAA,CAAK,MAAA,CAAO;YAAE,MAAM;YAAW;QAAS,CAAC;IAC3C;IAEA,OAAO,QAAA,EAAiD;QACtD,MAAM,QAAQ,SAAS,QAAQ;QAC/B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,yBAAyB,IAAA,EAAK,MAAA,CAAQ,GAAA,CAAI,KAAK;YACrD,MAAM,uBAAuB,wBAAwB,KACnD,CAAC,IAAM,EAAE,KAAA,CAAM,MAAA,KAAW;YAI5B,OAAO,CAAC,wBAAwB,yBAAyB;QAC3D,OAAO;YAGL,OAAO;QACT;IACF;IAEA,QAAQ,QAAA,EAA0D;QAChE,MAAM,QAAQ,SAAS,QAAQ;QAC/B,IAAI,OAAO,UAAU,UAAU;YAC7B,MAAM,gBAAgB,IAAA,EAAK,MAAA,CACxB,GAAA,CAAI,KAAK,GACR,KAAK,CAAC,IAAM,MAAM,YAAY,EAAE,KAAA,CAAM,QAAQ;YAElD,OAAO,eAAe,SAAS,KAAK,QAAQ,OAAA,CAAQ;QACtD,OAAO;YACL,OAAO,QAAQ,OAAA,CAAQ;QACzB;IACF;IAEA,QAAc;QACZ,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,EAAK,SAAA,CAAW,OAAA,CAAQ,CAAC,aAAa;gBACpC,IAAA,CAAK,MAAA,CAAO;oBAAE,MAAM;oBAAW;gBAAS,CAAC;YAC3C,CAAC;YACD,IAAA,EAAK,SAAA,CAAW,KAAA,CAAM;YACtB,IAAA,EAAK,MAAA,CAAQ,KAAA,CAAM;QACrB,CAAC;IACH;IAEA,SAA0B;QACxB,OAAO,MAAM,IAAA,CAAK,IAAA,EAAK,SAAU;IACnC;IAEA,KAME,OAAA,EAC2D;QAC3D,MAAM,mBAAmB;YAAE,OAAO;YAAM,GAAG,OAAA;QAAQ;QAEnD,OAAO,IAAA,CAAK,MAAA,CAAO,EAAE,IAAA,CAAK,CAAC,uLACzB,gBAAA,EAAc,kBAAkB,QAAQ;IAE5C;IAEA,QAAQ,UAA2B,CAAC,CAAA,EAAoB;QACtD,OAAO,IAAA,CAAK,MAAA,CAAO,EAAE,MAAA,CAAO,CAAC,uLAAa,gBAAA,EAAc,SAAS,QAAQ,CAAC;IAC5E;IAEA,OAAO,KAAA,EAAiC;QACtC,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,CAAC,aAAa;gBACnC,SAAS,KAAK;YAChB,CAAC;QACH,CAAC;IACH;IAEA,wBAA0C;QACxC,MAAM,kBAAkB,IAAA,CAAK,MAAA,CAAO,EAAE,MAAA,CAAO,CAAC,IAAM,EAAE,KAAA,CAAM,QAAQ;QAEpE,uLAAO,gBAAA,CAAc,KAAA,CAAM,IACzB,QAAQ,GAAA,CACN,gBAAgB,GAAA,CAAI,CAAC,WAAa,SAAS,QAAA,CAAS,EAAE,KAAA,yKAAM,OAAI,CAAC;IAGvE;AACF;AAEA,SAAS,SAAS,QAAA,EAAwC;IACxD,OAAO,SAAS,OAAA,CAAQ,KAAA,EAAO;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5889, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/infiniteQueryBehavior.ts"], "sourcesContent": ["import { addToEnd, addToStart, ensureQueryFn } from './utils'\nimport type { QueryBehavior } from './query'\nimport type {\n  InfiniteData,\n  InfiniteQueryPageParamsOptions,\n  OmitKeyof,\n  QueryFunctionContext,\n  QueryKey,\n} from './types'\n\nexport function infiniteQueryBehavior<TQueryFnData, TError, TData, TPageParam>(\n  pages?: number,\n): QueryBehavior<TQueryFnData, TError, InfiniteData<TData, TPageParam>> {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options as InfiniteQueryPageParamsOptions<TData>\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction\n      const oldPages = context.state.data?.pages || []\n      const oldPageParams = context.state.data?.pageParams || []\n      let result: InfiniteData<unknown> = { pages: [], pageParams: [] }\n      let currentPage = 0\n\n      const fetchFn = async () => {\n        let cancelled = false\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true\n              } else {\n                context.signal.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions)\n\n        // Create function to fetch a page\n        const fetchPage = async (\n          data: InfiniteData<unknown>,\n          param: unknown,\n          previous?: boolean,\n        ): Promise<InfiniteData<unknown>> => {\n          if (cancelled) {\n            return Promise.reject()\n          }\n\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data)\n          }\n\n          const createQueryFnContext = () => {\n            const queryFnContext: OmitKeyof<\n              QueryFunctionContext<QueryKey, unknown>,\n              'signal'\n            > = {\n              client: context.client,\n              queryKey: context.queryKey,\n              pageParam: param,\n              direction: previous ? 'backward' : 'forward',\n              meta: context.options.meta,\n            }\n            addSignalProperty(queryFnContext)\n            return queryFnContext as QueryFunctionContext<QueryKey, unknown>\n          }\n\n          const queryFnContext = createQueryFnContext()\n\n          const page = await queryFn(queryFnContext)\n\n          const { maxPages } = context.options\n          const addTo = previous ? addToStart : addToEnd\n\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages),\n          }\n        }\n\n        // fetch next / previous page?\n        if (direction && oldPages.length) {\n          const previous = direction === 'backward'\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams,\n          }\n          const param = pageParamFn(options, oldData)\n\n          result = await fetchPage(oldData, param, previous)\n        } else {\n          const remainingPages = pages ?? oldPages.length\n\n          // Fetch all pages\n          do {\n            const param =\n              currentPage === 0\n                ? (oldPageParams[0] ?? options.initialPageParam)\n                : getNextPageParam(options, result)\n            if (currentPage > 0 && param == null) {\n              break\n            }\n            result = await fetchPage(result, param)\n            currentPage++\n          } while (currentPage < remainingPages)\n        }\n\n        return result\n      }\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn as any,\n            {\n              client: context.client,\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal,\n            },\n            query,\n          )\n        }\n      } else {\n        context.fetchFn = fetchFn\n      }\n    },\n  }\n}\n\nfunction getNextPageParam(\n  options: InfiniteQueryPageParamsOptions<any>,\n  { pages, pageParams }: InfiniteData<unknown>,\n): unknown | undefined {\n  const lastIndex = pages.length - 1\n  return pages.length > 0\n    ? options.getNextPageParam(\n        pages[lastIndex],\n        pages,\n        pageParams[lastIndex],\n        pageParams,\n      )\n    : undefined\n}\n\nfunction getPreviousPageParam(\n  options: InfiniteQueryPageParamsOptions<any>,\n  { pages, pageParams }: InfiniteData<unknown>,\n): unknown | undefined {\n  return pages.length > 0\n    ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams)\n    : undefined\n}\n\n/**\n * Checks if there is a next page.\n */\nexport function hasNextPage(\n  options: InfiniteQueryPageParamsOptions<any, any>,\n  data?: InfiniteData<unknown>,\n): boolean {\n  if (!data) return false\n  return getNextPageParam(options, data) != null\n}\n\n/**\n * Checks if there is a previous page.\n */\nexport function hasPreviousPage(\n  options: InfiniteQueryPageParamsOptions<any, any>,\n  data?: InfiniteData<unknown>,\n): boolean {\n  if (!data || !options.getPreviousPageParam) return false\n  return getPreviousPageParam(options, data) != null\n}\n"], "names": ["queryFnContext"], "mappings": ";;;;;;AAAA,SAAS,UAAU,YAAY,qBAAqB;;AAU7C,SAAS,sBACd,KAAA,EACsE;IACtE,OAAO;QACL,SAAS,CAAC,SAAS,UAAU;YAC3B,MAAM,UAAU,QAAQ,OAAA;YACxB,MAAM,YAAY,QAAQ,YAAA,EAAc,MAAM,WAAW;YACzD,MAAM,WAAW,QAAQ,KAAA,CAAM,IAAA,EAAM,SAAS,CAAC,CAAA;YAC/C,MAAM,gBAAgB,QAAQ,KAAA,CAAM,IAAA,EAAM,cAAc,CAAC,CAAA;YACzD,IAAI,SAAgC;gBAAE,OAAO,CAAC,CAAA;gBAAG,YAAY,CAAC,CAAA;YAAE;YAChE,IAAI,cAAc;YAElB,MAAM,UAAU,YAAY;gBAC1B,IAAI,YAAY;gBAChB,MAAM,oBAAoB,CAAC,WAAoB;oBAC7C,OAAO,cAAA,CAAe,QAAQ,UAAU;wBACtC,YAAY;wBACZ,KAAK,MAAM;4BACT,IAAI,QAAQ,MAAA,CAAO,OAAA,EAAS;gCAC1B,YAAY;4BACd,OAAO;gCACL,QAAQ,MAAA,CAAO,gBAAA,CAAiB,SAAS,MAAM;oCAC7C,YAAY;gCACd,CAAC;4BACH;4BACA,OAAO,QAAQ,MAAA;wBACjB;oBACF,CAAC;gBACH;gBAEA,MAAM,cAAU,wLAAA,EAAc,QAAQ,OAAA,EAAS,QAAQ,YAAY;gBAGnE,MAAM,YAAY,OAChB,MACA,OACA,aACmC;oBACnC,IAAI,WAAW;wBACb,OAAO,QAAQ,MAAA,CAAO;oBACxB;oBAEA,IAAI,SAAS,QAAQ,KAAK,KAAA,CAAM,MAAA,EAAQ;wBACtC,OAAO,QAAQ,OAAA,CAAQ,IAAI;oBAC7B;oBAEA,MAAM,uBAAuB,MAAM;wBACjC,MAAMA,kBAGF;4BACF,QAAQ,QAAQ,MAAA;4BAChB,UAAU,QAAQ,QAAA;4BAClB,WAAW;4BACX,WAAW,WAAW,aAAa;4BACnC,MAAM,QAAQ,OAAA,CAAQ,IAAA;wBACxB;wBACA,kBAAkBA,eAAc;wBAChC,OAAOA;oBACT;oBAEA,MAAM,iBAAiB,qBAAqB;oBAE5C,MAAM,OAAO,MAAM,QAAQ,cAAc;oBAEzC,MAAM,EAAE,QAAA,CAAS,CAAA,GAAI,QAAQ,OAAA;oBAC7B,MAAM,QAAQ,mLAAW,aAAA,0KAAa,YAAA;oBAEtC,OAAO;wBACL,OAAO,MAAM,KAAK,KAAA,EAAO,MAAM,QAAQ;wBACvC,YAAY,MAAM,KAAK,UAAA,EAAY,OAAO,QAAQ;oBACpD;gBACF;gBAGA,IAAI,aAAa,SAAS,MAAA,EAAQ;oBAChC,MAAM,WAAW,cAAc;oBAC/B,MAAM,cAAc,WAAW,uBAAuB;oBACtD,MAAM,UAAU;wBACd,OAAO;wBACP,YAAY;oBACd;oBACA,MAAM,QAAQ,YAAY,SAAS,OAAO;oBAE1C,SAAS,MAAM,UAAU,SAAS,OAAO,QAAQ;gBACnD,OAAO;oBACL,MAAM,iBAAiB,SAAS,SAAS,MAAA;oBAGzC,GAAG;wBACD,MAAM,QACJ,gBAAgB,IACX,aAAA,CAAc,CAAC,CAAA,IAAK,QAAQ,gBAAA,GAC7B,iBAAiB,SAAS,MAAM;wBACtC,IAAI,cAAc,KAAK,SAAS,MAAM;4BACpC;wBACF;wBACA,SAAS,MAAM,UAAU,QAAQ,KAAK;wBACtC;oBACF,QAAS,cAAc,eAAA;gBACzB;gBAEA,OAAO;YACT;YACA,IAAI,QAAQ,OAAA,CAAQ,SAAA,EAAW;gBAC7B,QAAQ,OAAA,GAAU,MAAM;oBACtB,OAAO,QAAQ,OAAA,CAAQ,SAAA,GACrB,SACA;wBACE,QAAQ,QAAQ,MAAA;wBAChB,UAAU,QAAQ,QAAA;wBAClB,MAAM,QAAQ,OAAA,CAAQ,IAAA;wBACtB,QAAQ,QAAQ,MAAA;oBAClB,GACA;gBAEJ;YACF,OAAO;gBACL,QAAQ,OAAA,GAAU;YACpB;QACF;IACF;AACF;AAEA,SAAS,iBACP,OAAA,EACA,EAAE,KAAA,EAAO,UAAA,CAAW,CAAA,EACC;IACrB,MAAM,YAAY,MAAM,MAAA,GAAS;IACjC,OAAO,MAAM,MAAA,GAAS,IAClB,QAAQ,gBAAA,CACN,KAAA,CAAM,SAAS,CAAA,EACf,OACA,UAAA,CAAW,SAAS,CAAA,EACpB,cAEF,KAAA;AACN;AAEA,SAAS,qBACP,OAAA,EACA,EAAE,KAAA,EAAO,UAAA,CAAW,CAAA,EACC;IACrB,OAAO,MAAM,MAAA,GAAS,IAClB,QAAQ,oBAAA,GAAuB,KAAA,CAAM,CAAC,CAAA,EAAG,OAAO,UAAA,CAAW,CAAC,CAAA,EAAG,UAAU,IACzE,KAAA;AACN;AAKO,SAAS,YACd,OAAA,EACA,IAAA,EACS;IACT,IAAI,CAAC,KAAM,CAAA,OAAO;IAClB,OAAO,iBAAiB,SAAS,IAAI,KAAK;AAC5C;AAKO,SAAS,gBACd,OAAA,EACA,IAAA,EACS;IACT,IAAI,CAAC,QAAQ,CAAC,QAAQ,oBAAA,CAAsB,CAAA,OAAO;IACnD,OAAO,qBAAqB,SAAS,IAAI,KAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6014, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/query-core/src/queryClient.ts"], "sourcesContent": ["import {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken,\n} from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport type {\n  CancelOptions,\n  DefaultError,\n  DefaultOptions,\n  DefaultedQueryObserverOptions,\n  EnsureInfiniteQueryDataOptions,\n  EnsureQueryDataOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InferDataFromTag,\n  InferErrorFromTag,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  NoInfer,\n  OmitKeyof,\n  QueryClientConfig,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  SetDataOptions,\n} from './types'\nimport type { QueryState } from './query'\nimport type { MutationFilters, QueryFilters, Updater } from './utils'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: OmitKeyof<QueryOptions<any, any, any>, 'queryKey'>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  #queryCache: QueryCache\n  #mutationCache: MutationCache\n  #defaultOptions: DefaultOptions\n  #queryDefaults: Map<string, QueryDefaults>\n  #mutationDefaults: Map<string, MutationDefaults>\n  #mountCount: number\n  #unsubscribeFocus?: () => void\n  #unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.#queryCache = config.queryCache || new QueryCache()\n    this.#mutationCache = config.mutationCache || new MutationCache()\n    this.#defaultOptions = config.defaultOptions || {}\n    this.#queryDefaults = new Map()\n    this.#mutationDefaults = new Map()\n    this.#mountCount = 0\n  }\n\n  mount(): void {\n    this.#mountCount++\n    if (this.#mountCount !== 1) return\n\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations()\n        this.#queryCache.onFocus()\n      }\n    })\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations()\n        this.#queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.#mountCount--\n    if (this.#mountCount !== 0) return\n\n    this.#unsubscribeFocus?.()\n    this.#unsubscribeFocus = undefined\n\n    this.#unsubscribeOnline?.()\n    this.#unsubscribeOnline = undefined\n  }\n\n  isFetching<TQueryFilters extends QueryFilters<any> = QueryFilters>(\n    filters?: TQueryFilters,\n  ): number {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: 'fetching' })\n      .length\n  }\n\n  isMutating<\n    TMutationFilters extends MutationFilters<any, any> = MutationFilters,\n  >(filters?: TMutationFilters): number {\n    return this.#mutationCache.findAll({ ...filters, status: 'pending' }).length\n  }\n\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData<\n    TQueryFnData = unknown,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n  >(queryKey: TTaggedQueryKey): TInferredQueryFnData | undefined {\n    const options = this.defaultQueryOptions({ queryKey })\n\n    return this.#queryCache.get<TInferredQueryFnData>(options.queryHash)?.state\n      .data\n  }\n\n  ensureQueryData<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: EnsureQueryDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const defaultedOptions = this.defaultQueryOptions(options)\n    const query = this.#queryCache.build(this, defaultedOptions)\n    const cachedData = query.state.data\n\n    if (cachedData === undefined) {\n      return this.fetchQuery(options)\n    }\n\n    if (\n      options.revalidateIfStale &&\n      query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))\n    ) {\n      void this.prefetchQuery(defaultedOptions)\n    }\n\n    return Promise.resolve(cachedData)\n  }\n\n  getQueriesData<\n    TQueryFnData = unknown,\n    TQueryFilters extends QueryFilters<any> = QueryFilters,\n  >(filters: TQueryFilters): Array<[QueryKey, TQueryFnData | undefined]> {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data as TQueryFnData | undefined\n      return [queryKey, data]\n    })\n  }\n\n  setQueryData<\n    TQueryFnData = unknown,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n  >(\n    queryKey: TTaggedQueryKey,\n    updater: Updater<\n      NoInfer<TInferredQueryFnData> | undefined,\n      NoInfer<TInferredQueryFnData> | undefined\n    >,\n    options?: SetDataOptions,\n  ): NoInfer<TInferredQueryFnData> | undefined {\n    const defaultedOptions = this.defaultQueryOptions<\n      any,\n      any,\n      unknown,\n      any,\n      QueryKey\n    >({ queryKey })\n\n    const query = this.#queryCache.get<TInferredQueryFnData>(\n      defaultedOptions.queryHash,\n    )\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (data === undefined) {\n      return undefined\n    }\n\n    return this.#queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<\n    TQueryFnData,\n    TQueryFilters extends QueryFilters<any> = QueryFilters,\n  >(\n    filters: TQueryFilters,\n    updater: Updater<\n      NoInfer<TQueryFnData> | undefined,\n      NoInfer<TQueryFnData> | undefined\n    >,\n    options?: SetDataOptions,\n  ): Array<[QueryKey, TQueryFnData | undefined]> {\n    return notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n    TInferredError = InferErrorFromTag<TError, TTaggedQueryKey>,\n  >(\n    queryKey: TTaggedQueryKey,\n  ): QueryState<TInferredQueryFnData, TInferredError> | undefined {\n    const options = this.defaultQueryOptions({ queryKey })\n    return this.#queryCache.get<TInferredQueryFnData, TInferredError>(\n      options.queryHash,\n    )?.state\n  }\n\n  removeQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n  ): void {\n    const queryCache = this.#queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n    options?: ResetOptions,\n  ): Promise<void> {\n    const queryCache = this.#queryCache\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(\n        {\n          type: 'active',\n          ...filters,\n        },\n        options,\n      )\n    })\n  }\n\n  cancelQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n    cancelOptions: CancelOptions = {},\n  ): Promise<void> {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions }\n\n    const promises = notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(defaultedCancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: InvalidateQueryFilters<TTaggedQueryKey>,\n    options: InvalidateOptions = {},\n  ): Promise<void> {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters?.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? 'active',\n        },\n        options,\n      )\n    })\n  }\n\n  refetchQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: RefetchQueryFilters<TTaggedQueryKey>,\n    options: RefetchOptions = {},\n  ): Promise<void> {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true,\n    }\n    const promises = notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled() && !query.isStatic())\n        .map((query) => {\n          let promise = query.fetch(undefined, fetchOptions)\n          if (!fetchOptions.throwOnError) {\n            promise = promise.catch(noop)\n          }\n          return query.state.fetchStatus === 'paused'\n            ? Promise.resolve()\n            : promise\n        }),\n    )\n\n    return Promise.all(promises).then(noop)\n  }\n\n  fetchQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = never,\n  >(\n    options: FetchQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<TData> {\n    const defaultedOptions = this.defaultQueryOptions(options)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (defaultedOptions.retry === undefined) {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.#queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query),\n    )\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void> {\n    return this.fetchQuery(options).then(noop).catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: FetchInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<InfiniteData<TData, TPageParam>> {\n    options.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData,\n      TPageParam\n    >(options.pages)\n    return this.fetchQuery(options as any)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: FetchInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop)\n  }\n\n  ensureInfiniteQueryData<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: EnsureInfiniteQueryDataOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<InfiniteData<TData, TPageParam>> {\n    options.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData,\n      TPageParam\n    >(options.pages)\n\n    return this.ensureQueryData(options as any)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations()\n    }\n    return Promise.resolve()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.#queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.#mutationCache\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.#defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.#defaultOptions = options\n  }\n\n  setQueryDefaults<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryData = TQueryFnData,\n  >(\n    queryKey: QueryKey,\n    options: Partial<\n      OmitKeyof<\n        QueryObserverOptions<TQueryFnData, TError, TData, TQueryData>,\n        'queryKey'\n      >\n    >,\n  ): void {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options,\n    })\n  }\n\n  getQueryDefaults(\n    queryKey: QueryKey,\n  ): OmitKeyof<QueryObserverOptions<any, any, any, any, any>, 'queryKey'> {\n    const defaults = [...this.#queryDefaults.values()]\n\n    const result: OmitKeyof<\n      QueryObserverOptions<any, any, any, any, any>,\n      'queryKey'\n    > = {}\n\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions)\n      }\n    })\n    return result\n  }\n\n  setMutationDefaults<\n    TData = unknown,\n    TError = DefaultError,\n    TVariables = void,\n    TContext = unknown,\n  >(\n    mutationKey: MutationKey,\n    options: OmitKeyof<\n      MutationObserverOptions<TData, TError, TVariables, TContext>,\n      'mutationKey'\n    >,\n  ): void {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options,\n    })\n  }\n\n  getMutationDefaults(\n    mutationKey: MutationKey,\n  ): OmitKeyof<MutationObserverOptions<any, any, any, any>, 'mutationKey'> {\n    const defaults = [...this.#mutationDefaults.values()]\n\n    const result: OmitKeyof<\n      MutationObserverOptions<any, any, any, any>,\n      'mutationKey'\n    > = {}\n\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions)\n      }\n    })\n\n    return result\n  }\n\n  defaultQueryOptions<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = never,\n  >(\n    options:\n      | QueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey,\n          TPageParam\n        >\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (defaultedOptions.refetchOnReconnect === undefined) {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (defaultedOptions.throwOnError === undefined) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense\n    }\n\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = 'offlineFirst'\n    }\n\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...(options?.mutationKey &&\n        this.getMutationDefaults(options.mutationKey)),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.#queryCache.clear()\n    this.#mutationCache.clear()\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AASA,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;AAC7B,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,6BAA6B;;;;;;;;AA8C/B,IAAM,cAAN,MAAkB;KACvB,UAAA,CAAA;KACA,aAAA,CAAA;KACA,cAAA,CAAA;KACA,aAAA,CAAA;KACA,gBAAA,CAAA;KACA,UAAA,CAAA;IACA,iBAAA,CAAA;KACA,iBAAA,CAAA;IAEA,YAAY,SAA4B,CAAC,CAAA,CAAG;QAC1C,IAAA,EAAK,UAAA,GAAc,OAAO,UAAA,IAAc,iLAAI,aAAA,CAAW;QACvD,IAAA,EAAK,aAAA,GAAiB,OAAO,aAAA,IAAiB,oLAAI,gBAAA,CAAc;QAChE,IAAA,EAAK,cAAA,GAAkB,OAAO,cAAA,IAAkB,CAAC;QACjD,IAAA,EAAK,aAAA,GAAiB,aAAA,GAAA,IAAI,IAAI;QAC9B,IAAA,EAAK,gBAAA,GAAoB,aAAA,GAAA,IAAI,IAAI;QACjC,IAAA,EAAK,UAAA,GAAc;IACrB;IAEA,QAAc;QACZ,IAAA,EAAK,UAAA;QACL,IAAI,IAAA,EAAK,UAAA,KAAgB,EAAG,CAAA;QAE5B,IAAA,EAAK,gBAAA,kLAAoB,eAAA,CAAa,SAAA,CAAU,OAAO,YAAY;YACjE,IAAI,SAAS;gBACX,MAAM,IAAA,CAAK,qBAAA,CAAsB;gBACjC,IAAA,EAAK,UAAA,CAAY,OAAA,CAAQ;YAC3B;QACF,CAAC;QACD,IAAA,EAAK,iBAAA,mLAAqB,gBAAA,CAAc,SAAA,CAAU,OAAO,WAAW;YAClE,IAAI,QAAQ;gBACV,MAAM,IAAA,CAAK,qBAAA,CAAsB;gBACjC,IAAA,EAAK,UAAA,CAAY,QAAA,CAAS;YAC5B;QACF,CAAC;IACH;IAEA,UAAgB;QACd,IAAA,EAAK,UAAA;QACL,IAAI,IAAA,EAAK,UAAA,KAAgB,EAAG,CAAA;QAE5B,IAAA,EAAK,gBAAA,GAAoB;QACzB,IAAA,EAAK,gBAAA,GAAoB,KAAA;QAEzB,IAAA,EAAK,iBAAA,GAAqB;QAC1B,IAAA,EAAK,iBAAA,GAAqB,KAAA;IAC5B;IAEA,WACE,OAAA,EACQ;QACR,OAAO,IAAA,CAAK,WAAA,CAAY,OAAA,CAAQ;YAAE,GAAG,OAAA;YAAS,aAAa;QAAW,CAAC,EACpE,MAAA;IACL;IAEA,WAEE,OAAA,EAAoC;QACpC,OAAO,IAAA,CAAK,cAAA,CAAe,OAAA,CAAQ;YAAE,GAAG,OAAA;YAAS,QAAQ;QAAU,CAAC,EAAE,MAAA;IACxE;IAAA;;;;;;GAAA,GASA,aAIE,QAAA,EAA6D;QAC7D,MAAM,UAAU,IAAA,CAAK,mBAAA,CAAoB;YAAE;QAAS,CAAC;QAErD,OAAO,IAAA,EAAK,UAAA,CAAY,GAAA,CAA0B,QAAQ,SAAS,GAAG,MACnE;IACL;IAEA,gBAME,OAAA,EACgB;QAChB,MAAM,mBAAmB,IAAA,CAAK,mBAAA,CAAoB,OAAO;QACzD,MAAM,QAAQ,IAAA,EAAK,UAAA,CAAY,KAAA,CAAM,IAAA,EAAM,gBAAgB;QAC3D,MAAM,aAAa,MAAM,KAAA,CAAM,IAAA;QAE/B,IAAI,eAAe,KAAA,GAAW;YAC5B,OAAO,IAAA,CAAK,UAAA,CAAW,OAAO;QAChC;QAEA,IACE,QAAQ,iBAAA,IACR,MAAM,aAAA,6KAAc,mBAAA,EAAiB,iBAAiB,SAAA,EAAW,KAAK,CAAC,GACvE;YACA,KAAK,IAAA,CAAK,aAAA,CAAc,gBAAgB;QAC1C;QAEA,OAAO,QAAQ,OAAA,CAAQ,UAAU;IACnC;IAEA,eAGE,OAAA,EAAqE;QACrE,OAAO,IAAA,EAAK,UAAA,CAAY,OAAA,CAAQ,OAAO,EAAE,GAAA,CAAI,CAAC,EAAE,QAAA,EAAU,KAAA,CAAM,CAAA,KAAM;YACpE,MAAM,OAAO,MAAM,IAAA;YACnB,OAAO;gBAAC;gBAAU,IAAI;aAAA;QACxB,CAAC;IACH;IAEA,aAKE,QAAA,EACA,OAAA,EAIA,OAAA,EAC2C;QAC3C,MAAM,mBAAmB,IAAA,CAAK,mBAAA,CAM5B;YAAE;QAAS,CAAC;QAEd,MAAM,QAAQ,IAAA,EAAK,UAAA,CAAY,GAAA,CAC7B,iBAAiB,SAAA;QAEnB,MAAM,WAAW,OAAO,MAAM;QAC9B,MAAM,mLAAO,mBAAA,EAAiB,SAAS,QAAQ;QAE/C,IAAI,SAAS,KAAA,GAAW;YACtB,OAAO,KAAA;QACT;QAEA,OAAO,IAAA,EAAK,UAAA,CACT,KAAA,CAAM,IAAA,EAAM,gBAAgB,EAC5B,OAAA,CAAQ,MAAM;YAAE,GAAG,OAAA;YAAS,QAAQ;QAAK,CAAC;IAC/C;IAEA,eAIE,OAAA,EACA,OAAA,EAIA,OAAA,EAC6C;QAC7C,uLAAO,gBAAA,CAAc,KAAA,CAAM,IACzB,IAAA,EAAK,UAAA,CACF,OAAA,CAAQ,OAAO,EACf,GAAA,CAAI,CAAC,EAAE,QAAA,CAAS,CAAA,GAAM;oBACrB;oBACA,IAAA,CAAK,YAAA,CAA2B,UAAU,SAAS,OAAO;iBAC3D;IAEP;IAEA,cAOE,QAAA,EAC8D;QAC9D,MAAM,UAAU,IAAA,CAAK,mBAAA,CAAoB;YAAE;QAAS,CAAC;QACrD,OAAO,IAAA,EAAK,UAAA,CAAY,GAAA,CACtB,QAAQ,SAAA,GACP;IACL;IAEA,cACE,OAAA,EACM;QACN,MAAM,aAAa,IAAA,EAAK,UAAA;QACxB,+KAAA,CAAA,gBAAA,CAAc,KAAA,CAAM,MAAM;YACxB,WAAW,OAAA,CAAQ,OAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBAC7C,WAAW,MAAA,CAAO,KAAK;YACzB,CAAC;QACH,CAAC;IACH;IAEA,aACE,OAAA,EACA,OAAA,EACe;QACf,MAAM,aAAa,IAAA,EAAK,UAAA;QAExB,uLAAO,gBAAA,CAAc,KAAA,CAAM,MAAM;YAC/B,WAAW,OAAA,CAAQ,OAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBAC7C,MAAM,KAAA,CAAM;YACd,CAAC;YACD,OAAO,IAAA,CAAK,cAAA,CACV;gBACE,MAAM;gBACN,GAAG,OAAA;YACL,GACA;QAEJ,CAAC;IACH;IAEA,cACE,OAAA,EACA,gBAA+B,CAAC,CAAA,EACjB;QACf,MAAM,yBAAyB;YAAE,QAAQ;YAAM,GAAG,aAAA;QAAc;QAEhE,MAAM,2LAAW,gBAAA,CAAc,KAAA,CAAM,IACnC,IAAA,EAAK,UAAA,CACF,OAAA,CAAQ,OAAO,EACf,GAAA,CAAI,CAAC,QAAU,MAAM,MAAA,CAAO,sBAAsB,CAAC;QAGxD,OAAO,QAAQ,GAAA,CAAI,QAAQ,EAAE,IAAA,yKAAK,OAAI,EAAE,KAAA,wKAAM,QAAI;IACpD;IAEA,kBACE,OAAA,EACA,UAA6B,CAAC,CAAA,EACf;QACf,uLAAO,gBAAA,CAAc,KAAA,CAAM,MAAM;YAC/B,IAAA,EAAK,UAAA,CAAY,OAAA,CAAQ,OAAO,EAAE,OAAA,CAAQ,CAAC,UAAU;gBACnD,MAAM,UAAA,CAAW;YACnB,CAAC;YAED,IAAI,SAAS,gBAAgB,QAAQ;gBACnC,OAAO,QAAQ,OAAA,CAAQ;YACzB;YACA,OAAO,IAAA,CAAK,cAAA,CACV;gBACE,GAAG,OAAA;gBACH,MAAM,SAAS,eAAe,SAAS,QAAQ;YACjD,GACA;QAEJ,CAAC;IACH;IAEA,eACE,OAAA,EACA,UAA0B,CAAC,CAAA,EACZ;QACf,MAAM,eAAe;YACnB,GAAG,OAAA;YACH,eAAe,QAAQ,aAAA,IAAiB;QAC1C;QACA,MAAM,2LAAW,gBAAA,CAAc,KAAA,CAAM,IACnC,IAAA,EAAK,UAAA,CACF,OAAA,CAAQ,OAAO,EACf,MAAA,CAAO,CAAC,QAAU,CAAC,MAAM,UAAA,CAAW,KAAK,CAAC,MAAM,QAAA,CAAS,CAAC,EAC1D,GAAA,CAAI,CAAC,UAAU;gBACd,IAAI,UAAU,MAAM,KAAA,CAAM,KAAA,GAAW,YAAY;gBACjD,IAAI,CAAC,aAAa,YAAA,EAAc;oBAC9B,UAAU,QAAQ,KAAA,yKAAM,OAAI;gBAC9B;gBACA,OAAO,MAAM,KAAA,CAAM,WAAA,KAAgB,WAC/B,QAAQ,OAAA,CAAQ,IAChB;YACN,CAAC;QAGL,OAAO,QAAQ,GAAA,CAAI,QAAQ,EAAE,IAAA,yKAAK,OAAI;IACxC;IAEA,WAOE,OAAA,EAOgB;QAChB,MAAM,mBAAmB,IAAA,CAAK,mBAAA,CAAoB,OAAO;QAGzD,IAAI,iBAAiB,KAAA,KAAU,KAAA,GAAW;YACxC,iBAAiB,KAAA,GAAQ;QAC3B;QAEA,MAAM,QAAQ,IAAA,EAAK,UAAA,CAAY,KAAA,CAAM,IAAA,EAAM,gBAAgB;QAE3D,OAAO,MAAM,aAAA,6KACX,mBAAA,EAAiB,iBAAiB,SAAA,EAAW,KAAK,KAEhD,MAAM,KAAA,CAAM,gBAAgB,IAC5B,QAAQ,OAAA,CAAQ,MAAM,KAAA,CAAM,IAAa;IAC/C;IAEA,cAME,OAAA,EACe;QACf,OAAO,IAAA,CAAK,UAAA,CAAW,OAAO,EAAE,IAAA,wKAAK,QAAI,EAAE,KAAA,yKAAM,OAAI;IACvD;IAEA,mBAOE,OAAA,EAO0C;QAC1C,QAAQ,QAAA,OAAW,gNAAA,EAKjB,QAAQ,KAAK;QACf,OAAO,IAAA,CAAK,UAAA,CAAW,OAAc;IACvC;IAEA,sBAOE,OAAA,EAOe;QACf,OAAO,IAAA,CAAK,kBAAA,CAAmB,OAAO,EAAE,IAAA,yKAAK,OAAI,EAAE,KAAA,yKAAM,OAAI;IAC/D;IAEA,wBAOE,OAAA,EAO0C;QAC1C,QAAQ,QAAA,+LAAW,wBAAA,EAKjB,QAAQ,KAAK;QAEf,OAAO,IAAA,CAAK,eAAA,CAAgB,OAAc;IAC5C;IAEA,wBAA0C;QACxC,oLAAI,gBAAA,CAAc,QAAA,CAAS,GAAG;YAC5B,OAAO,IAAA,EAAK,aAAA,CAAe,qBAAA,CAAsB;QACnD;QACA,OAAO,QAAQ,OAAA,CAAQ;IACzB;IAEA,gBAA4B;QAC1B,OAAO,IAAA,EAAK,UAAA;IACd;IAEA,mBAAkC;QAChC,OAAO,IAAA,EAAK,aAAA;IACd;IAEA,oBAAoC;QAClC,OAAO,IAAA,EAAK,cAAA;IACd;IAEA,kBAAkB,OAAA,EAA+B;QAC/C,IAAA,EAAK,cAAA,GAAkB;IACzB;IAEA,iBAME,QAAA,EACA,OAAA,EAMM;QACN,IAAA,EAAK,aAAA,CAAe,GAAA,KAAI,kLAAA,EAAQ,QAAQ,GAAG;YACzC;YACA,gBAAgB;QAClB,CAAC;IACH;IAEA,iBACE,QAAA,EACsE;QACtE,MAAM,WAAW,CAAC;eAAG,IAAA,EAAK,aAAA,CAAe,MAAA,CAAO,CAAC;SAAA;QAEjD,MAAM,SAGF,CAAC;QAEL,SAAS,OAAA,CAAQ,CAAC,iBAAiB;YACjC,gLAAI,kBAAA,EAAgB,UAAU,aAAa,QAAQ,GAAG;gBACpD,OAAO,MAAA,CAAO,QAAQ,aAAa,cAAc;YACnD;QACF,CAAC;QACD,OAAO;IACT;IAEA,oBAME,WAAA,EACA,OAAA,EAIM;QACN,IAAA,EAAK,gBAAA,CAAkB,GAAA,KAAI,kLAAA,EAAQ,WAAW,GAAG;YAC/C;YACA,gBAAgB;QAClB,CAAC;IACH;IAEA,oBACE,WAAA,EACuE;QACvE,MAAM,WAAW,CAAC;eAAG,IAAA,EAAK,gBAAA,CAAkB,MAAA,CAAO,CAAC;SAAA;QAEpD,MAAM,SAGF,CAAC;QAEL,SAAS,OAAA,CAAQ,CAAC,iBAAiB;YACjC,IAAI,8LAAA,EAAgB,aAAa,aAAa,WAAW,GAAG;gBAC1D,OAAO,MAAA,CAAO,QAAQ,aAAa,cAAc;YACnD;QACF,CAAC;QAED,OAAO;IACT;IAEA,oBAQE,OAAA,EAsBA;QACA,IAAI,QAAQ,UAAA,EAAY;YACtB,OAAO;QAOT;QAEA,MAAM,mBAAmB;YACvB,GAAG,IAAA,EAAK,cAAA,CAAgB,OAAA;YACxB,GAAG,IAAA,CAAK,gBAAA,CAAiB,QAAQ,QAAQ,CAAA;YACzC,GAAG,OAAA;YACH,YAAY;QACd;QAEA,IAAI,CAAC,iBAAiB,SAAA,EAAW;YAC/B,iBAAiB,SAAA,+KAAY,wBAAA,EAC3B,iBAAiB,QAAA,EACjB;QAEJ;QAGA,IAAI,iBAAiB,kBAAA,KAAuB,KAAA,GAAW;YACrD,iBAAiB,kBAAA,GACf,iBAAiB,WAAA,KAAgB;QACrC;QACA,IAAI,iBAAiB,YAAA,KAAiB,KAAA,GAAW;YAC/C,iBAAiB,YAAA,GAAe,CAAC,CAAC,iBAAiB,QAAA;QACrD;QAEA,IAAI,CAAC,iBAAiB,WAAA,IAAe,iBAAiB,SAAA,EAAW;YAC/D,iBAAiB,WAAA,GAAc;QACjC;QAEA,IAAI,iBAAiB,OAAA,6KAAY,YAAA,EAAW;YAC1C,iBAAiB,OAAA,GAAU;QAC7B;QAEA,OAAO;IAOT;IAEA,uBACE,OAAA,EACG;QACH,IAAI,SAAS,YAAY;YACvB,OAAO;QACT;QACA,OAAO;YACL,GAAG,IAAA,EAAK,cAAA,CAAgB,SAAA;YACxB,GAAI,SAAS,eACX,IAAA,CAAK,mBAAA,CAAoB,QAAQ,WAAW,CAAA;YAC9C,GAAG,OAAA;YACH,YAAY;QACd;IACF;IAEA,QAAc;QACZ,IAAA,EAAK,UAAA,CAAY,KAAA,CAAM;QACvB,IAAA,EAAK,aAAA,CAAe,KAAA,CAAM;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6328, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/%40tanstack/react-query/src/QueryClientProvider.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport type { QueryClient } from '@tanstack/query-core'\n\nexport const QueryClientContext = React.createContext<QueryClient | undefined>(\n  undefined,\n)\n\nexport const useQueryClient = (queryClient?: QueryClient) => {\n  const client = React.useContext(QueryClientContext)\n\n  if (queryClient) {\n    return queryClient\n  }\n\n  if (!client) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one')\n  }\n\n  return client\n}\n\nexport type QueryClientProviderProps = {\n  client: QueryClient\n  children?: React.ReactNode\n}\n\nexport const QueryClientProvider = ({\n  client,\n  children,\n}: QueryClientProviderProps): React.JSX.Element => {\n  React.useEffect(() => {\n    client.mount()\n    return () => {\n      client.unmount()\n    }\n  }, [client])\n\n  return (\n    <QueryClientContext.Provider value={client}>\n      {children}\n    </QueryClientContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AACA,YAAY,WAAW;AAuCnB;;;;AAnCG,IAAM,+NAA2B,gBAAA,EACtC,KAAA;AAGK,IAAM,iBAAiB,CAAC,gBAA8B;IAC3D,MAAM,UAAe,sNAAA,EAAW,kBAAkB;IAElD,IAAI,aAAa;QACf,OAAO;IACT;IAEA,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM,wDAAwD;IAC1E;IAEA,OAAO;AACT;AAOO,IAAM,sBAAsB,CAAC,EAClC,MAAA,EACA,QAAA,EACF,KAAmD;8MAC3C,YAAA,EAAU,MAAM;QACpB,OAAO,KAAA,CAAM;QACb,OAAO,MAAM;YACX,OAAO,OAAA,CAAQ;QACjB;IACF,GAAG;QAAC,MAAM;KAAC;IAEX,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,mBAAmB,QAAA,EAAnB;QAA4B,OAAO;QACjC;IAAA,CACH;AAEJ", "ignoreList": [0], "debugId": null}}]}