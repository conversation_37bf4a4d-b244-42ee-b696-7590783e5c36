{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/lib/auth-client.ts"], "sourcesContent": ["import { createAuthClient } from 'better-auth/react';\n\nexport const authClient = createAuthClient({\n  baseURL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n  fetchOptions: {\n    credentials: 'include', // Ensure cookies are sent with requests\n  },\n});\n\nexport const { \n  signIn, \n  signUp, \n  signOut,\n  useSession \n} = authClient;"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,aAAa,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE;IACzC,SAAS,6DAAmC;IAC5C,cAAc;QACZ,aAAa;IACf;AACF;AAEO,MAAM,EACX,MAAM,EACN,MAAM,EACN,OAAO,EACP,UAAU,EACX,GAAG", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/hooks/useAutumnCustomer.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useCallback, ReactNode } from 'react';\nimport { useCustomer as useAutumnCustomer, UseCustomerParams } from 'autumn-js/react';\n\n// Create a context for the refetch function\ninterface AutumnCustomerContextType {\n  refetchCustomer: () => Promise<void>;\n}\n\nconst AutumnCustomerContext = createContext<AutumnCustomerContextType | null>(null);\n\n// Provider component\nexport function AutumnCustomerProvider({ children }: { children: ReactNode }) {\n  const { refetch } = useAutumnCustomer({ skip: true });\n\n  const refetchCustomer = useCallback(async () => {\n    await refetch();\n  }, [refetch]);\n\n  return (\n    <AutumnCustomerContext.Provider value={{ refetchCustomer }}>\n      {children}\n    </AutumnCustomerContext.Provider>\n  );\n}\n\n// Hook to use the customer data with global refetch\nexport function useCustomer(params?: UseCustomerParams) {\n  const autumnCustomer = useAutumnCustomer(params);\n  const context = useContext(AutumnCustomerContext);\n\n  // Create a wrapped refetch that can be used globally\n  const globalRefetch = useCallback(async () => {\n    // Refetch the local instance\n    const result = await autumnCustomer.refetch();\n    \n    // Also trigger any global refetch if in context\n    if (context?.refetchCustomer) {\n      await context.refetchCustomer();\n    }\n    \n    return result;\n  }, [autumnCustomer, context]);\n\n  return {\n    ...autumnCustomer,\n    refetch: globalRefetch,\n  };\n}\n\n// Hook to trigger a global customer data refresh from anywhere\nexport function useRefreshCustomer() {\n  const context = useContext(AutumnCustomerContext);\n  \n  if (!context) {\n    // Return a no-op function if not in provider\n    return async () => {\n      console.warn('useRefreshCustomer called outside of AutumnCustomerProvider');\n    };\n  }\n  \n  return context.refetchCustomer;\n}"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAGvE,SAAS,uBAAuB,EAAE,QAAQ,EAA2B;IAC1E,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,cAAiB,AAAD,EAAE;QAAE,MAAM;IAAK;IAEnD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,MAAM;IACR,GAAG;QAAC;KAAQ;IAEZ,qBACE,8OAAC,sBAAsB,QAAQ;QAAC,OAAO;YAAE;QAAgB;kBACtD;;;;;;AAGP;AAGO,SAAS,YAAY,MAA0B;IACpD,MAAM,iBAAiB,CAAA,GAAA,oKAAA,CAAA,cAAiB,AAAD,EAAE;IACzC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,qDAAqD;IACrD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,6BAA6B;QAC7B,MAAM,SAAS,MAAM,eAAe,OAAO;QAE3C,gDAAgD;QAChD,IAAI,SAAS,iBAAiB;YAC5B,MAAM,QAAQ,eAAe;QAC/B;QAEA,OAAO;IACT,GAAG;QAAC;QAAgB;KAAQ;IAE5B,OAAO;QACL,GAAG,cAAc;QACjB,SAAS;IACX;AACF;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,CAAC,SAAS;QACZ,6CAA6C;QAC7C,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,OAAO,QAAQ,eAAe;AAChC", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/components/navbar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useSession, signOut } from '@/lib/auth-client';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\nimport { useCustomer } from '@/hooks/useAutumnCustomer';\n\n// Separate component that only renders when Autumn is available\nfunction UserCredits() {\n  const { customer } = useCustomer();\n  const messageUsage = customer?.features?.messages;\n  const remainingMessages = messageUsage ? (messageUsage.balance || 0) : 0;\n  \n  return (\n    <div className=\"flex items-center text-sm font-medium text-gray-700\">\n      <span>{remainingMessages}</span>\n      <span className=\"ml-1\">credits</span>\n    </div>\n  );\n}\n\nexport function Navbar() {\n  const { data: session, isPending } = useSession();\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    setIsLoggingOut(true);\n    try {\n      await signOut();\n      // Small delay to ensure the session is cleared\n      setTimeout(() => {\n        router.refresh();\n        setIsLoggingOut(false);\n      }, 100);\n    } catch (error) {\n      console.error('Logout error:', error);\n      setIsLoggingOut(false);\n    }\n  };\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <Image\n                src=\"/firecrawl-logo-with-fire.webp\"\n                alt=\"Firecrawl\"\n                width={120}\n                height={25}\n                priority\n              />\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {session && (\n              <>\n                <Link\n                  href=\"/chat\"\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900\"\n                >\n                  Basic Chat\n                </Link>\n                <Link\n                  href=\"/brand-monitor\"\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900\"\n                >\n                  Brand Monitor\n                </Link>\n              </>\n            )}\n            <Link\n              href=\"/plans\"\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900\"\n            >\n              Plans\n            </Link>\n            {session && (\n              <UserCredits />\n            )}\n            {isPending ? (\n              <div className=\"text-sm text-gray-400\">Loading...</div>\n            ) : session ? (\n              <>\n                <Link\n                  href=\"/dashboard\"\n                  className=\"btn-firecrawl-orange inline-flex items-center justify-center whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 h-8 px-3\"\n                >\n                  Dashboard\n                </Link>\n                <button\n                  onClick={handleLogout}\n                  disabled={isLoggingOut}\n                  className=\"btn-firecrawl-default inline-flex items-center justify-center whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 h-8 px-3\"\n                >\n                  {isLoggingOut ? 'Logging out...' : 'Logout'}\n                </button>\n              </>\n            ) : (\n              <>\n                <Link \n                  href=\"/login\"\n                  className=\"bg-black text-white hover:bg-gray-800 inline-flex items-center justify-center whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 h-8 px-3 shadow-sm hover:shadow-md\"\n                >\n                  Login\n                </Link>\n                <Link \n                  href=\"/register\"\n                  className=\"btn-firecrawl-orange inline-flex items-center justify-center whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 h-8 px-3\"\n                >\n                  Register\n                </Link>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,gEAAgE;AAChE,SAAS;IACP,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,eAAe,UAAU,UAAU;IACzC,MAAM,oBAAoB,eAAgB,aAAa,OAAO,IAAI,IAAK;IAEvE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;0BAAM;;;;;;0BACP,8OAAC;gBAAK,WAAU;0BAAO;;;;;;;;;;;;AAG7B;AAEO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAO,AAAD;YACZ,+CAA+C;YAC/C,WAAW;gBACT,OAAO,OAAO;gBACd,gBAAgB;YAClB,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,QAAQ;;;;;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;4BACZ,yBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;0CAKL,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,yBACC,8OAAC;;;;;4BAEF,0BACC,8OAAC;gCAAI,WAAU;0CAAwB;;;;;uCACrC,wBACF;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,eAAe,mBAAmB;;;;;;;6DAIvC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/lib/providers/query-provider.tsx"], "sourcesContent": ["'use client';\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { useState } from 'react';\n\nexport function QueryProvider({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            staleTime: 60 * 1000, // 1 minute\n            gcTime: 5 * 60 * 1000, // 5 minutes (formerly cacheTime)\n          },\n        },\n      })\n  );\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n    </QueryClientProvider>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAHA;;;;AAKO,SAAS,cAAc,EAAE,QAAQ,EAAiC;IACvE,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC3B,IACE,IAAI,6KAAA,CAAA,cAAW,CAAC;YACd,gBAAgB;gBACd,SAAS;oBACP,WAAW,KAAK;oBAChB,QAAQ,IAAI,KAAK;gBACnB;YACF;QACF;IAGJ,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/components/providers.tsx"], "sourcesContent": ["'use client';\n\nimport { AutumnProvider } from 'autumn-js/react';\nimport { QueryProvider } from '@/lib/providers/query-provider';\nimport { AutumnCustomerProvider } from '@/hooks/useAutumnCustomer';\nimport { useSession } from '@/lib/auth-client';\n\nfunction AuthAwareAutumnProvider({ children }: { children: React.ReactNode }) {\n  const { data: session } = useSession();\n  \n  // Only render AutumnProvider when logged in\n  if (!session) {\n    return <>{children}</>;\n  }\n  \n  return (\n    <AutumnProvider\n      backendUrl=\"/api/auth/autumn\"\n      betterAuthUrl={process.env.NEXT_PUBLIC_APP_URL || \"http://localhost:3000\"}\n      allowAnonymous={false}\n      skipInitialFetch={false}\n    >\n      <AutumnCustomerProvider>\n        {children}\n      </AutumnCustomerProvider>\n    </AutumnProvider>\n  );\n}\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <QueryProvider>\n      <AuthAwareAutumnProvider>\n        {children}\n      </AuthAwareAutumnProvider>\n    </QueryProvider>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,SAAS,wBAAwB,EAAE,QAAQ,EAAiC;IAC1E,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;IAEnC,4CAA4C;IAC5C,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBACE,8OAAC,oKAAA,CAAA,iBAAc;QACb,YAAW;QACX,eAAe,6DAAmC;QAClD,gBAAgB;QAChB,kBAAkB;kBAElB,cAAA,8OAAC,2HAAA,CAAA,yBAAsB;sBACpB;;;;;;;;;;;AAIT;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBACE,8OAAC,sIAAA,CAAA,gBAAa;kBACZ,cAAA,8OAAC;sBACE;;;;;;;;;;;AAIT", "debugId": null}}]}