{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/crypto/index.mjs"], "sourcesContent": ["import { createHash } from '@better-auth/utils/hash';\nimport { xchacha20poly1305 } from '@noble/ciphers/chacha';\nimport { utf8ToBytes, bytesToHex, hexToBytes as hexToBytes$1 } from '@noble/ciphers/utils';\nimport { managedNonce } from '@noble/ciphers/webcrypto';\nimport { base64 } from '@better-auth/utils/base64';\nimport { SignJWT } from 'jose';\nimport { scryptAsync } from '@noble/hashes/scrypt';\nimport { getRandomValues } from '@better-auth/utils';\nimport { hex } from '@better-auth/utils/hex';\nimport { hexToBytes } from '@noble/hashes/utils';\nexport { g as generateRandomString } from '../shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-auth/utils/random';\n\nasync function signJWT(payload, secret, expiresIn = 3600) {\n  const jwt = await new SignJWT(payload).setProtectedHeader({ alg: \"HS256\" }).setIssuedAt().setExpirationTime(Math.floor(Date.now() / 1e3) + expiresIn).sign(new TextEncoder().encode(secret));\n  return jwt;\n}\n\nfunction constantTimeEqual(a, b) {\n  const aBuffer = new Uint8Array(a);\n  const bBuffer = new Uint8Array(b);\n  if (aBuffer.length !== bBuffer.length) {\n    return false;\n  }\n  let c = 0;\n  for (let i = 0; i < aBuffer.length; i++) {\n    c |= aBuffer[i] ^ bBuffer[i];\n  }\n  return c === 0;\n}\n\nasync function hashToBase64(data) {\n  const buffer = await createHash(\"SHA-256\").digest(data);\n  return base64.encode(buffer);\n}\nasync function compareHash(data, hash) {\n  const buffer = await createHash(\"SHA-256\").digest(\n    typeof data === \"string\" ? new TextEncoder().encode(data) : data\n  );\n  const hashBuffer = base64.decode(hash);\n  return constantTimeEqual(buffer, hashBuffer);\n}\n\nconst config = {\n  N: 16384,\n  r: 16,\n  p: 1,\n  dkLen: 64\n};\nasync function generateKey(password, salt) {\n  return await scryptAsync(password.normalize(\"NFKC\"), salt, {\n    N: config.N,\n    p: config.p,\n    r: config.r,\n    dkLen: config.dkLen,\n    maxmem: 128 * config.N * config.r * 2\n  });\n}\nconst hashPassword = async (password) => {\n  const salt = hex.encode(getRandomValues(new Uint8Array(16)));\n  const key = await generateKey(password, salt);\n  return `${salt}:${hex.encode(key)}`;\n};\nconst verifyPassword = async ({\n  hash,\n  password\n}) => {\n  const [salt, key] = hash.split(\":\");\n  const targetKey = await generateKey(password, salt);\n  return constantTimeEqual(targetKey, hexToBytes(key));\n};\n\nconst symmetricEncrypt = async ({\n  key,\n  data\n}) => {\n  const keyAsBytes = await createHash(\"SHA-256\").digest(key);\n  const dataAsBytes = utf8ToBytes(data);\n  const chacha = managedNonce(xchacha20poly1305)(new Uint8Array(keyAsBytes));\n  return bytesToHex(chacha.encrypt(dataAsBytes));\n};\nconst symmetricDecrypt = async ({\n  key,\n  data\n}) => {\n  const keyAsBytes = await createHash(\"SHA-256\").digest(key);\n  const dataAsBytes = hexToBytes$1(data);\n  const chacha = managedNonce(xchacha20poly1305)(new Uint8Array(keyAsBytes));\n  return new TextDecoder().decode(chacha.decrypt(dataAsBytes));\n};\n\nexport { compareHash, constantTimeEqual, hashPassword, hashToBase64, signJWT, symmetricDecrypt, symmetricEncrypt, verifyPassword };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAEA,eAAe,QAAQ,OAAO,EAAE,MAAM,EAAE,YAAY,IAAI;IACtD,MAAM,MAAM,MAAM,IAAI,4JAAA,CAAA,UAAO,CAAC,SAAS,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAAG,WAAW,GAAG,iBAAiB,CAAC,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,OAAO,WAAW,IAAI,CAAC,IAAI,cAAc,MAAM,CAAC;IACpL,OAAO;AACT;AAEA,SAAS,kBAAkB,CAAC,EAAE,CAAC;IAC7B,MAAM,UAAU,IAAI,WAAW;IAC/B,MAAM,UAAU,IAAI,WAAW;IAC/B,IAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,EAAE;QACrC,OAAO;IACT;IACA,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,KAAK,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;IAC9B;IACA,OAAO,MAAM;AACf;AAEA,eAAe,aAAa,IAAI;IAC9B,MAAM,SAAS,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAAC;IAClD,OAAO,8JAAA,CAAA,SAAM,CAAC,MAAM,CAAC;AACvB;AACA,eAAe,YAAY,IAAI,EAAE,IAAI;IACnC,MAAM,SAAS,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAC/C,OAAO,SAAS,WAAW,IAAI,cAAc,MAAM,CAAC,QAAQ;IAE9D,MAAM,aAAa,8JAAA,CAAA,SAAM,CAAC,MAAM,CAAC;IACjC,OAAO,kBAAkB,QAAQ;AACnC;AAEA,MAAM,SAAS;IACb,GAAG;IACH,GAAG;IACH,GAAG;IACH,OAAO;AACT;AACA,eAAe,YAAY,QAAQ,EAAE,IAAI;IACvC,OAAO,MAAM,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,SAAS,SAAS,CAAC,SAAS,MAAM;QACzD,GAAG,OAAO,CAAC;QACX,GAAG,OAAO,CAAC;QACX,GAAG,OAAO,CAAC;QACX,OAAO,OAAO,KAAK;QACnB,QAAQ,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG;IACtC;AACF;AACA,MAAM,eAAe,OAAO;IAC1B,MAAM,OAAO,2JAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,WAAW;IACvD,MAAM,MAAM,MAAM,YAAY,UAAU;IACxC,OAAO,GAAG,KAAK,CAAC,EAAE,2JAAA,CAAA,MAAG,CAAC,MAAM,CAAC,MAAM;AACrC;AACA,MAAM,iBAAiB,OAAO,EAC5B,IAAI,EACJ,QAAQ,EACT;IACC,MAAM,CAAC,MAAM,IAAI,GAAG,KAAK,KAAK,CAAC;IAC/B,MAAM,YAAY,MAAM,YAAY,UAAU;IAC9C,OAAO,kBAAkB,WAAW,CAAA,GAAA,mJAAA,CAAA,aAAU,AAAD,EAAE;AACjD;AAEA,MAAM,mBAAmB,OAAO,EAC9B,GAAG,EACH,IAAI,EACL;IACC,MAAM,aAAa,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAAC;IACtD,MAAM,cAAc,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE;IAChC,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,qJAAA,CAAA,oBAAiB,EAAE,IAAI,WAAW;IAC9D,OAAO,CAAA,GAAA,oJAAA,CAAA,aAAU,AAAD,EAAE,OAAO,OAAO,CAAC;AACnC;AACA,MAAM,mBAAmB,OAAO,EAC9B,GAAG,EACH,IAAI,EACL;IACC,MAAM,aAAa,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAAC;IACtD,MAAM,cAAc,CAAA,GAAA,oJAAA,CAAA,aAAY,AAAD,EAAE;IACjC,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,qJAAA,CAAA,oBAAiB,EAAE,IAAI,WAAW;IAC9D,OAAO,IAAI,cAAc,MAAM,CAAC,OAAO,OAAO,CAAC;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/cookies/index.mjs"], "sourcesContent": ["import { B as BetterAuthError } from '../shared/better-auth.DdzSJf-n.mjs';\nimport { g as getDate } from '../shared/better-auth.CW6D9eSx.mjs';\nimport { a as isProduction, e as env } from '../shared/better-auth.8zoxzg-F.mjs';\nimport { base64Url } from '@better-auth/utils/base64';\nimport { createHMAC } from '@better-auth/utils/hmac';\nimport { s as safeJSONParse } from '../shared/better-auth.tB5eU6EY.mjs';\nimport { a as getBaseURL } from '../shared/better-auth.VTXNLFMT.mjs';\nimport { binary } from '@better-auth/utils/binary';\n\nconst createTime = (value, format) => {\n  const toMilliseconds = () => {\n    switch (format) {\n      case \"ms\":\n        return value;\n      case \"s\":\n        return value * 1e3;\n      case \"m\":\n        return value * 1e3 * 60;\n      case \"h\":\n        return value * 1e3 * 60 * 60;\n      case \"d\":\n        return value * 1e3 * 60 * 60 * 24;\n      case \"w\":\n        return value * 1e3 * 60 * 60 * 24 * 7;\n      case \"y\":\n        return value * 1e3 * 60 * 60 * 24 * 365;\n    }\n  };\n  const time = {\n    t: `${value}${format}`,\n    value,\n    tFormat: format,\n    toMilliseconds,\n    toSeconds: () => time.toMilliseconds() / 1e3,\n    toMinutes: () => time.toSeconds() / 60,\n    toHours: () => time.toMinutes() / 60,\n    toDays: () => time.toHours() / 24,\n    toWeeks: () => time.toDays() / 7,\n    toYears: () => time.toDays() / 365,\n    getDate: () => new Date(Date.now() + time.toMilliseconds()),\n    add: (other) => {\n      const otherMs = typeof other === \"string\" ? parseTime(other).toMilliseconds() : other.toMilliseconds();\n      return createTime(time.toMilliseconds() + otherMs, \"ms\");\n    },\n    subtract: (other) => {\n      const otherMs = typeof other === \"string\" ? parseTime(other).toMilliseconds() : other.toMilliseconds();\n      return createTime(time.toMilliseconds() - otherMs, \"ms\");\n    },\n    multiply: (factor) => createTime(time.toMilliseconds() * factor, \"ms\"),\n    divide: (divisor) => createTime(time.toMilliseconds() / divisor, \"ms\"),\n    equals: (other) => {\n      const otherMs = typeof other === \"string\" ? parseTime(other).toMilliseconds() : other.toMilliseconds();\n      return time.toMilliseconds() === otherMs;\n    },\n    lessThan: (other) => {\n      const otherMs = typeof other === \"string\" ? parseTime(other).toMilliseconds() : other.toMilliseconds();\n      return time.toMilliseconds() < otherMs;\n    },\n    greaterThan: (other) => {\n      const otherMs = typeof other === \"string\" ? parseTime(other).toMilliseconds() : other.toMilliseconds();\n      return time.toMilliseconds() > otherMs;\n    },\n    format: (pattern) => {\n      const date = time.getDate();\n      return pattern.replace(/YYYY|MM|DD|HH|mm|ss/g, (match) => {\n        switch (match) {\n          case \"YYYY\":\n            return date.getFullYear().toString();\n          case \"MM\":\n            return (date.getMonth() + 1).toString().padStart(2, \"0\");\n          case \"DD\":\n            return date.getDate().toString().padStart(2, \"0\");\n          case \"HH\":\n            return date.getHours().toString().padStart(2, \"0\");\n          case \"mm\":\n            return date.getMinutes().toString().padStart(2, \"0\");\n          case \"ss\":\n            return date.getSeconds().toString().padStart(2, \"0\");\n          default:\n            return match;\n        }\n      });\n    },\n    fromNow: () => {\n      const ms = time.toMilliseconds();\n      if (ms < 0) return time.ago();\n      if (ms < 1e3) return \"in a few seconds\";\n      if (ms < 6e4) return `in ${Math.round(ms / 1e3)} seconds`;\n      if (ms < 36e5) return `in ${Math.round(ms / 6e4)} minutes`;\n      if (ms < 864e5) return `in ${Math.round(ms / 36e5)} hours`;\n      if (ms < 6048e5) return `in ${Math.round(ms / 864e5)} days`;\n      if (ms < 26298e5) return `in ${Math.round(ms / 6048e5)} weeks`;\n      if (ms < 315576e5) return `in ${Math.round(ms / 26298e5)} months`;\n      return `in ${Math.round(ms / 315576e5)} years`;\n    },\n    ago: () => {\n      const ms = -time.toMilliseconds();\n      if (ms < 0) return time.fromNow();\n      if (ms < 1e3) return \"a few seconds ago\";\n      if (ms < 6e4) return `${Math.round(ms / 1e3)} seconds ago`;\n      if (ms < 36e5) return `${Math.round(ms / 6e4)} minutes ago`;\n      if (ms < 864e5) return `${Math.round(ms / 36e5)} hours ago`;\n      if (ms < 6048e5) return `${Math.round(ms / 864e5)} days ago`;\n      if (ms < 26298e5) return `${Math.round(ms / 6048e5)} weeks ago`;\n      if (ms < 315576e5) return `${Math.round(ms / 26298e5)} months ago`;\n      return `${Math.round(ms / 315576e5)} years ago`;\n    }\n  };\n  return time;\n};\nconst parseTime = (time) => {\n  const match = time.match(/^(\\d+)(ms|s|m|h|d|w|y)$/);\n  if (!match) throw new Error(\"Invalid time format\");\n  return createTime(parseInt(match[1]), match[2]);\n};\n\nfunction parseSetCookieHeader(setCookie) {\n  const cookies = /* @__PURE__ */ new Map();\n  const cookieArray = setCookie.split(\", \");\n  cookieArray.forEach((cookieString) => {\n    const parts = cookieString.split(\";\").map((part) => part.trim());\n    const [nameValue, ...attributes] = parts;\n    const [name, ...valueParts] = nameValue.split(\"=\");\n    const value = valueParts.join(\"=\");\n    if (!name || value === void 0) {\n      return;\n    }\n    const attrObj = { value };\n    attributes.forEach((attribute) => {\n      const [attrName, ...attrValueParts] = attribute.split(\"=\");\n      const attrValue = attrValueParts.join(\"=\");\n      const normalizedAttrName = attrName.trim().toLowerCase();\n      switch (normalizedAttrName) {\n        case \"max-age\":\n          attrObj[\"max-age\"] = attrValue ? parseInt(attrValue.trim(), 10) : void 0;\n          break;\n        case \"expires\":\n          attrObj.expires = attrValue ? new Date(attrValue.trim()) : void 0;\n          break;\n        case \"domain\":\n          attrObj.domain = attrValue ? attrValue.trim() : void 0;\n          break;\n        case \"path\":\n          attrObj.path = attrValue ? attrValue.trim() : void 0;\n          break;\n        case \"secure\":\n          attrObj.secure = true;\n          break;\n        case \"httponly\":\n          attrObj.httponly = true;\n          break;\n        case \"samesite\":\n          attrObj.samesite = attrValue ? attrValue.trim().toLowerCase() : void 0;\n          break;\n        default:\n          attrObj[normalizedAttrName] = attrValue ? attrValue.trim() : true;\n          break;\n      }\n    });\n    cookies.set(name, attrObj);\n  });\n  return cookies;\n}\nfunction setCookieToHeader(headers) {\n  return (context) => {\n    const setCookieHeader = context.response.headers.get(\"set-cookie\");\n    if (!setCookieHeader) {\n      return;\n    }\n    const cookieMap = /* @__PURE__ */ new Map();\n    const existingCookiesHeader = headers.get(\"cookie\") || \"\";\n    existingCookiesHeader.split(\";\").forEach((cookie) => {\n      const [name, ...rest] = cookie.trim().split(\"=\");\n      if (name && rest.length > 0) {\n        cookieMap.set(name, rest.join(\"=\"));\n      }\n    });\n    const setCookieHeaders = setCookieHeader.split(\",\");\n    setCookieHeaders.forEach((header) => {\n      const cookies = parseSetCookieHeader(header);\n      cookies.forEach((value, name) => {\n        cookieMap.set(name, value.value);\n      });\n    });\n    const updatedCookies = Array.from(cookieMap.entries()).map(([name, value]) => `${name}=${value}`).join(\"; \");\n    headers.set(\"cookie\", updatedCookies);\n  };\n}\n\nfunction createCookieGetter(options) {\n  const secure = options.advanced?.useSecureCookies !== void 0 ? options.advanced?.useSecureCookies : options.baseURL !== void 0 ? options.baseURL.startsWith(\"https://\") ? true : false : isProduction;\n  const secureCookiePrefix = secure ? \"__Secure-\" : \"\";\n  const crossSubdomainEnabled = !!options.advanced?.crossSubDomainCookies?.enabled;\n  const domain = crossSubdomainEnabled ? options.advanced?.crossSubDomainCookies?.domain || (options.baseURL ? new URL(options.baseURL).hostname : void 0) : void 0;\n  if (crossSubdomainEnabled && !domain) {\n    throw new BetterAuthError(\n      \"baseURL is required when crossSubdomainCookies are enabled\"\n    );\n  }\n  function createCookie(cookieName, overrideAttributes = {}) {\n    const prefix = options.advanced?.cookiePrefix || \"better-auth\";\n    const name = options.advanced?.cookies?.[cookieName]?.name || `${prefix}.${cookieName}`;\n    const attributes = options.advanced?.cookies?.[cookieName]?.attributes;\n    return {\n      name: `${secureCookiePrefix}${name}`,\n      attributes: {\n        secure: !!secureCookiePrefix,\n        sameSite: \"lax\",\n        path: \"/\",\n        httpOnly: true,\n        ...crossSubdomainEnabled ? { domain } : {},\n        ...options.advanced?.defaultCookieAttributes,\n        ...overrideAttributes,\n        ...attributes\n      }\n    };\n  }\n  return createCookie;\n}\nfunction getCookies(options) {\n  const createCookie = createCookieGetter(options);\n  const sessionMaxAge = options.session?.expiresIn || createTime(7, \"d\").toSeconds();\n  const sessionToken = createCookie(\"session_token\", {\n    maxAge: sessionMaxAge\n  });\n  const sessionData = createCookie(\"session_data\", {\n    maxAge: options.session?.cookieCache?.maxAge || 60 * 5\n  });\n  const dontRememberToken = createCookie(\"dont_remember\");\n  return {\n    sessionToken: {\n      name: sessionToken.name,\n      options: sessionToken.attributes\n    },\n    /**\n     * This cookie is used to store the session data in the cookie\n     * This is useful for when you want to cache the session in the cookie\n     */\n    sessionData: {\n      name: sessionData.name,\n      options: sessionData.attributes\n    },\n    dontRememberToken: {\n      name: dontRememberToken.name,\n      options: dontRememberToken.attributes\n    }\n  };\n}\nasync function setCookieCache(ctx, session) {\n  const shouldStoreSessionDataInCookie = ctx.context.options.session?.cookieCache?.enabled;\n  if (shouldStoreSessionDataInCookie) {\n    const filteredSession = Object.entries(session.session).reduce(\n      (acc, [key, value]) => {\n        const fieldConfig = ctx.context.options.session?.additionalFields?.[key];\n        if (!fieldConfig || fieldConfig.returned !== false) {\n          acc[key] = value;\n        }\n        return acc;\n      },\n      {}\n    );\n    const sessionData = { session: filteredSession, user: session.user };\n    const data = base64Url.encode(\n      JSON.stringify({\n        session: sessionData,\n        expiresAt: getDate(\n          ctx.context.authCookies.sessionData.options.maxAge || 60,\n          \"sec\"\n        ).getTime(),\n        signature: await createHMAC(\"SHA-256\", \"base64urlnopad\").sign(\n          ctx.context.secret,\n          JSON.stringify({\n            ...sessionData,\n            expiresAt: getDate(\n              ctx.context.authCookies.sessionData.options.maxAge || 60,\n              \"sec\"\n            ).getTime()\n          })\n        )\n      }),\n      {\n        padding: false\n      }\n    );\n    if (data.length > 4093) {\n      throw new BetterAuthError(\n        \"Session data is too large to store in the cookie. Please disable session cookie caching or reduce the size of the session data\"\n      );\n    }\n    ctx.setCookie(\n      ctx.context.authCookies.sessionData.name,\n      data,\n      ctx.context.authCookies.sessionData.options\n    );\n  }\n}\nasync function setSessionCookie(ctx, session, dontRememberMe, overrides) {\n  const dontRememberMeCookie = await ctx.getSignedCookie(\n    ctx.context.authCookies.dontRememberToken.name,\n    ctx.context.secret\n  );\n  dontRememberMe = dontRememberMe !== void 0 ? dontRememberMe : !!dontRememberMeCookie;\n  const options = ctx.context.authCookies.sessionToken.options;\n  const maxAge = dontRememberMe ? void 0 : ctx.context.sessionConfig.expiresIn;\n  await ctx.setSignedCookie(\n    ctx.context.authCookies.sessionToken.name,\n    session.session.token,\n    ctx.context.secret,\n    {\n      ...options,\n      maxAge,\n      ...overrides\n    }\n  );\n  if (dontRememberMe) {\n    await ctx.setSignedCookie(\n      ctx.context.authCookies.dontRememberToken.name,\n      \"true\",\n      ctx.context.secret,\n      ctx.context.authCookies.dontRememberToken.options\n    );\n  }\n  await setCookieCache(ctx, session);\n  ctx.context.setNewSession(session);\n  if (ctx.context.options.secondaryStorage) {\n    await ctx.context.secondaryStorage?.set(\n      session.session.token,\n      JSON.stringify({\n        user: session.user,\n        session: session.session\n      }),\n      Math.floor(\n        (new Date(session.session.expiresAt).getTime() - Date.now()) / 1e3\n      )\n    );\n  }\n}\nfunction deleteSessionCookie(ctx, skipDontRememberMe) {\n  ctx.setCookie(ctx.context.authCookies.sessionToken.name, \"\", {\n    ...ctx.context.authCookies.sessionToken.options,\n    maxAge: 0\n  });\n  ctx.setCookie(ctx.context.authCookies.sessionData.name, \"\", {\n    ...ctx.context.authCookies.sessionData.options,\n    maxAge: 0\n  });\n  if (!skipDontRememberMe) {\n    ctx.setCookie(ctx.context.authCookies.dontRememberToken.name, \"\", {\n      ...ctx.context.authCookies.dontRememberToken.options,\n      maxAge: 0\n    });\n  }\n}\nfunction parseCookies(cookieHeader) {\n  const cookies = cookieHeader.split(\"; \");\n  const cookieMap = /* @__PURE__ */ new Map();\n  cookies.forEach((cookie) => {\n    const [name, value] = cookie.split(\"=\");\n    cookieMap.set(name, value);\n  });\n  return cookieMap;\n}\nconst getSessionCookie = (request, config) => {\n  if (config?.cookiePrefix) {\n    if (config.cookieName) {\n      config.cookiePrefix = `${config.cookiePrefix}-`;\n    } else {\n      config.cookiePrefix = `${config.cookiePrefix}.`;\n    }\n  }\n  const headers = \"headers\" in request ? request.headers : request;\n  const req = request instanceof Request ? request : void 0;\n  getBaseURL(req?.url, config?.path, req);\n  const cookies = headers.get(\"cookie\");\n  if (!cookies) {\n    return null;\n  }\n  const { cookieName = \"session_token\", cookiePrefix = \"better-auth.\" } = config || {};\n  const name = `${cookiePrefix}${cookieName}`;\n  const secureCookieName = `__Secure-${name}`;\n  const parsedCookie = parseCookies(cookies);\n  const sessionToken = parsedCookie.get(name) || parsedCookie.get(secureCookieName);\n  if (sessionToken) {\n    return sessionToken;\n  }\n  return null;\n};\nconst getCookieCache = async (request, config) => {\n  const headers = request instanceof Headers ? request : request.headers;\n  const cookies = headers.get(\"cookie\");\n  if (!cookies) {\n    return null;\n  }\n  const { cookieName = \"session_data\", cookiePrefix = \"better-auth\" } = config || {};\n  const name = config?.isSecure !== void 0 ? config.isSecure ? `__Secure-${cookiePrefix}.${cookieName}` : `${cookiePrefix}.${cookieName}` : isProduction ? `__Secure-${cookiePrefix}.${cookieName}` : `${cookiePrefix}.${cookieName}`;\n  const parsedCookie = parseCookies(cookies);\n  const sessionData = parsedCookie.get(name);\n  if (sessionData) {\n    const sessionDataPayload = safeJSONParse(binary.decode(base64Url.decode(sessionData)));\n    if (!sessionDataPayload) {\n      return null;\n    }\n    const secret = config?.secret || env.BETTER_AUTH_SECRET;\n    if (!secret) {\n      throw new BetterAuthError(\n        \"getCookieCache requires a secret to be provided. Either pass it as an option or set the BETTER_AUTH_SECRET environment variable\"\n      );\n    }\n    const isValid = await createHMAC(\"SHA-256\", \"base64urlnopad\").verify(\n      secret,\n      JSON.stringify({\n        ...sessionDataPayload.session,\n        expiresAt: sessionDataPayload.expiresAt\n      }),\n      sessionDataPayload.signature\n    );\n    if (!isValid) {\n      return null;\n    }\n    return sessionDataPayload.session;\n  }\n  return null;\n};\n\nexport { createCookieGetter, deleteSessionCookie, getCookieCache, getCookies, getSessionCookie, parseCookies, parseSetCookieHeader, setCookieCache, setCookieToHeader, setSessionCookie };\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,aAAa,CAAC,OAAO;IACzB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,QAAQ,MAAM;YACvB,KAAK;gBACH,OAAO,QAAQ,MAAM,KAAK;YAC5B,KAAK;gBACH,OAAO,QAAQ,MAAM,KAAK,KAAK;YACjC,KAAK;gBACH,OAAO,QAAQ,MAAM,KAAK,KAAK,KAAK;YACtC,KAAK;gBACH,OAAO,QAAQ,MAAM,KAAK,KAAK,KAAK;QACxC;IACF;IACA,MAAM,OAAO;QACX,GAAG,GAAG,QAAQ,QAAQ;QACtB;QACA,SAAS;QACT;QACA,WAAW,IAAM,KAAK,cAAc,KAAK;QACzC,WAAW,IAAM,KAAK,SAAS,KAAK;QACpC,SAAS,IAAM,KAAK,SAAS,KAAK;QAClC,QAAQ,IAAM,KAAK,OAAO,KAAK;QAC/B,SAAS,IAAM,KAAK,MAAM,KAAK;QAC/B,SAAS,IAAM,KAAK,MAAM,KAAK;QAC/B,SAAS,IAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,cAAc;QACxD,KAAK,CAAC;YACJ,MAAM,UAAU,OAAO,UAAU,WAAW,UAAU,OAAO,cAAc,KAAK,MAAM,cAAc;YACpG,OAAO,WAAW,KAAK,cAAc,KAAK,SAAS;QACrD;QACA,UAAU,CAAC;YACT,MAAM,UAAU,OAAO,UAAU,WAAW,UAAU,OAAO,cAAc,KAAK,MAAM,cAAc;YACpG,OAAO,WAAW,KAAK,cAAc,KAAK,SAAS;QACrD;QACA,UAAU,CAAC,SAAW,WAAW,KAAK,cAAc,KAAK,QAAQ;QACjE,QAAQ,CAAC,UAAY,WAAW,KAAK,cAAc,KAAK,SAAS;QACjE,QAAQ,CAAC;YACP,MAAM,UAAU,OAAO,UAAU,WAAW,UAAU,OAAO,cAAc,KAAK,MAAM,cAAc;YACpG,OAAO,KAAK,cAAc,OAAO;QACnC;QACA,UAAU,CAAC;YACT,MAAM,UAAU,OAAO,UAAU,WAAW,UAAU,OAAO,cAAc,KAAK,MAAM,cAAc;YACpG,OAAO,KAAK,cAAc,KAAK;QACjC;QACA,aAAa,CAAC;YACZ,MAAM,UAAU,OAAO,UAAU,WAAW,UAAU,OAAO,cAAc,KAAK,MAAM,cAAc;YACpG,OAAO,KAAK,cAAc,KAAK;QACjC;QACA,QAAQ,CAAC;YACP,MAAM,OAAO,KAAK,OAAO;YACzB,OAAO,QAAQ,OAAO,CAAC,wBAAwB,CAAC;gBAC9C,OAAQ;oBACN,KAAK;wBACH,OAAO,KAAK,WAAW,GAAG,QAAQ;oBACpC,KAAK;wBACH,OAAO,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBACtD,KAAK;wBACH,OAAO,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBAC/C,KAAK;wBACH,OAAO,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBAChD,KAAK;wBACH,OAAO,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBAClD,KAAK;wBACH,OAAO,KAAK,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;oBAClD;wBACE,OAAO;gBACX;YACF;QACF;QACA,SAAS;YACP,MAAM,KAAK,KAAK,cAAc;YAC9B,IAAI,KAAK,GAAG,OAAO,KAAK,GAAG;YAC3B,IAAI,KAAK,KAAK,OAAO;YACrB,IAAI,KAAK,KAAK,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC;YACzD,IAAI,KAAK,MAAM,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC;YAC1D,IAAI,KAAK,OAAO,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,MAAM,CAAC;YAC1D,IAAI,KAAK,QAAQ,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,OAAO,KAAK,CAAC;YAC3D,IAAI,KAAK,SAAS,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,QAAQ,MAAM,CAAC;YAC9D,IAAI,KAAK,UAAU,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,SAAS,OAAO,CAAC;YACjE,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,KAAK,UAAU,MAAM,CAAC;QAChD;QACA,KAAK;YACH,MAAM,KAAK,CAAC,KAAK,cAAc;YAC/B,IAAI,KAAK,GAAG,OAAO,KAAK,OAAO;YAC/B,IAAI,KAAK,KAAK,OAAO;YACrB,IAAI,KAAK,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,KAAK,YAAY,CAAC;YAC1D,IAAI,KAAK,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,KAAK,YAAY,CAAC;YAC3D,IAAI,KAAK,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,UAAU,CAAC;YAC3D,IAAI,KAAK,QAAQ,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,OAAO,SAAS,CAAC;YAC5D,IAAI,KAAK,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,QAAQ,UAAU,CAAC;YAC/D,IAAI,KAAK,UAAU,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,SAAS,WAAW,CAAC;YAClE,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,UAAU,UAAU,CAAC;QACjD;IACF;IACA,OAAO;AACT;AACA,MAAM,YAAY,CAAC;IACjB,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,IAAI,CAAC,OAAO,MAAM,IAAI,MAAM;IAC5B,OAAO,WAAW,SAAS,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;AAChD;AAEA,SAAS,qBAAqB,SAAS;IACrC,MAAM,UAAU,aAAa,GAAG,IAAI;IACpC,MAAM,cAAc,UAAU,KAAK,CAAC;IACpC,YAAY,OAAO,CAAC,CAAC;QACnB,MAAM,QAAQ,aAAa,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,OAAS,KAAK,IAAI;QAC7D,MAAM,CAAC,WAAW,GAAG,WAAW,GAAG;QACnC,MAAM,CAAC,MAAM,GAAG,WAAW,GAAG,UAAU,KAAK,CAAC;QAC9C,MAAM,QAAQ,WAAW,IAAI,CAAC;QAC9B,IAAI,CAAC,QAAQ,UAAU,KAAK,GAAG;YAC7B;QACF;QACA,MAAM,UAAU;YAAE;QAAM;QACxB,WAAW,OAAO,CAAC,CAAC;YAClB,MAAM,CAAC,UAAU,GAAG,eAAe,GAAG,UAAU,KAAK,CAAC;YACtD,MAAM,YAAY,eAAe,IAAI,CAAC;YACtC,MAAM,qBAAqB,SAAS,IAAI,GAAG,WAAW;YACtD,OAAQ;gBACN,KAAK;oBACH,OAAO,CAAC,UAAU,GAAG,YAAY,SAAS,UAAU,IAAI,IAAI,MAAM,KAAK;oBACvE;gBACF,KAAK;oBACH,QAAQ,OAAO,GAAG,YAAY,IAAI,KAAK,UAAU,IAAI,MAAM,KAAK;oBAChE;gBACF,KAAK;oBACH,QAAQ,MAAM,GAAG,YAAY,UAAU,IAAI,KAAK,KAAK;oBACrD;gBACF,KAAK;oBACH,QAAQ,IAAI,GAAG,YAAY,UAAU,IAAI,KAAK,KAAK;oBACnD;gBACF,KAAK;oBACH,QAAQ,MAAM,GAAG;oBACjB;gBACF,KAAK;oBACH,QAAQ,QAAQ,GAAG;oBACnB;gBACF,KAAK;oBACH,QAAQ,QAAQ,GAAG,YAAY,UAAU,IAAI,GAAG,WAAW,KAAK,KAAK;oBACrE;gBACF;oBACE,OAAO,CAAC,mBAAmB,GAAG,YAAY,UAAU,IAAI,KAAK;oBAC7D;YACJ;QACF;QACA,QAAQ,GAAG,CAAC,MAAM;IACpB;IACA,OAAO;AACT;AACA,SAAS,kBAAkB,OAAO;IAChC,OAAO,CAAC;QACN,MAAM,kBAAkB,QAAQ,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;QACrD,IAAI,CAAC,iBAAiB;YACpB;QACF;QACA,MAAM,YAAY,aAAa,GAAG,IAAI;QACtC,MAAM,wBAAwB,QAAQ,GAAG,CAAC,aAAa;QACvD,sBAAsB,KAAK,CAAC,KAAK,OAAO,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,GAAG,KAAK,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;YAC5C,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;gBAC3B,UAAU,GAAG,CAAC,MAAM,KAAK,IAAI,CAAC;YAChC;QACF;QACA,MAAM,mBAAmB,gBAAgB,KAAK,CAAC;QAC/C,iBAAiB,OAAO,CAAC,CAAC;YACxB,MAAM,UAAU,qBAAqB;YACrC,QAAQ,OAAO,CAAC,CAAC,OAAO;gBACtB,UAAU,GAAG,CAAC,MAAM,MAAM,KAAK;YACjC;QACF;QACA,MAAM,iBAAiB,MAAM,IAAI,CAAC,UAAU,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,GAAG,KAAK,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC;QACvG,QAAQ,GAAG,CAAC,UAAU;IACxB;AACF;AAEA,SAAS,mBAAmB,OAAO;IACjC,MAAM,SAAS,QAAQ,QAAQ,EAAE,qBAAqB,KAAK,IAAI,QAAQ,QAAQ,EAAE,mBAAmB,QAAQ,OAAO,KAAK,KAAK,IAAI,QAAQ,OAAO,CAAC,UAAU,CAAC,cAAc,OAAO,QAAQ,oLAAA,CAAA,IAAY;IACrM,MAAM,qBAAqB,SAAS,cAAc;IAClD,MAAM,wBAAwB,CAAC,CAAC,QAAQ,QAAQ,EAAE,uBAAuB;IACzE,MAAM,SAAS,wBAAwB,QAAQ,QAAQ,EAAE,uBAAuB,UAAU,CAAC,QAAQ,OAAO,GAAG,IAAI,IAAI,QAAQ,OAAO,EAAE,QAAQ,GAAG,KAAK,CAAC,IAAI,KAAK;IAChK,IAAI,yBAAyB,CAAC,QAAQ;QACpC,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB;IAEJ;IACA,SAAS,aAAa,UAAU,EAAE,qBAAqB,CAAC,CAAC;QACvD,MAAM,SAAS,QAAQ,QAAQ,EAAE,gBAAgB;QACjD,MAAM,OAAO,QAAQ,QAAQ,EAAE,SAAS,CAAC,WAAW,EAAE,QAAQ,GAAG,OAAO,CAAC,EAAE,YAAY;QACvF,MAAM,aAAa,QAAQ,QAAQ,EAAE,SAAS,CAAC,WAAW,EAAE;QAC5D,OAAO;YACL,MAAM,GAAG,qBAAqB,MAAM;YACpC,YAAY;gBACV,QAAQ,CAAC,CAAC;gBACV,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,GAAG,wBAAwB;oBAAE;gBAAO,IAAI,CAAC,CAAC;gBAC1C,GAAG,QAAQ,QAAQ,EAAE,uBAAuB;gBAC5C,GAAG,kBAAkB;gBACrB,GAAG,UAAU;YACf;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,WAAW,OAAO;IACzB,MAAM,eAAe,mBAAmB;IACxC,MAAM,gBAAgB,QAAQ,OAAO,EAAE,aAAa,WAAW,GAAG,KAAK,SAAS;IAChF,MAAM,eAAe,aAAa,iBAAiB;QACjD,QAAQ;IACV;IACA,MAAM,cAAc,aAAa,gBAAgB;QAC/C,QAAQ,QAAQ,OAAO,EAAE,aAAa,UAAU,KAAK;IACvD;IACA,MAAM,oBAAoB,aAAa;IACvC,OAAO;QACL,cAAc;YACZ,MAAM,aAAa,IAAI;YACvB,SAAS,aAAa,UAAU;QAClC;QACA;;;KAGC,GACD,aAAa;YACX,MAAM,YAAY,IAAI;YACtB,SAAS,YAAY,UAAU;QACjC;QACA,mBAAmB;YACjB,MAAM,kBAAkB,IAAI;YAC5B,SAAS,kBAAkB,UAAU;QACvC;IACF;AACF;AACA,eAAe,eAAe,GAAG,EAAE,OAAO;IACxC,MAAM,iCAAiC,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,aAAa;IACjF,IAAI,gCAAgC;QAClC,MAAM,kBAAkB,OAAO,OAAO,CAAC,QAAQ,OAAO,EAAE,MAAM,CAC5D,CAAC,KAAK,CAAC,KAAK,MAAM;YAChB,MAAM,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,kBAAkB,CAAC,IAAI;YACxE,IAAI,CAAC,eAAe,YAAY,QAAQ,KAAK,OAAO;gBAClD,GAAG,CAAC,IAAI,GAAG;YACb;YACA,OAAO;QACT,GACA,CAAC;QAEH,MAAM,cAAc;YAAE,SAAS;YAAiB,MAAM,QAAQ,IAAI;QAAC;QACnE,MAAM,OAAO,8JAAA,CAAA,YAAS,CAAC,MAAM,CAC3B,KAAK,SAAS,CAAC;YACb,SAAS;YACT,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EACf,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,IAAI,IACtD,OACA,OAAO;YACT,WAAW,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,kBAAkB,IAAI,CAC3D,IAAI,OAAO,CAAC,MAAM,EAClB,KAAK,SAAS,CAAC;gBACb,GAAG,WAAW;gBACd,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EACf,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,IAAI,IACtD,OACA,OAAO;YACX;QAEJ,IACA;YACE,SAAS;QACX;QAEF,IAAI,KAAK,MAAM,GAAG,MAAM;YACtB,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB;QAEJ;QACA,IAAI,SAAS,CACX,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EACxC,MACA,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO;IAE/C;AACF;AACA,eAAe,iBAAiB,GAAG,EAAE,OAAO,EAAE,cAAc,EAAE,SAAS;IACrE,MAAM,uBAAuB,MAAM,IAAI,eAAe,CACpD,IAAI,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,EAC9C,IAAI,OAAO,CAAC,MAAM;IAEpB,iBAAiB,mBAAmB,KAAK,IAAI,iBAAiB,CAAC,CAAC;IAChE,MAAM,UAAU,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO;IAC5D,MAAM,SAAS,iBAAiB,KAAK,IAAI,IAAI,OAAO,CAAC,aAAa,CAAC,SAAS;IAC5E,MAAM,IAAI,eAAe,CACvB,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,EACzC,QAAQ,OAAO,CAAC,KAAK,EACrB,IAAI,OAAO,CAAC,MAAM,EAClB;QACE,GAAG,OAAO;QACV;QACA,GAAG,SAAS;IACd;IAEF,IAAI,gBAAgB;QAClB,MAAM,IAAI,eAAe,CACvB,IAAI,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,EAC9C,QACA,IAAI,OAAO,CAAC,MAAM,EAClB,IAAI,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO;IAErD;IACA,MAAM,eAAe,KAAK;IAC1B,IAAI,OAAO,CAAC,aAAa,CAAC;IAC1B,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE;QACxC,MAAM,IAAI,OAAO,CAAC,gBAAgB,EAAE,IAClC,QAAQ,OAAO,CAAC,KAAK,EACrB,KAAK,SAAS,CAAC;YACb,MAAM,QAAQ,IAAI;YAClB,SAAS,QAAQ,OAAO;QAC1B,IACA,KAAK,KAAK,CACR,CAAC,IAAI,KAAK,QAAQ,OAAO,CAAC,SAAS,EAAE,OAAO,KAAK,KAAK,GAAG,EAAE,IAAI;IAGrE;AACF;AACA,SAAS,oBAAoB,GAAG,EAAE,kBAAkB;IAClD,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI;QAC3D,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO;QAC/C,QAAQ;IACV;IACA,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI;QAC1D,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO;QAC9C,QAAQ;IACV;IACA,IAAI,CAAC,oBAAoB;QACvB,IAAI,SAAS,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI;YAChE,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO;YACpD,QAAQ;QACV;IACF;AACF;AACA,SAAS,aAAa,YAAY;IAChC,MAAM,UAAU,aAAa,KAAK,CAAC;IACnC,MAAM,YAAY,aAAa,GAAG,IAAI;IACtC,QAAQ,OAAO,CAAC,CAAC;QACf,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,KAAK,CAAC;QACnC,UAAU,GAAG,CAAC,MAAM;IACtB;IACA,OAAO;AACT;AACA,MAAM,mBAAmB,CAAC,SAAS;IACjC,IAAI,QAAQ,cAAc;QACxB,IAAI,OAAO,UAAU,EAAE;YACrB,OAAO,YAAY,GAAG,GAAG,OAAO,YAAY,CAAC,CAAC,CAAC;QACjD,OAAO;YACL,OAAO,YAAY,GAAG,GAAG,OAAO,YAAY,CAAC,CAAC,CAAC;QACjD;IACF;IACA,MAAM,UAAU,aAAa,UAAU,QAAQ,OAAO,GAAG;IACzD,MAAM,MAAM,mBAAmB,UAAU,UAAU,KAAK;IACxD,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD,EAAE,KAAK,KAAK,QAAQ,MAAM;IACnC,MAAM,UAAU,QAAQ,GAAG,CAAC;IAC5B,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IACA,MAAM,EAAE,aAAa,eAAe,EAAE,eAAe,cAAc,EAAE,GAAG,UAAU,CAAC;IACnF,MAAM,OAAO,GAAG,eAAe,YAAY;IAC3C,MAAM,mBAAmB,CAAC,SAAS,EAAE,MAAM;IAC3C,MAAM,eAAe,aAAa;IAClC,MAAM,eAAe,aAAa,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC;IAChE,IAAI,cAAc;QAChB,OAAO;IACT;IACA,OAAO;AACT;AACA,MAAM,iBAAiB,OAAO,SAAS;IACrC,MAAM,UAAU,mBAAmB,UAAU,UAAU,QAAQ,OAAO;IACtE,MAAM,UAAU,QAAQ,GAAG,CAAC;IAC5B,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IACA,MAAM,EAAE,aAAa,cAAc,EAAE,eAAe,aAAa,EAAE,GAAG,UAAU,CAAC;IACjF,MAAM,OAAO,QAAQ,aAAa,KAAK,IAAI,OAAO,QAAQ,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE,YAAY,GAAG,GAAG,aAAa,CAAC,EAAE,YAAY,GAAG,oLAAA,CAAA,IAAY,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,EAAE,YAAY,GAAG,GAAG,aAAa,CAAC,EAAE,YAAY;IACnO,MAAM,eAAe,aAAa;IAClC,MAAM,cAAc,aAAa,GAAG,CAAC;IACrC,IAAI,aAAa;QACf,MAAM,qBAAqB,CAAA,GAAA,iLAAA,CAAA,IAAa,AAAD,EAAE,8JAAA,CAAA,SAAM,CAAC,MAAM,CAAC,8JAAA,CAAA,YAAS,CAAC,MAAM,CAAC;QACxE,IAAI,CAAC,oBAAoB;YACvB,OAAO;QACT;QACA,MAAM,SAAS,QAAQ,UAAU,oLAAA,CAAA,IAAG,CAAC,kBAAkB;QACvD,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,oLAAA,CAAA,IAAe,CACvB;QAEJ;QACA,MAAM,UAAU,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,kBAAkB,MAAM,CAClE,QACA,KAAK,SAAS,CAAC;YACb,GAAG,mBAAmB,OAAO;YAC7B,WAAW,mBAAmB,SAAS;QACzC,IACA,mBAAmB,SAAS;QAE9B,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QACA,OAAO,mBAAmB,OAAO;IACnC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/api/index.mjs"], "sourcesContent": ["import { APIError, toR<PERSON>ponse, createRouter } from 'better-call';\nexport { APIError } from 'better-call';\nimport { j as createAuthEndpoint, B as BASE_ERROR_CODES, w as createEmailVerificationToken, x as wildcardMatch, y as listSessions, z as updateUser, m as getSession, A as originCheckMiddleware, C as error, D as ok, E as accountInfo, F as getAccessToken, G as refreshToken, I as unlinkAccount, J as deleteUserCallback, K as listUserAccounts, L as linkSocialAccount, M as revokeOtherSessions, N as revokeSessions, O as revokeSession, P as requestPasswordResetCallback, Q as requestPasswordReset, R as forgetPasswordCallback, S as deleteUser, T as setPassword, U as changePassword, V as changeEmail, W as sendVerificationEmail, X as verifyEmail, Y as resetPassword, Z as forgetPassword, _ as signInEmail, $ as signOut, a0 as callbackOAuth, a1 as signInSocial } from '../shared/better-auth.D4HhkCZJ.mjs';\nexport { i as createAuthMiddleware, n as freshSessionMiddleware, k as getSessionFromCtx, q as optionsMiddleware, o as originCheck, a2 as requestOnlySessionMiddleware, u as sendVerificationEmailFn, l as sessionMiddleware } from '../shared/better-auth.D4HhkCZJ.mjs';\nimport * as z from 'zod/v4';\nimport { setSessionCookie } from '../cookies/index.mjs';\nimport { f as parseUserInput } from '../shared/better-auth.n2KFGwjY.mjs';\nimport { b as isDevelopment } from '../shared/better-auth.8zoxzg-F.mjs';\nimport { a as logger } from '../shared/better-auth.DBGfIDnh.mjs';\nimport { g as getIp } from '../shared/better-auth.DcfNPS8q.mjs';\nimport defu from 'defu';\nimport '../shared/better-auth.CW6D9eSx.mjs';\nimport '@better-auth/utils/hash';\nimport '@better-auth/utils/base64';\nimport '../crypto/index.mjs';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-auth/utils/random';\nimport '@better-fetch/fetch';\nimport '../shared/better-auth.VTXNLFMT.mjs';\nimport '../shared/better-auth.DdzSJf-n.mjs';\nimport '../shared/better-auth.tB5eU6EY.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport 'jose/errors';\n\nconst signUpEmail = () => createAuthEndpoint(\n  \"/sign-up/email\",\n  {\n    method: \"POST\",\n    body: z.record(z.string(), z.any()),\n    metadata: {\n      $Infer: {\n        body: {}\n      },\n      openapi: {\n        description: \"Sign up a user using email and password\",\n        requestBody: {\n          content: {\n            \"application/json\": {\n              schema: {\n                type: \"object\",\n                properties: {\n                  name: {\n                    type: \"string\",\n                    description: \"The name of the user\"\n                  },\n                  email: {\n                    type: \"string\",\n                    description: \"The email of the user\"\n                  },\n                  password: {\n                    type: \"string\",\n                    description: \"The password of the user\"\n                  },\n                  image: {\n                    type: \"string\",\n                    description: \"The profile image URL of the user\"\n                  },\n                  callbackURL: {\n                    type: \"string\",\n                    description: \"The URL to use for email verification callback\"\n                  },\n                  rememberMe: {\n                    type: \"boolean\",\n                    description: \"If this is false, the session will not be remembered. Default is `true`.\"\n                  }\n                },\n                required: [\"name\", \"email\", \"password\"]\n              }\n            }\n          }\n        },\n        responses: {\n          \"200\": {\n            description: \"Successfully created user\",\n            content: {\n              \"application/json\": {\n                schema: {\n                  type: \"object\",\n                  properties: {\n                    token: {\n                      type: \"string\",\n                      nullable: true,\n                      description: \"Authentication token for the session\"\n                    },\n                    user: {\n                      type: \"object\",\n                      properties: {\n                        id: {\n                          type: \"string\",\n                          description: \"The unique identifier of the user\"\n                        },\n                        email: {\n                          type: \"string\",\n                          format: \"email\",\n                          description: \"The email address of the user\"\n                        },\n                        name: {\n                          type: \"string\",\n                          description: \"The name of the user\"\n                        },\n                        image: {\n                          type: \"string\",\n                          format: \"uri\",\n                          nullable: true,\n                          description: \"The profile image URL of the user\"\n                        },\n                        emailVerified: {\n                          type: \"boolean\",\n                          description: \"Whether the email has been verified\"\n                        },\n                        createdAt: {\n                          type: \"string\",\n                          format: \"date-time\",\n                          description: \"When the user was created\"\n                        },\n                        updatedAt: {\n                          type: \"string\",\n                          format: \"date-time\",\n                          description: \"When the user was last updated\"\n                        }\n                      },\n                      required: [\n                        \"id\",\n                        \"email\",\n                        \"name\",\n                        \"emailVerified\",\n                        \"createdAt\",\n                        \"updatedAt\"\n                      ]\n                    }\n                  },\n                  required: [\"user\"]\n                  // token is optional\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  },\n  async (ctx) => {\n    if (!ctx.context.options.emailAndPassword?.enabled || ctx.context.options.emailAndPassword?.disableSignUp) {\n      throw new APIError(\"BAD_REQUEST\", {\n        message: \"Email and password sign up is not enabled\"\n      });\n    }\n    const body = ctx.body;\n    const {\n      name,\n      email,\n      password,\n      image,\n      callbackURL,\n      rememberMe,\n      ...additionalFields\n    } = body;\n    const isValidEmail = z.email().safeParse(email);\n    if (!isValidEmail.success) {\n      throw new APIError(\"BAD_REQUEST\", {\n        message: BASE_ERROR_CODES.INVALID_EMAIL\n      });\n    }\n    const minPasswordLength = ctx.context.password.config.minPasswordLength;\n    if (password.length < minPasswordLength) {\n      ctx.context.logger.error(\"Password is too short\");\n      throw new APIError(\"BAD_REQUEST\", {\n        message: BASE_ERROR_CODES.PASSWORD_TOO_SHORT\n      });\n    }\n    const maxPasswordLength = ctx.context.password.config.maxPasswordLength;\n    if (password.length > maxPasswordLength) {\n      ctx.context.logger.error(\"Password is too long\");\n      throw new APIError(\"BAD_REQUEST\", {\n        message: BASE_ERROR_CODES.PASSWORD_TOO_LONG\n      });\n    }\n    const dbUser = await ctx.context.internalAdapter.findUserByEmail(email);\n    if (dbUser?.user) {\n      ctx.context.logger.info(`Sign-up attempt for existing email: ${email}`);\n      throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n        message: BASE_ERROR_CODES.USER_ALREADY_EXISTS\n      });\n    }\n    const additionalData = parseUserInput(\n      ctx.context.options,\n      additionalFields\n    );\n    const hash = await ctx.context.password.hash(password);\n    let createdUser;\n    try {\n      createdUser = await ctx.context.internalAdapter.createUser(\n        {\n          email: email.toLowerCase(),\n          name,\n          image,\n          ...additionalData,\n          emailVerified: false\n        },\n        ctx\n      );\n      if (!createdUser) {\n        throw new APIError(\"BAD_REQUEST\", {\n          message: BASE_ERROR_CODES.FAILED_TO_CREATE_USER\n        });\n      }\n    } catch (e) {\n      if (isDevelopment) {\n        ctx.context.logger.error(\"Failed to create user\", e);\n      }\n      if (e instanceof APIError) {\n        throw e;\n      }\n      throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n        message: BASE_ERROR_CODES.FAILED_TO_CREATE_USER,\n        details: e\n      });\n    }\n    if (!createdUser) {\n      throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n        message: BASE_ERROR_CODES.FAILED_TO_CREATE_USER\n      });\n    }\n    await ctx.context.internalAdapter.linkAccount(\n      {\n        userId: createdUser.id,\n        providerId: \"credential\",\n        accountId: createdUser.id,\n        password: hash\n      },\n      ctx\n    );\n    if (ctx.context.options.emailVerification?.sendOnSignUp || ctx.context.options.emailAndPassword.requireEmailVerification) {\n      const token = await createEmailVerificationToken(\n        ctx.context.secret,\n        createdUser.email,\n        void 0,\n        ctx.context.options.emailVerification?.expiresIn\n      );\n      const url = `${ctx.context.baseURL}/verify-email?token=${token}&callbackURL=${body.callbackURL || \"/\"}`;\n      await ctx.context.options.emailVerification?.sendVerificationEmail?.(\n        {\n          user: createdUser,\n          url,\n          token\n        },\n        ctx.request\n      );\n    }\n    if (ctx.context.options.emailAndPassword.autoSignIn === false || ctx.context.options.emailAndPassword.requireEmailVerification) {\n      return ctx.json({\n        token: null,\n        user: {\n          id: createdUser.id,\n          email: createdUser.email,\n          name: createdUser.name,\n          image: createdUser.image,\n          emailVerified: createdUser.emailVerified,\n          createdAt: createdUser.createdAt,\n          updatedAt: createdUser.updatedAt\n        }\n      });\n    }\n    const session = await ctx.context.internalAdapter.createSession(\n      createdUser.id,\n      ctx,\n      rememberMe === false\n    );\n    if (!session) {\n      throw new APIError(\"BAD_REQUEST\", {\n        message: BASE_ERROR_CODES.FAILED_TO_CREATE_SESSION\n      });\n    }\n    await setSessionCookie(\n      ctx,\n      {\n        session,\n        user: createdUser\n      },\n      rememberMe === false\n    );\n    return ctx.json({\n      token: session.token,\n      user: {\n        id: createdUser.id,\n        email: createdUser.email,\n        name: createdUser.name,\n        image: createdUser.image,\n        emailVerified: createdUser.emailVerified,\n        createdAt: createdUser.createdAt,\n        updatedAt: createdUser.updatedAt\n      }\n    });\n  }\n);\n\nfunction shouldRateLimit(max, window, rateLimitData) {\n  const now = Date.now();\n  const windowInMs = window * 1e3;\n  const timeSinceLastRequest = now - rateLimitData.lastRequest;\n  return timeSinceLastRequest < windowInMs && rateLimitData.count >= max;\n}\nfunction rateLimitResponse(retryAfter) {\n  return new Response(\n    JSON.stringify({\n      message: \"Too many requests. Please try again later.\"\n    }),\n    {\n      status: 429,\n      statusText: \"Too Many Requests\",\n      headers: {\n        \"X-Retry-After\": retryAfter.toString()\n      }\n    }\n  );\n}\nfunction getRetryAfter(lastRequest, window) {\n  const now = Date.now();\n  const windowInMs = window * 1e3;\n  return Math.ceil((lastRequest + windowInMs - now) / 1e3);\n}\nfunction createDBStorage(ctx) {\n  const model = ctx.options.rateLimit?.modelName || \"rateLimit\";\n  const db = ctx.adapter;\n  return {\n    get: async (key) => {\n      const res = await db.findMany({\n        model,\n        where: [{ field: \"key\", value: key }]\n      });\n      const data = res[0];\n      if (typeof data?.lastRequest === \"bigint\") {\n        data.lastRequest = Number(data.lastRequest);\n      }\n      return data;\n    },\n    set: async (key, value, _update) => {\n      try {\n        if (_update) {\n          await db.updateMany({\n            model: \"rateLimit\",\n            where: [{ field: \"key\", value: key }],\n            update: {\n              count: value.count,\n              lastRequest: value.lastRequest\n            }\n          });\n        } else {\n          await db.create({\n            model: \"rateLimit\",\n            data: {\n              key,\n              count: value.count,\n              lastRequest: value.lastRequest\n            }\n          });\n        }\n      } catch (e) {\n        ctx.logger.error(\"Error setting rate limit\", e);\n      }\n    }\n  };\n}\nconst memory = /* @__PURE__ */ new Map();\nfunction getRateLimitStorage(ctx) {\n  if (ctx.options.rateLimit?.customStorage) {\n    return ctx.options.rateLimit.customStorage;\n  }\n  if (ctx.rateLimit.storage === \"secondary-storage\") {\n    return {\n      get: async (key) => {\n        const stringified = await ctx.options.secondaryStorage?.get(key);\n        return stringified ? JSON.parse(stringified) : void 0;\n      },\n      set: async (key, value) => {\n        await ctx.options.secondaryStorage?.set?.(key, JSON.stringify(value));\n      }\n    };\n  }\n  const storage = ctx.rateLimit.storage;\n  if (storage === \"memory\") {\n    return {\n      async get(key) {\n        return memory.get(key);\n      },\n      async set(key, value, _update) {\n        memory.set(key, value);\n      }\n    };\n  }\n  return createDBStorage(ctx);\n}\nasync function onRequestRateLimit(req, ctx) {\n  if (!ctx.rateLimit.enabled) {\n    return;\n  }\n  const path = new URL(req.url).pathname.replace(\n    ctx.options.basePath || \"/api/auth\",\n    \"\"\n  );\n  let window = ctx.rateLimit.window;\n  let max = ctx.rateLimit.max;\n  const ip = getIp(req, ctx.options);\n  if (!ip) {\n    console.warn(\"No IP address found for rate limiting\");\n    return;\n  }\n  const key = ip + path;\n  const specialRules = getDefaultSpecialRules();\n  const specialRule = specialRules.find((rule) => rule.pathMatcher(path));\n  if (specialRule) {\n    window = specialRule.window;\n    max = specialRule.max;\n  }\n  for (const plugin of ctx.options.plugins || []) {\n    if (plugin.rateLimit) {\n      const matchedRule = plugin.rateLimit.find(\n        (rule) => rule.pathMatcher(path)\n      );\n      if (matchedRule) {\n        window = matchedRule.window;\n        max = matchedRule.max;\n        break;\n      }\n    }\n  }\n  if (ctx.rateLimit.customRules) {\n    const _path = Object.keys(ctx.rateLimit.customRules).find((p) => {\n      if (p.includes(\"*\")) {\n        const isMatch = wildcardMatch(p)(path);\n        return isMatch;\n      }\n      return p === path;\n    });\n    if (_path) {\n      const customRule = ctx.rateLimit.customRules[_path];\n      const resolved = typeof customRule === \"function\" ? await customRule(req) : customRule;\n      if (resolved) {\n        window = resolved.window;\n        max = resolved.max;\n      }\n    }\n  }\n  const storage = getRateLimitStorage(ctx);\n  const data = await storage.get(key);\n  const now = Date.now();\n  if (!data) {\n    await storage.set(key, {\n      key,\n      count: 1,\n      lastRequest: now\n    });\n  } else {\n    const timeSinceLastRequest = now - data.lastRequest;\n    if (shouldRateLimit(max, window, data)) {\n      const retryAfter = getRetryAfter(data.lastRequest, window);\n      return rateLimitResponse(retryAfter);\n    } else if (timeSinceLastRequest > window * 1e3) {\n      await storage.set(\n        key,\n        {\n          ...data,\n          count: 1,\n          lastRequest: now\n        },\n        true\n      );\n    } else {\n      await storage.set(\n        key,\n        {\n          ...data,\n          count: data.count + 1,\n          lastRequest: now\n        },\n        true\n      );\n    }\n  }\n}\nfunction getDefaultSpecialRules() {\n  const specialRules = [\n    {\n      pathMatcher(path) {\n        return path.startsWith(\"/sign-in\") || path.startsWith(\"/sign-up\") || path.startsWith(\"/change-password\") || path.startsWith(\"/change-email\");\n      },\n      window: 10,\n      max: 3\n    }\n  ];\n  return specialRules;\n}\n\nfunction toAuthEndpoints(endpoints, ctx) {\n  const api = {};\n  for (const [key, endpoint] of Object.entries(endpoints)) {\n    api[key] = async (context) => {\n      const authContext = await ctx;\n      let internalContext = {\n        ...context,\n        context: {\n          ...authContext,\n          returned: void 0,\n          responseHeaders: void 0,\n          session: null\n        },\n        path: endpoint.path,\n        headers: context?.headers ? new Headers(context?.headers) : void 0\n      };\n      const { beforeHooks, afterHooks } = getHooks(authContext);\n      const before = await runBeforeHooks(internalContext, beforeHooks);\n      if (\"context\" in before && before.context && typeof before.context === \"object\") {\n        const { headers, ...rest } = before.context;\n        if (headers) {\n          headers.forEach((value, key2) => {\n            internalContext.headers.set(key2, value);\n          });\n        }\n        internalContext = defu(rest, internalContext);\n      } else if (before) {\n        return before;\n      }\n      internalContext.asResponse = false;\n      internalContext.returnHeaders = true;\n      const result = await endpoint(internalContext).catch((e) => {\n        if (e instanceof APIError) {\n          return {\n            response: e,\n            headers: e.headers ? new Headers(e.headers) : null\n          };\n        }\n        throw e;\n      });\n      internalContext.context.returned = result.response;\n      internalContext.context.responseHeaders = result.headers;\n      const after = await runAfterHooks(internalContext, afterHooks);\n      if (after.response) {\n        result.response = after.response;\n      }\n      if (result.response instanceof APIError && !context?.asResponse) {\n        throw result.response;\n      }\n      const response = context?.asResponse ? toResponse(result.response, {\n        headers: result.headers\n      }) : context?.returnHeaders ? {\n        headers: result.headers,\n        response: result.response\n      } : result.response;\n      return response;\n    };\n    api[key].path = endpoint.path;\n    api[key].options = endpoint.options;\n  }\n  return api;\n}\nasync function runBeforeHooks(context, hooks) {\n  let modifiedContext = {};\n  for (const hook of hooks) {\n    if (hook.matcher(context)) {\n      const result = await hook.handler({\n        ...context,\n        returnHeaders: false\n      });\n      if (result && typeof result === \"object\") {\n        if (\"context\" in result && typeof result.context === \"object\") {\n          const { headers, ...rest } = result.context;\n          if (headers instanceof Headers) {\n            if (modifiedContext.headers) {\n              headers.forEach((value, key) => {\n                modifiedContext.headers?.set(key, value);\n              });\n            } else {\n              modifiedContext.headers = headers;\n            }\n          }\n          modifiedContext = defu(rest, modifiedContext);\n          continue;\n        }\n        return result;\n      }\n    }\n  }\n  return { context: modifiedContext };\n}\nasync function runAfterHooks(context, hooks) {\n  for (const hook of hooks) {\n    if (hook.matcher(context)) {\n      const result = await hook.handler(context).catch((e) => {\n        if (e instanceof APIError) {\n          return {\n            response: e,\n            headers: e.headers ? new Headers(e.headers) : null\n          };\n        }\n        throw e;\n      });\n      if (result.headers) {\n        result.headers.forEach((value, key) => {\n          if (!context.context.responseHeaders) {\n            context.context.responseHeaders = new Headers({\n              [key]: value\n            });\n          } else {\n            if (key.toLowerCase() === \"set-cookie\") {\n              context.context.responseHeaders.append(key, value);\n            } else {\n              context.context.responseHeaders.set(key, value);\n            }\n          }\n        });\n      }\n      if (result.response) {\n        context.context.returned = result.response;\n      }\n    }\n  }\n  return {\n    response: context.context.returned,\n    headers: context.context.responseHeaders\n  };\n}\nfunction getHooks(authContext) {\n  const plugins = authContext.options.plugins || [];\n  const beforeHooks = [];\n  const afterHooks = [];\n  if (authContext.options.hooks?.before) {\n    beforeHooks.push({\n      matcher: () => true,\n      handler: authContext.options.hooks.before\n    });\n  }\n  if (authContext.options.hooks?.after) {\n    afterHooks.push({\n      matcher: () => true,\n      handler: authContext.options.hooks.after\n    });\n  }\n  const pluginBeforeHooks = plugins.map((plugin) => {\n    if (plugin.hooks?.before) {\n      return plugin.hooks.before;\n    }\n  }).filter((plugin) => plugin !== void 0).flat();\n  const pluginAfterHooks = plugins.map((plugin) => {\n    if (plugin.hooks?.after) {\n      return plugin.hooks.after;\n    }\n  }).filter((plugin) => plugin !== void 0).flat();\n  pluginBeforeHooks.length && beforeHooks.push(...pluginBeforeHooks);\n  pluginAfterHooks.length && afterHooks.push(...pluginAfterHooks);\n  return {\n    beforeHooks,\n    afterHooks\n  };\n}\n\nfunction getEndpoints(ctx, options) {\n  const pluginEndpoints = options.plugins?.reduce(\n    (acc, plugin) => {\n      return {\n        ...acc,\n        ...plugin.endpoints\n      };\n    },\n    {}\n  );\n  const middlewares = options.plugins?.map(\n    (plugin) => plugin.middlewares?.map((m) => {\n      const middleware = async (context) => {\n        return m.middleware({\n          ...context,\n          context: {\n            ...ctx,\n            ...context.context\n          }\n        });\n      };\n      middleware.options = m.middleware.options;\n      return {\n        path: m.path,\n        middleware\n      };\n    })\n  ).filter((plugin) => plugin !== void 0).flat() || [];\n  const baseEndpoints = {\n    signInSocial,\n    callbackOAuth,\n    getSession: getSession(),\n    signOut,\n    signUpEmail: signUpEmail(),\n    signInEmail,\n    forgetPassword,\n    resetPassword,\n    verifyEmail,\n    sendVerificationEmail,\n    changeEmail,\n    changePassword,\n    setPassword,\n    updateUser: updateUser(),\n    deleteUser,\n    forgetPasswordCallback,\n    requestPasswordReset,\n    requestPasswordResetCallback,\n    listSessions: listSessions(),\n    revokeSession,\n    revokeSessions,\n    revokeOtherSessions,\n    linkSocialAccount,\n    listUserAccounts,\n    deleteUserCallback,\n    unlinkAccount,\n    refreshToken,\n    getAccessToken,\n    accountInfo\n  };\n  const endpoints = {\n    ...baseEndpoints,\n    ...pluginEndpoints,\n    ok,\n    error\n  };\n  const api = toAuthEndpoints(endpoints, ctx);\n  return {\n    api,\n    middlewares\n  };\n}\nconst router = (ctx, options) => {\n  const { api, middlewares } = getEndpoints(ctx, options);\n  const basePath = new URL(ctx.baseURL).pathname;\n  return createRouter(api, {\n    routerContext: ctx,\n    openapi: {\n      disabled: true\n    },\n    basePath,\n    routerMiddleware: [\n      {\n        path: \"/**\",\n        middleware: originCheckMiddleware\n      },\n      ...middlewares\n    ],\n    async onRequest(req) {\n      const disabledPaths = ctx.options.disabledPaths || [];\n      const path = new URL(req.url).pathname.replace(basePath, \"\");\n      if (disabledPaths.includes(path)) {\n        return new Response(\"Not Found\", { status: 404 });\n      }\n      for (const plugin of ctx.options.plugins || []) {\n        if (plugin.onRequest) {\n          const response = await plugin.onRequest(req, ctx);\n          if (response && \"response\" in response) {\n            return response.response;\n          }\n        }\n      }\n      return onRequestRateLimit(req, ctx);\n    },\n    async onResponse(res) {\n      for (const plugin of ctx.options.plugins || []) {\n        if (plugin.onResponse) {\n          const response = await plugin.onResponse(res, ctx);\n          if (response) {\n            return response.response;\n          }\n        }\n      }\n      return res;\n    },\n    onError(e) {\n      if (e instanceof APIError && e.status === \"FOUND\") {\n        return;\n      }\n      if (options.onAPIError?.throw) {\n        throw e;\n      }\n      if (options.onAPIError?.onError) {\n        options.onAPIError.onError(e, ctx);\n        return;\n      }\n      const optLogLevel = options.logger?.level;\n      const log = optLogLevel === \"error\" || optLogLevel === \"warn\" || optLogLevel === \"debug\" ? logger : void 0;\n      if (options.logger?.disabled !== true) {\n        if (e && typeof e === \"object\" && \"message\" in e && typeof e.message === \"string\") {\n          if (e.message.includes(\"no column\") || e.message.includes(\"column\") || e.message.includes(\"relation\") || e.message.includes(\"table\") || e.message.includes(\"does not exist\")) {\n            ctx.logger?.error(e.message);\n            return;\n          }\n        }\n        if (e instanceof APIError) {\n          if (e.status === \"INTERNAL_SERVER_ERROR\") {\n            ctx.logger.error(e.status, e);\n          }\n          log?.error(e.message);\n        } else {\n          ctx.logger?.error(\n            e && typeof e === \"object\" && \"name\" in e ? e.name : \"\",\n            e\n          );\n        }\n      }\n    }\n  });\n};\n\nexport { accountInfo, callbackOAuth, changeEmail, changePassword, createAuthEndpoint, createEmailVerificationToken, deleteUser, deleteUserCallback, error, forgetPassword, forgetPasswordCallback, getAccessToken, getEndpoints, getSession, linkSocialAccount, listSessions, listUserAccounts, ok, originCheckMiddleware, refreshToken, requestPasswordReset, requestPasswordResetCallback, resetPassword, revokeOtherSessions, revokeSession, revokeSessions, router, sendVerificationEmail, setPassword, signInEmail, signInSocial, signOut, signUpEmail, unlinkAccount, updateUser, verifyEmail };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,cAAc,IAAM,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACzC,kBACA;QACE,QAAQ;QACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,KAAK,CAAA,GAAA,mLAAA,CAAA,MAAK,AAAD;QAC/B,UAAU;YACR,QAAQ;gBACN,MAAM,CAAC;YACT;YACA,SAAS;gBACP,aAAa;gBACb,aAAa;oBACX,SAAS;wBACP,oBAAoB;4BAClB,QAAQ;gCACN,MAAM;gCACN,YAAY;oCACV,MAAM;wCACJ,MAAM;wCACN,aAAa;oCACf;oCACA,OAAO;wCACL,MAAM;wCACN,aAAa;oCACf;oCACA,UAAU;wCACR,MAAM;wCACN,aAAa;oCACf;oCACA,OAAO;wCACL,MAAM;wCACN,aAAa;oCACf;oCACA,aAAa;wCACX,MAAM;wCACN,aAAa;oCACf;oCACA,YAAY;wCACV,MAAM;wCACN,aAAa;oCACf;gCACF;gCACA,UAAU;oCAAC;oCAAQ;oCAAS;iCAAW;4BACzC;wBACF;oBACF;gBACF;gBACA,WAAW;oBACT,OAAO;wBACL,aAAa;wBACb,SAAS;4BACP,oBAAoB;gCAClB,QAAQ;oCACN,MAAM;oCACN,YAAY;wCACV,OAAO;4CACL,MAAM;4CACN,UAAU;4CACV,aAAa;wCACf;wCACA,MAAM;4CACJ,MAAM;4CACN,YAAY;gDACV,IAAI;oDACF,MAAM;oDACN,aAAa;gDACf;gDACA,OAAO;oDACL,MAAM;oDACN,QAAQ;oDACR,aAAa;gDACf;gDACA,MAAM;oDACJ,MAAM;oDACN,aAAa;gDACf;gDACA,OAAO;oDACL,MAAM;oDACN,QAAQ;oDACR,UAAU;oDACV,aAAa;gDACf;gDACA,eAAe;oDACb,MAAM;oDACN,aAAa;gDACf;gDACA,WAAW;oDACT,MAAM;oDACN,QAAQ;oDACR,aAAa;gDACf;gDACA,WAAW;oDACT,MAAM;oDACN,QAAQ;oDACR,aAAa;gDACf;4CACF;4CACA,UAAU;gDACR;gDACA;gDACA;gDACA;gDACA;gDACA;6CACD;wCACH;oCACF;oCACA,UAAU;wCAAC;qCAAO;gCAEpB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF,GACA,OAAO;QACL,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,eAAe;YACzG,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS;YACX;QACF;QACA,MAAM,OAAO,IAAI,IAAI;QACrB,MAAM,EACJ,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,EACX,UAAU,EACV,GAAG,kBACJ,GAAG;QACJ,MAAM,eAAe,CAAA,GAAA,mLAAA,CAAA,QAAO,AAAD,IAAI,SAAS,CAAC;QACzC,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,aAAa;YACzC;QACF;QACA,MAAM,oBAAoB,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,iBAAiB;QACvE,IAAI,SAAS,MAAM,GAAG,mBAAmB;YACvC,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;YACzB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,kBAAkB;YAC9C;QACF;QACA,MAAM,oBAAoB,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,iBAAiB;QACvE,IAAI,SAAS,MAAM,GAAG,mBAAmB;YACvC,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;YACzB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,iBAAiB;YAC7C;QACF;QACA,MAAM,SAAS,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC;QACjE,IAAI,QAAQ,MAAM;YAChB,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,oCAAoC,EAAE,OAAO;YACtE,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;gBACzC,SAAS,iLAAA,CAAA,IAAgB,CAAC,mBAAmB;YAC/C;QACF;QACA,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,IAAc,AAAD,EAClC,IAAI,OAAO,CAAC,OAAO,EACnB;QAEF,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC7C,IAAI;QACJ,IAAI;YACF,cAAc,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CACxD;gBACE,OAAO,MAAM,WAAW;gBACxB;gBACA;gBACA,GAAG,cAAc;gBACjB,eAAe;YACjB,GACA;YAEF,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;oBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,qBAAqB;gBACjD;YACF;QACF,EAAE,OAAO,GAAG;YACV,IAAI,oLAAA,CAAA,IAAa,EAAE;gBACjB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB;YACpD;YACA,IAAI,aAAa,iJAAA,CAAA,WAAQ,EAAE;gBACzB,MAAM;YACR;YACA,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;gBACzC,SAAS,iLAAA,CAAA,IAAgB,CAAC,qBAAqB;gBAC/C,SAAS;YACX;QACF;QACA,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;gBACzC,SAAS,iLAAA,CAAA,IAAgB,CAAC,qBAAqB;YACjD;QACF;QACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,WAAW,CAC3C;YACE,QAAQ,YAAY,EAAE;YACtB,YAAY;YACZ,WAAW,YAAY,EAAE;YACzB,UAAU;QACZ,GACA;QAEF,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,wBAAwB,EAAE;YACxH,MAAM,QAAQ,MAAM,CAAA,GAAA,iLAAA,CAAA,IAA4B,AAAD,EAC7C,IAAI,OAAO,CAAC,MAAM,EAClB,YAAY,KAAK,EACjB,KAAK,GACL,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAEzC,MAAM,MAAM,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,oBAAoB,EAAE,MAAM,aAAa,EAAE,KAAK,WAAW,IAAI,KAAK;YACvG,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,wBAC3C;gBACE,MAAM;gBACN;gBACA;YACF,GACA,IAAI,OAAO;QAEf;QACA,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,UAAU,KAAK,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,wBAAwB,EAAE;YAC9H,OAAO,IAAI,IAAI,CAAC;gBACd,OAAO;gBACP,MAAM;oBACJ,IAAI,YAAY,EAAE;oBAClB,OAAO,YAAY,KAAK;oBACxB,MAAM,YAAY,IAAI;oBACtB,OAAO,YAAY,KAAK;oBACxB,eAAe,YAAY,aAAa;oBACxC,WAAW,YAAY,SAAS;oBAChC,WAAW,YAAY,SAAS;gBAClC;YACF;QACF;QACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7D,YAAY,EAAE,EACd,KACA,eAAe;QAEjB,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,wBAAwB;YACpD;QACF;QACA,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EACnB,KACA;YACE;YACA,MAAM;QACR,GACA,eAAe;QAEjB,OAAO,IAAI,IAAI,CAAC;YACd,OAAO,QAAQ,KAAK;YACpB,MAAM;gBACJ,IAAI,YAAY,EAAE;gBAClB,OAAO,YAAY,KAAK;gBACxB,MAAM,YAAY,IAAI;gBACtB,OAAO,YAAY,KAAK;gBACxB,eAAe,YAAY,aAAa;gBACxC,WAAW,YAAY,SAAS;gBAChC,WAAW,YAAY,SAAS;YAClC;QACF;IACF;AAGF,SAAS,gBAAgB,GAAG,EAAE,MAAM,EAAE,aAAa;IACjD,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,aAAa,SAAS;IAC5B,MAAM,uBAAuB,MAAM,cAAc,WAAW;IAC5D,OAAO,uBAAuB,cAAc,cAAc,KAAK,IAAI;AACrE;AACA,SAAS,kBAAkB,UAAU;IACnC,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;QACb,SAAS;IACX,IACA;QACE,QAAQ;QACR,YAAY;QACZ,SAAS;YACP,iBAAiB,WAAW,QAAQ;QACtC;IACF;AAEJ;AACA,SAAS,cAAc,WAAW,EAAE,MAAM;IACxC,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,aAAa,SAAS;IAC5B,OAAO,KAAK,IAAI,CAAC,CAAC,cAAc,aAAa,GAAG,IAAI;AACtD;AACA,SAAS,gBAAgB,GAAG;IAC1B,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,aAAa;IAClD,MAAM,KAAK,IAAI,OAAO;IACtB,OAAO;QACL,KAAK,OAAO;YACV,MAAM,MAAM,MAAM,GAAG,QAAQ,CAAC;gBAC5B;gBACA,OAAO;oBAAC;wBAAE,OAAO;wBAAO,OAAO;oBAAI;iBAAE;YACvC;YACA,MAAM,OAAO,GAAG,CAAC,EAAE;YACnB,IAAI,OAAO,MAAM,gBAAgB,UAAU;gBACzC,KAAK,WAAW,GAAG,OAAO,KAAK,WAAW;YAC5C;YACA,OAAO;QACT;QACA,KAAK,OAAO,KAAK,OAAO;YACtB,IAAI;gBACF,IAAI,SAAS;oBACX,MAAM,GAAG,UAAU,CAAC;wBAClB,OAAO;wBACP,OAAO;4BAAC;gCAAE,OAAO;gCAAO,OAAO;4BAAI;yBAAE;wBACrC,QAAQ;4BACN,OAAO,MAAM,KAAK;4BAClB,aAAa,MAAM,WAAW;wBAChC;oBACF;gBACF,OAAO;oBACL,MAAM,GAAG,MAAM,CAAC;wBACd,OAAO;wBACP,MAAM;4BACJ;4BACA,OAAO,MAAM,KAAK;4BAClB,aAAa,MAAM,WAAW;wBAChC;oBACF;gBACF;YACF,EAAE,OAAO,GAAG;gBACV,IAAI,MAAM,CAAC,KAAK,CAAC,4BAA4B;YAC/C;QACF;IACF;AACF;AACA,MAAM,SAAS,aAAa,GAAG,IAAI;AACnC,SAAS,oBAAoB,GAAG;IAC9B,IAAI,IAAI,OAAO,CAAC,SAAS,EAAE,eAAe;QACxC,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,aAAa;IAC5C;IACA,IAAI,IAAI,SAAS,CAAC,OAAO,KAAK,qBAAqB;QACjD,OAAO;YACL,KAAK,OAAO;gBACV,MAAM,cAAc,MAAM,IAAI,OAAO,CAAC,gBAAgB,EAAE,IAAI;gBAC5D,OAAO,cAAc,KAAK,KAAK,CAAC,eAAe,KAAK;YACtD;YACA,KAAK,OAAO,KAAK;gBACf,MAAM,IAAI,OAAO,CAAC,gBAAgB,EAAE,MAAM,KAAK,KAAK,SAAS,CAAC;YAChE;QACF;IACF;IACA,MAAM,UAAU,IAAI,SAAS,CAAC,OAAO;IACrC,IAAI,YAAY,UAAU;QACxB,OAAO;YACL,MAAM,KAAI,GAAG;gBACX,OAAO,OAAO,GAAG,CAAC;YACpB;YACA,MAAM,KAAI,GAAG,EAAE,KAAK,EAAE,OAAO;gBAC3B,OAAO,GAAG,CAAC,KAAK;YAClB;QACF;IACF;IACA,OAAO,gBAAgB;AACzB;AACA,eAAe,mBAAmB,GAAG,EAAE,GAAG;IACxC,IAAI,CAAC,IAAI,SAAS,CAAC,OAAO,EAAE;QAC1B;IACF;IACA,MAAM,OAAO,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC,OAAO,CAC5C,IAAI,OAAO,CAAC,QAAQ,IAAI,aACxB;IAEF,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM;IACjC,IAAI,MAAM,IAAI,SAAS,CAAC,GAAG;IAC3B,MAAM,KAAK,CAAA,GAAA,iLAAA,CAAA,IAAK,AAAD,EAAE,KAAK,IAAI,OAAO;IACjC,IAAI,CAAC,IAAI;QACP,QAAQ,IAAI,CAAC;QACb;IACF;IACA,MAAM,MAAM,KAAK;IACjB,MAAM,eAAe;IACrB,MAAM,cAAc,aAAa,IAAI,CAAC,CAAC,OAAS,KAAK,WAAW,CAAC;IACjE,IAAI,aAAa;QACf,SAAS,YAAY,MAAM;QAC3B,MAAM,YAAY,GAAG;IACvB;IACA,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE,CAAE;QAC9C,IAAI,OAAO,SAAS,EAAE;YACpB,MAAM,cAAc,OAAO,SAAS,CAAC,IAAI,CACvC,CAAC,OAAS,KAAK,WAAW,CAAC;YAE7B,IAAI,aAAa;gBACf,SAAS,YAAY,MAAM;gBAC3B,MAAM,YAAY,GAAG;gBACrB;YACF;QACF;IACF;IACA,IAAI,IAAI,SAAS,CAAC,WAAW,EAAE;QAC7B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACzD,IAAI,EAAE,QAAQ,CAAC,MAAM;gBACnB,MAAM,UAAU,CAAA,GAAA,iLAAA,CAAA,IAAa,AAAD,EAAE,GAAG;gBACjC,OAAO;YACT;YACA,OAAO,MAAM;QACf;QACA,IAAI,OAAO;YACT,MAAM,aAAa,IAAI,SAAS,CAAC,WAAW,CAAC,MAAM;YACnD,MAAM,WAAW,OAAO,eAAe,aAAa,MAAM,WAAW,OAAO;YAC5E,IAAI,UAAU;gBACZ,SAAS,SAAS,MAAM;gBACxB,MAAM,SAAS,GAAG;YACpB;QACF;IACF;IACA,MAAM,UAAU,oBAAoB;IACpC,MAAM,OAAO,MAAM,QAAQ,GAAG,CAAC;IAC/B,MAAM,MAAM,KAAK,GAAG;IACpB,IAAI,CAAC,MAAM;QACT,MAAM,QAAQ,GAAG,CAAC,KAAK;YACrB;YACA,OAAO;YACP,aAAa;QACf;IACF,OAAO;QACL,MAAM,uBAAuB,MAAM,KAAK,WAAW;QACnD,IAAI,gBAAgB,KAAK,QAAQ,OAAO;YACtC,MAAM,aAAa,cAAc,KAAK,WAAW,EAAE;YACnD,OAAO,kBAAkB;QAC3B,OAAO,IAAI,uBAAuB,SAAS,KAAK;YAC9C,MAAM,QAAQ,GAAG,CACf,KACA;gBACE,GAAG,IAAI;gBACP,OAAO;gBACP,aAAa;YACf,GACA;QAEJ,OAAO;YACL,MAAM,QAAQ,GAAG,CACf,KACA;gBACE,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,GAAG;gBACpB,aAAa;YACf,GACA;QAEJ;IACF;AACF;AACA,SAAS;IACP,MAAM,eAAe;QACnB;YACE,aAAY,IAAI;gBACd,OAAO,KAAK,UAAU,CAAC,eAAe,KAAK,UAAU,CAAC,eAAe,KAAK,UAAU,CAAC,uBAAuB,KAAK,UAAU,CAAC;YAC9H;YACA,QAAQ;YACR,KAAK;QACP;KACD;IACD,OAAO;AACT;AAEA,SAAS,gBAAgB,SAAS,EAAE,GAAG;IACrC,MAAM,MAAM,CAAC;IACb,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,OAAO,OAAO,CAAC,WAAY;QACvD,GAAG,CAAC,IAAI,GAAG,OAAO;YAChB,MAAM,cAAc,MAAM;YAC1B,IAAI,kBAAkB;gBACpB,GAAG,OAAO;gBACV,SAAS;oBACP,GAAG,WAAW;oBACd,UAAU,KAAK;oBACf,iBAAiB,KAAK;oBACtB,SAAS;gBACX;gBACA,MAAM,SAAS,IAAI;gBACnB,SAAS,SAAS,UAAU,IAAI,QAAQ,SAAS,WAAW,KAAK;YACnE;YACA,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,SAAS;YAC7C,MAAM,SAAS,MAAM,eAAe,iBAAiB;YACrD,IAAI,aAAa,UAAU,OAAO,OAAO,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU;gBAC/E,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,GAAG,OAAO,OAAO;gBAC3C,IAAI,SAAS;oBACX,QAAQ,OAAO,CAAC,CAAC,OAAO;wBACtB,gBAAgB,OAAO,CAAC,GAAG,CAAC,MAAM;oBACpC;gBACF;gBACA,kBAAkB,CAAA,GAAA,uIAAA,CAAA,UAAI,AAAD,EAAE,MAAM;YAC/B,OAAO,IAAI,QAAQ;gBACjB,OAAO;YACT;YACA,gBAAgB,UAAU,GAAG;YAC7B,gBAAgB,aAAa,GAAG;YAChC,MAAM,SAAS,MAAM,SAAS,iBAAiB,KAAK,CAAC,CAAC;gBACpD,IAAI,aAAa,iJAAA,CAAA,WAAQ,EAAE;oBACzB,OAAO;wBACL,UAAU;wBACV,SAAS,EAAE,OAAO,GAAG,IAAI,QAAQ,EAAE,OAAO,IAAI;oBAChD;gBACF;gBACA,MAAM;YACR;YACA,gBAAgB,OAAO,CAAC,QAAQ,GAAG,OAAO,QAAQ;YAClD,gBAAgB,OAAO,CAAC,eAAe,GAAG,OAAO,OAAO;YACxD,MAAM,QAAQ,MAAM,cAAc,iBAAiB;YACnD,IAAI,MAAM,QAAQ,EAAE;gBAClB,OAAO,QAAQ,GAAG,MAAM,QAAQ;YAClC;YACA,IAAI,OAAO,QAAQ,YAAY,iJAAA,CAAA,WAAQ,IAAI,CAAC,SAAS,YAAY;gBAC/D,MAAM,OAAO,QAAQ;YACvB;YACA,MAAM,WAAW,SAAS,aAAa,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,OAAO,QAAQ,EAAE;gBACjE,SAAS,OAAO,OAAO;YACzB,KAAK,SAAS,gBAAgB;gBAC5B,SAAS,OAAO,OAAO;gBACvB,UAAU,OAAO,QAAQ;YAC3B,IAAI,OAAO,QAAQ;YACnB,OAAO;QACT;QACA,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,IAAI;QAC7B,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,SAAS,OAAO;IACrC;IACA,OAAO;AACT;AACA,eAAe,eAAe,OAAO,EAAE,KAAK;IAC1C,IAAI,kBAAkB,CAAC;IACvB,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,OAAO,CAAC,UAAU;YACzB,MAAM,SAAS,MAAM,KAAK,OAAO,CAAC;gBAChC,GAAG,OAAO;gBACV,eAAe;YACjB;YACA,IAAI,UAAU,OAAO,WAAW,UAAU;gBACxC,IAAI,aAAa,UAAU,OAAO,OAAO,OAAO,KAAK,UAAU;oBAC7D,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,GAAG,OAAO,OAAO;oBAC3C,IAAI,mBAAmB,SAAS;wBAC9B,IAAI,gBAAgB,OAAO,EAAE;4BAC3B,QAAQ,OAAO,CAAC,CAAC,OAAO;gCACtB,gBAAgB,OAAO,EAAE,IAAI,KAAK;4BACpC;wBACF,OAAO;4BACL,gBAAgB,OAAO,GAAG;wBAC5B;oBACF;oBACA,kBAAkB,CAAA,GAAA,uIAAA,CAAA,UAAI,AAAD,EAAE,MAAM;oBAC7B;gBACF;gBACA,OAAO;YACT;QACF;IACF;IACA,OAAO;QAAE,SAAS;IAAgB;AACpC;AACA,eAAe,cAAc,OAAO,EAAE,KAAK;IACzC,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,OAAO,CAAC,UAAU;YACzB,MAAM,SAAS,MAAM,KAAK,OAAO,CAAC,SAAS,KAAK,CAAC,CAAC;gBAChD,IAAI,aAAa,iJAAA,CAAA,WAAQ,EAAE;oBACzB,OAAO;wBACL,UAAU;wBACV,SAAS,EAAE,OAAO,GAAG,IAAI,QAAQ,EAAE,OAAO,IAAI;oBAChD;gBACF;gBACA,MAAM;YACR;YACA,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;oBAC7B,IAAI,CAAC,QAAQ,OAAO,CAAC,eAAe,EAAE;wBACpC,QAAQ,OAAO,CAAC,eAAe,GAAG,IAAI,QAAQ;4BAC5C,CAAC,IAAI,EAAE;wBACT;oBACF,OAAO;wBACL,IAAI,IAAI,WAAW,OAAO,cAAc;4BACtC,QAAQ,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK;wBAC9C,OAAO;4BACL,QAAQ,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;wBAC3C;oBACF;gBACF;YACF;YACA,IAAI,OAAO,QAAQ,EAAE;gBACnB,QAAQ,OAAO,CAAC,QAAQ,GAAG,OAAO,QAAQ;YAC5C;QACF;IACF;IACA,OAAO;QACL,UAAU,QAAQ,OAAO,CAAC,QAAQ;QAClC,SAAS,QAAQ,OAAO,CAAC,eAAe;IAC1C;AACF;AACA,SAAS,SAAS,WAAW;IAC3B,MAAM,UAAU,YAAY,OAAO,CAAC,OAAO,IAAI,EAAE;IACjD,MAAM,cAAc,EAAE;IACtB,MAAM,aAAa,EAAE;IACrB,IAAI,YAAY,OAAO,CAAC,KAAK,EAAE,QAAQ;QACrC,YAAY,IAAI,CAAC;YACf,SAAS,IAAM;YACf,SAAS,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM;QAC3C;IACF;IACA,IAAI,YAAY,OAAO,CAAC,KAAK,EAAE,OAAO;QACpC,WAAW,IAAI,CAAC;YACd,SAAS,IAAM;YACf,SAAS,YAAY,OAAO,CAAC,KAAK,CAAC,KAAK;QAC1C;IACF;IACA,MAAM,oBAAoB,QAAQ,GAAG,CAAC,CAAC;QACrC,IAAI,OAAO,KAAK,EAAE,QAAQ;YACxB,OAAO,OAAO,KAAK,CAAC,MAAM;QAC5B;IACF,GAAG,MAAM,CAAC,CAAC,SAAW,WAAW,KAAK,GAAG,IAAI;IAC7C,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAC;QACpC,IAAI,OAAO,KAAK,EAAE,OAAO;YACvB,OAAO,OAAO,KAAK,CAAC,KAAK;QAC3B;IACF,GAAG,MAAM,CAAC,CAAC,SAAW,WAAW,KAAK,GAAG,IAAI;IAC7C,kBAAkB,MAAM,IAAI,YAAY,IAAI,IAAI;IAChD,iBAAiB,MAAM,IAAI,WAAW,IAAI,IAAI;IAC9C,OAAO;QACL;QACA;IACF;AACF;AAEA,SAAS,aAAa,GAAG,EAAE,OAAO;IAChC,MAAM,kBAAkB,QAAQ,OAAO,EAAE,OACvC,CAAC,KAAK;QACJ,OAAO;YACL,GAAG,GAAG;YACN,GAAG,OAAO,SAAS;QACrB;IACF,GACA,CAAC;IAEH,MAAM,cAAc,QAAQ,OAAO,EAAE,IACnC,CAAC,SAAW,OAAO,WAAW,EAAE,IAAI,CAAC;YACnC,MAAM,aAAa,OAAO;gBACxB,OAAO,EAAE,UAAU,CAAC;oBAClB,GAAG,OAAO;oBACV,SAAS;wBACP,GAAG,GAAG;wBACN,GAAG,QAAQ,OAAO;oBACpB;gBACF;YACF;YACA,WAAW,OAAO,GAAG,EAAE,UAAU,CAAC,OAAO;YACzC,OAAO;gBACL,MAAM,EAAE,IAAI;gBACZ;YACF;QACF,IACA,OAAO,CAAC,SAAW,WAAW,KAAK,GAAG,UAAU,EAAE;IACpD,MAAM,gBAAgB;QACpB,cAAA,iLAAA,CAAA,KAAY;QACZ,eAAA,iLAAA,CAAA,KAAa;QACb,YAAY,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD;QACrB,SAAA,iLAAA,CAAA,IAAO;QACP,aAAa;QACb,aAAA,iLAAA,CAAA,IAAW;QACX,gBAAA,iLAAA,CAAA,IAAc;QACd,eAAA,iLAAA,CAAA,IAAa;QACb,aAAA,iLAAA,CAAA,IAAW;QACX,uBAAA,iLAAA,CAAA,IAAqB;QACrB,aAAA,iLAAA,CAAA,IAAW;QACX,gBAAA,iLAAA,CAAA,IAAc;QACd,aAAA,iLAAA,CAAA,IAAW;QACX,YAAY,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD;QACrB,YAAA,iLAAA,CAAA,IAAU;QACV,wBAAA,iLAAA,CAAA,IAAsB;QACtB,sBAAA,iLAAA,CAAA,IAAoB;QACpB,8BAAA,iLAAA,CAAA,IAA4B;QAC5B,cAAc,CAAA,GAAA,iLAAA,CAAA,IAAY,AAAD;QACzB,eAAA,iLAAA,CAAA,IAAa;QACb,gBAAA,iLAAA,CAAA,IAAc;QACd,qBAAA,iLAAA,CAAA,IAAmB;QACnB,mBAAA,iLAAA,CAAA,IAAiB;QACjB,kBAAA,iLAAA,CAAA,IAAgB;QAChB,oBAAA,iLAAA,CAAA,IAAkB;QAClB,eAAA,iLAAA,CAAA,IAAa;QACb,cAAA,iLAAA,CAAA,IAAY;QACZ,gBAAA,iLAAA,CAAA,IAAc;QACd,aAAA,iLAAA,CAAA,IAAW;IACb;IACA,MAAM,YAAY;QAChB,GAAG,aAAa;QAChB,GAAG,eAAe;QAClB,IAAA,iLAAA,CAAA,IAAE;QACF,OAAA,iLAAA,CAAA,IAAK;IACP;IACA,MAAM,MAAM,gBAAgB,WAAW;IACvC,OAAO;QACL;QACA;IACF;AACF;AACA,MAAM,SAAS,CAAC,KAAK;IACnB,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,aAAa,KAAK;IAC/C,MAAM,WAAW,IAAI,IAAI,IAAI,OAAO,EAAE,QAAQ;IAC9C,OAAO,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,KAAK;QACvB,eAAe;QACf,SAAS;YACP,UAAU;QACZ;QACA;QACA,kBAAkB;YAChB;gBACE,MAAM;gBACN,YAAY,iLAAA,CAAA,IAAqB;YACnC;eACG;SACJ;QACD,MAAM,WAAU,GAAG;YACjB,MAAM,gBAAgB,IAAI,OAAO,CAAC,aAAa,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,UAAU;YACzD,IAAI,cAAc,QAAQ,CAAC,OAAO;gBAChC,OAAO,IAAI,SAAS,aAAa;oBAAE,QAAQ;gBAAI;YACjD;YACA,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE,CAAE;gBAC9C,IAAI,OAAO,SAAS,EAAE;oBACpB,MAAM,WAAW,MAAM,OAAO,SAAS,CAAC,KAAK;oBAC7C,IAAI,YAAY,cAAc,UAAU;wBACtC,OAAO,SAAS,QAAQ;oBAC1B;gBACF;YACF;YACA,OAAO,mBAAmB,KAAK;QACjC;QACA,MAAM,YAAW,GAAG;YAClB,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,OAAO,IAAI,EAAE,CAAE;gBAC9C,IAAI,OAAO,UAAU,EAAE;oBACrB,MAAM,WAAW,MAAM,OAAO,UAAU,CAAC,KAAK;oBAC9C,IAAI,UAAU;wBACZ,OAAO,SAAS,QAAQ;oBAC1B;gBACF;YACF;YACA,OAAO;QACT;QACA,SAAQ,CAAC;YACP,IAAI,aAAa,iJAAA,CAAA,WAAQ,IAAI,EAAE,MAAM,KAAK,SAAS;gBACjD;YACF;YACA,IAAI,QAAQ,UAAU,EAAE,OAAO;gBAC7B,MAAM;YACR;YACA,IAAI,QAAQ,UAAU,EAAE,SAAS;gBAC/B,QAAQ,UAAU,CAAC,OAAO,CAAC,GAAG;gBAC9B;YACF;YACA,MAAM,cAAc,QAAQ,MAAM,EAAE;YACpC,MAAM,MAAM,gBAAgB,WAAW,gBAAgB,UAAU,gBAAgB,UAAU,iLAAA,CAAA,IAAM,GAAG,KAAK;YACzG,IAAI,QAAQ,MAAM,EAAE,aAAa,MAAM;gBACrC,IAAI,KAAK,OAAO,MAAM,YAAY,aAAa,KAAK,OAAO,EAAE,OAAO,KAAK,UAAU;oBACjF,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,OAAO,CAAC,QAAQ,CAAC,eAAe,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,mBAAmB;wBAC5K,IAAI,MAAM,EAAE,MAAM,EAAE,OAAO;wBAC3B;oBACF;gBACF;gBACA,IAAI,aAAa,iJAAA,CAAA,WAAQ,EAAE;oBACzB,IAAI,EAAE,MAAM,KAAK,yBAAyB;wBACxC,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE;oBAC7B;oBACA,KAAK,MAAM,EAAE,OAAO;gBACtB,OAAO;oBACL,IAAI,MAAM,EAAE,MACV,KAAK,OAAO,MAAM,YAAY,UAAU,IAAI,EAAE,IAAI,GAAG,IACrD;gBAEJ;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1364, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/index.mjs"], "sourcesContent": ["export { b as betterAuth } from './shared/better-auth.VOBpdwFt.mjs';\nexport { B as BetterAuthError, M as MissingDependencyError } from './shared/better-auth.DdzSJf-n.mjs';\nexport { c as capitalizeFirstLetter } from './shared/better-auth.D-2CmEwz.mjs';\nexport { H as HIDE_METADATA, g as generateState, p as parseState } from './shared/better-auth.D4HhkCZJ.mjs';\nexport { c as createLogger, g as generateId, l as levels, a as logger, s as shouldPublishLog } from './shared/better-auth.DBGfIDnh.mjs';\nimport './api/index.mjs';\nimport 'better-call';\nimport 'zod/v4';\nimport './cookies/index.mjs';\nimport './shared/better-auth.CW6D9eSx.mjs';\nimport './shared/better-auth.8zoxzg-F.mjs';\nimport '@better-auth/utils/base64';\nimport '@better-auth/utils/hmac';\nimport './shared/better-auth.tB5eU6EY.mjs';\nimport './shared/better-auth.VTXNLFMT.mjs';\nimport '@better-auth/utils/binary';\nimport './shared/better-auth.n2KFGwjY.mjs';\nimport './shared/better-auth.DcfNPS8q.mjs';\nimport 'defu';\nimport './crypto/index.mjs';\nimport '@better-auth/utils/hash';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport './shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-auth/utils/random';\nimport './shared/better-auth.ByruPN9q.mjs';\nimport './shared/better-auth.DORkW_Ge.mjs';\nimport './shared/better-auth.DwzM-9N1.mjs';\nimport 'kysely';\nimport './shared/better-auth.gBl3F_xa.mjs';\nimport './shared/better-auth.DGpadpzN.mjs';\nimport './shared/better-auth.YwDQhoPc.mjs';\nimport '@better-fetch/fetch';\nimport 'jose/errors';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/integrations/next-js.mjs"], "sourcesContent": ["import '../shared/better-auth.8zoxzg-F.mjs';\nimport '@better-auth/utils/base64';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport { parseSetCookieHeader } from '../cookies/index.mjs';\nimport 'better-call';\nimport 'zod/v4';\nimport { i as createAuthMiddleware } from '../shared/better-auth.D4HhkCZJ.mjs';\nimport '../shared/better-auth.n2KFGwjY.mjs';\nimport '../shared/better-auth.DGaVMVAI.mjs';\nimport '../plugins/organization/access/index.mjs';\nimport '@better-auth/utils/random';\nimport '@better-auth/utils/hash';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../shared/better-auth.B4Qoxdgc.mjs';\nimport '../shared/better-auth.DBGfIDnh.mjs';\nimport 'kysely';\nimport 'defu';\nimport '@better-auth/utils/otp';\nimport '../plugins/admin/access/index.mjs';\nimport '@better-fetch/fetch';\nimport '../shared/better-auth.CGrHn1Ih.mjs';\nimport '../shared/better-auth.DdzSJf-n.mjs';\nimport '../shared/better-auth.CW6D9eSx.mjs';\nimport '../shared/better-auth.tB5eU6EY.mjs';\nimport '../shared/better-auth.VTXNLFMT.mjs';\nimport '../crypto/index.mjs';\nimport 'jose/errors';\nimport '../plugins/access/index.mjs';\n\nfunction toNextJsHandler(auth) {\n  const handler = async (request) => {\n    return \"handler\" in auth ? auth.handler(request) : auth(request);\n  };\n  return {\n    GET: handler,\n    POST: handler\n  };\n}\nconst nextCookies = () => {\n  return {\n    id: \"next-cookies\",\n    hooks: {\n      after: [\n        {\n          matcher(ctx) {\n            return true;\n          },\n          handler: createAuthMiddleware(async (ctx) => {\n            const returned = ctx.context.responseHeaders;\n            if (\"_flag\" in ctx && ctx._flag === \"router\") {\n              return;\n            }\n            if (returned instanceof Headers) {\n              const setCookies = returned?.get(\"set-cookie\");\n              if (!setCookies) return;\n              const parsed = parseSetCookieHeader(setCookies);\n              const { cookies } = await import('next/headers');\n              let cookieHelper;\n              try {\n                cookieHelper = await cookies();\n              } catch (error) {\n                if (error instanceof Error && error.message.startsWith(\n                  \"`cookies` was called outside a request scope.\"\n                )) {\n                  return;\n                }\n                throw error;\n              }\n              parsed.forEach((value, key) => {\n                if (!key) return;\n                const opts = {\n                  sameSite: value.samesite,\n                  secure: value.secure,\n                  maxAge: value[\"max-age\"],\n                  httpOnly: value.httponly,\n                  domain: value.domain,\n                  path: value.path\n                };\n                try {\n                  cookieHelper.set(key, decodeURIComponent(value.value), opts);\n                } catch (e) {\n                }\n              });\n              return;\n            }\n          })\n        }\n      ]\n    }\n  };\n};\n\nexport { nextCookies, toNextJsHandler };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAS,gBAAgB,IAAI;IAC3B,MAAM,UAAU,OAAO;QACrB,OAAO,aAAa,OAAO,KAAK,OAAO,CAAC,WAAW,KAAK;IAC1D;IACA,OAAO;QACL,KAAK;QACL,MAAM;IACR;AACF;AACA,MAAM,cAAc;IAClB,OAAO;QACL,IAAI;QACJ,OAAO;YACL,OAAO;gBACL;oBACE,SAAQ,GAAG;wBACT,OAAO;oBACT;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,WAAW,IAAI,OAAO,CAAC,eAAe;wBAC5C,IAAI,WAAW,OAAO,IAAI,KAAK,KAAK,UAAU;4BAC5C;wBACF;wBACA,IAAI,oBAAoB,SAAS;4BAC/B,MAAM,aAAa,UAAU,IAAI;4BACjC,IAAI,CAAC,YAAY;4BACjB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,uBAAoB,AAAD,EAAE;4BACpC,MAAM,EAAE,OAAO,EAAE,GAAG;4BACpB,IAAI;4BACJ,IAAI;gCACF,eAAe,MAAM;4BACvB,EAAE,OAAO,OAAO;gCACd,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,UAAU,CACpD,kDACC;oCACD;gCACF;gCACA,MAAM;4BACR;4BACA,OAAO,OAAO,CAAC,CAAC,OAAO;gCACrB,IAAI,CAAC,KAAK;gCACV,MAAM,OAAO;oCACX,UAAU,MAAM,QAAQ;oCACxB,QAAQ,MAAM,MAAM;oCACpB,QAAQ,KAAK,CAAC,UAAU;oCACxB,UAAU,MAAM,QAAQ;oCACxB,QAAQ,MAAM,MAAM;oCACpB,MAAM,MAAM,IAAI;gCAClB;gCACA,IAAI;oCACF,aAAa,GAAG,CAAC,KAAK,mBAAmB,MAAM,KAAK,GAAG;gCACzD,EAAE,OAAO,GAAG,CACZ;4BACF;4BACA;wBACF;oBACF;gBACF;aACD;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}]}