{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/lib/email.ts"], "sourcesContent": ["import { Resend } from 'resend';\n\n// Initialize Resend - you'll need to add RESEND_API_KEY to your .env.local\nconst resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;\n\nexport const sendEmail = async ({ \n  to, \n  subject, \n  text, \n  html \n}: { \n  to: string; \n  subject: string; \n  text?: string; \n  html?: string; \n}) => {\n  // In development without API key, just log to console\n  if (!process.env.RESEND_API_KEY || !resend) {\n    console.log('📧 Email would be sent:');\n    console.log('To:', to);\n    console.log('Subject:', subject);\n    console.log('Content:', html || text);\n    console.log('\\n⚠️  Add RESEND_API_KEY to .env.local to send real emails');\n    return { id: 'dev-email' };\n  }\n\n  try {\n    const data = await resend.emails.send({\n      from: process.env.EMAIL_FROM || 'SaaS Starter <<EMAIL>>',\n      to,\n      subject,\n      text,\n      html: html || text,\n    });\n    \n    console.log('Email sent:', data.id);\n    return data;\n  } catch (error) {\n    console.error('Failed to send email:', error);\n    throw error;\n  }\n};"], "names": [], "mappings": ";;;AAAA;;AAEA,2EAA2E;AAC3E,MAAM,SAAS,QAAQ,GAAG,CAAC,cAAc,GAAG,IAAI,0IAAA,CAAA,SAAM,CAAC,QAAQ,GAAG,CAAC,cAAc,IAAI;AAE9E,MAAM,YAAY,OAAO,EAC9B,EAAE,EACF,OAAO,EACP,IAAI,EACJ,IAAI,EAML;IACC,sDAAsD;IACtD,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,IAAI,CAAC,QAAQ;QAC1C,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,OAAO;QACnB,QAAQ,GAAG,CAAC,YAAY;QACxB,QAAQ,GAAG,CAAC,YAAY,QAAQ;QAChC,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,IAAI;QAAY;IAC3B;IAEA,IAAI;QACF,MAAM,OAAO,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YACpC,MAAM,QAAQ,GAAG,CAAC,UAAU,IAAI;YAChC;YACA;YACA;YACA,MAAM,QAAQ;QAChB;QAEA,QAAQ,GAAG,CAAC,eAAe,KAAK,EAAE;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/lib/auth.ts"], "sourcesContent": ["import { betterAuth } from 'better-auth';\nimport { Pool } from 'pg';\nimport { sendEmail } from './email';\nimport { autumn } from 'autumn-js/better-auth';\n\nexport const auth = betterAuth({\n  database: new Pool({\n    connectionString: process.env.DATABASE_URL!,\n  }),\n  secret: process.env.BETTER_AUTH_SECRET!,\n  baseURL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n  emailAndPassword: {\n    enabled: true,\n    requireEmailVerification: false, // Set to true to require email verification\n    sendResetPassword: async ({ user, url }, request) => {\n      console.log('Password reset link:', url);\n      \n      await sendEmail({\n        to: user.email,\n        subject: 'Reset your password - Fire SaaS',\n        html: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <h2 style=\"color: #333;\">Reset Your Password</h2>\n            <p style=\"color: #666; line-height: 1.6;\">\n              You requested to reset your password. Click the button below to create a new password.\n            </p>\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"${url}\" style=\"background-color: #f97316; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;\">\n                Reset Password\n              </a>\n            </div>\n            <p style=\"color: #999; font-size: 14px;\">\n              If you didn't request this, you can safely ignore this email.\n            </p>\n            <p style=\"color: #999; font-size: 14px;\">\n              This link will expire in 1 hour.\n            </p>\n          </div>\n        `\n      });\n    },\n  },\n  trustedOrigins: [process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'],\n  session: {\n    expiresIn: 60 * 60 * 24 * 7, // 7 days\n    updateAge: 60 * 60 * 24, // Update session if older than 1 day\n    cookieOptions: {\n      httpOnly: true,\n      sameSite: 'lax',\n      secure: process.env.NODE_ENV === 'production',\n      path: '/',\n    },\n  },\n  emailVerification: {\n    sendOnSignUp: false, // Set to true to send verification email on signup\n    autoSignInAfterVerification: true,\n    sendVerificationEmail: async ({ user, url }, request) => {\n      console.log('Verification link:', url);\n      \n      await sendEmail({\n        to: user.email,\n        subject: 'Verify your email - Fire SaaS',\n        html: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <h2 style=\"color: #333;\">Verify Your Email Address</h2>\n            <p style=\"color: #666; line-height: 1.6;\">\n              Thanks for signing up! Please verify your email address by clicking the button below.\n            </p>\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"${url}\" style=\"background-color: #f97316; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;\">\n                Verify Email\n              </a>\n            </div>\n            <p style=\"color: #999; font-size: 14px;\">\n              If you didn't create an account, you can safely ignore this email.\n            </p>\n          </div>\n        `\n      });\n    },\n  },\n  plugins: [autumn()],\n});"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;AAEO,MAAM,OAAO,CAAA,GAAA,oNAAA,CAAA,aAAU,AAAD,EAAE;IAC7B,UAAU,IAAI,oGAAA,CAAA,OAAI,CAAC;QACjB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;IAC5C;IACA,QAAQ,QAAQ,GAAG,CAAC,kBAAkB;IACtC,SAAS,6DAAmC;IAC5C,kBAAkB;QAChB,SAAS;QACT,0BAA0B;QAC1B,mBAAmB,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;YACvC,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,MAAM,CAAA,GAAA,8GAAA,CAAA,YAAS,AAAD,EAAE;gBACd,IAAI,KAAK,KAAK;gBACd,SAAS;gBACT,MAAM,CAAC;;;;;;;uBAOQ,EAAE,IAAI;;;;;;;;;;;QAWrB,CAAC;YACH;QACF;IACF;IACA,gBAAgB;QAAC,6DAAmC;KAAwB;IAC5E,SAAS;QACP,WAAW,KAAK,KAAK,KAAK;QAC1B,WAAW,KAAK,KAAK;QACrB,eAAe;YACb,UAAU;YACV,UAAU;YACV,QAAQ,oDAAyB;YACjC,MAAM;QACR;IACF;IACA,mBAAmB;QACjB,cAAc;QACd,6BAA6B;QAC7B,uBAAuB,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;YAC3C,QAAQ,GAAG,CAAC,sBAAsB;YAElC,MAAM,CAAA,GAAA,8GAAA,CAAA,YAAS,AAAD,EAAE;gBACd,IAAI,KAAK,KAAK;gBACd,SAAS;gBACT,MAAM,CAAC;;;;;;;uBAOQ,EAAE,IAAI;;;;;;;;QAQrB,CAAC;YACH;QACF;IACF;IACA,SAAS;QAAC,CAAA,GAAA,iLAAA,CAAA,SAAM,AAAD;KAAI;AACrB", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/app/api/auth/%5B...all%5D/route.ts"], "sourcesContent": ["import { auth } from '@/lib/auth';\nimport { toNextJsHandler } from 'better-auth/next-js';\n\nexport const { GET, POST } = toNextJsHandler(auth.handler);"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;;;AAEO,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uKAAA,CAAA,kBAAe,AAAD,EAAE,6GAAA,CAAA,OAAI,CAAC,OAAO", "debugId": null}}]}