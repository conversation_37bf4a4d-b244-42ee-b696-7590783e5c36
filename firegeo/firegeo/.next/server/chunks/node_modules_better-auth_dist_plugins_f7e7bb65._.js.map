{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/access/index.mjs"], "sourcesContent": ["import { B as BetterAuthError } from '../../shared/better-auth.DdzSJf-n.mjs';\n\nfunction role(statements) {\n  return {\n    authorize(request, connector = \"AND\") {\n      let success = false;\n      for (const [requestedResource, requestedActions] of Object.entries(\n        request\n      )) {\n        const allowedActions = statements[requestedResource];\n        if (!allowedActions) {\n          return {\n            success: false,\n            error: `You are not allowed to access resource: ${requestedResource}`\n          };\n        }\n        if (Array.isArray(requestedActions)) {\n          success = requestedActions.every(\n            (requestedAction) => allowedActions.includes(requestedAction)\n          );\n        } else {\n          if (typeof requestedActions === \"object\") {\n            const actions = requestedActions;\n            if (actions.connector === \"OR\") {\n              success = actions.actions.some(\n                (requestedAction) => allowedActions.includes(requestedAction)\n              );\n            } else {\n              success = actions.actions.every(\n                (requestedAction) => allowedActions.includes(requestedAction)\n              );\n            }\n          } else {\n            throw new BetterAuthError(\"Invalid access control request\");\n          }\n        }\n        if (success && connector === \"OR\") {\n          return { success };\n        }\n        if (!success && connector === \"AND\") {\n          return {\n            success: false,\n            error: `unauthorized to access resource \"${requestedResource}\"`\n          };\n        }\n      }\n      if (success) {\n        return {\n          success\n        };\n      }\n      return {\n        success: false,\n        error: \"Not authorized\"\n      };\n    },\n    statements\n  };\n}\nfunction createAccessControl(s) {\n  return {\n    newRole(statements) {\n      return role(statements);\n    },\n    statements: s\n  };\n}\n\nexport { createAccessControl, role };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,KAAK,UAAU;IACtB,OAAO;QACL,WAAU,OAAO,EAAE,YAAY,KAAK;YAClC,IAAI,UAAU;YACd,KAAK,MAAM,CAAC,mBAAmB,iBAAiB,IAAI,OAAO,OAAO,CAChE,SACC;gBACD,MAAM,iBAAiB,UAAU,CAAC,kBAAkB;gBACpD,IAAI,CAAC,gBAAgB;oBACnB,OAAO;wBACL,SAAS;wBACT,OAAO,CAAC,wCAAwC,EAAE,mBAAmB;oBACvE;gBACF;gBACA,IAAI,MAAM,OAAO,CAAC,mBAAmB;oBACnC,UAAU,iBAAiB,KAAK,CAC9B,CAAC,kBAAoB,eAAe,QAAQ,CAAC;gBAEjD,OAAO;oBACL,IAAI,OAAO,qBAAqB,UAAU;wBACxC,MAAM,UAAU;wBAChB,IAAI,QAAQ,SAAS,KAAK,MAAM;4BAC9B,UAAU,QAAQ,OAAO,CAAC,IAAI,CAC5B,CAAC,kBAAoB,eAAe,QAAQ,CAAC;wBAEjD,OAAO;4BACL,UAAU,QAAQ,OAAO,CAAC,KAAK,CAC7B,CAAC,kBAAoB,eAAe,QAAQ,CAAC;wBAEjD;oBACF,OAAO;wBACL,MAAM,IAAI,oLAAA,CAAA,IAAe,CAAC;oBAC5B;gBACF;gBACA,IAAI,WAAW,cAAc,MAAM;oBACjC,OAAO;wBAAE;oBAAQ;gBACnB;gBACA,IAAI,CAAC,WAAW,cAAc,OAAO;oBACnC,OAAO;wBACL,SAAS;wBACT,OAAO,CAAC,iCAAiC,EAAE,kBAAkB,CAAC,CAAC;oBACjE;gBACF;YACF;YACA,IAAI,SAAS;gBACX,OAAO;oBACL;gBACF;YACF;YACA,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QACA;IACF;AACF;AACA,SAAS,oBAAoB,CAAC;IAC5B,OAAO;QACL,SAAQ,UAAU;YAChB,OAAO,KAAK;QACd;QACA,YAAY;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/organization/access/index.mjs"], "sourcesContent": ["import { createAccessControl } from '../../access/index.mjs';\nimport '../../../shared/better-auth.DdzSJf-n.mjs';\n\nconst defaultStatements = {\n  organization: [\"update\", \"delete\"],\n  member: [\"create\", \"update\", \"delete\"],\n  invitation: [\"create\", \"cancel\"],\n  team: [\"create\", \"update\", \"delete\"]\n};\nconst defaultAc = createAccessControl(defaultStatements);\nconst adminAc = defaultAc.newRole({\n  organization: [\"update\"],\n  invitation: [\"create\", \"cancel\"],\n  member: [\"create\", \"update\", \"delete\"],\n  team: [\"create\", \"update\", \"delete\"]\n});\nconst ownerAc = defaultAc.newRole({\n  organization: [\"update\", \"delete\"],\n  member: [\"create\", \"update\", \"delete\"],\n  invitation: [\"create\", \"cancel\"],\n  team: [\"create\", \"update\", \"delete\"]\n});\nconst memberAc = defaultAc.newRole({\n  organization: [],\n  member: [],\n  invitation: [],\n  team: []\n});\nconst defaultRoles = {\n  admin: adminAc,\n  owner: ownerAc,\n  member: memberAc\n};\n\nexport { adminAc, defaultAc, defaultRoles, defaultStatements, memberAc, ownerAc };\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEA,MAAM,oBAAoB;IACxB,cAAc;QAAC;QAAU;KAAS;IAClC,QAAQ;QAAC;QAAU;QAAU;KAAS;IACtC,YAAY;QAAC;QAAU;KAAS;IAChC,MAAM;QAAC;QAAU;QAAU;KAAS;AACtC;AACA,MAAM,YAAY,CAAA,GAAA,uKAAA,CAAA,sBAAmB,AAAD,EAAE;AACtC,MAAM,UAAU,UAAU,OAAO,CAAC;IAChC,cAAc;QAAC;KAAS;IACxB,YAAY;QAAC;QAAU;KAAS;IAChC,QAAQ;QAAC;QAAU;QAAU;KAAS;IACtC,MAAM;QAAC;QAAU;QAAU;KAAS;AACtC;AACA,MAAM,UAAU,UAAU,OAAO,CAAC;IAChC,cAAc;QAAC;QAAU;KAAS;IAClC,QAAQ;QAAC;QAAU;QAAU;KAAS;IACtC,YAAY;QAAC;QAAU;KAAS;IAChC,MAAM;QAAC;QAAU;QAAU;KAAS;AACtC;AACA,MAAM,WAAW,UAAU,OAAO,CAAC;IACjC,cAAc,EAAE;IAChB,QAAQ,EAAE;IACV,YAAY,EAAE;IACd,MAAM,EAAE;AACV;AACA,MAAM,eAAe;IACnB,OAAO;IACP,OAAO;IACP,QAAQ;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/two-factor/index.mjs"], "sourcesContent": ["import { g as generateRandomString } from '../../shared/better-auth.B4Qoxdgc.mjs';\nimport * as z from 'zod/v4';\nimport { k as getSessionFromCtx, j as createAuthEndpoint, l as sessionMiddleware, B as BASE_ERROR_CODES, i as createAuthMiddleware } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport { APIError } from 'better-call';\nimport { setSessionCookie, deleteSessionCookie } from '../../cookies/index.mjs';\nimport { m as mergeSchema } from '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport { symmetricEncrypt, symmetricDecrypt } from '../../crypto/index.mjs';\nimport { base64Url } from '@better-auth/utils/base64';\nimport { createHMAC } from '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport { createHash } from '@better-auth/utils/hash';\nimport { createOTP } from '@better-auth/utils/otp';\nimport { v as validatePassword } from '../../shared/better-auth.YwDQhoPc.mjs';\nexport { t as twoFactorClient } from '../../shared/better-auth.Ddw8bVyV.mjs';\nimport '@better-auth/utils/random';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '@better-fetch/fetch';\nimport 'jose';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport 'jose/errors';\n\nconst TWO_FACTOR_ERROR_CODES = {\n  OTP_NOT_ENABLED: \"OTP not enabled\",\n  OTP_HAS_EXPIRED: \"OTP has expired\",\n  TOTP_NOT_ENABLED: \"TOTP not enabled\",\n  TWO_FACTOR_NOT_ENABLED: \"Two factor isn't enabled\",\n  BACKUP_CODES_NOT_ENABLED: \"Backup codes aren't enabled\",\n  INVALID_BACKUP_CODE: \"Invalid backup code\",\n  INVALID_CODE: \"Invalid code\",\n  TOO_MANY_ATTEMPTS_REQUEST_NEW_CODE: \"Too many attempts. Please request a new code.\",\n  INVALID_TWO_FACTOR_COOKIE: \"Invalid two factor cookie\"\n};\n\nconst TWO_FACTOR_COOKIE_NAME = \"two_factor\";\nconst TRUST_DEVICE_COOKIE_NAME = \"trust_device\";\n\nasync function verifyTwoFactor(ctx) {\n  const session = await getSessionFromCtx(ctx);\n  if (!session) {\n    const cookieName = ctx.context.createAuthCookie(TWO_FACTOR_COOKIE_NAME);\n    const twoFactorCookie = await ctx.getSignedCookie(\n      cookieName.name,\n      ctx.context.secret\n    );\n    if (!twoFactorCookie) {\n      throw new APIError(\"UNAUTHORIZED\", {\n        message: TWO_FACTOR_ERROR_CODES.INVALID_TWO_FACTOR_COOKIE\n      });\n    }\n    const verificationToken = await ctx.context.internalAdapter.findVerificationValue(twoFactorCookie);\n    if (!verificationToken) {\n      throw new APIError(\"UNAUTHORIZED\", {\n        message: TWO_FACTOR_ERROR_CODES.INVALID_TWO_FACTOR_COOKIE\n      });\n    }\n    const user = await ctx.context.internalAdapter.findUserById(\n      verificationToken.value\n    );\n    if (!user) {\n      throw new APIError(\"UNAUTHORIZED\", {\n        message: TWO_FACTOR_ERROR_CODES.INVALID_TWO_FACTOR_COOKIE\n      });\n    }\n    const dontRememberMe = await ctx.getSignedCookie(\n      ctx.context.authCookies.dontRememberToken.name,\n      ctx.context.secret\n    );\n    return {\n      valid: async (ctx2) => {\n        const session2 = await ctx2.context.internalAdapter.createSession(\n          verificationToken.value,\n          ctx2,\n          !!dontRememberMe\n        );\n        if (!session2) {\n          throw new APIError(\"INTERNAL_SERVER_ERROR\", {\n            message: \"failed to create session\"\n          });\n        }\n        await setSessionCookie(ctx2, {\n          session: session2,\n          user\n        });\n        if (ctx2.body.trustDevice) {\n          const trustDeviceCookie = ctx2.context.createAuthCookie(\n            TRUST_DEVICE_COOKIE_NAME,\n            {\n              maxAge: 30 * 24 * 60 * 60\n              // 30 days, it'll be refreshed on sign in requests\n            }\n          );\n          const token = await createHMAC(\"SHA-256\", \"base64urlnopad\").sign(\n            ctx2.context.secret,\n            `${user.id}!${session2.token}`\n          );\n          await ctx2.setSignedCookie(\n            trustDeviceCookie.name,\n            `${token}!${session2.token}`,\n            ctx2.context.secret,\n            trustDeviceCookie.attributes\n          );\n          ctx2.setCookie(ctx2.context.authCookies.dontRememberToken.name, \"\", {\n            maxAge: 0\n          });\n          ctx2.setCookie(cookieName.name, \"\", {\n            maxAge: 0\n          });\n        }\n        return ctx2.json({\n          token: session2.token,\n          user: {\n            id: user.id,\n            email: user.email,\n            emailVerified: user.emailVerified,\n            name: user.name,\n            image: user.image,\n            createdAt: user.createdAt,\n            updatedAt: user.updatedAt\n          }\n        });\n      },\n      invalid: async (errorKey) => {\n        throw new APIError(\"UNAUTHORIZED\", {\n          message: TWO_FACTOR_ERROR_CODES[errorKey]\n        });\n      },\n      session: {\n        session: null,\n        user\n      },\n      key: twoFactorCookie\n    };\n  }\n  return {\n    valid: async (ctx2) => {\n      return ctx2.json({\n        token: session.session.token,\n        user: {\n          id: session.user.id,\n          email: session.user.email,\n          emailVerified: session.user.emailVerified,\n          name: session.user.name,\n          image: session.user.image,\n          createdAt: session.user.createdAt,\n          updatedAt: session.user.updatedAt\n        }\n      });\n    },\n    invalid: async () => {\n      throw new APIError(\"UNAUTHORIZED\", {\n        message: TWO_FACTOR_ERROR_CODES.INVALID_TWO_FACTOR_COOKIE\n      });\n    },\n    session,\n    key: `${session.user.id}!${session.session.id}`\n  };\n}\n\nfunction generateBackupCodesFn(options) {\n  return Array.from({ length: options?.amount ?? 10 }).fill(null).map(() => generateRandomString(options?.length ?? 10, \"a-z\", \"0-9\", \"A-Z\")).map((code) => `${code.slice(0, 5)}-${code.slice(5)}`);\n}\nasync function generateBackupCodes(secret, options) {\n  const key = secret;\n  const backupCodes = options?.customBackupCodesGenerate ? options.customBackupCodesGenerate() : generateBackupCodesFn(options);\n  const encCodes = await symmetricEncrypt({\n    data: JSON.stringify(backupCodes),\n    key\n  });\n  return {\n    backupCodes,\n    encryptedBackupCodes: encCodes\n  };\n}\nasync function verifyBackupCode(data, key) {\n  const codes = await getBackupCodes(data.backupCodes, key);\n  if (!codes) {\n    return {\n      status: false,\n      updated: null\n    };\n  }\n  return {\n    status: codes.includes(data.code),\n    updated: codes.filter((code) => code !== data.code)\n  };\n}\nasync function getBackupCodes(backupCodes, key) {\n  const secret = new TextDecoder(\"utf-8\").decode(\n    new TextEncoder().encode(\n      await symmetricDecrypt({ key, data: backupCodes })\n    )\n  );\n  const data = JSON.parse(secret);\n  const result = z.array(z.string()).safeParse(data);\n  if (result.success) {\n    return result.data;\n  }\n  return null;\n}\nconst backupCode2fa = (options) => {\n  const twoFactorTable = \"twoFactor\";\n  async function storeBackupCodes(ctx, backupCodes) {\n    if (options?.storeBackupCodes === \"encrypted\") {\n      return await symmetricEncrypt({\n        key: ctx.context.secret,\n        data: backupCodes\n      });\n    }\n    if (typeof options?.storeBackupCodes === \"object\" && \"encrypt\" in options?.storeBackupCodes) {\n      return await options?.storeBackupCodes.encrypt(backupCodes);\n    }\n    return backupCodes;\n  }\n  async function decryptBackupCodes(ctx, backupCodes) {\n    if (options?.storeBackupCodes === \"encrypted\") {\n      return await symmetricDecrypt({\n        key: ctx.context.secret,\n        data: backupCodes\n      });\n    }\n    if (typeof options?.storeBackupCodes === \"object\" && \"decrypt\" in options?.storeBackupCodes) {\n      return await options?.storeBackupCodes.decrypt(backupCodes);\n    }\n    return backupCodes;\n  }\n  return {\n    id: \"backup_code\",\n    endpoints: {\n      /**\n       * ### Endpoint\n       *\n       * POST `/two-factor/verify-backup-code`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.verifyBackupCode`\n       *\n       * **client:**\n       * `authClient.twoFactor.verifyBackupCode`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/2fa#api-method-two-factor-verify-backup-code)\n       */\n      verifyBackupCode: createAuthEndpoint(\n        \"/two-factor/verify-backup-code\",\n        {\n          method: \"POST\",\n          body: z.object({\n            code: z.string().meta({\n              description: `A backup code to verify. Eg: \"123456\"`\n            }),\n            /**\n             * Disable setting the session cookie\n             */\n            disableSession: z.boolean().meta({\n              description: \"If true, the session cookie will not be set.\"\n            }).optional(),\n            /**\n             * if true, the device will be trusted\n             * for 30 days. It'll be refreshed on\n             * every sign in request within this time.\n             */\n            trustDevice: z.boolean().meta({\n              description: \"If true, the device will be trusted for 30 days. It'll be refreshed on every sign in request within this time. Eg: true\"\n            }).optional()\n          }),\n          metadata: {\n            openapi: {\n              description: \"Verify a backup code for two-factor authentication\",\n              responses: {\n                \"200\": {\n                  description: \"Backup code verified successfully\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          user: {\n                            type: \"object\",\n                            properties: {\n                              id: {\n                                type: \"string\",\n                                description: \"Unique identifier of the user\"\n                              },\n                              email: {\n                                type: \"string\",\n                                format: \"email\",\n                                nullable: true,\n                                description: \"User's email address\"\n                              },\n                              emailVerified: {\n                                type: \"boolean\",\n                                nullable: true,\n                                description: \"Whether the email is verified\"\n                              },\n                              name: {\n                                type: \"string\",\n                                nullable: true,\n                                description: \"User's name\"\n                              },\n                              image: {\n                                type: \"string\",\n                                format: \"uri\",\n                                nullable: true,\n                                description: \"User's profile image URL\"\n                              },\n                              twoFactorEnabled: {\n                                type: \"boolean\",\n                                description: \"Whether two-factor authentication is enabled for the user\"\n                              },\n                              createdAt: {\n                                type: \"string\",\n                                format: \"date-time\",\n                                description: \"Timestamp when the user was created\"\n                              },\n                              updatedAt: {\n                                type: \"string\",\n                                format: \"date-time\",\n                                description: \"Timestamp when the user was last updated\"\n                              }\n                            },\n                            required: [\n                              \"id\",\n                              \"twoFactorEnabled\",\n                              \"createdAt\",\n                              \"updatedAt\"\n                            ],\n                            description: \"The authenticated user object with two-factor details\"\n                          },\n                          session: {\n                            type: \"object\",\n                            properties: {\n                              token: {\n                                type: \"string\",\n                                description: \"Session token\"\n                              },\n                              userId: {\n                                type: \"string\",\n                                description: \"ID of the user associated with the session\"\n                              },\n                              createdAt: {\n                                type: \"string\",\n                                format: \"date-time\",\n                                description: \"Timestamp when the session was created\"\n                              },\n                              expiresAt: {\n                                type: \"string\",\n                                format: \"date-time\",\n                                description: \"Timestamp when the session expires\"\n                              }\n                            },\n                            required: [\n                              \"token\",\n                              \"userId\",\n                              \"createdAt\",\n                              \"expiresAt\"\n                            ],\n                            description: \"The current session object, included unless disableSession is true\"\n                          }\n                        },\n                        required: [\"user\", \"session\"]\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const { session, valid } = await verifyTwoFactor(ctx);\n          const user = session.user;\n          const twoFactor = await ctx.context.adapter.findOne({\n            model: twoFactorTable,\n            where: [\n              {\n                field: \"userId\",\n                value: user.id\n              }\n            ]\n          });\n          if (!twoFactor) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: TWO_FACTOR_ERROR_CODES.BACKUP_CODES_NOT_ENABLED\n            });\n          }\n          const decryptedBackupCodes = await decryptBackupCodes(\n            ctx,\n            twoFactor.backupCodes\n          );\n          const validate = await verifyBackupCode(\n            {\n              backupCodes: decryptedBackupCodes,\n              code: ctx.body.code\n            },\n            ctx.context.secret\n          );\n          if (!validate.status) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: TWO_FACTOR_ERROR_CODES.INVALID_BACKUP_CODE\n            });\n          }\n          const updatedBackupCodes = await symmetricEncrypt({\n            key: ctx.context.secret,\n            data: JSON.stringify(validate.updated)\n          });\n          await ctx.context.adapter.updateMany({\n            model: twoFactorTable,\n            update: {\n              backupCodes: updatedBackupCodes\n            },\n            where: [\n              {\n                field: \"userId\",\n                value: user.id\n              }\n            ]\n          });\n          if (!ctx.body.disableSession) {\n            return valid(ctx);\n          }\n          return ctx.json({\n            token: session.session?.token,\n            user: {\n              id: session.user?.id,\n              email: session.user.email,\n              emailVerified: session.user.emailVerified,\n              name: session.user.name,\n              image: session.user.image,\n              createdAt: session.user.createdAt,\n              updatedAt: session.user.updatedAt\n            }\n          });\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * POST `/two-factor/generate-backup-codes`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.generateBackupCodes`\n       *\n       * **client:**\n       * `authClient.twoFactor.generateBackupCodes`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/2fa#api-method-two-factor-generate-backup-codes)\n       */\n      generateBackupCodes: createAuthEndpoint(\n        \"/two-factor/generate-backup-codes\",\n        {\n          method: \"POST\",\n          body: z.object({\n            password: z.string().meta({\n              description: \"The users password.\"\n            })\n          }),\n          use: [sessionMiddleware],\n          metadata: {\n            openapi: {\n              description: \"Generate new backup codes for two-factor authentication\",\n              responses: {\n                \"200\": {\n                  description: \"Backup codes generated successfully\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          status: {\n                            type: \"boolean\",\n                            description: \"Indicates if the backup codes were generated successfully\",\n                            enum: [true]\n                          },\n                          backupCodes: {\n                            type: \"array\",\n                            items: { type: \"string\" },\n                            description: \"Array of generated backup codes in plain text\"\n                          }\n                        },\n                        required: [\"status\", \"backupCodes\"]\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const user = ctx.context.session.user;\n          if (!user.twoFactorEnabled) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: TWO_FACTOR_ERROR_CODES.TWO_FACTOR_NOT_ENABLED\n            });\n          }\n          await ctx.context.password.checkPassword(user.id, ctx);\n          const backupCodes = await generateBackupCodes(\n            ctx.context.secret,\n            options\n          );\n          const storedBackupCodes = await storeBackupCodes(\n            ctx,\n            backupCodes.encryptedBackupCodes\n          );\n          await ctx.context.adapter.update({\n            model: twoFactorTable,\n            update: {\n              backupCodes: storedBackupCodes\n            },\n            where: [\n              {\n                field: \"userId\",\n                value: ctx.context.session.user.id\n              }\n            ]\n          });\n          return ctx.json({\n            status: true,\n            backupCodes: backupCodes.backupCodes\n          });\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * GET `/two-factor/view-backup-codes`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.viewBackupCodes`\n       *\n       * **client:**\n       * `authClient.twoFactor.viewBackupCodes`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/2fa#api-method-two-factor-view-backup-codes)\n       */\n      viewBackupCodes: createAuthEndpoint(\n        \"/two-factor/view-backup-codes\",\n        {\n          method: \"GET\",\n          body: z.object({\n            userId: z.coerce.string().meta({\n              description: `The user ID to view all backup codes. Eg: \"user-id\"`\n            })\n          }),\n          metadata: {\n            SERVER_ONLY: true\n          }\n        },\n        async (ctx) => {\n          const twoFactor = await ctx.context.adapter.findOne({\n            model: twoFactorTable,\n            where: [\n              {\n                field: \"userId\",\n                value: ctx.body.userId\n              }\n            ]\n          });\n          if (!twoFactor) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: \"Backup codes aren't enabled\"\n            });\n          }\n          const backupCodes = await getBackupCodes(\n            twoFactor.backupCodes,\n            ctx.context.secret\n          );\n          if (!backupCodes) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: TWO_FACTOR_ERROR_CODES.BACKUP_CODES_NOT_ENABLED\n            });\n          }\n          const decryptedBackupCodes = await decryptBackupCodes(\n            ctx,\n            twoFactor.backupCodes\n          );\n          return ctx.json({\n            status: true,\n            backupCodes: decryptedBackupCodes\n          });\n        }\n      )\n    }\n  };\n};\n\nconst defaultKeyHasher = async (token) => {\n  const hash = await createHash(\"SHA-256\").digest(\n    new TextEncoder().encode(token)\n  );\n  const hashed = base64Url.encode(new Uint8Array(hash), {\n    padding: false\n  });\n  return hashed;\n};\n\nconst otp2fa = (options) => {\n  const opts = {\n    storeOTP: \"plain\",\n    digits: 6,\n    ...options,\n    period: (options?.period || 3) * 60 * 1e3\n  };\n  async function storeOTP(ctx, otp) {\n    if (opts.storeOTP === \"hashed\") {\n      return await defaultKeyHasher(otp);\n    }\n    if (typeof opts.storeOTP === \"object\" && \"hash\" in opts.storeOTP) {\n      return await opts.storeOTP.hash(otp);\n    }\n    if (typeof opts.storeOTP === \"object\" && \"encrypt\" in opts.storeOTP) {\n      return await opts.storeOTP.encrypt(otp);\n    }\n    if (opts.storeOTP === \"encrypted\") {\n      return await symmetricEncrypt({\n        key: ctx.context.secret,\n        data: otp\n      });\n    }\n    return otp;\n  }\n  async function decryptOTP(ctx, otp) {\n    if (opts.storeOTP === \"hashed\") {\n      return await defaultKeyHasher(otp);\n    }\n    if (opts.storeOTP === \"encrypted\") {\n      return await symmetricDecrypt({\n        key: ctx.context.secret,\n        data: otp\n      });\n    }\n    if (typeof opts.storeOTP === \"object\" && \"encrypt\" in opts.storeOTP) {\n      return await opts.storeOTP.decrypt(otp);\n    }\n    if (typeof opts.storeOTP === \"object\" && \"hash\" in opts.storeOTP) {\n      return await opts.storeOTP.hash(otp);\n    }\n    return otp;\n  }\n  const send2FaOTP = createAuthEndpoint(\n    \"/two-factor/send-otp\",\n    {\n      method: \"POST\",\n      body: z.object({\n        /**\n         * if true, the device will be trusted\n         * for 30 days. It'll be refreshed on\n         * every sign in request within this time.\n         */\n        trustDevice: z.boolean().optional().meta({\n          description: \"If true, the device will be trusted for 30 days. It'll be refreshed on every sign in request within this time. Eg: true\"\n        })\n      }).optional(),\n      metadata: {\n        openapi: {\n          summary: \"Send two factor OTP\",\n          description: \"Send two factor OTP to the user\",\n          responses: {\n            200: {\n              description: \"Successful response\",\n              content: {\n                \"application/json\": {\n                  schema: {\n                    type: \"object\",\n                    properties: {\n                      status: {\n                        type: \"boolean\"\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n    async (ctx) => {\n      if (!options || !options.sendOTP) {\n        ctx.context.logger.error(\n          \"send otp isn't configured. Please configure the send otp function on otp options.\"\n        );\n        throw new APIError(\"BAD_REQUEST\", {\n          message: \"otp isn't configured\"\n        });\n      }\n      const { session, key } = await verifyTwoFactor(ctx);\n      if (!session.user.twoFactorEnabled) {\n        throw new APIError(\"BAD_REQUEST\", {\n          message: TWO_FACTOR_ERROR_CODES.OTP_NOT_ENABLED\n        });\n      }\n      const code = generateRandomString(opts.digits, \"0-9\");\n      const hashedCode = await storeOTP(ctx, code);\n      await ctx.context.internalAdapter.createVerificationValue(\n        {\n          value: `${hashedCode}:0`,\n          identifier: `2fa-otp-${key}`,\n          expiresAt: new Date(Date.now() + opts.period)\n        },\n        ctx\n      );\n      await options.sendOTP(\n        { user: session.user, otp: code },\n        ctx.request\n      );\n      return ctx.json({ status: true });\n    }\n  );\n  const verifyOTP = createAuthEndpoint(\n    \"/two-factor/verify-otp\",\n    {\n      method: \"POST\",\n      body: z.object({\n        code: z.string().meta({\n          description: 'The otp code to verify. Eg: \"012345\"'\n        }),\n        /**\n         * if true, the device will be trusted\n         * for 30 days. It'll be refreshed on\n         * every sign in request within this time.\n         */\n        trustDevice: z.boolean().optional().meta({\n          description: \"If true, the device will be trusted for 30 days. It'll be refreshed on every sign in request within this time. Eg: true\"\n        })\n      }),\n      metadata: {\n        openapi: {\n          summary: \"Verify two factor OTP\",\n          description: \"Verify two factor OTP\",\n          responses: {\n            \"200\": {\n              description: \"Two-factor OTP verified successfully\",\n              content: {\n                \"application/json\": {\n                  schema: {\n                    type: \"object\",\n                    properties: {\n                      token: {\n                        type: \"string\",\n                        description: \"Session token for the authenticated session\"\n                      },\n                      user: {\n                        type: \"object\",\n                        properties: {\n                          id: {\n                            type: \"string\",\n                            description: \"Unique identifier of the user\"\n                          },\n                          email: {\n                            type: \"string\",\n                            format: \"email\",\n                            nullable: true,\n                            description: \"User's email address\"\n                          },\n                          emailVerified: {\n                            type: \"boolean\",\n                            nullable: true,\n                            description: \"Whether the email is verified\"\n                          },\n                          name: {\n                            type: \"string\",\n                            nullable: true,\n                            description: \"User's name\"\n                          },\n                          image: {\n                            type: \"string\",\n                            format: \"uri\",\n                            nullable: true,\n                            description: \"User's profile image URL\"\n                          },\n                          createdAt: {\n                            type: \"string\",\n                            format: \"date-time\",\n                            description: \"Timestamp when the user was created\"\n                          },\n                          updatedAt: {\n                            type: \"string\",\n                            format: \"date-time\",\n                            description: \"Timestamp when the user was last updated\"\n                          }\n                        },\n                        required: [\"id\", \"createdAt\", \"updatedAt\"],\n                        description: \"The authenticated user object\"\n                      }\n                    },\n                    required: [\"token\", \"user\"]\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n    async (ctx) => {\n      const { session, key, valid, invalid } = await verifyTwoFactor(ctx);\n      const toCheckOtp = await ctx.context.internalAdapter.findVerificationValue(\n        `2fa-otp-${key}`\n      );\n      const [otp, counter] = toCheckOtp?.value?.split(\":\") ?? [];\n      const decryptedOtp = await decryptOTP(ctx, otp);\n      if (!toCheckOtp || toCheckOtp.expiresAt < /* @__PURE__ */ new Date()) {\n        if (toCheckOtp) {\n          await ctx.context.internalAdapter.deleteVerificationValue(\n            toCheckOtp.id\n          );\n        }\n        throw new APIError(\"BAD_REQUEST\", {\n          message: TWO_FACTOR_ERROR_CODES.OTP_HAS_EXPIRED\n        });\n      }\n      const allowedAttempts = options?.allowedAttempts || 5;\n      if (parseInt(counter) >= allowedAttempts) {\n        await ctx.context.internalAdapter.deleteVerificationValue(\n          toCheckOtp.id\n        );\n        throw new APIError(\"BAD_REQUEST\", {\n          message: TWO_FACTOR_ERROR_CODES.TOO_MANY_ATTEMPTS_REQUEST_NEW_CODE\n        });\n      }\n      if (decryptedOtp === ctx.body.code) {\n        if (!session.user.twoFactorEnabled) {\n          if (!session.session) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: BASE_ERROR_CODES.FAILED_TO_CREATE_SESSION\n            });\n          }\n          const updatedUser = await ctx.context.internalAdapter.updateUser(\n            session.user.id,\n            {\n              twoFactorEnabled: true\n            }\n          );\n          const newSession = await ctx.context.internalAdapter.createSession(\n            session.user.id,\n            ctx,\n            false,\n            session.session\n          );\n          await ctx.context.internalAdapter.deleteSession(\n            session.session.token\n          );\n          await setSessionCookie(ctx, {\n            session: newSession,\n            user: updatedUser\n          });\n          return ctx.json({\n            token: newSession.token,\n            user: {\n              id: updatedUser.id,\n              email: updatedUser.email,\n              emailVerified: updatedUser.emailVerified,\n              name: updatedUser.name,\n              image: updatedUser.image,\n              createdAt: updatedUser.createdAt,\n              updatedAt: updatedUser.updatedAt\n            }\n          });\n        }\n        return valid(ctx);\n      } else {\n        await ctx.context.internalAdapter.updateVerificationValue(\n          toCheckOtp.id,\n          {\n            value: `${otp}:${(parseInt(counter, 10) || 0) + 1}`\n          }\n        );\n        return invalid(\"INVALID_CODE\");\n      }\n    }\n  );\n  return {\n    id: \"otp\",\n    endpoints: {\n      /**\n       * ### Endpoint\n       *\n       * POST `/two-factor/send-otp`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.send2FaOTP`\n       *\n       * **client:**\n       * `authClient.twoFactor.sendOtp`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/2fa#api-method-two-factor-send-otp)\n       */\n      sendTwoFactorOTP: send2FaOTP,\n      /**\n       * ### Endpoint\n       *\n       * POST `/two-factor/verify-otp`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.verifyOTP`\n       *\n       * **client:**\n       * `authClient.twoFactor.verifyOtp`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/2fa#api-method-two-factor-verify-otp)\n       */\n      verifyTwoFactorOTP: verifyOTP\n    }\n  };\n};\n\nconst totp2fa = (options) => {\n  const opts = {\n    ...options,\n    digits: options?.digits || 6,\n    period: options?.period || 30\n  };\n  const twoFactorTable = \"twoFactor\";\n  const generateTOTP = createAuthEndpoint(\n    \"/totp/generate\",\n    {\n      method: \"POST\",\n      body: z.object({\n        secret: z.string().meta({\n          description: \"The secret to generate the TOTP code\"\n        })\n      }),\n      metadata: {\n        openapi: {\n          summary: \"Generate TOTP code\",\n          description: \"Use this endpoint to generate a TOTP code\",\n          responses: {\n            200: {\n              description: \"Successful response\",\n              content: {\n                \"application/json\": {\n                  schema: {\n                    type: \"object\",\n                    properties: {\n                      code: {\n                        type: \"string\"\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        SERVER_ONLY: true\n      }\n    },\n    async (ctx) => {\n      if (options?.disable) {\n        ctx.context.logger.error(\n          \"totp isn't configured. please pass totp option on two factor plugin to enable totp\"\n        );\n        throw new APIError(\"BAD_REQUEST\", {\n          message: \"totp isn't configured\"\n        });\n      }\n      const code = await createOTP(ctx.body.secret, {\n        period: opts.period,\n        digits: opts.digits\n      }).totp();\n      return { code };\n    }\n  );\n  const getTOTPURI = createAuthEndpoint(\n    \"/two-factor/get-totp-uri\",\n    {\n      method: \"POST\",\n      use: [sessionMiddleware],\n      body: z.object({\n        password: z.string().meta({\n          description: \"User password\"\n        })\n      }),\n      metadata: {\n        openapi: {\n          summary: \"Get TOTP URI\",\n          description: \"Use this endpoint to get the TOTP URI\",\n          responses: {\n            200: {\n              description: \"Successful response\",\n              content: {\n                \"application/json\": {\n                  schema: {\n                    type: \"object\",\n                    properties: {\n                      totpURI: {\n                        type: \"string\"\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n    async (ctx) => {\n      if (options?.disable) {\n        ctx.context.logger.error(\n          \"totp isn't configured. please pass totp option on two factor plugin to enable totp\"\n        );\n        throw new APIError(\"BAD_REQUEST\", {\n          message: \"totp isn't configured\"\n        });\n      }\n      const user = ctx.context.session.user;\n      const twoFactor = await ctx.context.adapter.findOne({\n        model: twoFactorTable,\n        where: [\n          {\n            field: \"userId\",\n            value: user.id\n          }\n        ]\n      });\n      if (!twoFactor) {\n        throw new APIError(\"BAD_REQUEST\", {\n          message: TWO_FACTOR_ERROR_CODES.TOTP_NOT_ENABLED\n        });\n      }\n      const secret = await symmetricDecrypt({\n        key: ctx.context.secret,\n        data: twoFactor.secret\n      });\n      await ctx.context.password.checkPassword(user.id, ctx);\n      const totpURI = createOTP(secret, {\n        digits: opts.digits,\n        period: opts.period\n      }).url(options?.issuer || ctx.context.appName, user.email);\n      return {\n        totpURI\n      };\n    }\n  );\n  const verifyTOTP = createAuthEndpoint(\n    \"/two-factor/verify-totp\",\n    {\n      method: \"POST\",\n      body: z.object({\n        code: z.string().meta({\n          description: 'The otp code to verify. Eg: \"012345\"'\n        }),\n        /**\n         * if true, the device will be trusted\n         * for 30 days. It'll be refreshed on\n         * every sign in request within this time.\n         */\n        trustDevice: z.boolean().meta({\n          description: \"If true, the device will be trusted for 30 days. It'll be refreshed on every sign in request within this time. Eg: true\"\n        }).optional()\n      }),\n      metadata: {\n        openapi: {\n          summary: \"Verify two factor TOTP\",\n          description: \"Verify two factor TOTP\",\n          responses: {\n            200: {\n              description: \"Successful response\",\n              content: {\n                \"application/json\": {\n                  schema: {\n                    type: \"object\",\n                    properties: {\n                      status: {\n                        type: \"boolean\"\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n    async (ctx) => {\n      if (options?.disable) {\n        ctx.context.logger.error(\n          \"totp isn't configured. please pass totp option on two factor plugin to enable totp\"\n        );\n        throw new APIError(\"BAD_REQUEST\", {\n          message: \"totp isn't configured\"\n        });\n      }\n      const { session, valid, invalid } = await verifyTwoFactor(ctx);\n      const user = session.user;\n      const twoFactor = await ctx.context.adapter.findOne({\n        model: twoFactorTable,\n        where: [\n          {\n            field: \"userId\",\n            value: user.id\n          }\n        ]\n      });\n      if (!twoFactor) {\n        throw new APIError(\"BAD_REQUEST\", {\n          message: TWO_FACTOR_ERROR_CODES.TOTP_NOT_ENABLED\n        });\n      }\n      const decrypted = await symmetricDecrypt({\n        key: ctx.context.secret,\n        data: twoFactor.secret\n      });\n      const status = await createOTP(decrypted, {\n        period: opts.period,\n        digits: opts.digits\n      }).verify(ctx.body.code);\n      if (!status) {\n        return invalid(\"INVALID_CODE\");\n      }\n      if (!user.twoFactorEnabled) {\n        if (!session.session) {\n          throw new APIError(\"BAD_REQUEST\", {\n            message: BASE_ERROR_CODES.FAILED_TO_CREATE_SESSION\n          });\n        }\n        const updatedUser = await ctx.context.internalAdapter.updateUser(\n          user.id,\n          {\n            twoFactorEnabled: true\n          },\n          ctx\n        );\n        const newSession = await ctx.context.internalAdapter.createSession(user.id, ctx, false, session.session).catch((e) => {\n          throw e;\n        });\n        await ctx.context.internalAdapter.deleteSession(session.session.token);\n        await setSessionCookie(ctx, {\n          session: newSession,\n          user: updatedUser\n        });\n      }\n      return valid(ctx);\n    }\n  );\n  return {\n    id: \"totp\",\n    endpoints: {\n      /**\n       * ### Endpoint\n       *\n       * POST `/totp/generate`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.generateTOTP`\n       *\n       * **client:**\n       * `authClient.totp.generate`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/totp#api-method-totp-generate)\n       */\n      generateTOTP,\n      /**\n       * ### Endpoint\n       *\n       * POST `/two-factor/get-totp-uri`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.getTOTPURI`\n       *\n       * **client:**\n       * `authClient.twoFactor.getTotpUri`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/two-factor#api-method-two-factor-get-totp-uri)\n       */\n      getTOTPURI,\n      verifyTOTP\n    }\n  };\n};\n\nconst schema = {\n  user: {\n    fields: {\n      twoFactorEnabled: {\n        type: \"boolean\",\n        required: false,\n        defaultValue: false,\n        input: false\n      }\n    }\n  },\n  twoFactor: {\n    fields: {\n      secret: {\n        type: \"string\",\n        required: true,\n        returned: false\n      },\n      backupCodes: {\n        type: \"string\",\n        required: true,\n        returned: false\n      },\n      userId: {\n        type: \"string\",\n        required: true,\n        returned: false,\n        references: {\n          model: \"user\",\n          field: \"id\"\n        }\n      }\n    }\n  }\n};\n\nconst twoFactor = (options) => {\n  const opts = {\n    twoFactorTable: \"twoFactor\"\n  };\n  const totp = totp2fa(options?.totpOptions);\n  const backupCode = backupCode2fa(options?.backupCodeOptions);\n  const otp = otp2fa(options?.otpOptions);\n  return {\n    id: \"two-factor\",\n    endpoints: {\n      ...totp.endpoints,\n      ...otp.endpoints,\n      ...backupCode.endpoints,\n      /**\n       * ### Endpoint\n       *\n       * POST `/two-factor/enable`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.enableTwoFactor`\n       *\n       * **client:**\n       * `authClient.twoFactor.enable`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/2fa#api-method-two-factor-enable)\n       */\n      enableTwoFactor: createAuthEndpoint(\n        \"/two-factor/enable\",\n        {\n          method: \"POST\",\n          body: z.object({\n            password: z.string().meta({\n              description: \"User password\"\n            }),\n            issuer: z.string().meta({\n              description: \"Custom issuer for the TOTP URI\"\n            }).optional()\n          }),\n          use: [sessionMiddleware],\n          metadata: {\n            openapi: {\n              summary: \"Enable two factor authentication\",\n              description: \"Use this endpoint to enable two factor authentication. This will generate a TOTP URI and backup codes. Once the user verifies the TOTP URI, the two factor authentication will be enabled.\",\n              responses: {\n                200: {\n                  description: \"Successful response\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          totpURI: {\n                            type: \"string\",\n                            description: \"TOTP URI\"\n                          },\n                          backupCodes: {\n                            type: \"array\",\n                            items: {\n                              type: \"string\"\n                            },\n                            description: \"Backup codes\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const user = ctx.context.session.user;\n          const { password, issuer } = ctx.body;\n          const isPasswordValid = await validatePassword(ctx, {\n            password,\n            userId: user.id\n          });\n          if (!isPasswordValid) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: BASE_ERROR_CODES.INVALID_PASSWORD\n            });\n          }\n          const secret = generateRandomString(32);\n          const encryptedSecret = await symmetricEncrypt({\n            key: ctx.context.secret,\n            data: secret\n          });\n          const backupCodes = await generateBackupCodes(\n            ctx.context.secret,\n            options?.backupCodeOptions\n          );\n          if (options?.skipVerificationOnEnable) {\n            const updatedUser = await ctx.context.internalAdapter.updateUser(\n              user.id,\n              {\n                twoFactorEnabled: true\n              },\n              ctx\n            );\n            const newSession = await ctx.context.internalAdapter.createSession(\n              updatedUser.id,\n              ctx,\n              false,\n              ctx.context.session.session\n            );\n            await setSessionCookie(ctx, {\n              session: newSession,\n              user: updatedUser\n            });\n            await ctx.context.internalAdapter.deleteSession(\n              ctx.context.session.session.token\n            );\n          }\n          await ctx.context.adapter.deleteMany({\n            model: opts.twoFactorTable,\n            where: [\n              {\n                field: \"userId\",\n                value: user.id\n              }\n            ]\n          });\n          await ctx.context.adapter.create({\n            model: opts.twoFactorTable,\n            data: {\n              secret: encryptedSecret,\n              backupCodes: backupCodes.encryptedBackupCodes,\n              userId: user.id\n            }\n          });\n          const totpURI = createOTP(secret, {\n            digits: options?.totpOptions?.digits || 6,\n            period: options?.totpOptions?.period\n          }).url(issuer || options?.issuer || ctx.context.appName, user.email);\n          return ctx.json({ totpURI, backupCodes: backupCodes.backupCodes });\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * POST `/two-factor/disable`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.disableTwoFactor`\n       *\n       * **client:**\n       * `authClient.twoFactor.disable`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/2fa#api-method-two-factor-disable)\n       */\n      disableTwoFactor: createAuthEndpoint(\n        \"/two-factor/disable\",\n        {\n          method: \"POST\",\n          body: z.object({\n            password: z.string().meta({\n              description: \"User password\"\n            })\n          }),\n          use: [sessionMiddleware],\n          metadata: {\n            openapi: {\n              summary: \"Disable two factor authentication\",\n              description: \"Use this endpoint to disable two factor authentication.\",\n              responses: {\n                200: {\n                  description: \"Successful response\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          status: {\n                            type: \"boolean\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const user = ctx.context.session.user;\n          const { password } = ctx.body;\n          const isPasswordValid = await validatePassword(ctx, {\n            password,\n            userId: user.id\n          });\n          if (!isPasswordValid) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: \"Invalid password\"\n            });\n          }\n          const updatedUser = await ctx.context.internalAdapter.updateUser(\n            user.id,\n            {\n              twoFactorEnabled: false\n            },\n            ctx\n          );\n          await ctx.context.adapter.delete({\n            model: opts.twoFactorTable,\n            where: [\n              {\n                field: \"userId\",\n                value: updatedUser.id\n              }\n            ]\n          });\n          const newSession = await ctx.context.internalAdapter.createSession(\n            updatedUser.id,\n            ctx,\n            false,\n            ctx.context.session.session\n          );\n          await setSessionCookie(ctx, {\n            session: newSession,\n            user: updatedUser\n          });\n          await ctx.context.internalAdapter.deleteSession(\n            ctx.context.session.session.token\n          );\n          return ctx.json({ status: true });\n        }\n      )\n    },\n    options,\n    hooks: {\n      after: [\n        {\n          matcher(context) {\n            return context.path === \"/sign-in/email\" || context.path === \"/sign-in/username\" || context.path === \"/sign-in/phone-number\";\n          },\n          handler: createAuthMiddleware(async (ctx) => {\n            const data = ctx.context.newSession;\n            if (!data) {\n              return;\n            }\n            if (!data?.user.twoFactorEnabled) {\n              return;\n            }\n            const trustDeviceCookieName = ctx.context.createAuthCookie(\n              TRUST_DEVICE_COOKIE_NAME\n            );\n            const trustDeviceCookie = await ctx.getSignedCookie(\n              trustDeviceCookieName.name,\n              ctx.context.secret\n            );\n            if (trustDeviceCookie) {\n              const [token, sessionToken] = trustDeviceCookie.split(\"!\");\n              const expectedToken = await createHMAC(\n                \"SHA-256\",\n                \"base64urlnopad\"\n              ).sign(ctx.context.secret, `${data.user.id}!${sessionToken}`);\n              if (token === expectedToken) {\n                const newToken = await createHMAC(\n                  \"SHA-256\",\n                  \"base64urlnopad\"\n                ).sign(ctx.context.secret, `${data.user.id}!${sessionToken}`);\n                await ctx.setSignedCookie(\n                  trustDeviceCookieName.name,\n                  `${newToken}!${data.session.token}`,\n                  ctx.context.secret,\n                  trustDeviceCookieName.attributes\n                );\n                return;\n              }\n            }\n            deleteSessionCookie(ctx, true);\n            await ctx.context.internalAdapter.deleteSession(data.session.token);\n            const maxAge = (options?.otpOptions?.period ?? 3) * 60;\n            const twoFactorCookie = ctx.context.createAuthCookie(\n              TWO_FACTOR_COOKIE_NAME,\n              {\n                maxAge\n              }\n            );\n            const identifier = `2fa-${generateRandomString(20)}`;\n            await ctx.context.internalAdapter.createVerificationValue(\n              {\n                value: data.user.id,\n                identifier,\n                expiresAt: new Date(Date.now() + maxAge * 1e3)\n              },\n              ctx\n            );\n            await ctx.setSignedCookie(\n              twoFactorCookie.name,\n              identifier,\n              ctx.context.secret,\n              twoFactorCookie.attributes\n            );\n            return ctx.json({\n              twoFactorRedirect: true\n            });\n          })\n        }\n      ]\n    },\n    schema: mergeSchema(schema, options?.schema),\n    rateLimit: [\n      {\n        pathMatcher(path) {\n          return path.startsWith(\"/two-factor/\");\n        },\n        window: 10,\n        max: 3\n      }\n    ],\n    $ERROR_CODES: TWO_FACTOR_ERROR_CODES\n  };\n};\n\nexport { TWO_FACTOR_ERROR_CODES, twoFactor };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,yBAAyB;IAC7B,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,wBAAwB;IACxB,0BAA0B;IAC1B,qBAAqB;IACrB,cAAc;IACd,oCAAoC;IACpC,2BAA2B;AAC7B;AAEA,MAAM,yBAAyB;AAC/B,MAAM,2BAA2B;AAEjC,eAAe,gBAAgB,GAAG;IAChC,MAAM,UAAU,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAiB,AAAD,EAAE;IACxC,IAAI,CAAC,SAAS;QACZ,MAAM,aAAa,IAAI,OAAO,CAAC,gBAAgB,CAAC;QAChD,MAAM,kBAAkB,MAAM,IAAI,eAAe,CAC/C,WAAW,IAAI,EACf,IAAI,OAAO,CAAC,MAAM;QAEpB,IAAI,CAAC,iBAAiB;YACpB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;gBACjC,SAAS,uBAAuB,yBAAyB;YAC3D;QACF;QACA,MAAM,oBAAoB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,qBAAqB,CAAC;QAClF,IAAI,CAAC,mBAAmB;YACtB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;gBACjC,SAAS,uBAAuB,yBAAyB;YAC3D;QACF;QACA,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,YAAY,CACzD,kBAAkB,KAAK;QAEzB,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;gBACjC,SAAS,uBAAuB,yBAAyB;YAC3D;QACF;QACA,MAAM,iBAAiB,MAAM,IAAI,eAAe,CAC9C,IAAI,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,EAC9C,IAAI,OAAO,CAAC,MAAM;QAEpB,OAAO;YACL,OAAO,OAAO;gBACZ,MAAM,WAAW,MAAM,KAAK,OAAO,CAAC,eAAe,CAAC,aAAa,CAC/D,kBAAkB,KAAK,EACvB,MACA,CAAC,CAAC;gBAEJ,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,yBAAyB;wBAC1C,SAAS;oBACX;gBACF;gBACA,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;oBAC3B,SAAS;oBACT;gBACF;gBACA,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;oBACzB,MAAM,oBAAoB,KAAK,OAAO,CAAC,gBAAgB,CACrD,0BACA;wBACE,QAAQ,KAAK,KAAK,KAAK;oBAEzB;oBAEF,MAAM,QAAQ,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,kBAAkB,IAAI,CAC9D,KAAK,OAAO,CAAC,MAAM,EACnB,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,KAAK,EAAE;oBAEhC,MAAM,KAAK,eAAe,CACxB,kBAAkB,IAAI,EACtB,GAAG,MAAM,CAAC,EAAE,SAAS,KAAK,EAAE,EAC5B,KAAK,OAAO,CAAC,MAAM,EACnB,kBAAkB,UAAU;oBAE9B,KAAK,SAAS,CAAC,KAAK,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI;wBAClE,QAAQ;oBACV;oBACA,KAAK,SAAS,CAAC,WAAW,IAAI,EAAE,IAAI;wBAClC,QAAQ;oBACV;gBACF;gBACA,OAAO,KAAK,IAAI,CAAC;oBACf,OAAO,SAAS,KAAK;oBACrB,MAAM;wBACJ,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,eAAe,KAAK,aAAa;wBACjC,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;wBACjB,WAAW,KAAK,SAAS;wBACzB,WAAW,KAAK,SAAS;oBAC3B;gBACF;YACF;YACA,SAAS,OAAO;gBACd,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;oBACjC,SAAS,sBAAsB,CAAC,SAAS;gBAC3C;YACF;YACA,SAAS;gBACP,SAAS;gBACT;YACF;YACA,KAAK;QACP;IACF;IACA,OAAO;QACL,OAAO,OAAO;YACZ,OAAO,KAAK,IAAI,CAAC;gBACf,OAAO,QAAQ,OAAO,CAAC,KAAK;gBAC5B,MAAM;oBACJ,IAAI,QAAQ,IAAI,CAAC,EAAE;oBACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;oBACzB,eAAe,QAAQ,IAAI,CAAC,aAAa;oBACzC,MAAM,QAAQ,IAAI,CAAC,IAAI;oBACvB,OAAO,QAAQ,IAAI,CAAC,KAAK;oBACzB,WAAW,QAAQ,IAAI,CAAC,SAAS;oBACjC,WAAW,QAAQ,IAAI,CAAC,SAAS;gBACnC;YACF;QACF;QACA,SAAS;YACP,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;gBACjC,SAAS,uBAAuB,yBAAyB;YAC3D;QACF;QACA;QACA,KAAK,GAAG,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,EAAE;IACjD;AACF;AAEA,SAAS,sBAAsB,OAAO;IACpC,OAAO,MAAM,IAAI,CAAC;QAAE,QAAQ,SAAS,UAAU;IAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,IAAM,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,SAAS,UAAU,IAAI,OAAO,OAAO,QAAQ,GAAG,CAAC,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,KAAK,KAAK,CAAC,IAAI;AAClM;AACA,eAAe,oBAAoB,MAAM,EAAE,OAAO;IAChD,MAAM,MAAM;IACZ,MAAM,cAAc,SAAS,4BAA4B,QAAQ,yBAAyB,KAAK,sBAAsB;IACrH,MAAM,WAAW,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;QACtC,MAAM,KAAK,SAAS,CAAC;QACrB;IACF;IACA,OAAO;QACL;QACA,sBAAsB;IACxB;AACF;AACA,eAAe,iBAAiB,IAAI,EAAE,GAAG;IACvC,MAAM,QAAQ,MAAM,eAAe,KAAK,WAAW,EAAE;IACrD,IAAI,CAAC,OAAO;QACV,OAAO;YACL,QAAQ;YACR,SAAS;QACX;IACF;IACA,OAAO;QACL,QAAQ,MAAM,QAAQ,CAAC,KAAK,IAAI;QAChC,SAAS,MAAM,MAAM,CAAC,CAAC,OAAS,SAAS,KAAK,IAAI;IACpD;AACF;AACA,eAAe,eAAe,WAAW,EAAE,GAAG;IAC5C,MAAM,SAAS,IAAI,YAAY,SAAS,MAAM,CAC5C,IAAI,cAAc,MAAM,CACtB,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;QAAE;QAAK,MAAM;IAAY;IAGpD,MAAM,OAAO,KAAK,KAAK,CAAC;IACxB,MAAM,SAAS,CAAA,GAAA,mLAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,KAAK,SAAS,CAAC;IAC7C,IAAI,OAAO,OAAO,EAAE;QAClB,OAAO,OAAO,IAAI;IACpB;IACA,OAAO;AACT;AACA,MAAM,gBAAgB,CAAC;IACrB,MAAM,iBAAiB;IACvB,eAAe,iBAAiB,GAAG,EAAE,WAAW;QAC9C,IAAI,SAAS,qBAAqB,aAAa;YAC7C,OAAO,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC5B,KAAK,IAAI,OAAO,CAAC,MAAM;gBACvB,MAAM;YACR;QACF;QACA,IAAI,OAAO,SAAS,qBAAqB,YAAY,aAAa,SAAS,kBAAkB;YAC3F,OAAO,MAAM,SAAS,iBAAiB,QAAQ;QACjD;QACA,OAAO;IACT;IACA,eAAe,mBAAmB,GAAG,EAAE,WAAW;QAChD,IAAI,SAAS,qBAAqB,aAAa;YAC7C,OAAO,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC5B,KAAK,IAAI,OAAO,CAAC,MAAM;gBACvB,MAAM;YACR;QACF;QACA,IAAI,OAAO,SAAS,qBAAqB,YAAY,aAAa,SAAS,kBAAkB;YAC3F,OAAO,MAAM,SAAS,iBAAiB,QAAQ;QACjD;QACA,OAAO;IACT;IACA,OAAO;QACL,IAAI;QACJ,WAAW;YACT;;;;;;;;;;;;;;OAcC,GACD,kBAAkB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACjC,kCACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACpB,aAAa,CAAC,qCAAqC,CAAC;oBACtD;oBACA;;aAEC,GACD,gBAAgB,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,IAAI,CAAC;wBAC/B,aAAa;oBACf,GAAG,QAAQ;oBACX;;;;aAIC,GACD,aAAa,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,IAAI,CAAC;wBAC5B,aAAa;oBACf,GAAG,QAAQ;gBACb;gBACA,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,OAAO;gCACL,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,MAAM;oDACJ,MAAM;oDACN,YAAY;wDACV,IAAI;4DACF,MAAM;4DACN,aAAa;wDACf;wDACA,OAAO;4DACL,MAAM;4DACN,QAAQ;4DACR,UAAU;4DACV,aAAa;wDACf;wDACA,eAAe;4DACb,MAAM;4DACN,UAAU;4DACV,aAAa;wDACf;wDACA,MAAM;4DACJ,MAAM;4DACN,UAAU;4DACV,aAAa;wDACf;wDACA,OAAO;4DACL,MAAM;4DACN,QAAQ;4DACR,UAAU;4DACV,aAAa;wDACf;wDACA,kBAAkB;4DAChB,MAAM;4DACN,aAAa;wDACf;wDACA,WAAW;4DACT,MAAM;4DACN,QAAQ;4DACR,aAAa;wDACf;wDACA,WAAW;4DACT,MAAM;4DACN,QAAQ;4DACR,aAAa;wDACf;oDACF;oDACA,UAAU;wDACR;wDACA;wDACA;wDACA;qDACD;oDACD,aAAa;gDACf;gDACA,SAAS;oDACP,MAAM;oDACN,YAAY;wDACV,OAAO;4DACL,MAAM;4DACN,aAAa;wDACf;wDACA,QAAQ;4DACN,MAAM;4DACN,aAAa;wDACf;wDACA,WAAW;4DACT,MAAM;4DACN,QAAQ;4DACR,aAAa;wDACf;wDACA,WAAW;4DACT,MAAM;4DACN,QAAQ;4DACR,aAAa;wDACf;oDACF;oDACA,UAAU;wDACR;wDACA;wDACA;wDACA;qDACD;oDACD,aAAa;gDACf;4CACF;4CACA,UAAU;gDAAC;gDAAQ;6CAAU;wCAC/B;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,gBAAgB;gBACjD,MAAM,OAAO,QAAQ,IAAI;gBACzB,MAAM,YAAY,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBAClD,OAAO;oBACP,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO,KAAK,EAAE;wBAChB;qBACD;gBACH;gBACA,IAAI,CAAC,WAAW;oBACd,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,uBAAuB,wBAAwB;oBAC1D;gBACF;gBACA,MAAM,uBAAuB,MAAM,mBACjC,KACA,UAAU,WAAW;gBAEvB,MAAM,WAAW,MAAM,iBACrB;oBACE,aAAa;oBACb,MAAM,IAAI,IAAI,CAAC,IAAI;gBACrB,GACA,IAAI,OAAO,CAAC,MAAM;gBAEpB,IAAI,CAAC,SAAS,MAAM,EAAE;oBACpB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,uBAAuB,mBAAmB;oBACrD;gBACF;gBACA,MAAM,qBAAqB,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;oBAChD,KAAK,IAAI,OAAO,CAAC,MAAM;oBACvB,MAAM,KAAK,SAAS,CAAC,SAAS,OAAO;gBACvC;gBACA,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;oBACnC,OAAO;oBACP,QAAQ;wBACN,aAAa;oBACf;oBACA,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO,KAAK,EAAE;wBAChB;qBACD;gBACH;gBACA,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;oBAC5B,OAAO,MAAM;gBACf;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,OAAO,QAAQ,OAAO,EAAE;oBACxB,MAAM;wBACJ,IAAI,QAAQ,IAAI,EAAE;wBAClB,OAAO,QAAQ,IAAI,CAAC,KAAK;wBACzB,eAAe,QAAQ,IAAI,CAAC,aAAa;wBACzC,MAAM,QAAQ,IAAI,CAAC,IAAI;wBACvB,OAAO,QAAQ,IAAI,CAAC,KAAK;wBACzB,WAAW,QAAQ,IAAI,CAAC,SAAS;wBACjC,WAAW,QAAQ,IAAI,CAAC,SAAS;oBACnC;gBACF;YACF;YAEF;;;;;;;;;;;;;;OAcC,GACD,qBAAqB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACpC,qCACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACxB,aAAa;oBACf;gBACF;gBACA,KAAK;oBAAC,iLAAA,CAAA,IAAiB;iBAAC;gBACxB,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,OAAO;gCACL,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,QAAQ;oDACN,MAAM;oDACN,aAAa;oDACb,MAAM;wDAAC;qDAAK;gDACd;gDACA,aAAa;oDACX,MAAM;oDACN,OAAO;wDAAE,MAAM;oDAAS;oDACxB,aAAa;gDACf;4CACF;4CACA,UAAU;gDAAC;gDAAU;6CAAc;wCACrC;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI;gBACrC,IAAI,CAAC,KAAK,gBAAgB,EAAE;oBAC1B,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,uBAAuB,sBAAsB;oBACxD;gBACF;gBACA,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE;gBAClD,MAAM,cAAc,MAAM,oBACxB,IAAI,OAAO,CAAC,MAAM,EAClB;gBAEF,MAAM,oBAAoB,MAAM,iBAC9B,KACA,YAAY,oBAAoB;gBAElC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;oBAC/B,OAAO;oBACP,QAAQ;wBACN,aAAa;oBACf;oBACA,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACpC;qBACD;gBACH;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,QAAQ;oBACR,aAAa,YAAY,WAAW;gBACtC;YACF;YAEF;;;;;;;;;;;;;;OAcC,GACD,iBAAiB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAChC,iCACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,QAAQ,oNAAA,CAAA,SAAQ,CAAC,MAAM,GAAG,IAAI,CAAC;wBAC7B,aAAa,CAAC,mDAAmD,CAAC;oBACpE;gBACF;gBACA,UAAU;oBACR,aAAa;gBACf;YACF,GACA,OAAO;gBACL,MAAM,YAAY,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBAClD,OAAO;oBACP,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO,IAAI,IAAI,CAAC,MAAM;wBACxB;qBACD;gBACH;gBACA,IAAI,CAAC,WAAW;oBACd,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS;oBACX;gBACF;gBACA,MAAM,cAAc,MAAM,eACxB,UAAU,WAAW,EACrB,IAAI,OAAO,CAAC,MAAM;gBAEpB,IAAI,CAAC,aAAa;oBAChB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,uBAAuB,wBAAwB;oBAC1D;gBACF;gBACA,MAAM,uBAAuB,MAAM,mBACjC,KACA,UAAU,WAAW;gBAEvB,OAAO,IAAI,IAAI,CAAC;oBACd,QAAQ;oBACR,aAAa;gBACf;YACF;QAEJ;IACF;AACF;AAEA,MAAM,mBAAmB,OAAO;IAC9B,MAAM,OAAO,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAC7C,IAAI,cAAc,MAAM,CAAC;IAE3B,MAAM,SAAS,8JAAA,CAAA,YAAS,CAAC,MAAM,CAAC,IAAI,WAAW,OAAO;QACpD,SAAS;IACX;IACA,OAAO;AACT;AAEA,MAAM,SAAS,CAAC;IACd,MAAM,OAAO;QACX,UAAU;QACV,QAAQ;QACR,GAAG,OAAO;QACV,QAAQ,CAAC,SAAS,UAAU,CAAC,IAAI,KAAK;IACxC;IACA,eAAe,SAAS,GAAG,EAAE,GAAG;QAC9B,IAAI,KAAK,QAAQ,KAAK,UAAU;YAC9B,OAAO,MAAM,iBAAiB;QAChC;QACA,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,UAAU,KAAK,QAAQ,EAAE;YAChE,OAAO,MAAM,KAAK,QAAQ,CAAC,IAAI,CAAC;QAClC;QACA,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,aAAa,KAAK,QAAQ,EAAE;YACnE,OAAO,MAAM,KAAK,QAAQ,CAAC,OAAO,CAAC;QACrC;QACA,IAAI,KAAK,QAAQ,KAAK,aAAa;YACjC,OAAO,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC5B,KAAK,IAAI,OAAO,CAAC,MAAM;gBACvB,MAAM;YACR;QACF;QACA,OAAO;IACT;IACA,eAAe,WAAW,GAAG,EAAE,GAAG;QAChC,IAAI,KAAK,QAAQ,KAAK,UAAU;YAC9B,OAAO,MAAM,iBAAiB;QAChC;QACA,IAAI,KAAK,QAAQ,KAAK,aAAa;YACjC,OAAO,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC5B,KAAK,IAAI,OAAO,CAAC,MAAM;gBACvB,MAAM;YACR;QACF;QACA,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,aAAa,KAAK,QAAQ,EAAE;YACnE,OAAO,MAAM,KAAK,QAAQ,CAAC,OAAO,CAAC;QACrC;QACA,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,UAAU,KAAK,QAAQ,EAAE;YAChE,OAAO,MAAM,KAAK,QAAQ,CAAC,IAAI,CAAC;QAClC;QACA,OAAO;IACT;IACA,MAAM,aAAa,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAClC,wBACA;QACE,QAAQ;QACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;YACb;;;;SAIC,GACD,aAAa,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,QAAQ,GAAG,IAAI,CAAC;gBACvC,aAAa;YACf;QACF,GAAG,QAAQ;QACX,UAAU;YACR,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,WAAW;oBACT,KAAK;wBACH,aAAa;wBACb,SAAS;4BACP,oBAAoB;gCAClB,QAAQ;oCACN,MAAM;oCACN,YAAY;wCACV,QAAQ;4CACN,MAAM;wCACR;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF,GACA,OAAO;QACL,IAAI,CAAC,WAAW,CAAC,QAAQ,OAAO,EAAE;YAChC,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CACtB;YAEF,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS;YACX;QACF;QACA,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,MAAM,gBAAgB;QAC/C,IAAI,CAAC,QAAQ,IAAI,CAAC,gBAAgB,EAAE;YAClC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,uBAAuB,eAAe;YACjD;QACF;QACA,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,KAAK,MAAM,EAAE;QAC/C,MAAM,aAAa,MAAM,SAAS,KAAK;QACvC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;YACE,OAAO,GAAG,WAAW,EAAE,CAAC;YACxB,YAAY,CAAC,QAAQ,EAAE,KAAK;YAC5B,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM;QAC9C,GACA;QAEF,MAAM,QAAQ,OAAO,CACnB;YAAE,MAAM,QAAQ,IAAI;YAAE,KAAK;QAAK,GAChC,IAAI,OAAO;QAEb,OAAO,IAAI,IAAI,CAAC;YAAE,QAAQ;QAAK;IACjC;IAEF,MAAM,YAAY,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACjC,0BACA;QACE,QAAQ;QACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;YACb,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;gBACpB,aAAa;YACf;YACA;;;;SAIC,GACD,aAAa,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,QAAQ,GAAG,IAAI,CAAC;gBACvC,aAAa;YACf;QACF;QACA,UAAU;YACR,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,WAAW;oBACT,OAAO;wBACL,aAAa;wBACb,SAAS;4BACP,oBAAoB;gCAClB,QAAQ;oCACN,MAAM;oCACN,YAAY;wCACV,OAAO;4CACL,MAAM;4CACN,aAAa;wCACf;wCACA,MAAM;4CACJ,MAAM;4CACN,YAAY;gDACV,IAAI;oDACF,MAAM;oDACN,aAAa;gDACf;gDACA,OAAO;oDACL,MAAM;oDACN,QAAQ;oDACR,UAAU;oDACV,aAAa;gDACf;gDACA,eAAe;oDACb,MAAM;oDACN,UAAU;oDACV,aAAa;gDACf;gDACA,MAAM;oDACJ,MAAM;oDACN,UAAU;oDACV,aAAa;gDACf;gDACA,OAAO;oDACL,MAAM;oDACN,QAAQ;oDACR,UAAU;oDACV,aAAa;gDACf;gDACA,WAAW;oDACT,MAAM;oDACN,QAAQ;oDACR,aAAa;gDACf;gDACA,WAAW;oDACT,MAAM;oDACN,QAAQ;oDACR,aAAa;gDACf;4CACF;4CACA,UAAU;gDAAC;gDAAM;gDAAa;6CAAY;4CAC1C,aAAa;wCACf;oCACF;oCACA,UAAU;wCAAC;wCAAS;qCAAO;gCAC7B;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF,GACA,OAAO;QACL,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,MAAM,gBAAgB;QAC/D,MAAM,aAAa,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,qBAAqB,CACxE,CAAC,QAAQ,EAAE,KAAK;QAElB,MAAM,CAAC,KAAK,QAAQ,GAAG,YAAY,OAAO,MAAM,QAAQ,EAAE;QAC1D,MAAM,eAAe,MAAM,WAAW,KAAK;QAC3C,IAAI,CAAC,cAAc,WAAW,SAAS,GAAG,aAAa,GAAG,IAAI,QAAQ;YACpE,IAAI,YAAY;gBACd,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,WAAW,EAAE;YAEjB;YACA,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,uBAAuB,eAAe;YACjD;QACF;QACA,MAAM,kBAAkB,SAAS,mBAAmB;QACpD,IAAI,SAAS,YAAY,iBAAiB;YACxC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,WAAW,EAAE;YAEf,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,uBAAuB,kCAAkC;YACpE;QACF;QACA,IAAI,iBAAiB,IAAI,IAAI,CAAC,IAAI,EAAE;YAClC,IAAI,CAAC,QAAQ,IAAI,CAAC,gBAAgB,EAAE;gBAClC,IAAI,CAAC,QAAQ,OAAO,EAAE;oBACpB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,wBAAwB;oBACpD;gBACF;gBACA,MAAM,cAAc,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAC9D,QAAQ,IAAI,CAAC,EAAE,EACf;oBACE,kBAAkB;gBACpB;gBAEF,MAAM,aAAa,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAChE,QAAQ,IAAI,CAAC,EAAE,EACf,KACA,OACA,QAAQ,OAAO;gBAEjB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7C,QAAQ,OAAO,CAAC,KAAK;gBAEvB,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;oBAC1B,SAAS;oBACT,MAAM;gBACR;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,OAAO,WAAW,KAAK;oBACvB,MAAM;wBACJ,IAAI,YAAY,EAAE;wBAClB,OAAO,YAAY,KAAK;wBACxB,eAAe,YAAY,aAAa;wBACxC,MAAM,YAAY,IAAI;wBACtB,OAAO,YAAY,KAAK;wBACxB,WAAW,YAAY,SAAS;wBAChC,WAAW,YAAY,SAAS;oBAClC;gBACF;YACF;YACA,OAAO,MAAM;QACf,OAAO;YACL,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,WAAW,EAAE,EACb;gBACE,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,SAAS,OAAO,CAAC,IAAI,GAAG;YACrD;YAEF,OAAO,QAAQ;QACjB;IACF;IAEF,OAAO;QACL,IAAI;QACJ,WAAW;YACT;;;;;;;;;;;;;;OAcC,GACD,kBAAkB;YAClB;;;;;;;;;;;;;;OAcC,GACD,oBAAoB;QACtB;IACF;AACF;AAEA,MAAM,UAAU,CAAC;IACf,MAAM,OAAO;QACX,GAAG,OAAO;QACV,QAAQ,SAAS,UAAU;QAC3B,QAAQ,SAAS,UAAU;IAC7B;IACA,MAAM,iBAAiB;IACvB,MAAM,eAAe,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACpC,kBACA;QACE,QAAQ;QACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;YACb,QAAQ,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;gBACtB,aAAa;YACf;QACF;QACA,UAAU;YACR,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,WAAW;oBACT,KAAK;wBACH,aAAa;wBACb,SAAS;4BACP,oBAAoB;gCAClB,QAAQ;oCACN,MAAM;oCACN,YAAY;wCACV,MAAM;4CACJ,MAAM;wCACR;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;YACA,aAAa;QACf;IACF,GACA,OAAO;QACL,IAAI,SAAS,SAAS;YACpB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CACtB;YAEF,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS;YACX;QACF;QACA,MAAM,OAAO,MAAM,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;YAC5C,QAAQ,KAAK,MAAM;YACnB,QAAQ,KAAK,MAAM;QACrB,GAAG,IAAI;QACP,OAAO;YAAE;QAAK;IAChB;IAEF,MAAM,aAAa,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAClC,4BACA;QACE,QAAQ;QACR,KAAK;YAAC,iLAAA,CAAA,IAAiB;SAAC;QACxB,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;YACb,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;gBACxB,aAAa;YACf;QACF;QACA,UAAU;YACR,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,WAAW;oBACT,KAAK;wBACH,aAAa;wBACb,SAAS;4BACP,oBAAoB;gCAClB,QAAQ;oCACN,MAAM;oCACN,YAAY;wCACV,SAAS;4CACP,MAAM;wCACR;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF,GACA,OAAO;QACL,IAAI,SAAS,SAAS;YACpB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CACtB;YAEF,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS;YACX;QACF;QACA,MAAM,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI;QACrC,MAAM,YAAY,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;YAClD,OAAO;YACP,OAAO;gBACL;oBACE,OAAO;oBACP,OAAO,KAAK,EAAE;gBAChB;aACD;QACH;QACA,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,uBAAuB,gBAAgB;YAClD;QACF;QACA,MAAM,SAAS,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;YACpC,KAAK,IAAI,OAAO,CAAC,MAAM;YACvB,MAAM,UAAU,MAAM;QACxB;QACA,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE;QAClD,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YAChC,QAAQ,KAAK,MAAM;YACnB,QAAQ,KAAK,MAAM;QACrB,GAAG,GAAG,CAAC,SAAS,UAAU,IAAI,OAAO,CAAC,OAAO,EAAE,KAAK,KAAK;QACzD,OAAO;YACL;QACF;IACF;IAEF,MAAM,aAAa,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAClC,2BACA;QACE,QAAQ;QACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;YACb,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;gBACpB,aAAa;YACf;YACA;;;;SAIC,GACD,aAAa,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,IAAI,CAAC;gBAC5B,aAAa;YACf,GAAG,QAAQ;QACb;QACA,UAAU;YACR,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,WAAW;oBACT,KAAK;wBACH,aAAa;wBACb,SAAS;4BACP,oBAAoB;gCAClB,QAAQ;oCACN,MAAM;oCACN,YAAY;wCACV,QAAQ;4CACN,MAAM;wCACR;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF,GACA,OAAO;QACL,IAAI,SAAS,SAAS;YACpB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CACtB;YAEF,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS;YACX;QACF;QACA,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,MAAM,gBAAgB;QAC1D,MAAM,OAAO,QAAQ,IAAI;QACzB,MAAM,YAAY,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;YAClD,OAAO;YACP,OAAO;gBACL;oBACE,OAAO;oBACP,OAAO,KAAK,EAAE;gBAChB;aACD;QACH;QACA,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,uBAAuB,gBAAgB;YAClD;QACF;QACA,MAAM,YAAY,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;YACvC,KAAK,IAAI,OAAO,CAAC,MAAM;YACvB,MAAM,UAAU,MAAM;QACxB;QACA,MAAM,SAAS,MAAM,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,WAAW;YACxC,QAAQ,KAAK,MAAM;YACnB,QAAQ,KAAK,MAAM;QACrB,GAAG,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI;QACvB,IAAI,CAAC,QAAQ;YACX,OAAO,QAAQ;QACjB;QACA,IAAI,CAAC,KAAK,gBAAgB,EAAE;YAC1B,IAAI,CAAC,QAAQ,OAAO,EAAE;gBACpB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;oBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,wBAAwB;gBACpD;YACF;YACA,MAAM,cAAc,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAC9D,KAAK,EAAE,EACP;gBACE,kBAAkB;YACpB,GACA;YAEF,MAAM,aAAa,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,KAAK,OAAO,QAAQ,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC9G,MAAM;YACR;YACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,QAAQ,OAAO,CAAC,KAAK;YACrE,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;gBAC1B,SAAS;gBACT,MAAM;YACR;QACF;QACA,OAAO,MAAM;IACf;IAEF,OAAO;QACL,IAAI;QACJ,WAAW;YACT;;;;;;;;;;;;;;OAcC,GACD;YACA;;;;;;;;;;;;;;OAcC,GACD;YACA;QACF;IACF;AACF;AAEA,MAAM,SAAS;IACb,MAAM;QACJ,QAAQ;YACN,kBAAkB;gBAChB,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,OAAO;YACT;QACF;IACF;IACA,WAAW;QACT,QAAQ;YACN,QAAQ;gBACN,MAAM;gBACN,UAAU;gBACV,UAAU;YACZ;YACA,aAAa;gBACX,MAAM;gBACN,UAAU;gBACV,UAAU;YACZ;YACA,QAAQ;gBACN,MAAM;gBACN,UAAU;gBACV,UAAU;gBACV,YAAY;oBACV,OAAO;oBACP,OAAO;gBACT;YACF;QACF;IACF;AACF;AAEA,MAAM,YAAY,CAAC;IACjB,MAAM,OAAO;QACX,gBAAgB;IAClB;IACA,MAAM,OAAO,QAAQ,SAAS;IAC9B,MAAM,aAAa,cAAc,SAAS;IAC1C,MAAM,MAAM,OAAO,SAAS;IAC5B,OAAO;QACL,IAAI;QACJ,WAAW;YACT,GAAG,KAAK,SAAS;YACjB,GAAG,IAAI,SAAS;YAChB,GAAG,WAAW,SAAS;YACvB;;;;;;;;;;;;;;OAcC,GACD,iBAAiB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAChC,sBACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACxB,aAAa;oBACf;oBACA,QAAQ,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACtB,aAAa;oBACf,GAAG,QAAQ;gBACb;gBACA,KAAK;oBAAC,iLAAA,CAAA,IAAiB;iBAAC;gBACxB,UAAU;oBACR,SAAS;wBACP,SAAS;wBACT,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,SAAS;oDACP,MAAM;oDACN,aAAa;gDACf;gDACA,aAAa;oDACX,MAAM;oDACN,OAAO;wDACL,MAAM;oDACR;oDACA,aAAa;gDACf;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI;gBACrC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI,IAAI;gBACrC,MAAM,kBAAkB,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAgB,AAAD,EAAE,KAAK;oBAClD;oBACA,QAAQ,KAAK,EAAE;gBACjB;gBACA,IAAI,CAAC,iBAAiB;oBACpB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,gBAAgB;oBAC5C;gBACF;gBACA,MAAM,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE;gBACpC,MAAM,kBAAkB,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC7C,KAAK,IAAI,OAAO,CAAC,MAAM;oBACvB,MAAM;gBACR;gBACA,MAAM,cAAc,MAAM,oBACxB,IAAI,OAAO,CAAC,MAAM,EAClB,SAAS;gBAEX,IAAI,SAAS,0BAA0B;oBACrC,MAAM,cAAc,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAC9D,KAAK,EAAE,EACP;wBACE,kBAAkB;oBACpB,GACA;oBAEF,MAAM,aAAa,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAChE,YAAY,EAAE,EACd,KACA,OACA,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO;oBAE7B,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;wBAC1B,SAAS;wBACT,MAAM;oBACR;oBACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7C,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK;gBAErC;gBACA,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;oBACnC,OAAO,KAAK,cAAc;oBAC1B,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO,KAAK,EAAE;wBAChB;qBACD;gBACH;gBACA,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;oBAC/B,OAAO,KAAK,cAAc;oBAC1B,MAAM;wBACJ,QAAQ;wBACR,aAAa,YAAY,oBAAoB;wBAC7C,QAAQ,KAAK,EAAE;oBACjB;gBACF;gBACA,MAAM,UAAU,CAAA,GAAA,2JAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;oBAChC,QAAQ,SAAS,aAAa,UAAU;oBACxC,QAAQ,SAAS,aAAa;gBAChC,GAAG,GAAG,CAAC,UAAU,SAAS,UAAU,IAAI,OAAO,CAAC,OAAO,EAAE,KAAK,KAAK;gBACnE,OAAO,IAAI,IAAI,CAAC;oBAAE;oBAAS,aAAa,YAAY,WAAW;gBAAC;YAClE;YAEF;;;;;;;;;;;;;;OAcC,GACD,kBAAkB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACjC,uBACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACxB,aAAa;oBACf;gBACF;gBACA,KAAK;oBAAC,iLAAA,CAAA,IAAiB;iBAAC;gBACxB,UAAU;oBACR,SAAS;wBACP,SAAS;wBACT,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,QAAQ;oDACN,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI;gBACrC,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,IAAI;gBAC7B,MAAM,kBAAkB,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAgB,AAAD,EAAE,KAAK;oBAClD;oBACA,QAAQ,KAAK,EAAE;gBACjB;gBACA,IAAI,CAAC,iBAAiB;oBACpB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS;oBACX;gBACF;gBACA,MAAM,cAAc,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAC9D,KAAK,EAAE,EACP;oBACE,kBAAkB;gBACpB,GACA;gBAEF,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;oBAC/B,OAAO,KAAK,cAAc;oBAC1B,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO,YAAY,EAAE;wBACvB;qBACD;gBACH;gBACA,MAAM,aAAa,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAChE,YAAY,EAAE,EACd,KACA,OACA,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO;gBAE7B,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;oBAC1B,SAAS;oBACT,MAAM;gBACR;gBACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7C,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK;gBAEnC,OAAO,IAAI,IAAI,CAAC;oBAAE,QAAQ;gBAAK;YACjC;QAEJ;QACA;QACA,OAAO;YACL,OAAO;gBACL;oBACE,SAAQ,OAAO;wBACb,OAAO,QAAQ,IAAI,KAAK,oBAAoB,QAAQ,IAAI,KAAK,uBAAuB,QAAQ,IAAI,KAAK;oBACvG;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,OAAO,IAAI,OAAO,CAAC,UAAU;wBACnC,IAAI,CAAC,MAAM;4BACT;wBACF;wBACA,IAAI,CAAC,MAAM,KAAK,kBAAkB;4BAChC;wBACF;wBACA,MAAM,wBAAwB,IAAI,OAAO,CAAC,gBAAgB,CACxD;wBAEF,MAAM,oBAAoB,MAAM,IAAI,eAAe,CACjD,sBAAsB,IAAI,EAC1B,IAAI,OAAO,CAAC,MAAM;wBAEpB,IAAI,mBAAmB;4BACrB,MAAM,CAAC,OAAO,aAAa,GAAG,kBAAkB,KAAK,CAAC;4BACtD,MAAM,gBAAgB,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EACnC,WACA,kBACA,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,cAAc;4BAC5D,IAAI,UAAU,eAAe;gCAC3B,MAAM,WAAW,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAC9B,WACA,kBACA,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,cAAc;gCAC5D,MAAM,IAAI,eAAe,CACvB,sBAAsB,IAAI,EAC1B,GAAG,SAAS,CAAC,EAAE,KAAK,OAAO,CAAC,KAAK,EAAE,EACnC,IAAI,OAAO,CAAC,MAAM,EAClB,sBAAsB,UAAU;gCAElC;4BACF;wBACF;wBACA,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK;wBACzB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC,KAAK,OAAO,CAAC,KAAK;wBAClE,MAAM,SAAS,CAAC,SAAS,YAAY,UAAU,CAAC,IAAI;wBACpD,MAAM,kBAAkB,IAAI,OAAO,CAAC,gBAAgB,CAClD,wBACA;4BACE;wBACF;wBAEF,MAAM,aAAa,CAAC,IAAI,EAAE,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,KAAK;wBACpD,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;4BACE,OAAO,KAAK,IAAI,CAAC,EAAE;4BACnB;4BACA,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS;wBAC5C,GACA;wBAEF,MAAM,IAAI,eAAe,CACvB,gBAAgB,IAAI,EACpB,YACA,IAAI,OAAO,CAAC,MAAM,EAClB,gBAAgB,UAAU;wBAE5B,OAAO,IAAI,IAAI,CAAC;4BACd,mBAAmB;wBACrB;oBACF;gBACF;aACD;QACH;QACA,QAAQ,CAAA,GAAA,iLAAA,CAAA,IAAW,AAAD,EAAE,QAAQ,SAAS;QACrC,WAAW;YACT;gBACE,aAAY,IAAI;oBACd,OAAO,KAAK,UAAU,CAAC;gBACzB;gBACA,QAAQ;gBACR,KAAK;YACP;SACD;QACD,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1636, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/username/index.mjs"], "sourcesContent": ["import * as z from 'zod/v4';\nimport { i as createAuthMiddleware, j as createAuthEndpoint, u as sendVerificationEmailFn, B as BASE_ERROR_CODES } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport { APIError } from 'better-call';\nimport { setSessionCookie } from '../../cookies/index.mjs';\nimport { m as mergeSchema } from '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '@better-auth/utils/hash';\nimport '@better-auth/utils/base64';\nimport '../../crypto/index.mjs';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-auth/utils/random';\nimport '@better-fetch/fetch';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport 'jose/errors';\n\nconst getSchema = (normalizer) => {\n  return {\n    user: {\n      fields: {\n        username: {\n          type: \"string\",\n          required: false,\n          sortable: true,\n          unique: true,\n          returned: true\n        },\n        displayUsername: {\n          type: \"string\",\n          required: false,\n          transform: {\n            input(value) {\n              return value == null ? value : normalizer(value);\n            }\n          }\n        }\n      }\n    }\n  };\n};\n\nconst USERNAME_ERROR_CODES = {\n  INVALID_USERNAME_OR_PASSWORD: \"invalid username or password\",\n  EMAIL_NOT_VERIFIED: \"email not verified\",\n  UNEXPECTED_ERROR: \"unexpected error\",\n  USERNAME_IS_ALREADY_TAKEN: \"username is already taken. please try another.\",\n  USERNAME_TOO_SHORT: \"username is too short\",\n  USERNAME_TOO_LONG: \"username is too long\",\n  INVALID_USERNAME: \"username is invalid\"\n};\n\nfunction defaultUsernameValidator(username2) {\n  return /^[a-zA-Z0-9_.]+$/.test(username2);\n}\nconst username = (options) => {\n  const normalizer = (username2) => {\n    if (options?.usernameNormalization === false) {\n      return username2;\n    }\n    if (options?.usernameNormalization) {\n      return options.usernameNormalization(username2);\n    }\n    return username2.toLowerCase();\n  };\n  return {\n    id: \"username\",\n    endpoints: {\n      signInUsername: createAuthEndpoint(\n        \"/sign-in/username\",\n        {\n          method: \"POST\",\n          body: z.object({\n            username: z.string().meta({ description: \"The username of the user\" }),\n            password: z.string().meta({ description: \"The password of the user\" }),\n            rememberMe: z.boolean().meta({\n              description: \"Remember the user session\"\n            }).optional(),\n            callbackURL: z.string().meta({\n              description: \"The URL to redirect to after email verification\"\n            }).optional()\n          }),\n          metadata: {\n            openapi: {\n              summary: \"Sign in with username\",\n              description: \"Sign in with username\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          token: {\n                            type: \"string\",\n                            description: \"Session token for the authenticated session\"\n                          },\n                          user: {\n                            $ref: \"#/components/schemas/User\"\n                          }\n                        },\n                        required: [\"token\", \"user\"]\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          if (!ctx.body.username || !ctx.body.password) {\n            ctx.context.logger.error(\"Username or password not found\");\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: USERNAME_ERROR_CODES.INVALID_USERNAME_OR_PASSWORD\n            });\n          }\n          const minUsernameLength = options?.minUsernameLength || 3;\n          const maxUsernameLength = options?.maxUsernameLength || 30;\n          if (ctx.body.username.length < minUsernameLength) {\n            ctx.context.logger.error(\"Username too short\", {\n              username: ctx.body.username\n            });\n            throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n              message: USERNAME_ERROR_CODES.USERNAME_TOO_SHORT\n            });\n          }\n          if (ctx.body.username.length > maxUsernameLength) {\n            ctx.context.logger.error(\"Username too long\", {\n              username: ctx.body.username\n            });\n            throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n              message: USERNAME_ERROR_CODES.USERNAME_TOO_LONG\n            });\n          }\n          const validator = options?.usernameValidator || defaultUsernameValidator;\n          if (!validator(ctx.body.username)) {\n            throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n              message: USERNAME_ERROR_CODES.INVALID_USERNAME\n            });\n          }\n          const user = await ctx.context.adapter.findOne({\n            model: \"user\",\n            where: [\n              {\n                field: \"username\",\n                value: normalizer(ctx.body.username)\n              }\n            ]\n          });\n          if (!user) {\n            await ctx.context.password.hash(ctx.body.password);\n            ctx.context.logger.error(\"User not found\", {\n              username: ctx.body.username\n            });\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: USERNAME_ERROR_CODES.INVALID_USERNAME_OR_PASSWORD\n            });\n          }\n          if (!user.emailVerified && ctx.context.options.emailAndPassword?.requireEmailVerification) {\n            await sendVerificationEmailFn(ctx, user);\n            throw new APIError(\"FORBIDDEN\", {\n              message: USERNAME_ERROR_CODES.EMAIL_NOT_VERIFIED\n            });\n          }\n          const account = await ctx.context.adapter.findOne({\n            model: \"account\",\n            where: [\n              {\n                field: \"userId\",\n                value: user.id\n              },\n              {\n                field: \"providerId\",\n                value: \"credential\"\n              }\n            ]\n          });\n          if (!account) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: USERNAME_ERROR_CODES.INVALID_USERNAME_OR_PASSWORD\n            });\n          }\n          const currentPassword = account?.password;\n          if (!currentPassword) {\n            ctx.context.logger.error(\"Password not found\", {\n              username: ctx.body.username\n            });\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: USERNAME_ERROR_CODES.INVALID_USERNAME_OR_PASSWORD\n            });\n          }\n          const validPassword = await ctx.context.password.verify({\n            hash: currentPassword,\n            password: ctx.body.password\n          });\n          if (!validPassword) {\n            ctx.context.logger.error(\"Invalid password\");\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: USERNAME_ERROR_CODES.INVALID_USERNAME_OR_PASSWORD\n            });\n          }\n          const session = await ctx.context.internalAdapter.createSession(\n            user.id,\n            ctx,\n            ctx.body.rememberMe === false\n          );\n          if (!session) {\n            return ctx.json(null, {\n              status: 500,\n              body: {\n                message: BASE_ERROR_CODES.FAILED_TO_CREATE_SESSION\n              }\n            });\n          }\n          await setSessionCookie(\n            ctx,\n            { session, user },\n            ctx.body.rememberMe === false\n          );\n          return ctx.json({\n            token: session.token,\n            user: {\n              id: user.id,\n              email: user.email,\n              emailVerified: user.emailVerified,\n              username: user.username,\n              name: user.name,\n              image: user.image,\n              createdAt: user.createdAt,\n              updatedAt: user.updatedAt\n            }\n          });\n        }\n      ),\n      isUsernameAvailable: createAuthEndpoint(\n        \"/is-username-available\",\n        {\n          method: \"POST\",\n          body: z.object({\n            username: z.string().meta({\n              description: \"The username to check\"\n            })\n          })\n        },\n        async (ctx) => {\n          const username2 = ctx.body.username;\n          if (!username2) {\n            throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n              message: USERNAME_ERROR_CODES.INVALID_USERNAME\n            });\n          }\n          const user = await ctx.context.adapter.findOne({\n            model: \"user\",\n            where: [\n              {\n                field: \"username\",\n                value: username2.toLowerCase()\n              }\n            ]\n          });\n          if (user) {\n            return ctx.json({\n              available: false\n            });\n          }\n          return ctx.json({\n            available: true\n          });\n        }\n      )\n    },\n    schema: mergeSchema(getSchema(normalizer), options?.schema),\n    hooks: {\n      before: [\n        {\n          matcher(context) {\n            return context.path === \"/sign-up/email\" || context.path === \"/update-user\";\n          },\n          handler: createAuthMiddleware(async (ctx) => {\n            const username2 = ctx.body.username;\n            if (username2 !== void 0 && typeof username2 === \"string\") {\n              const minUsernameLength = options?.minUsernameLength || 3;\n              const maxUsernameLength = options?.maxUsernameLength || 30;\n              if (username2.length < minUsernameLength) {\n                throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n                  message: USERNAME_ERROR_CODES.USERNAME_TOO_SHORT\n                });\n              }\n              if (username2.length > maxUsernameLength) {\n                throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n                  message: USERNAME_ERROR_CODES.USERNAME_TOO_LONG\n                });\n              }\n              const validator = options?.usernameValidator || defaultUsernameValidator;\n              const valid = await validator(username2);\n              if (!valid) {\n                throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n                  message: USERNAME_ERROR_CODES.INVALID_USERNAME\n                });\n              }\n              const user = await ctx.context.adapter.findOne({\n                model: \"user\",\n                where: [\n                  {\n                    field: \"username\",\n                    value: normalizer(username2)\n                  }\n                ]\n              });\n              const blockChangeSignUp = ctx.path === \"/sign-up/email\" && user;\n              const blockChangeUpdateUser = ctx.path === \"/update-user\" && user && ctx.context.session && user.id !== ctx.context.session.session.userId;\n              if (blockChangeSignUp || blockChangeUpdateUser) {\n                throw new APIError(\"UNPROCESSABLE_ENTITY\", {\n                  message: USERNAME_ERROR_CODES.USERNAME_IS_ALREADY_TAKEN\n                });\n              }\n            }\n          })\n        },\n        {\n          matcher(context) {\n            return context.path === \"/sign-up/email\" || context.path === \"/update-user\";\n          },\n          handler: createAuthMiddleware(async (ctx) => {\n            if (ctx.body.username) {\n              ctx.body.displayUsername ||= ctx.body.username;\n              ctx.body.username = normalizer(ctx.body.username);\n            }\n          })\n        }\n      ]\n    },\n    $ERROR_CODES: USERNAME_ERROR_CODES\n  };\n};\n\nexport { USERNAME_ERROR_CODES, username };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,YAAY,CAAC;IACjB,OAAO;QACL,MAAM;YACJ,QAAQ;gBACN,UAAU;oBACR,MAAM;oBACN,UAAU;oBACV,UAAU;oBACV,QAAQ;oBACR,UAAU;gBACZ;gBACA,iBAAiB;oBACf,MAAM;oBACN,UAAU;oBACV,WAAW;wBACT,OAAM,KAAK;4BACT,OAAO,SAAS,OAAO,QAAQ,WAAW;wBAC5C;oBACF;gBACF;YACF;QACF;IACF;AACF;AAEA,MAAM,uBAAuB;IAC3B,8BAA8B;IAC9B,oBAAoB;IACpB,kBAAkB;IAClB,2BAA2B;IAC3B,oBAAoB;IACpB,mBAAmB;IACnB,kBAAkB;AACpB;AAEA,SAAS,yBAAyB,SAAS;IACzC,OAAO,mBAAmB,IAAI,CAAC;AACjC;AACA,MAAM,WAAW,CAAC;IAChB,MAAM,aAAa,CAAC;QAClB,IAAI,SAAS,0BAA0B,OAAO;YAC5C,OAAO;QACT;QACA,IAAI,SAAS,uBAAuB;YAClC,OAAO,QAAQ,qBAAqB,CAAC;QACvC;QACA,OAAO,UAAU,WAAW;IAC9B;IACA,OAAO;QACL,IAAI;QACJ,WAAW;YACT,gBAAgB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAC/B,qBACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAAE,aAAa;oBAA2B;oBACpE,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAAE,aAAa;oBAA2B;oBACpE,YAAY,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa;oBACf,GAAG,QAAQ;oBACX,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa;oBACf,GAAG,QAAQ;gBACb;gBACA,UAAU;oBACR,SAAS;wBACP,SAAS;wBACT,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,OAAO;oDACL,MAAM;oDACN,aAAa;gDACf;gDACA,MAAM;oDACJ,MAAM;gDACR;4CACF;4CACA,UAAU;gDAAC;gDAAS;6CAAO;wCAC7B;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;oBAC5C,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;oBACzB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,qBAAqB,4BAA4B;oBAC5D;gBACF;gBACA,MAAM,oBAAoB,SAAS,qBAAqB;gBACxD,MAAM,oBAAoB,SAAS,qBAAqB;gBACxD,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,mBAAmB;oBAChD,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB;wBAC7C,UAAU,IAAI,IAAI,CAAC,QAAQ;oBAC7B;oBACA,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;wBACzC,SAAS,qBAAqB,kBAAkB;oBAClD;gBACF;gBACA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,mBAAmB;oBAChD,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB;wBAC5C,UAAU,IAAI,IAAI,CAAC,QAAQ;oBAC7B;oBACA,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;wBACzC,SAAS,qBAAqB,iBAAiB;oBACjD;gBACF;gBACA,MAAM,YAAY,SAAS,qBAAqB;gBAChD,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,GAAG;oBACjC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;wBACzC,SAAS,qBAAqB,gBAAgB;oBAChD;gBACF;gBACA,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBAC7C,OAAO;oBACP,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO,WAAW,IAAI,IAAI,CAAC,QAAQ;wBACrC;qBACD;gBACH;gBACA,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ;oBACjD,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB;wBACzC,UAAU,IAAI,IAAI,CAAC,QAAQ;oBAC7B;oBACA,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,qBAAqB,4BAA4B;oBAC5D;gBACF;gBACA,IAAI,CAAC,KAAK,aAAa,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,0BAA0B;oBACzF,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAuB,AAAD,EAAE,KAAK;oBACnC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,aAAa;wBAC9B,SAAS,qBAAqB,kBAAkB;oBAClD;gBACF;gBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBAChD,OAAO;oBACP,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO,KAAK,EAAE;wBAChB;wBACA;4BACE,OAAO;4BACP,OAAO;wBACT;qBACD;gBACH;gBACA,IAAI,CAAC,SAAS;oBACZ,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,qBAAqB,4BAA4B;oBAC5D;gBACF;gBACA,MAAM,kBAAkB,SAAS;gBACjC,IAAI,CAAC,iBAAiB;oBACpB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB;wBAC7C,UAAU,IAAI,IAAI,CAAC,QAAQ;oBAC7B;oBACA,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,qBAAqB,4BAA4B;oBAC5D;gBACF;gBACA,MAAM,gBAAgB,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;oBACtD,MAAM;oBACN,UAAU,IAAI,IAAI,CAAC,QAAQ;gBAC7B;gBACA,IAAI,CAAC,eAAe;oBAClB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;oBACzB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,qBAAqB,4BAA4B;oBAC5D;gBACF;gBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7D,KAAK,EAAE,EACP,KACA,IAAI,IAAI,CAAC,UAAU,KAAK;gBAE1B,IAAI,CAAC,SAAS;oBACZ,OAAO,IAAI,IAAI,CAAC,MAAM;wBACpB,QAAQ;wBACR,MAAM;4BACJ,SAAS,iLAAA,CAAA,IAAgB,CAAC,wBAAwB;wBACpD;oBACF;gBACF;gBACA,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EACnB,KACA;oBAAE;oBAAS;gBAAK,GAChB,IAAI,IAAI,CAAC,UAAU,KAAK;gBAE1B,OAAO,IAAI,IAAI,CAAC;oBACd,OAAO,QAAQ,KAAK;oBACpB,MAAM;wBACJ,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,eAAe,KAAK,aAAa;wBACjC,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;wBACjB,WAAW,KAAK,SAAS;wBACzB,WAAW,KAAK,SAAS;oBAC3B;gBACF;YACF;YAEF,qBAAqB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACpC,0BACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACxB,aAAa;oBACf;gBACF;YACF,GACA,OAAO;gBACL,MAAM,YAAY,IAAI,IAAI,CAAC,QAAQ;gBACnC,IAAI,CAAC,WAAW;oBACd,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;wBACzC,SAAS,qBAAqB,gBAAgB;oBAChD;gBACF;gBACA,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBAC7C,OAAO;oBACP,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO,UAAU,WAAW;wBAC9B;qBACD;gBACH;gBACA,IAAI,MAAM;oBACR,OAAO,IAAI,IAAI,CAAC;wBACd,WAAW;oBACb;gBACF;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,WAAW;gBACb;YACF;QAEJ;QACA,QAAQ,CAAA,GAAA,iLAAA,CAAA,IAAW,AAAD,EAAE,UAAU,aAAa,SAAS;QACpD,OAAO;YACL,QAAQ;gBACN;oBACE,SAAQ,OAAO;wBACb,OAAO,QAAQ,IAAI,KAAK,oBAAoB,QAAQ,IAAI,KAAK;oBAC/D;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,YAAY,IAAI,IAAI,CAAC,QAAQ;wBACnC,IAAI,cAAc,KAAK,KAAK,OAAO,cAAc,UAAU;4BACzD,MAAM,oBAAoB,SAAS,qBAAqB;4BACxD,MAAM,oBAAoB,SAAS,qBAAqB;4BACxD,IAAI,UAAU,MAAM,GAAG,mBAAmB;gCACxC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;oCACzC,SAAS,qBAAqB,kBAAkB;gCAClD;4BACF;4BACA,IAAI,UAAU,MAAM,GAAG,mBAAmB;gCACxC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;oCACzC,SAAS,qBAAqB,iBAAiB;gCACjD;4BACF;4BACA,MAAM,YAAY,SAAS,qBAAqB;4BAChD,MAAM,QAAQ,MAAM,UAAU;4BAC9B,IAAI,CAAC,OAAO;gCACV,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;oCACzC,SAAS,qBAAqB,gBAAgB;gCAChD;4BACF;4BACA,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;gCAC7C,OAAO;gCACP,OAAO;oCACL;wCACE,OAAO;wCACP,OAAO,WAAW;oCACpB;iCACD;4BACH;4BACA,MAAM,oBAAoB,IAAI,IAAI,KAAK,oBAAoB;4BAC3D,MAAM,wBAAwB,IAAI,IAAI,KAAK,kBAAkB,QAAQ,IAAI,OAAO,CAAC,OAAO,IAAI,KAAK,EAAE,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM;4BAC1I,IAAI,qBAAqB,uBAAuB;gCAC9C,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,wBAAwB;oCACzC,SAAS,qBAAqB,yBAAyB;gCACzD;4BACF;wBACF;oBACF;gBACF;gBACA;oBACE,SAAQ,OAAO;wBACb,OAAO,QAAQ,IAAI,KAAK,oBAAoB,QAAQ,IAAI,KAAK;oBAC/D;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;4BACrB,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,CAAC,QAAQ;4BAC9C,IAAI,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,IAAI,CAAC,QAAQ;wBAClD;oBACF;gBACF;aACD;QACH;QACA,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2010, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/bearer/index.mjs"], "sourcesContent": ["import { serializeSignedCookie } from 'better-call';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '@better-auth/utils/base64';\nimport { createHMAC } from '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport { parseSetCookieHeader } from '../../cookies/index.mjs';\nimport { i as createAuthMiddleware } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport 'zod/v4';\nimport '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '@better-auth/utils/hash';\nimport '../../crypto/index.mjs';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-auth/utils/random';\nimport '@better-fetch/fetch';\nimport 'jose/errors';\n\nconst bearer = (options) => {\n  return {\n    id: \"bearer\",\n    hooks: {\n      before: [\n        {\n          matcher(context) {\n            return Boolean(\n              context.request?.headers.get(\"authorization\") || context.headers?.get(\"authorization\")\n            );\n          },\n          handler: createAuthMiddleware(async (c) => {\n            const token = c.request?.headers.get(\"authorization\")?.replace(\"Bearer \", \"\") || c.headers?.get(\"Authorization\")?.replace(\"Bearer \", \"\");\n            if (!token) {\n              return;\n            }\n            let signedToken = \"\";\n            if (token.includes(\".\")) {\n              signedToken = token.replace(\"=\", \"\");\n            } else {\n              if (options?.requireSignature) {\n                return;\n              }\n              signedToken = (await serializeSignedCookie(\"\", token, c.context.secret)).replace(\"=\", \"\");\n            }\n            try {\n              const decodedToken = decodeURIComponent(signedToken);\n              const isValid = await createHMAC(\n                \"SHA-256\",\n                \"base64urlnopad\"\n              ).verify(\n                c.context.secret,\n                decodedToken.split(\".\")[0],\n                decodedToken.split(\".\")[1]\n              );\n              if (!isValid) {\n                return;\n              }\n            } catch (e) {\n              return;\n            }\n            const existingHeaders = c.request?.headers || c.headers;\n            const headers = new Headers({\n              ...Object.fromEntries(existingHeaders?.entries())\n            });\n            headers.append(\n              \"cookie\",\n              `${c.context.authCookies.sessionToken.name}=${signedToken}`\n            );\n            return {\n              context: {\n                headers\n              }\n            };\n          })\n        }\n      ],\n      after: [\n        {\n          matcher(context) {\n            return true;\n          },\n          handler: createAuthMiddleware(async (ctx) => {\n            const setCookie = ctx.context.responseHeaders?.get(\"set-cookie\");\n            if (!setCookie) {\n              return;\n            }\n            const parsedCookies = parseSetCookieHeader(setCookie);\n            const cookieName = ctx.context.authCookies.sessionToken.name;\n            const sessionCookie = parsedCookies.get(cookieName);\n            if (!sessionCookie || !sessionCookie.value || sessionCookie[\"max-age\"] === 0) {\n              return;\n            }\n            const token = sessionCookie.value;\n            const exposedHeaders = ctx.context.responseHeaders?.get(\n              \"access-control-expose-headers\"\n            ) || \"\";\n            const headersSet = new Set(\n              exposedHeaders.split(\",\").map((header) => header.trim()).filter(Boolean)\n            );\n            headersSet.add(\"set-auth-token\");\n            ctx.setHeader(\"set-auth-token\", token);\n            ctx.setHeader(\n              \"Access-Control-Expose-Headers\",\n              Array.from(headersSet).join(\", \")\n            );\n          })\n        }\n      ]\n    }\n  };\n};\n\nexport { bearer };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,SAAS,CAAC;IACd,OAAO;QACL,IAAI;QACJ,OAAO;YACL,QAAQ;gBACN;oBACE,SAAQ,OAAO;wBACb,OAAO,QACL,QAAQ,OAAO,EAAE,QAAQ,IAAI,oBAAoB,QAAQ,OAAO,EAAE,IAAI;oBAE1E;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,kBAAkB,QAAQ,WAAW,OAAO,EAAE,OAAO,EAAE,IAAI,kBAAkB,QAAQ,WAAW;wBACrI,IAAI,CAAC,OAAO;4BACV;wBACF;wBACA,IAAI,cAAc;wBAClB,IAAI,MAAM,QAAQ,CAAC,MAAM;4BACvB,cAAc,MAAM,OAAO,CAAC,KAAK;wBACnC,OAAO;4BACL,IAAI,SAAS,kBAAkB;gCAC7B;4BACF;4BACA,cAAc,CAAC,MAAM,CAAA,GAAA,iJAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,KAAK;wBACxF;wBACA,IAAI;4BACF,MAAM,eAAe,mBAAmB;4BACxC,MAAM,UAAU,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAC7B,WACA,kBACA,MAAM,CACN,EAAE,OAAO,CAAC,MAAM,EAChB,aAAa,KAAK,CAAC,IAAI,CAAC,EAAE,EAC1B,aAAa,KAAK,CAAC,IAAI,CAAC,EAAE;4BAE5B,IAAI,CAAC,SAAS;gCACZ;4BACF;wBACF,EAAE,OAAO,GAAG;4BACV;wBACF;wBACA,MAAM,kBAAkB,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO;wBACvD,MAAM,UAAU,IAAI,QAAQ;4BAC1B,GAAG,OAAO,WAAW,CAAC,iBAAiB,UAAU;wBACnD;wBACA,QAAQ,MAAM,CACZ,UACA,GAAG,EAAE,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa;wBAE7D,OAAO;4BACL,SAAS;gCACP;4BACF;wBACF;oBACF;gBACF;aACD;YACD,OAAO;gBACL;oBACE,SAAQ,OAAO;wBACb,OAAO;oBACT;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,YAAY,IAAI,OAAO,CAAC,eAAe,EAAE,IAAI;wBACnD,IAAI,CAAC,WAAW;4BACd;wBACF;wBACA,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,uBAAoB,AAAD,EAAE;wBAC3C,MAAM,aAAa,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI;wBAC5D,MAAM,gBAAgB,cAAc,GAAG,CAAC;wBACxC,IAAI,CAAC,iBAAiB,CAAC,cAAc,KAAK,IAAI,aAAa,CAAC,UAAU,KAAK,GAAG;4BAC5E;wBACF;wBACA,MAAM,QAAQ,cAAc,KAAK;wBACjC,MAAM,iBAAiB,IAAI,OAAO,CAAC,eAAe,EAAE,IAClD,oCACG;wBACL,MAAM,aAAa,IAAI,IACrB,eAAe,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,SAAW,OAAO,IAAI,IAAI,MAAM,CAAC;wBAElE,WAAW,GAAG,CAAC;wBACf,IAAI,SAAS,CAAC,kBAAkB;wBAChC,IAAI,SAAS,CACX,iCACA,MAAM,IAAI,CAAC,YAAY,IAAI,CAAC;oBAEhC;gBACF;aACD;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2143, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/magic-link/index.mjs"], "sourcesContent": ["import * as z from 'zod/v4';\nimport { j as createAuthEndpoint, o as originCheck, B as BASE_ERROR_CODES } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport { APIError } from 'better-call';\nimport { setSessionCookie } from '../../cookies/index.mjs';\nimport { createHash } from '@better-auth/utils/hash';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport { base64Url } from '@better-auth/utils/base64';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport { g as generateRandomString } from '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '../../crypto/index.mjs';\nimport '@better-fetch/fetch';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport 'jose/errors';\nimport '@better-auth/utils/random';\n\nconst defaultKeyHasher = async (otp) => {\n  const hash = await createHash(\"SHA-256\").digest(\n    new TextEncoder().encode(otp)\n  );\n  const hashed = base64Url.encode(new Uint8Array(hash), {\n    padding: false\n  });\n  return hashed;\n};\n\nconst magicLink = (options) => {\n  const opts = {\n    storeToken: \"plain\",\n    ...options\n  };\n  async function storeToken(ctx, token) {\n    if (opts.storeToken === \"hashed\") {\n      return await defaultKeyHasher(token);\n    }\n    if (typeof opts.storeToken === \"object\" && \"type\" in opts.storeToken && opts.storeToken.type === \"custom-hasher\") {\n      return await opts.storeToken.hash(token);\n    }\n    return token;\n  }\n  return {\n    id: \"magic-link\",\n    endpoints: {\n      /**\n       * ### Endpoint\n       *\n       * POST `/sign-in/magic-link`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.signInMagicLink`\n       *\n       * **client:**\n       * `authClient.signIn.magicLink`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/sign-in#api-method-sign-in-magic-link)\n       */\n      signInMagicLink: createAuthEndpoint(\n        \"/sign-in/magic-link\",\n        {\n          method: \"POST\",\n          requireHeaders: true,\n          body: z.object({\n            email: z.string().meta({\n              description: \"Email address to send the magic link\"\n            }).email(),\n            name: z.string().meta({\n              description: 'User display name. Only used if the user is registering for the first time. Eg: \"my-name\"'\n            }).optional(),\n            callbackURL: z.string().meta({\n              description: \"URL to redirect after magic link verification\"\n            }).optional(),\n            newUserCallbackURL: z.string().meta({\n              description: \"URL to redirect after new user signup. Only used if the user is registering for the first time.\"\n            }).optional(),\n            errorCallbackURL: z.string().meta({\n              description: \"URL to redirect after error.\"\n            }).optional()\n          }),\n          metadata: {\n            openapi: {\n              description: \"Sign in with magic link\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          status: {\n                            type: \"boolean\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const { email } = ctx.body;\n          if (opts.disableSignUp) {\n            const user = await ctx.context.internalAdapter.findUserByEmail(email);\n            if (!user) {\n              throw new APIError(\"BAD_REQUEST\", {\n                message: BASE_ERROR_CODES.USER_NOT_FOUND\n              });\n            }\n          }\n          const verificationToken = opts?.generateToken ? await opts.generateToken(email) : generateRandomString(32, \"a-z\", \"A-Z\");\n          const storedToken = await storeToken(ctx, verificationToken);\n          await ctx.context.internalAdapter.createVerificationValue(\n            {\n              identifier: storedToken,\n              value: JSON.stringify({ email, name: ctx.body.name }),\n              expiresAt: new Date(\n                Date.now() + (opts.expiresIn || 60 * 5) * 1e3\n              )\n            },\n            ctx\n          );\n          const realBaseURL = new URL(ctx.context.baseURL);\n          const url = new URL(\n            `${realBaseURL.pathname}/magic-link/verify`,\n            realBaseURL.origin\n          );\n          url.searchParams.set(\"token\", verificationToken);\n          url.searchParams.set(\"callbackURL\", ctx.body.callbackURL || \"/\");\n          if (ctx.body.newUserCallbackURL) {\n            url.searchParams.set(\n              \"newUserCallbackURL\",\n              ctx.body.newUserCallbackURL\n            );\n          }\n          if (ctx.body.errorCallbackURL) {\n            url.searchParams.set(\"errorCallbackURL\", ctx.body.errorCallbackURL);\n          }\n          await options.sendMagicLink(\n            {\n              email,\n              url: url.toString(),\n              token: verificationToken\n            },\n            ctx.request\n          );\n          return ctx.json({\n            status: true\n          });\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * GET `/magic-link/verify`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.magicLinkVerify`\n       *\n       * **client:**\n       * `authClient.magicLink.verify`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/magic-link#api-method-magic-link-verify)\n       */\n      magicLinkVerify: createAuthEndpoint(\n        \"/magic-link/verify\",\n        {\n          method: \"GET\",\n          query: z.object({\n            token: z.string().meta({\n              description: \"Verification token\"\n            }),\n            callbackURL: z.string().meta({\n              description: 'URL to redirect after magic link verification, if not provided the user will be redirected to the root URL. Eg: \"/dashboard\"'\n            }).optional(),\n            errorCallbackURL: z.string().meta({\n              description: \"URL to redirect after error.\"\n            }).optional(),\n            newUserCallbackURL: z.string().meta({\n              description: \"URL to redirect after new user signup. Only used if the user is registering for the first time.\"\n            }).optional()\n          }),\n          use: [\n            originCheck((ctx) => {\n              return ctx.query.callbackURL ? decodeURIComponent(ctx.query.callbackURL) : \"/\";\n            }),\n            originCheck((ctx) => {\n              return ctx.query.newUserCallbackURL ? decodeURIComponent(ctx.query.newUserCallbackURL) : \"/\";\n            }),\n            originCheck((ctx) => {\n              return ctx.query.errorCallbackURL ? decodeURIComponent(ctx.query.errorCallbackURL) : \"/\";\n            })\n          ],\n          requireHeaders: true,\n          metadata: {\n            openapi: {\n              description: \"Verify magic link\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          session: {\n                            $ref: \"#/components/schemas/Session\"\n                          },\n                          user: {\n                            $ref: \"#/components/schemas/User\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const token = ctx.query.token;\n          const callbackURL = new URL(\n            ctx.query.callbackURL ? decodeURIComponent(ctx.query.callbackURL) : \"/\",\n            ctx.context.baseURL\n          ).toString();\n          const errorCallbackURL = new URL(\n            ctx.query.errorCallbackURL ? decodeURIComponent(ctx.query.errorCallbackURL) : callbackURL,\n            ctx.context.baseURL\n          ).toString();\n          const newUserCallbackURL = new URL(\n            ctx.query.newUserCallbackURL ? decodeURIComponent(ctx.query.newUserCallbackURL) : callbackURL,\n            ctx.context.baseURL\n          ).toString();\n          callbackURL?.startsWith(\"http\") ? callbackURL : callbackURL ? `${ctx.context.options.baseURL}${callbackURL}` : ctx.context.options.baseURL;\n          const storedToken = await storeToken(ctx, token);\n          const tokenValue = await ctx.context.internalAdapter.findVerificationValue(\n            storedToken\n          );\n          if (!tokenValue) {\n            throw ctx.redirect(`${errorCallbackURL}?error=INVALID_TOKEN`);\n          }\n          if (tokenValue.expiresAt < /* @__PURE__ */ new Date()) {\n            await ctx.context.internalAdapter.deleteVerificationValue(\n              tokenValue.id\n            );\n            throw ctx.redirect(`${errorCallbackURL}?error=EXPIRED_TOKEN`);\n          }\n          await ctx.context.internalAdapter.deleteVerificationValue(\n            tokenValue.id\n          );\n          const { email, name } = JSON.parse(tokenValue.value);\n          let isNewUser = false;\n          let user = await ctx.context.internalAdapter.findUserByEmail(email).then((res) => res?.user);\n          if (!user) {\n            if (!opts.disableSignUp) {\n              const newUser = await ctx.context.internalAdapter.createUser(\n                {\n                  email,\n                  emailVerified: true,\n                  name: name || \"\"\n                },\n                ctx\n              );\n              isNewUser = true;\n              user = newUser;\n              if (!user) {\n                throw ctx.redirect(\n                  `${errorCallbackURL}?error=failed_to_create_user`\n                );\n              }\n            } else {\n              throw ctx.redirect(\n                `${errorCallbackURL}?error=new_user_signup_disabled`\n              );\n            }\n          }\n          if (!user.emailVerified) {\n            await ctx.context.internalAdapter.updateUser(\n              user.id,\n              {\n                emailVerified: true\n              },\n              ctx\n            );\n          }\n          const session = await ctx.context.internalAdapter.createSession(\n            user.id,\n            ctx\n          );\n          if (!session) {\n            throw ctx.redirect(\n              `${errorCallbackURL}?error=failed_to_create_session`\n            );\n          }\n          await setSessionCookie(ctx, {\n            session,\n            user\n          });\n          if (!ctx.query.callbackURL) {\n            return ctx.json({\n              token: session.token,\n              user: {\n                id: user.id,\n                email: user.email,\n                emailVerified: user.emailVerified,\n                name: user.name,\n                image: user.image,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n              }\n            });\n          }\n          if (isNewUser) {\n            throw ctx.redirect(newUserCallbackURL);\n          }\n          throw ctx.redirect(callbackURL);\n        }\n      )\n    },\n    rateLimit: [\n      {\n        pathMatcher(path) {\n          return path.startsWith(\"/sign-in/magic-link\") || path.startsWith(\"/magic-link/verify\");\n        },\n        window: opts.rateLimit?.window || 60,\n        max: opts.rateLimit?.max || 5\n      }\n    ]\n  };\n};\n\nexport { magicLink };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,mBAAmB,OAAO;IAC9B,MAAM,OAAO,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAC7C,IAAI,cAAc,MAAM,CAAC;IAE3B,MAAM,SAAS,8JAAA,CAAA,YAAS,CAAC,MAAM,CAAC,IAAI,WAAW,OAAO;QACpD,SAAS;IACX;IACA,OAAO;AACT;AAEA,MAAM,YAAY,CAAC;IACjB,MAAM,OAAO;QACX,YAAY;QACZ,GAAG,OAAO;IACZ;IACA,eAAe,WAAW,GAAG,EAAE,KAAK;QAClC,IAAI,KAAK,UAAU,KAAK,UAAU;YAChC,OAAO,MAAM,iBAAiB;QAChC;QACA,IAAI,OAAO,KAAK,UAAU,KAAK,YAAY,UAAU,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,IAAI,KAAK,iBAAiB;YAChH,OAAO,MAAM,KAAK,UAAU,CAAC,IAAI,CAAC;QACpC;QACA,OAAO;IACT;IACA,OAAO;QACL,IAAI;QACJ,WAAW;YACT;;;;;;;;;;;;;;OAcC,GACD,iBAAiB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAChC,uBACA;gBACE,QAAQ;gBACR,gBAAgB;gBAChB,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACrB,aAAa;oBACf,GAAG,KAAK;oBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACpB,aAAa;oBACf,GAAG,QAAQ;oBACX,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa;oBACf,GAAG,QAAQ;oBACX,oBAAoB,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAClC,aAAa;oBACf,GAAG,QAAQ;oBACX,kBAAkB,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAChC,aAAa;oBACf,GAAG,QAAQ;gBACb;gBACA,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,QAAQ;oDACN,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,IAAI;gBAC1B,IAAI,KAAK,aAAa,EAAE;oBACtB,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC;oBAC/D,IAAI,CAAC,MAAM;wBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,SAAS,iLAAA,CAAA,IAAgB,CAAC,cAAc;wBAC1C;oBACF;gBACF;gBACA,MAAM,oBAAoB,MAAM,gBAAgB,MAAM,KAAK,aAAa,CAAC,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,IAAI,OAAO;gBAClH,MAAM,cAAc,MAAM,WAAW,KAAK;gBAC1C,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;oBACE,YAAY;oBACZ,OAAO,KAAK,SAAS,CAAC;wBAAE;wBAAO,MAAM,IAAI,IAAI,CAAC,IAAI;oBAAC;oBACnD,WAAW,IAAI,KACb,KAAK,GAAG,KAAK,CAAC,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI;gBAE9C,GACA;gBAEF,MAAM,cAAc,IAAI,IAAI,IAAI,OAAO,CAAC,OAAO;gBAC/C,MAAM,MAAM,IAAI,IACd,GAAG,YAAY,QAAQ,CAAC,kBAAkB,CAAC,EAC3C,YAAY,MAAM;gBAEpB,IAAI,YAAY,CAAC,GAAG,CAAC,SAAS;gBAC9B,IAAI,YAAY,CAAC,GAAG,CAAC,eAAe,IAAI,IAAI,CAAC,WAAW,IAAI;gBAC5D,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBAC/B,IAAI,YAAY,CAAC,GAAG,CAClB,sBACA,IAAI,IAAI,CAAC,kBAAkB;gBAE/B;gBACA,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBAC7B,IAAI,YAAY,CAAC,GAAG,CAAC,oBAAoB,IAAI,IAAI,CAAC,gBAAgB;gBACpE;gBACA,MAAM,QAAQ,aAAa,CACzB;oBACE;oBACA,KAAK,IAAI,QAAQ;oBACjB,OAAO;gBACT,GACA,IAAI,OAAO;gBAEb,OAAO,IAAI,IAAI,CAAC;oBACd,QAAQ;gBACV;YACF;YAEF;;;;;;;;;;;;;;OAcC,GACD,iBAAiB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAChC,sBACA;gBACE,QAAQ;gBACR,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACd,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACrB,aAAa;oBACf;oBACA,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa;oBACf,GAAG,QAAQ;oBACX,kBAAkB,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAChC,aAAa;oBACf,GAAG,QAAQ;oBACX,oBAAoB,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAClC,aAAa;oBACf,GAAG,QAAQ;gBACb;gBACA,KAAK;oBACH,CAAA,GAAA,iLAAA,CAAA,IAAW,AAAD,EAAE,CAAC;wBACX,OAAO,IAAI,KAAK,CAAC,WAAW,GAAG,mBAAmB,IAAI,KAAK,CAAC,WAAW,IAAI;oBAC7E;oBACA,CAAA,GAAA,iLAAA,CAAA,IAAW,AAAD,EAAE,CAAC;wBACX,OAAO,IAAI,KAAK,CAAC,kBAAkB,GAAG,mBAAmB,IAAI,KAAK,CAAC,kBAAkB,IAAI;oBAC3F;oBACA,CAAA,GAAA,iLAAA,CAAA,IAAW,AAAD,EAAE,CAAC;wBACX,OAAO,IAAI,KAAK,CAAC,gBAAgB,GAAG,mBAAmB,IAAI,KAAK,CAAC,gBAAgB,IAAI;oBACvF;iBACD;gBACD,gBAAgB;gBAChB,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,SAAS;oDACP,MAAM;gDACR;gDACA,MAAM;oDACJ,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,QAAQ,IAAI,KAAK,CAAC,KAAK;gBAC7B,MAAM,cAAc,IAAI,IACtB,IAAI,KAAK,CAAC,WAAW,GAAG,mBAAmB,IAAI,KAAK,CAAC,WAAW,IAAI,KACpE,IAAI,OAAO,CAAC,OAAO,EACnB,QAAQ;gBACV,MAAM,mBAAmB,IAAI,IAC3B,IAAI,KAAK,CAAC,gBAAgB,GAAG,mBAAmB,IAAI,KAAK,CAAC,gBAAgB,IAAI,aAC9E,IAAI,OAAO,CAAC,OAAO,EACnB,QAAQ;gBACV,MAAM,qBAAqB,IAAI,IAC7B,IAAI,KAAK,CAAC,kBAAkB,GAAG,mBAAmB,IAAI,KAAK,CAAC,kBAAkB,IAAI,aAClF,IAAI,OAAO,CAAC,OAAO,EACnB,QAAQ;gBACV,aAAa,WAAW,UAAU,cAAc,cAAc,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,GAAG,aAAa,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO;gBAC1I,MAAM,cAAc,MAAM,WAAW,KAAK;gBAC1C,MAAM,aAAa,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,qBAAqB,CACxE;gBAEF,IAAI,CAAC,YAAY;oBACf,MAAM,IAAI,QAAQ,CAAC,GAAG,iBAAiB,oBAAoB,CAAC;gBAC9D;gBACA,IAAI,WAAW,SAAS,GAAG,aAAa,GAAG,IAAI,QAAQ;oBACrD,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,WAAW,EAAE;oBAEf,MAAM,IAAI,QAAQ,CAAC,GAAG,iBAAiB,oBAAoB,CAAC;gBAC9D;gBACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,WAAW,EAAE;gBAEf,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,KAAK,KAAK,CAAC,WAAW,KAAK;gBACnD,IAAI,YAAY;gBAChB,IAAI,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC,OAAO,IAAI,CAAC,CAAC,MAAQ,KAAK;gBACvF,IAAI,CAAC,MAAM;oBACT,IAAI,CAAC,KAAK,aAAa,EAAE;wBACvB,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAC1D;4BACE;4BACA,eAAe;4BACf,MAAM,QAAQ;wBAChB,GACA;wBAEF,YAAY;wBACZ,OAAO;wBACP,IAAI,CAAC,MAAM;4BACT,MAAM,IAAI,QAAQ,CAChB,GAAG,iBAAiB,4BAA4B,CAAC;wBAErD;oBACF,OAAO;wBACL,MAAM,IAAI,QAAQ,CAChB,GAAG,iBAAiB,+BAA+B,CAAC;oBAExD;gBACF;gBACA,IAAI,CAAC,KAAK,aAAa,EAAE;oBACvB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAC1C,KAAK,EAAE,EACP;wBACE,eAAe;oBACjB,GACA;gBAEJ;gBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7D,KAAK,EAAE,EACP;gBAEF,IAAI,CAAC,SAAS;oBACZ,MAAM,IAAI,QAAQ,CAChB,GAAG,iBAAiB,+BAA+B,CAAC;gBAExD;gBACA,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;oBAC1B;oBACA;gBACF;gBACA,IAAI,CAAC,IAAI,KAAK,CAAC,WAAW,EAAE;oBAC1B,OAAO,IAAI,IAAI,CAAC;wBACd,OAAO,QAAQ,KAAK;wBACpB,MAAM;4BACJ,IAAI,KAAK,EAAE;4BACX,OAAO,KAAK,KAAK;4BACjB,eAAe,KAAK,aAAa;4BACjC,MAAM,KAAK,IAAI;4BACf,OAAO,KAAK,KAAK;4BACjB,WAAW,KAAK,SAAS;4BACzB,WAAW,KAAK,SAAS;wBAC3B;oBACF;gBACF;gBACA,IAAI,WAAW;oBACb,MAAM,IAAI,QAAQ,CAAC;gBACrB;gBACA,MAAM,IAAI,QAAQ,CAAC;YACrB;QAEJ;QACA,WAAW;YACT;gBACE,aAAY,IAAI;oBACd,OAAO,KAAK,UAAU,CAAC,0BAA0B,KAAK,UAAU,CAAC;gBACnE;gBACA,QAAQ,KAAK,SAAS,EAAE,UAAU;gBAClC,KAAK,KAAK,SAAS,EAAE,OAAO;YAC9B;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2469, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/phone-number/index.mjs"], "sourcesContent": ["import * as z from 'zod/v4';\nimport { j as createAuthEndpoint, k as getSessionFromCtx, B as BASE_ERROR_CODES } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport { APIError } from 'better-call';\nimport { m as mergeSchema } from '../../shared/better-auth.n2KFGwjY.mjs';\nimport { g as generateRandomString } from '../../shared/better-auth.B4Qoxdgc.mjs';\nimport { setSessionCookie } from '../../cookies/index.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport { g as getDate } from '../../shared/better-auth.CW6D9eSx.mjs';\nimport '@better-auth/utils/hash';\nimport '@better-auth/utils/base64';\nimport '../../crypto/index.mjs';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '@better-fetch/fetch';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport 'jose/errors';\nimport '@better-auth/utils/random';\n\nconst ERROR_CODES = {\n  INVALID_PHONE_NUMBER: \"Invalid phone number\",\n  PHONE_NUMBER_EXIST: \"Phone number already exists\",\n  INVALID_PHONE_NUMBER_OR_PASSWORD: \"Invalid phone number or password\",\n  UNEXPECTED_ERROR: \"Unexpected error\",\n  OTP_NOT_FOUND: \"OTP not found\",\n  OTP_EXPIRED: \"OTP expired\",\n  INVALID_OTP: \"Invalid OTP\",\n  PHONE_NUMBER_NOT_VERIFIED: \"Phone number not verified\"\n};\n\nfunction generateOTP(size) {\n  return generateRandomString(size, \"0-9\");\n}\nconst phoneNumber = (options) => {\n  const opts = {\n    expiresIn: options?.expiresIn || 300,\n    otpLength: options?.otpLength || 6,\n    ...options,\n    phoneNumber: \"phoneNumber\",\n    phoneNumberVerified: \"phoneNumberVerified\",\n    code: \"code\",\n    createdAt: \"createdAt\"\n  };\n  return {\n    id: \"phone-number\",\n    endpoints: {\n      /**\n       * ### Endpoint\n       *\n       * POST `/sign-in/phone-number`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.signInPhoneNumber`\n       *\n       * **client:**\n       * `authClient.signIn.phoneNumber`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/phone-number#api-method-sign-in-phone-number)\n       */\n      signInPhoneNumber: createAuthEndpoint(\n        \"/sign-in/phone-number\",\n        {\n          method: \"POST\",\n          body: z.object({\n            phoneNumber: z.string().meta({\n              description: 'Phone number to sign in. Eg: \"+**********\"'\n            }),\n            password: z.string().meta({\n              description: \"Password to use for sign in.\"\n            }),\n            rememberMe: z.boolean().meta({\n              description: \"Remember the session. Eg: true\"\n            }).optional()\n          }),\n          metadata: {\n            openapi: {\n              summary: \"Sign in with phone number\",\n              description: \"Use this endpoint to sign in with phone number\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          user: {\n                            $ref: \"#/components/schemas/User\"\n                          },\n                          session: {\n                            $ref: \"#/components/schemas/Session\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                },\n                400: {\n                  description: \"Invalid phone number or password\"\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const { password, phoneNumber: phoneNumber2 } = ctx.body;\n          if (opts.phoneNumberValidator) {\n            const isValidNumber = await opts.phoneNumberValidator(\n              ctx.body.phoneNumber\n            );\n            if (!isValidNumber) {\n              throw new APIError(\"BAD_REQUEST\", {\n                message: ERROR_CODES.INVALID_PHONE_NUMBER\n              });\n            }\n          }\n          const user = await ctx.context.adapter.findOne({\n            model: \"user\",\n            where: [\n              {\n                field: \"phoneNumber\",\n                value: phoneNumber2\n              }\n            ]\n          });\n          if (!user) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: ERROR_CODES.INVALID_PHONE_NUMBER_OR_PASSWORD\n            });\n          }\n          if (opts.requireVerification) {\n            if (!user.phoneNumberVerified) {\n              const otp = generateOTP(opts.otpLength);\n              await ctx.context.internalAdapter.createVerificationValue(\n                {\n                  value: otp,\n                  identifier: phoneNumber2,\n                  expiresAt: getDate(opts.expiresIn, \"sec\")\n                },\n                ctx\n              );\n              await opts.sendOTP?.(\n                {\n                  phoneNumber: phoneNumber2,\n                  code: otp\n                },\n                ctx.request\n              );\n              throw new APIError(\"UNAUTHORIZED\", {\n                message: ERROR_CODES.PHONE_NUMBER_NOT_VERIFIED\n              });\n            }\n          }\n          const accounts = await ctx.context.internalAdapter.findAccountByUserId(user.id);\n          const credentialAccount = accounts.find(\n            (a) => a.providerId === \"credential\"\n          );\n          if (!credentialAccount) {\n            ctx.context.logger.error(\"Credential account not found\", {\n              phoneNumber: phoneNumber2\n            });\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: ERROR_CODES.INVALID_PHONE_NUMBER_OR_PASSWORD\n            });\n          }\n          const currentPassword = credentialAccount?.password;\n          if (!currentPassword) {\n            ctx.context.logger.error(\"Password not found\", { phoneNumber: phoneNumber2 });\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: ERROR_CODES.UNEXPECTED_ERROR\n            });\n          }\n          const validPassword = await ctx.context.password.verify({\n            hash: currentPassword,\n            password\n          });\n          if (!validPassword) {\n            ctx.context.logger.error(\"Invalid password\");\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: ERROR_CODES.INVALID_PHONE_NUMBER_OR_PASSWORD\n            });\n          }\n          const session = await ctx.context.internalAdapter.createSession(\n            user.id,\n            ctx,\n            ctx.body.rememberMe === false\n          );\n          if (!session) {\n            ctx.context.logger.error(\"Failed to create session\");\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: BASE_ERROR_CODES.FAILED_TO_CREATE_SESSION\n            });\n          }\n          await setSessionCookie(\n            ctx,\n            {\n              session,\n              user\n            },\n            ctx.body.rememberMe === false\n          );\n          return ctx.json({\n            token: session.token,\n            user: {\n              id: user.id,\n              email: user.email,\n              emailVerified: user.emailVerified,\n              name: user.name,\n              image: user.image,\n              phoneNumber: user.phoneNumber,\n              phoneNumberVerified: user.phoneNumberVerified,\n              createdAt: user.createdAt,\n              updatedAt: user.updatedAt\n            }\n          });\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * POST `/phone-number/send-otp`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.sendPhoneNumberOTP`\n       *\n       * **client:**\n       * `authClient.phoneNumber.sendOtp`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/phone-number#api-method-phone-number-send-otp)\n       */\n      sendPhoneNumberOTP: createAuthEndpoint(\n        \"/phone-number/send-otp\",\n        {\n          method: \"POST\",\n          body: z.object({\n            phoneNumber: z.string().meta({\n              description: 'Phone number to send OTP. Eg: \"+**********\"'\n            })\n          }),\n          metadata: {\n            openapi: {\n              summary: \"Send OTP to phone number\",\n              description: \"Use this endpoint to send OTP to phone number\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          message: {\n                            type: \"string\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          if (!options?.sendOTP) {\n            ctx.context.logger.warn(\"sendOTP not implemented\");\n            throw new APIError(\"NOT_IMPLEMENTED\", {\n              message: \"sendOTP not implemented\"\n            });\n          }\n          if (opts.phoneNumberValidator) {\n            const isValidNumber = await opts.phoneNumberValidator(\n              ctx.body.phoneNumber\n            );\n            if (!isValidNumber) {\n              throw new APIError(\"BAD_REQUEST\", {\n                message: ERROR_CODES.INVALID_PHONE_NUMBER\n              });\n            }\n          }\n          const code = generateOTP(opts.otpLength);\n          await ctx.context.internalAdapter.createVerificationValue(\n            {\n              value: `${code}:0`,\n              identifier: ctx.body.phoneNumber,\n              expiresAt: getDate(opts.expiresIn, \"sec\")\n            },\n            ctx\n          );\n          await options.sendOTP(\n            {\n              phoneNumber: ctx.body.phoneNumber,\n              code\n            },\n            ctx.request\n          );\n          return ctx.json({ message: \"code sent\" });\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * POST `/phone-number/verify`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.verifyPhoneNumber`\n       *\n       * **client:**\n       * `authClient.phoneNumber.verify`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/phone-number#api-method-phone-number-verify)\n       */\n      verifyPhoneNumber: createAuthEndpoint(\n        \"/phone-number/verify\",\n        {\n          method: \"POST\",\n          body: z.object({\n            /**\n             * Phone number\n             */\n            phoneNumber: z.string().meta({\n              description: 'Phone number to verify. Eg: \"+**********\"'\n            }),\n            /**\n             * OTP code\n             */\n            code: z.string().meta({\n              description: 'OTP code. Eg: \"123456\"'\n            }),\n            /**\n             * Disable session creation after verification\n             * @default false\n             */\n            disableSession: z.boolean().meta({\n              description: \"Disable session creation after verification. Eg: false\"\n            }).optional(),\n            /**\n             * This checks if there is a session already\n             * and updates the phone number with the provided\n             * phone number\n             */\n            updatePhoneNumber: z.boolean().meta({\n              description: \"Check if there is a session and update the phone number. Eg: true\"\n            }).optional()\n          }),\n          metadata: {\n            openapi: {\n              summary: \"Verify phone number\",\n              description: \"Use this endpoint to verify phone number\",\n              responses: {\n                \"200\": {\n                  description: \"Phone number verified successfully\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          status: {\n                            type: \"boolean\",\n                            description: \"Indicates if the verification was successful\",\n                            enum: [true]\n                          },\n                          token: {\n                            type: \"string\",\n                            nullable: true,\n                            description: \"Session token if session is created, null if disableSession is true or no session is created\"\n                          },\n                          user: {\n                            type: \"object\",\n                            nullable: true,\n                            properties: {\n                              id: {\n                                type: \"string\",\n                                description: \"Unique identifier of the user\"\n                              },\n                              email: {\n                                type: \"string\",\n                                format: \"email\",\n                                nullable: true,\n                                description: \"User's email address\"\n                              },\n                              emailVerified: {\n                                type: \"boolean\",\n                                nullable: true,\n                                description: \"Whether the email is verified\"\n                              },\n                              name: {\n                                type: \"string\",\n                                nullable: true,\n                                description: \"User's name\"\n                              },\n                              image: {\n                                type: \"string\",\n                                format: \"uri\",\n                                nullable: true,\n                                description: \"User's profile image URL\"\n                              },\n                              phoneNumber: {\n                                type: \"string\",\n                                description: \"User's phone number\"\n                              },\n                              phoneNumberVerified: {\n                                type: \"boolean\",\n                                description: \"Whether the phone number is verified\"\n                              },\n                              createdAt: {\n                                type: \"string\",\n                                format: \"date-time\",\n                                description: \"Timestamp when the user was created\"\n                              },\n                              updatedAt: {\n                                type: \"string\",\n                                format: \"date-time\",\n                                description: \"Timestamp when the user was last updated\"\n                              }\n                            },\n                            required: [\n                              \"id\",\n                              \"phoneNumber\",\n                              \"phoneNumberVerified\",\n                              \"createdAt\",\n                              \"updatedAt\"\n                            ],\n                            description: \"User object with phone number details, null if no user is created or found\"\n                          }\n                        },\n                        required: [\"status\"]\n                      }\n                    }\n                  }\n                },\n                400: {\n                  description: \"Invalid OTP\"\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const otp = await ctx.context.internalAdapter.findVerificationValue(\n            ctx.body.phoneNumber\n          );\n          if (!otp || otp.expiresAt < /* @__PURE__ */ new Date()) {\n            if (otp && otp.expiresAt < /* @__PURE__ */ new Date()) {\n              throw new APIError(\"BAD_REQUEST\", {\n                message: \"OTP expired\"\n              });\n            }\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.OTP_NOT_FOUND\n            });\n          }\n          const [otpValue, attempts] = otp.value.split(\":\");\n          const allowedAttempts = options?.allowedAttempts || 3;\n          if (attempts && parseInt(attempts) >= allowedAttempts) {\n            await ctx.context.internalAdapter.deleteVerificationValue(otp.id);\n            throw new APIError(\"FORBIDDEN\", {\n              message: \"Too many attempts\"\n            });\n          }\n          if (otpValue !== ctx.body.code) {\n            await ctx.context.internalAdapter.updateVerificationValue(otp.id, {\n              value: `${otpValue}:${parseInt(attempts || \"0\") + 1}`\n            });\n            throw new APIError(\"BAD_REQUEST\", {\n              message: \"Invalid OTP\"\n            });\n          }\n          await ctx.context.internalAdapter.deleteVerificationValue(otp.id);\n          if (ctx.body.updatePhoneNumber) {\n            const session = await getSessionFromCtx(ctx);\n            if (!session) {\n              throw new APIError(\"UNAUTHORIZED\", {\n                message: BASE_ERROR_CODES.USER_NOT_FOUND\n              });\n            }\n            const existingUser = await ctx.context.adapter.findMany({\n              model: \"user\",\n              where: [\n                {\n                  field: \"phoneNumber\",\n                  value: ctx.body.phoneNumber\n                }\n              ]\n            });\n            if (existingUser.length) {\n              throw ctx.error(\"BAD_REQUEST\", {\n                message: ERROR_CODES.PHONE_NUMBER_EXIST\n              });\n            }\n            let user2 = await ctx.context.internalAdapter.updateUser(\n              session.user.id,\n              {\n                [opts.phoneNumber]: ctx.body.phoneNumber,\n                [opts.phoneNumberVerified]: true\n              },\n              ctx\n            );\n            return ctx.json({\n              status: true,\n              token: session.session.token,\n              user: {\n                id: user2.id,\n                email: user2.email,\n                emailVerified: user2.emailVerified,\n                name: user2.name,\n                image: user2.image,\n                phoneNumber: user2.phoneNumber,\n                phoneNumberVerified: user2.phoneNumberVerified,\n                createdAt: user2.createdAt,\n                updatedAt: user2.updatedAt\n              }\n            });\n          }\n          let user = await ctx.context.adapter.findOne({\n            model: \"user\",\n            where: [\n              {\n                value: ctx.body.phoneNumber,\n                field: opts.phoneNumber\n              }\n            ]\n          });\n          if (!user) {\n            if (options?.signUpOnVerification) {\n              user = await ctx.context.internalAdapter.createUser(\n                {\n                  email: options.signUpOnVerification.getTempEmail(\n                    ctx.body.phoneNumber\n                  ),\n                  name: options.signUpOnVerification.getTempName ? options.signUpOnVerification.getTempName(\n                    ctx.body.phoneNumber\n                  ) : ctx.body.phoneNumber,\n                  [opts.phoneNumber]: ctx.body.phoneNumber,\n                  [opts.phoneNumberVerified]: true\n                },\n                ctx\n              );\n              if (!user) {\n                throw new APIError(\"INTERNAL_SERVER_ERROR\", {\n                  message: BASE_ERROR_CODES.FAILED_TO_CREATE_USER\n                });\n              }\n            }\n          } else {\n            user = await ctx.context.internalAdapter.updateUser(\n              user.id,\n              {\n                [opts.phoneNumberVerified]: true\n              },\n              ctx\n            );\n          }\n          if (!user) {\n            return ctx.json(null);\n          }\n          await options?.callbackOnVerification?.(\n            {\n              phoneNumber: ctx.body.phoneNumber,\n              user\n            },\n            ctx.request\n          );\n          if (!user) {\n            throw new APIError(\"INTERNAL_SERVER_ERROR\", {\n              message: BASE_ERROR_CODES.FAILED_TO_UPDATE_USER\n            });\n          }\n          if (!ctx.body.disableSession) {\n            const session = await ctx.context.internalAdapter.createSession(\n              user.id,\n              ctx\n            );\n            if (!session) {\n              throw new APIError(\"INTERNAL_SERVER_ERROR\", {\n                message: BASE_ERROR_CODES.FAILED_TO_CREATE_SESSION\n              });\n            }\n            await setSessionCookie(ctx, {\n              session,\n              user\n            });\n            return ctx.json({\n              status: true,\n              token: session.token,\n              user: {\n                id: user.id,\n                email: user.email,\n                emailVerified: user.emailVerified,\n                name: user.name,\n                image: user.image,\n                phoneNumber: user.phoneNumber,\n                phoneNumberVerified: user.phoneNumberVerified,\n                createdAt: user.createdAt,\n                updatedAt: user.updatedAt\n              }\n            });\n          }\n          return ctx.json({\n            status: true,\n            token: null,\n            user: {\n              id: user.id,\n              email: user.email,\n              emailVerified: user.emailVerified,\n              name: user.name,\n              image: user.image,\n              phoneNumber: user.phoneNumber,\n              phoneNumberVerified: user.phoneNumberVerified,\n              createdAt: user.createdAt,\n              updatedAt: user.updatedAt\n            }\n          });\n        }\n      ),\n      /**\n       * @deprecated Use requestPasswordResetPhoneNumber instead. This endpoint will be removed in the next major version.\n       */\n      forgetPasswordPhoneNumber: createAuthEndpoint(\n        \"/phone-number/forget-password\",\n        {\n          method: \"POST\",\n          body: z.object({\n            phoneNumber: z.string().meta({\n              description: `The phone number which is associated with the user. Eg: \"+**********\"`\n            })\n          }),\n          metadata: {\n            openapi: {\n              description: \"Request OTP for password reset via phone number\",\n              responses: {\n                \"200\": {\n                  description: \"OTP sent successfully for password reset\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          status: {\n                            type: \"boolean\",\n                            description: \"Indicates if the OTP was sent successfully\",\n                            enum: [true]\n                          }\n                        },\n                        required: [\"status\"]\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const user = await ctx.context.adapter.findOne({\n            model: \"user\",\n            where: [\n              {\n                value: ctx.body.phoneNumber,\n                field: opts.phoneNumber\n              }\n            ]\n          });\n          if (!user) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: \"phone number isn't registered\"\n            });\n          }\n          const code = generateOTP(opts.otpLength);\n          await ctx.context.internalAdapter.createVerificationValue(\n            {\n              value: `${code}:0`,\n              identifier: `${ctx.body.phoneNumber}-request-password-reset`,\n              expiresAt: getDate(opts.expiresIn, \"sec\")\n            },\n            ctx\n          );\n          await options?.sendForgetPasswordOTP?.(\n            {\n              phoneNumber: ctx.body.phoneNumber,\n              code\n            },\n            ctx.request\n          );\n          return ctx.json({\n            status: true\n          });\n        }\n      ),\n      requestPasswordResetPhoneNumber: createAuthEndpoint(\n        \"/phone-number/request-password-reset\",\n        {\n          method: \"POST\",\n          body: z.object({\n            phoneNumber: z.string()\n          }),\n          metadata: {\n            openapi: {\n              description: \"Request OTP for password reset via phone number\",\n              responses: {\n                \"200\": {\n                  description: \"OTP sent successfully for password reset\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          status: {\n                            type: \"boolean\",\n                            description: \"Indicates if the OTP was sent successfully\",\n                            enum: [true]\n                          }\n                        },\n                        required: [\"status\"]\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const user = await ctx.context.adapter.findOne({\n            model: \"user\",\n            where: [\n              {\n                value: ctx.body.phoneNumber,\n                field: opts.phoneNumber\n              }\n            ]\n          });\n          if (!user) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: \"phone number isn't registered\"\n            });\n          }\n          const code = generateOTP(opts.otpLength);\n          await ctx.context.internalAdapter.createVerificationValue(\n            {\n              value: `${code}:0`,\n              identifier: `${ctx.body.phoneNumber}-request-password-reset`,\n              expiresAt: getDate(opts.expiresIn, \"sec\")\n            },\n            ctx\n          );\n          await options?.sendPasswordResetOTP?.(\n            {\n              phoneNumber: ctx.body.phoneNumber,\n              code\n            },\n            ctx.request\n          );\n          return ctx.json({\n            status: true\n          });\n        }\n      ),\n      resetPasswordPhoneNumber: createAuthEndpoint(\n        \"/phone-number/reset-password\",\n        {\n          method: \"POST\",\n          body: z.object({\n            otp: z.string().meta({\n              description: 'The one time password to reset the password. Eg: \"123456\"'\n            }),\n            phoneNumber: z.string().meta({\n              description: 'The phone number to the account which intends to reset the password for. Eg: \"+**********\"'\n            }),\n            newPassword: z.string().meta({\n              description: `The new password. Eg: \"new-and-secure-password\"`\n            })\n          }),\n          metadata: {\n            openapi: {\n              description: \"Reset password using phone number OTP\",\n              responses: {\n                \"200\": {\n                  description: \"Password reset successfully\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          status: {\n                            type: \"boolean\",\n                            description: \"Indicates if the password was reset successfully\",\n                            enum: [true]\n                          }\n                        },\n                        required: [\"status\"]\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const verification = await ctx.context.internalAdapter.findVerificationValue(\n            `${ctx.body.phoneNumber}-request-password-reset`\n          );\n          if (!verification) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.OTP_NOT_FOUND\n            });\n          }\n          if (verification.expiresAt < /* @__PURE__ */ new Date()) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.OTP_EXPIRED\n            });\n          }\n          const [otpValue, attempts] = verification.value.split(\":\");\n          const allowedAttempts = options?.allowedAttempts || 3;\n          if (attempts && parseInt(attempts) >= allowedAttempts) {\n            await ctx.context.internalAdapter.deleteVerificationValue(\n              verification.id\n            );\n            throw new APIError(\"FORBIDDEN\", {\n              message: \"Too many attempts\"\n            });\n          }\n          if (ctx.body.otp !== otpValue) {\n            await ctx.context.internalAdapter.updateVerificationValue(\n              verification.id,\n              {\n                value: `${otpValue}:${parseInt(attempts || \"0\") + 1}`\n              }\n            );\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.INVALID_OTP\n            });\n          }\n          const user = await ctx.context.adapter.findOne({\n            model: \"user\",\n            where: [\n              {\n                field: \"phoneNumber\",\n                value: ctx.body.phoneNumber\n              }\n            ]\n          });\n          if (!user) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.UNEXPECTED_ERROR\n            });\n          }\n          const hashedPassword = await ctx.context.password.hash(\n            ctx.body.newPassword\n          );\n          await ctx.context.internalAdapter.updatePassword(\n            user.id,\n            hashedPassword\n          );\n          await ctx.context.internalAdapter.deleteVerificationValue(\n            verification.id\n          );\n          return ctx.json({\n            status: true\n          });\n        }\n      )\n    },\n    schema: mergeSchema(schema, options?.schema),\n    rateLimit: [\n      {\n        pathMatcher(path) {\n          return path.startsWith(\"/phone-number\");\n        },\n        window: 60 * 1e3,\n        max: 10\n      }\n    ],\n    $ERROR_CODES: ERROR_CODES\n  };\n};\nconst schema = {\n  user: {\n    fields: {\n      phoneNumber: {\n        type: \"string\",\n        required: false,\n        unique: true,\n        sortable: true,\n        returned: true\n      },\n      phoneNumberVerified: {\n        type: \"boolean\",\n        required: false,\n        returned: true,\n        input: false\n      }\n    }\n  }\n};\n\nexport { phoneNumber };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,cAAc;IAClB,sBAAsB;IACtB,oBAAoB;IACpB,kCAAkC;IAClC,kBAAkB;IAClB,eAAe;IACf,aAAa;IACb,aAAa;IACb,2BAA2B;AAC7B;AAEA,SAAS,YAAY,IAAI;IACvB,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,MAAM;AACpC;AACA,MAAM,cAAc,CAAC;IACnB,MAAM,OAAO;QACX,WAAW,SAAS,aAAa;QACjC,WAAW,SAAS,aAAa;QACjC,GAAG,OAAO;QACV,aAAa;QACb,qBAAqB;QACrB,MAAM;QACN,WAAW;IACb;IACA,OAAO;QACL,IAAI;QACJ,WAAW;YACT;;;;;;;;;;;;;;OAcC,GACD,mBAAmB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAClC,yBACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa;oBACf;oBACA,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACxB,aAAa;oBACf;oBACA,YAAY,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa;oBACf,GAAG,QAAQ;gBACb;gBACA,UAAU;oBACR,SAAS;wBACP,SAAS;wBACT,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,MAAM;oDACJ,MAAM;gDACR;gDACA,SAAS;oDACP,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;4BACA,KAAK;gCACH,aAAa;4BACf;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,EAAE,QAAQ,EAAE,aAAa,YAAY,EAAE,GAAG,IAAI,IAAI;gBACxD,IAAI,KAAK,oBAAoB,EAAE;oBAC7B,MAAM,gBAAgB,MAAM,KAAK,oBAAoB,CACnD,IAAI,IAAI,CAAC,WAAW;oBAEtB,IAAI,CAAC,eAAe;wBAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,SAAS,YAAY,oBAAoB;wBAC3C;oBACF;gBACF;gBACA,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBAC7C,OAAO;oBACP,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO;wBACT;qBACD;gBACH;gBACA,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,YAAY,gCAAgC;oBACvD;gBACF;gBACA,IAAI,KAAK,mBAAmB,EAAE;oBAC5B,IAAI,CAAC,KAAK,mBAAmB,EAAE;wBAC7B,MAAM,MAAM,YAAY,KAAK,SAAS;wBACtC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;4BACE,OAAO;4BACP,YAAY;4BACZ,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EAAE,KAAK,SAAS,EAAE;wBACrC,GACA;wBAEF,MAAM,KAAK,OAAO,GAChB;4BACE,aAAa;4BACb,MAAM;wBACR,GACA,IAAI,OAAO;wBAEb,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;4BACjC,SAAS,YAAY,yBAAyB;wBAChD;oBACF;gBACF;gBACA,MAAM,WAAW,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,mBAAmB,CAAC,KAAK,EAAE;gBAC9E,MAAM,oBAAoB,SAAS,IAAI,CACrC,CAAC,IAAM,EAAE,UAAU,KAAK;gBAE1B,IAAI,CAAC,mBAAmB;oBACtB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC;wBACvD,aAAa;oBACf;oBACA,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,YAAY,gCAAgC;oBACvD;gBACF;gBACA,MAAM,kBAAkB,mBAAmB;gBAC3C,IAAI,CAAC,iBAAiB;oBACpB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,sBAAsB;wBAAE,aAAa;oBAAa;oBAC3E,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,YAAY,gBAAgB;oBACvC;gBACF;gBACA,MAAM,gBAAgB,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;oBACtD,MAAM;oBACN;gBACF;gBACA,IAAI,CAAC,eAAe;oBAClB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;oBACzB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,YAAY,gCAAgC;oBACvD;gBACF;gBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7D,KAAK,EAAE,EACP,KACA,IAAI,IAAI,CAAC,UAAU,KAAK;gBAE1B,IAAI,CAAC,SAAS;oBACZ,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;oBACzB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,iLAAA,CAAA,IAAgB,CAAC,wBAAwB;oBACpD;gBACF;gBACA,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EACnB,KACA;oBACE;oBACA;gBACF,GACA,IAAI,IAAI,CAAC,UAAU,KAAK;gBAE1B,OAAO,IAAI,IAAI,CAAC;oBACd,OAAO,QAAQ,KAAK;oBACpB,MAAM;wBACJ,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,eAAe,KAAK,aAAa;wBACjC,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;wBACjB,aAAa,KAAK,WAAW;wBAC7B,qBAAqB,KAAK,mBAAmB;wBAC7C,WAAW,KAAK,SAAS;wBACzB,WAAW,KAAK,SAAS;oBAC3B;gBACF;YACF;YAEF;;;;;;;;;;;;;;OAcC,GACD,oBAAoB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACnC,0BACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa;oBACf;gBACF;gBACA,UAAU;oBACR,SAAS;wBACP,SAAS;wBACT,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,SAAS;oDACP,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,IAAI,CAAC,SAAS,SAAS;oBACrB,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;oBACxB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,mBAAmB;wBACpC,SAAS;oBACX;gBACF;gBACA,IAAI,KAAK,oBAAoB,EAAE;oBAC7B,MAAM,gBAAgB,MAAM,KAAK,oBAAoB,CACnD,IAAI,IAAI,CAAC,WAAW;oBAEtB,IAAI,CAAC,eAAe;wBAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,SAAS,YAAY,oBAAoB;wBAC3C;oBACF;gBACF;gBACA,MAAM,OAAO,YAAY,KAAK,SAAS;gBACvC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;oBACE,OAAO,GAAG,KAAK,EAAE,CAAC;oBAClB,YAAY,IAAI,IAAI,CAAC,WAAW;oBAChC,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EAAE,KAAK,SAAS,EAAE;gBACrC,GACA;gBAEF,MAAM,QAAQ,OAAO,CACnB;oBACE,aAAa,IAAI,IAAI,CAAC,WAAW;oBACjC;gBACF,GACA,IAAI,OAAO;gBAEb,OAAO,IAAI,IAAI,CAAC;oBAAE,SAAS;gBAAY;YACzC;YAEF;;;;;;;;;;;;;;OAcC,GACD,mBAAmB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAClC,wBACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb;;aAEC,GACD,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa;oBACf;oBACA;;aAEC,GACD,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACpB,aAAa;oBACf;oBACA;;;aAGC,GACD,gBAAgB,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,IAAI,CAAC;wBAC/B,aAAa;oBACf,GAAG,QAAQ;oBACX;;;;aAIC,GACD,mBAAmB,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,IAAI,CAAC;wBAClC,aAAa;oBACf,GAAG,QAAQ;gBACb;gBACA,UAAU;oBACR,SAAS;wBACP,SAAS;wBACT,aAAa;wBACb,WAAW;4BACT,OAAO;gCACL,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,QAAQ;oDACN,MAAM;oDACN,aAAa;oDACb,MAAM;wDAAC;qDAAK;gDACd;gDACA,OAAO;oDACL,MAAM;oDACN,UAAU;oDACV,aAAa;gDACf;gDACA,MAAM;oDACJ,MAAM;oDACN,UAAU;oDACV,YAAY;wDACV,IAAI;4DACF,MAAM;4DACN,aAAa;wDACf;wDACA,OAAO;4DACL,MAAM;4DACN,QAAQ;4DACR,UAAU;4DACV,aAAa;wDACf;wDACA,eAAe;4DACb,MAAM;4DACN,UAAU;4DACV,aAAa;wDACf;wDACA,MAAM;4DACJ,MAAM;4DACN,UAAU;4DACV,aAAa;wDACf;wDACA,OAAO;4DACL,MAAM;4DACN,QAAQ;4DACR,UAAU;4DACV,aAAa;wDACf;wDACA,aAAa;4DACX,MAAM;4DACN,aAAa;wDACf;wDACA,qBAAqB;4DACnB,MAAM;4DACN,aAAa;wDACf;wDACA,WAAW;4DACT,MAAM;4DACN,QAAQ;4DACR,aAAa;wDACf;wDACA,WAAW;4DACT,MAAM;4DACN,QAAQ;4DACR,aAAa;wDACf;oDACF;oDACA,UAAU;wDACR;wDACA;wDACA;wDACA;wDACA;qDACD;oDACD,aAAa;gDACf;4CACF;4CACA,UAAU;gDAAC;6CAAS;wCACtB;oCACF;gCACF;4BACF;4BACA,KAAK;gCACH,aAAa;4BACf;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,MAAM,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,qBAAqB,CACjE,IAAI,IAAI,CAAC,WAAW;gBAEtB,IAAI,CAAC,OAAO,IAAI,SAAS,GAAG,aAAa,GAAG,IAAI,QAAQ;oBACtD,IAAI,OAAO,IAAI,SAAS,GAAG,aAAa,GAAG,IAAI,QAAQ;wBACrD,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,SAAS;wBACX;oBACF;oBACA,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,aAAa;oBACpC;gBACF;gBACA,MAAM,CAAC,UAAU,SAAS,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC;gBAC7C,MAAM,kBAAkB,SAAS,mBAAmB;gBACpD,IAAI,YAAY,SAAS,aAAa,iBAAiB;oBACrD,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CAAC,IAAI,EAAE;oBAChE,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,aAAa;wBAC9B,SAAS;oBACX;gBACF;gBACA,IAAI,aAAa,IAAI,IAAI,CAAC,IAAI,EAAE;oBAC9B,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CAAC,IAAI,EAAE,EAAE;wBAChE,OAAO,GAAG,SAAS,CAAC,EAAE,SAAS,YAAY,OAAO,GAAG;oBACvD;oBACA,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS;oBACX;gBACF;gBACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CAAC,IAAI,EAAE;gBAChE,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBAC9B,MAAM,UAAU,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAiB,AAAD,EAAE;oBACxC,IAAI,CAAC,SAAS;wBACZ,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;4BACjC,SAAS,iLAAA,CAAA,IAAgB,CAAC,cAAc;wBAC1C;oBACF;oBACA,MAAM,eAAe,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;wBACtD,OAAO;wBACP,OAAO;4BACL;gCACE,OAAO;gCACP,OAAO,IAAI,IAAI,CAAC,WAAW;4BAC7B;yBACD;oBACH;oBACA,IAAI,aAAa,MAAM,EAAE;wBACvB,MAAM,IAAI,KAAK,CAAC,eAAe;4BAC7B,SAAS,YAAY,kBAAkB;wBACzC;oBACF;oBACA,IAAI,QAAQ,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CACtD,QAAQ,IAAI,CAAC,EAAE,EACf;wBACE,CAAC,KAAK,WAAW,CAAC,EAAE,IAAI,IAAI,CAAC,WAAW;wBACxC,CAAC,KAAK,mBAAmB,CAAC,EAAE;oBAC9B,GACA;oBAEF,OAAO,IAAI,IAAI,CAAC;wBACd,QAAQ;wBACR,OAAO,QAAQ,OAAO,CAAC,KAAK;wBAC5B,MAAM;4BACJ,IAAI,MAAM,EAAE;4BACZ,OAAO,MAAM,KAAK;4BAClB,eAAe,MAAM,aAAa;4BAClC,MAAM,MAAM,IAAI;4BAChB,OAAO,MAAM,KAAK;4BAClB,aAAa,MAAM,WAAW;4BAC9B,qBAAqB,MAAM,mBAAmB;4BAC9C,WAAW,MAAM,SAAS;4BAC1B,WAAW,MAAM,SAAS;wBAC5B;oBACF;gBACF;gBACA,IAAI,OAAO,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBAC3C,OAAO;oBACP,OAAO;wBACL;4BACE,OAAO,IAAI,IAAI,CAAC,WAAW;4BAC3B,OAAO,KAAK,WAAW;wBACzB;qBACD;gBACH;gBACA,IAAI,CAAC,MAAM;oBACT,IAAI,SAAS,sBAAsB;wBACjC,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CACjD;4BACE,OAAO,QAAQ,oBAAoB,CAAC,YAAY,CAC9C,IAAI,IAAI,CAAC,WAAW;4BAEtB,MAAM,QAAQ,oBAAoB,CAAC,WAAW,GAAG,QAAQ,oBAAoB,CAAC,WAAW,CACvF,IAAI,IAAI,CAAC,WAAW,IAClB,IAAI,IAAI,CAAC,WAAW;4BACxB,CAAC,KAAK,WAAW,CAAC,EAAE,IAAI,IAAI,CAAC,WAAW;4BACxC,CAAC,KAAK,mBAAmB,CAAC,EAAE;wBAC9B,GACA;wBAEF,IAAI,CAAC,MAAM;4BACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,yBAAyB;gCAC1C,SAAS,iLAAA,CAAA,IAAgB,CAAC,qBAAqB;4BACjD;wBACF;oBACF;gBACF,OAAO;oBACL,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CACjD,KAAK,EAAE,EACP;wBACE,CAAC,KAAK,mBAAmB,CAAC,EAAE;oBAC9B,GACA;gBAEJ;gBACA,IAAI,CAAC,MAAM;oBACT,OAAO,IAAI,IAAI,CAAC;gBAClB;gBACA,MAAM,SAAS,yBACb;oBACE,aAAa,IAAI,IAAI,CAAC,WAAW;oBACjC;gBACF,GACA,IAAI,OAAO;gBAEb,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,yBAAyB;wBAC1C,SAAS,iLAAA,CAAA,IAAgB,CAAC,qBAAqB;oBACjD;gBACF;gBACA,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;oBAC5B,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7D,KAAK,EAAE,EACP;oBAEF,IAAI,CAAC,SAAS;wBACZ,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,yBAAyB;4BAC1C,SAAS,iLAAA,CAAA,IAAgB,CAAC,wBAAwB;wBACpD;oBACF;oBACA,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;wBAC1B;wBACA;oBACF;oBACA,OAAO,IAAI,IAAI,CAAC;wBACd,QAAQ;wBACR,OAAO,QAAQ,KAAK;wBACpB,MAAM;4BACJ,IAAI,KAAK,EAAE;4BACX,OAAO,KAAK,KAAK;4BACjB,eAAe,KAAK,aAAa;4BACjC,MAAM,KAAK,IAAI;4BACf,OAAO,KAAK,KAAK;4BACjB,aAAa,KAAK,WAAW;4BAC7B,qBAAqB,KAAK,mBAAmB;4BAC7C,WAAW,KAAK,SAAS;4BACzB,WAAW,KAAK,SAAS;wBAC3B;oBACF;gBACF;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,QAAQ;oBACR,OAAO;oBACP,MAAM;wBACJ,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,eAAe,KAAK,aAAa;wBACjC,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;wBACjB,aAAa,KAAK,WAAW;wBAC7B,qBAAqB,KAAK,mBAAmB;wBAC7C,WAAW,KAAK,SAAS;wBACzB,WAAW,KAAK,SAAS;oBAC3B;gBACF;YACF;YAEF;;OAEC,GACD,2BAA2B,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAC1C,iCACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa,CAAC,qEAAqE,CAAC;oBACtF;gBACF;gBACA,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,OAAO;gCACL,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,QAAQ;oDACN,MAAM;oDACN,aAAa;oDACb,MAAM;wDAAC;qDAAK;gDACd;4CACF;4CACA,UAAU;gDAAC;6CAAS;wCACtB;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBAC7C,OAAO;oBACP,OAAO;wBACL;4BACE,OAAO,IAAI,IAAI,CAAC,WAAW;4BAC3B,OAAO,KAAK,WAAW;wBACzB;qBACD;gBACH;gBACA,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS;oBACX;gBACF;gBACA,MAAM,OAAO,YAAY,KAAK,SAAS;gBACvC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;oBACE,OAAO,GAAG,KAAK,EAAE,CAAC;oBAClB,YAAY,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAAC;oBAC5D,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EAAE,KAAK,SAAS,EAAE;gBACrC,GACA;gBAEF,MAAM,SAAS,wBACb;oBACE,aAAa,IAAI,IAAI,CAAC,WAAW;oBACjC;gBACF,GACA,IAAI,OAAO;gBAEb,OAAO,IAAI,IAAI,CAAC;oBACd,QAAQ;gBACV;YACF;YAEF,iCAAiC,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAChD,wCACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD;gBACtB;gBACA,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,OAAO;gCACL,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,QAAQ;oDACN,MAAM;oDACN,aAAa;oDACb,MAAM;wDAAC;qDAAK;gDACd;4CACF;4CACA,UAAU;gDAAC;6CAAS;wCACtB;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBAC7C,OAAO;oBACP,OAAO;wBACL;4BACE,OAAO,IAAI,IAAI,CAAC,WAAW;4BAC3B,OAAO,KAAK,WAAW;wBACzB;qBACD;gBACH;gBACA,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS;oBACX;gBACF;gBACA,MAAM,OAAO,YAAY,KAAK,SAAS;gBACvC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;oBACE,OAAO,GAAG,KAAK,EAAE,CAAC;oBAClB,YAAY,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAAC;oBAC5D,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EAAE,KAAK,SAAS,EAAE;gBACrC,GACA;gBAEF,MAAM,SAAS,uBACb;oBACE,aAAa,IAAI,IAAI,CAAC,WAAW;oBACjC;gBACF,GACA,IAAI,OAAO;gBAEb,OAAO,IAAI,IAAI,CAAC;oBACd,QAAQ;gBACV;YACF;YAEF,0BAA0B,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACzC,gCACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,KAAK,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACnB,aAAa;oBACf;oBACA,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa;oBACf;oBACA,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa,CAAC,+CAA+C,CAAC;oBAChE;gBACF;gBACA,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,OAAO;gCACL,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,QAAQ;oDACN,MAAM;oDACN,aAAa;oDACb,MAAM;wDAAC;qDAAK;gDACd;4CACF;4CACA,UAAU;gDAAC;6CAAS;wCACtB;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,eAAe,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,qBAAqB,CAC1E,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAAC;gBAElD,IAAI,CAAC,cAAc;oBACjB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,aAAa;oBACpC;gBACF;gBACA,IAAI,aAAa,SAAS,GAAG,aAAa,GAAG,IAAI,QAAQ;oBACvD,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,WAAW;oBAClC;gBACF;gBACA,MAAM,CAAC,UAAU,SAAS,GAAG,aAAa,KAAK,CAAC,KAAK,CAAC;gBACtD,MAAM,kBAAkB,SAAS,mBAAmB;gBACpD,IAAI,YAAY,SAAS,aAAa,iBAAiB;oBACrD,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,aAAa,EAAE;oBAEjB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,aAAa;wBAC9B,SAAS;oBACX;gBACF;gBACA,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,UAAU;oBAC7B,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,aAAa,EAAE,EACf;wBACE,OAAO,GAAG,SAAS,CAAC,EAAE,SAAS,YAAY,OAAO,GAAG;oBACvD;oBAEF,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,WAAW;oBAClC;gBACF;gBACA,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBAC7C,OAAO;oBACP,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO,IAAI,IAAI,CAAC,WAAW;wBAC7B;qBACD;gBACH;gBACA,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,gBAAgB;oBACvC;gBACF;gBACA,MAAM,iBAAiB,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CACpD,IAAI,IAAI,CAAC,WAAW;gBAEtB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,cAAc,CAC9C,KAAK,EAAE,EACP;gBAEF,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,aAAa,EAAE;gBAEjB,OAAO,IAAI,IAAI,CAAC;oBACd,QAAQ;gBACV;YACF;QAEJ;QACA,QAAQ,CAAA,GAAA,iLAAA,CAAA,IAAW,AAAD,EAAE,QAAQ,SAAS;QACrC,WAAW;YACT;gBACE,aAAY,IAAI;oBACd,OAAO,KAAK,UAAU,CAAC;gBACzB;gBACA,QAAQ,KAAK;gBACb,KAAK;YACP;SACD;QACD,cAAc;IAChB;AACF;AACA,MAAM,SAAS;IACb,MAAM;QACJ,QAAQ;YACN,aAAa;gBACX,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,UAAU;YACZ;YACA,qBAAqB;gBACnB,MAAM;gBACN,UAAU;gBACV,UAAU;gBACV,OAAO;YACT;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3325, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/anonymous/index.mjs"], "sourcesContent": ["import { APIError } from 'better-call';\nimport { i as createAuthMiddleware, j as createAuthEndpoint, k as getSessionFromCtx } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport 'zod/v4';\nimport { parseSet<PERSON><PERSON>ieHeader, setSessionCookie } from '../../cookies/index.mjs';\nimport { m as mergeSchema } from '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport { g as getOrigin } from '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '@better-auth/utils/hash';\nimport '@better-auth/utils/base64';\nimport '../../crypto/index.mjs';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-auth/utils/random';\nimport '@better-fetch/fetch';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport 'jose/errors';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\n\nconst schema = {\n  user: {\n    fields: {\n      isAnonymous: {\n        type: \"boolean\",\n        required: false\n      }\n    }\n  }\n};\nconst anonymous = (options) => {\n  const ERROR_CODES = {\n    FAILED_TO_CREATE_USER: \"Failed to create user\",\n    COULD_NOT_CREATE_SESSION: \"Could not create session\",\n    ANONYMOUS_USERS_CANNOT_SIGN_IN_AGAIN_ANONYMOUSLY: \"Anonymous users cannot sign in again anonymously\"\n  };\n  return {\n    id: \"anonymous\",\n    endpoints: {\n      signInAnonymous: createAuthEndpoint(\n        \"/sign-in/anonymous\",\n        {\n          method: \"POST\",\n          metadata: {\n            openapi: {\n              description: \"Sign in anonymously\",\n              responses: {\n                200: {\n                  description: \"Sign in anonymously\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          user: {\n                            $ref: \"#/components/schemas/User\"\n                          },\n                          session: {\n                            $ref: \"#/components/schemas/Session\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const { emailDomainName = getOrigin(ctx.context.baseURL) } = options || {};\n          const id = ctx.context.generateId({ model: \"user\" });\n          const email = `temp-${id}@${emailDomainName}`;\n          const name = await options?.generateName?.(ctx) || \"Anonymous\";\n          const newUser = await ctx.context.internalAdapter.createUser(\n            {\n              email,\n              emailVerified: false,\n              isAnonymous: true,\n              name,\n              createdAt: /* @__PURE__ */ new Date(),\n              updatedAt: /* @__PURE__ */ new Date()\n            },\n            ctx\n          );\n          if (!newUser) {\n            throw ctx.error(\"INTERNAL_SERVER_ERROR\", {\n              message: ERROR_CODES.FAILED_TO_CREATE_USER\n            });\n          }\n          const session = await ctx.context.internalAdapter.createSession(\n            newUser.id,\n            ctx\n          );\n          if (!session) {\n            return ctx.json(null, {\n              status: 400,\n              body: {\n                message: ERROR_CODES.COULD_NOT_CREATE_SESSION\n              }\n            });\n          }\n          await setSessionCookie(ctx, {\n            session,\n            user: newUser\n          });\n          return ctx.json({\n            token: session.token,\n            user: {\n              id: newUser.id,\n              email: newUser.email,\n              emailVerified: newUser.emailVerified,\n              name: newUser.name,\n              createdAt: newUser.createdAt,\n              updatedAt: newUser.updatedAt\n            }\n          });\n        }\n      )\n    },\n    hooks: {\n      after: [\n        {\n          matcher(ctx) {\n            return ctx.path.startsWith(\"/sign-in\") || ctx.path.startsWith(\"/sign-up\") || ctx.path.startsWith(\"/callback\") || ctx.path.startsWith(\"/oauth2/callback\") || ctx.path.startsWith(\"/magic-link/verify\") || ctx.path.startsWith(\"/email-otp/verify-email\") || ctx.path.startsWith(\"/one-tap/callback\") || ctx.path.startsWith(\"/passkey/verify-authentication\");\n          },\n          handler: createAuthMiddleware(async (ctx) => {\n            const setCookie = ctx.context.responseHeaders?.get(\"set-cookie\");\n            const sessionTokenName = ctx.context.authCookies.sessionToken.name;\n            const sessionCookie = parseSetCookieHeader(setCookie || \"\").get(sessionTokenName)?.value.split(\".\")[0];\n            if (!sessionCookie) {\n              return;\n            }\n            const session = await getSessionFromCtx(\n              ctx,\n              {\n                disableRefresh: true\n              }\n            );\n            if (!session || !session.user.isAnonymous) {\n              return;\n            }\n            if (ctx.path === \"/sign-in/anonymous\") {\n              throw new APIError(\"BAD_REQUEST\", {\n                message: ERROR_CODES.ANONYMOUS_USERS_CANNOT_SIGN_IN_AGAIN_ANONYMOUSLY\n              });\n            }\n            const newSession = ctx.context.newSession;\n            if (!newSession) {\n              return;\n            }\n            if (options?.onLinkAccount) {\n              await options?.onLinkAccount?.({\n                anonymousUser: session,\n                newUser: newSession\n              });\n            }\n            if (!options?.disableDeleteAnonymousUser) {\n              await ctx.context.internalAdapter.deleteUser(session.user.id);\n            }\n          })\n        }\n      ]\n    },\n    schema: mergeSchema(schema, options?.schema),\n    $ERROR_CODES: ERROR_CODES\n  };\n};\n\nexport { anonymous };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,SAAS;IACb,MAAM;QACJ,QAAQ;YACN,aAAa;gBACX,MAAM;gBACN,UAAU;YACZ;QACF;IACF;AACF;AACA,MAAM,YAAY,CAAC;IACjB,MAAM,cAAc;QAClB,uBAAuB;QACvB,0BAA0B;QAC1B,kDAAkD;IACpD;IACA,OAAO;QACL,IAAI;QACJ,WAAW;YACT,iBAAiB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAChC,sBACA;gBACE,QAAQ;gBACR,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,MAAM;oDACJ,MAAM;gDACR;gDACA,SAAS;oDACP,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,EAAE,kBAAkB,CAAA,GAAA,iLAAA,CAAA,IAAS,AAAD,EAAE,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,GAAG,WAAW,CAAC;gBACzE,MAAM,KAAK,IAAI,OAAO,CAAC,UAAU,CAAC;oBAAE,OAAO;gBAAO;gBAClD,MAAM,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,iBAAiB;gBAC7C,MAAM,OAAO,MAAM,SAAS,eAAe,QAAQ;gBACnD,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAC1D;oBACE;oBACA,eAAe;oBACf,aAAa;oBACb;oBACA,WAAW,aAAa,GAAG,IAAI;oBAC/B,WAAW,aAAa,GAAG,IAAI;gBACjC,GACA;gBAEF,IAAI,CAAC,SAAS;oBACZ,MAAM,IAAI,KAAK,CAAC,yBAAyB;wBACvC,SAAS,YAAY,qBAAqB;oBAC5C;gBACF;gBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7D,QAAQ,EAAE,EACV;gBAEF,IAAI,CAAC,SAAS;oBACZ,OAAO,IAAI,IAAI,CAAC,MAAM;wBACpB,QAAQ;wBACR,MAAM;4BACJ,SAAS,YAAY,wBAAwB;wBAC/C;oBACF;gBACF;gBACA,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;oBAC1B;oBACA,MAAM;gBACR;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,OAAO,QAAQ,KAAK;oBACpB,MAAM;wBACJ,IAAI,QAAQ,EAAE;wBACd,OAAO,QAAQ,KAAK;wBACpB,eAAe,QAAQ,aAAa;wBACpC,MAAM,QAAQ,IAAI;wBAClB,WAAW,QAAQ,SAAS;wBAC5B,WAAW,QAAQ,SAAS;oBAC9B;gBACF;YACF;QAEJ;QACA,OAAO;YACL,OAAO;gBACL;oBACE,SAAQ,GAAG;wBACT,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,CAAC,eAAe,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,IAAI,CAAC,UAAU,CAAC,uBAAuB,IAAI,IAAI,CAAC,UAAU,CAAC,yBAAyB,IAAI,IAAI,CAAC,UAAU,CAAC,8BAA8B,IAAI,IAAI,CAAC,UAAU,CAAC,wBAAwB,IAAI,IAAI,CAAC,UAAU,CAAC;oBAC7T;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,YAAY,IAAI,OAAO,CAAC,eAAe,EAAE,IAAI;wBACnD,MAAM,mBAAmB,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI;wBAClE,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,IAAI,GAAG,CAAC,mBAAmB,MAAM,MAAM,IAAI,CAAC,EAAE;wBACtG,IAAI,CAAC,eAAe;4BAClB;wBACF;wBACA,MAAM,UAAU,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAiB,AAAD,EACpC,KACA;4BACE,gBAAgB;wBAClB;wBAEF,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,CAAC,WAAW,EAAE;4BACzC;wBACF;wBACA,IAAI,IAAI,IAAI,KAAK,sBAAsB;4BACrC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gCAChC,SAAS,YAAY,gDAAgD;4BACvE;wBACF;wBACA,MAAM,aAAa,IAAI,OAAO,CAAC,UAAU;wBACzC,IAAI,CAAC,YAAY;4BACf;wBACF;wBACA,IAAI,SAAS,eAAe;4BAC1B,MAAM,SAAS,gBAAgB;gCAC7B,eAAe;gCACf,SAAS;4BACX;wBACF;wBACA,IAAI,CAAC,SAAS,4BAA4B;4BACxC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,EAAE;wBAC9D;oBACF;gBACF;aACD;QACH;QACA,QAAQ,CAAA,GAAA,iLAAA,CAAA,IAAW,AAAD,EAAE,QAAQ,SAAS;QACrC,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3522, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/admin/access/index.mjs"], "sourcesContent": ["import { createAccessControl } from '../../access/index.mjs';\nimport '../../../shared/better-auth.DdzSJf-n.mjs';\n\nconst defaultStatements = {\n  user: [\n    \"create\",\n    \"list\",\n    \"set-role\",\n    \"ban\",\n    \"impersonate\",\n    \"delete\",\n    \"set-password\",\n    \"update\"\n  ],\n  session: [\"list\", \"revoke\", \"delete\"]\n};\nconst defaultAc = createAccessControl(defaultStatements);\nconst adminAc = defaultAc.newRole({\n  user: [\n    \"create\",\n    \"list\",\n    \"set-role\",\n    \"ban\",\n    \"impersonate\",\n    \"delete\",\n    \"set-password\",\n    \"update\"\n  ],\n  session: [\"list\", \"revoke\", \"delete\"]\n});\nconst userAc = defaultAc.newRole({\n  user: [],\n  session: []\n});\nconst defaultRoles = {\n  admin: adminAc,\n  user: userAc\n};\n\nexport { adminAc, defaultAc, defaultRoles, defaultStatements, userAc };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEA,MAAM,oBAAoB;IACxB,MAAM;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QAAC;QAAQ;QAAU;KAAS;AACvC;AACA,MAAM,YAAY,CAAA,GAAA,uKAAA,CAAA,sBAAmB,AAAD,EAAE;AACtC,MAAM,UAAU,UAAU,OAAO,CAAC;IAChC,MAAM;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QAAC;QAAQ;QAAU;KAAS;AACvC;AACA,MAAM,SAAS,UAAU,OAAO,CAAC;IAC/B,MAAM,EAAE;IACR,SAAS,EAAE;AACb;AACA,MAAM,eAAe;IACnB,OAAO;IACP,MAAM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3583, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/generic-oauth/index.mjs"], "sourcesContent": ["import { betterFetch } from '@better-fetch/fetch';\nimport { APIError } from 'better-call';\nimport { decodeJwt } from 'jose';\nimport * as z from 'zod/v4';\nimport { j as createAuthEndpoint, l as sessionMiddleware, B as BASE_ERROR_CODES, g as generateState, c as createAuthorizationURL, p as parseState, v as validateAuthorizationCode, h as handleOAuthUserInfo, r as refreshAccessToken } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport { setSessionCookie } from '../../cookies/index.mjs';\nimport '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport '@better-auth/utils/hash';\nimport '@better-auth/utils/base64';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '../../crypto/index.mjs';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport 'jose/errors';\nimport '@better-auth/utils/random';\n\nasync function getUserInfo(tokens, finalUserInfoUrl) {\n  if (tokens.idToken) {\n    const decoded = decodeJwt(tokens.idToken);\n    if (decoded) {\n      if (decoded.sub && decoded.email) {\n        return {\n          id: decoded.sub,\n          emailVerified: decoded.email_verified,\n          image: decoded.picture,\n          ...decoded\n        };\n      }\n    }\n  }\n  if (!finalUserInfoUrl) {\n    return null;\n  }\n  const userInfo = await betterFetch(finalUserInfoUrl, {\n    method: \"GET\",\n    headers: {\n      Authorization: `Bearer ${tokens.accessToken}`\n    }\n  });\n  return {\n    id: userInfo.data?.sub,\n    emailVerified: userInfo.data?.email_verified,\n    email: userInfo.data?.email,\n    image: userInfo.data?.picture,\n    name: userInfo.data?.name,\n    ...userInfo.data\n  };\n}\nconst genericOAuth = (options) => {\n  const ERROR_CODES = {\n    INVALID_OAUTH_CONFIGURATION: \"Invalid OAuth configuration\"\n  };\n  return {\n    id: \"generic-oauth\",\n    init: (ctx) => {\n      const genericProviders = options.config.map((c) => {\n        let finalUserInfoUrl = c.userInfoUrl;\n        return {\n          id: c.providerId,\n          name: c.providerId,\n          createAuthorizationURL(data) {\n            return createAuthorizationURL({\n              id: c.providerId,\n              options: {\n                clientId: c.clientId,\n                clientSecret: c.clientSecret,\n                redirectURI: c.redirectURI\n              },\n              authorizationEndpoint: c.authorizationUrl,\n              state: data.state,\n              codeVerifier: c.pkce ? data.codeVerifier : void 0,\n              scopes: c.scopes || [],\n              redirectURI: `${ctx.baseURL}/oauth2/callback/${c.providerId}`\n            });\n          },\n          async validateAuthorizationCode(data) {\n            let finalTokenUrl = c.tokenUrl;\n            if (c.discoveryUrl) {\n              const discovery = await betterFetch(c.discoveryUrl, {\n                method: \"GET\",\n                headers: c.discoveryHeaders\n              });\n              if (discovery.data) {\n                finalTokenUrl = discovery.data.token_endpoint;\n                finalUserInfoUrl = discovery.data.userinfo_endpoint;\n              }\n            }\n            if (!finalTokenUrl) {\n              throw new APIError(\"BAD_REQUEST\", {\n                message: \"Invalid OAuth configuration. Token URL not found.\"\n              });\n            }\n            return validateAuthorizationCode({\n              headers: c.authorizationHeaders,\n              code: data.code,\n              codeVerifier: data.codeVerifier,\n              redirectURI: data.redirectURI,\n              options: {\n                clientId: c.clientId,\n                clientSecret: c.clientSecret,\n                redirectURI: c.redirectURI\n              },\n              tokenEndpoint: finalTokenUrl,\n              authentication: c.authentication\n            });\n          },\n          async refreshAccessToken(refreshToken) {\n            let finalTokenUrl = c.tokenUrl;\n            if (c.discoveryUrl) {\n              const discovery = await betterFetch(c.discoveryUrl, {\n                method: \"GET\",\n                headers: c.discoveryHeaders\n              });\n              if (discovery.data) {\n                finalTokenUrl = discovery.data.token_endpoint;\n              }\n            }\n            if (!finalTokenUrl) {\n              throw new APIError(\"BAD_REQUEST\", {\n                message: \"Invalid OAuth configuration. Token URL not found.\"\n              });\n            }\n            return refreshAccessToken({\n              refreshToken,\n              options: {\n                clientId: c.clientId,\n                clientSecret: c.clientSecret\n              },\n              authentication: c.authentication,\n              tokenEndpoint: finalTokenUrl\n            });\n          },\n          async getUserInfo(tokens) {\n            const userInfo = c.getUserInfo ? await c.getUserInfo(tokens) : await getUserInfo(tokens, finalUserInfoUrl);\n            if (!userInfo) {\n              return null;\n            }\n            return {\n              user: {\n                id: userInfo?.id,\n                email: userInfo?.email,\n                emailVerified: userInfo?.emailVerified,\n                image: userInfo?.image,\n                name: userInfo?.name,\n                ...c.mapProfileToUser?.(userInfo)\n              },\n              data: userInfo\n            };\n          }\n        };\n      });\n      return {\n        context: {\n          socialProviders: genericProviders.concat(ctx.socialProviders)\n        }\n      };\n    },\n    endpoints: {\n      /**\n       * ### Endpoint\n       *\n       * POST `/sign-in/oauth2`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.signInWithOAuth2`\n       *\n       * **client:**\n       * `authClient.signIn.oauth2`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/sign-in#api-method-sign-in-oauth2)\n       */\n      signInWithOAuth2: createAuthEndpoint(\n        \"/sign-in/oauth2\",\n        {\n          method: \"POST\",\n          body: z.object({\n            providerId: z.string().meta({\n              description: \"The provider ID for the OAuth provider\"\n            }),\n            callbackURL: z.string().meta({\n              description: \"The URL to redirect to after sign in\"\n            }).optional(),\n            errorCallbackURL: z.string().meta({\n              description: \"The URL to redirect to if an error occurs\"\n            }).optional(),\n            newUserCallbackURL: z.string().meta({\n              description: 'The URL to redirect to after login if the user is new. Eg: \"/welcome\"'\n            }).optional(),\n            disableRedirect: z.boolean().meta({\n              description: \"Disable redirect\"\n            }).optional(),\n            scopes: z.array(z.string()).meta({\n              description: \"Scopes to be passed to the provider authorization request.\"\n            }).optional(),\n            requestSignUp: z.boolean().meta({\n              description: \"Explicitly request sign-up. Useful when disableImplicitSignUp is true for this provider. Eg: false\"\n            }).optional()\n          }),\n          metadata: {\n            openapi: {\n              description: \"Sign in with OAuth2\",\n              responses: {\n                200: {\n                  description: \"Sign in with OAuth2\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          url: {\n                            type: \"string\"\n                          },\n                          redirect: {\n                            type: \"boolean\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const { providerId } = ctx.body;\n          const config = options.config.find(\n            (c) => c.providerId === providerId\n          );\n          if (!config) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: `No config found for provider ${providerId}`\n            });\n          }\n          const {\n            discoveryUrl,\n            authorizationUrl,\n            tokenUrl,\n            clientId,\n            clientSecret,\n            scopes,\n            redirectURI,\n            responseType,\n            pkce,\n            prompt,\n            accessType,\n            authorizationUrlParams,\n            responseMode,\n            authentication\n          } = config;\n          let finalAuthUrl = authorizationUrl;\n          let finalTokenUrl = tokenUrl;\n          if (discoveryUrl) {\n            const discovery = await betterFetch(discoveryUrl, {\n              method: \"GET\",\n              headers: config.discoveryHeaders,\n              onError(context) {\n                ctx.context.logger.error(context.error.message, context.error, {\n                  discoveryUrl\n                });\n              }\n            });\n            if (discovery.data) {\n              finalAuthUrl = discovery.data.authorization_endpoint;\n              finalTokenUrl = discovery.data.token_endpoint;\n            }\n          }\n          if (!finalAuthUrl || !finalTokenUrl) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.INVALID_OAUTH_CONFIGURATION\n            });\n          }\n          if (authorizationUrlParams) {\n            const withAdditionalParams = new URL(finalAuthUrl);\n            for (const [paramName, paramValue] of Object.entries(\n              authorizationUrlParams\n            )) {\n              withAdditionalParams.searchParams.set(paramName, paramValue);\n            }\n            finalAuthUrl = withAdditionalParams.toString();\n          }\n          const { state, codeVerifier } = await generateState(ctx);\n          const authUrl = await createAuthorizationURL({\n            id: providerId,\n            options: {\n              clientId,\n              redirectURI\n            },\n            authorizationEndpoint: finalAuthUrl,\n            state,\n            codeVerifier: pkce ? codeVerifier : void 0,\n            scopes: ctx.body.scopes ? [...ctx.body.scopes, ...scopes || []] : scopes || [],\n            redirectURI: `${ctx.context.baseURL}/oauth2/callback/${providerId}`,\n            prompt,\n            accessType,\n            responseType,\n            responseMode,\n            additionalParams: authorizationUrlParams\n          });\n          return ctx.json({\n            url: authUrl.toString(),\n            redirect: !ctx.body.disableRedirect\n          });\n        }\n      ),\n      oAuth2Callback: createAuthEndpoint(\n        \"/oauth2/callback/:providerId\",\n        {\n          method: \"GET\",\n          query: z.object({\n            code: z.string().meta({\n              description: \"The OAuth2 code\"\n            }).optional(),\n            error: z.string().meta({\n              description: \"The error message, if any\"\n            }).optional(),\n            error_description: z.string().meta({\n              description: \"The error description, if any\"\n            }).optional(),\n            state: z.string().meta({\n              description: \"The state parameter from the OAuth2 request\"\n            }).optional()\n          }),\n          metadata: {\n            client: false,\n            openapi: {\n              description: \"OAuth2 callback\",\n              responses: {\n                200: {\n                  description: \"OAuth2 callback\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          url: {\n                            type: \"string\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const defaultErrorURL = ctx.context.options.onAPIError?.errorURL || `${ctx.context.baseURL}/error`;\n          if (ctx.query.error || !ctx.query.code) {\n            throw ctx.redirect(\n              `${defaultErrorURL}?error=${ctx.query.error || \"oAuth_code_missing\"}&error_description=${ctx.query.error_description}`\n            );\n          }\n          const provider = options.config.find(\n            (p) => p.providerId === ctx.params.providerId\n          );\n          if (!provider) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: `No config found for provider ${ctx.params.providerId}`\n            });\n          }\n          let tokens = void 0;\n          const parsedState = await parseState(ctx);\n          const {\n            callbackURL,\n            codeVerifier,\n            errorURL,\n            requestSignUp,\n            newUserURL,\n            link\n          } = parsedState;\n          const code = ctx.query.code;\n          function redirectOnError(error) {\n            const defaultErrorURL2 = ctx.context.options.onAPIError?.errorURL || `${ctx.context.baseURL}/error`;\n            let url = errorURL || defaultErrorURL2;\n            if (url.includes(\"?\")) {\n              url = `${url}&error=${error}`;\n            } else {\n              url = `${url}?error=${error}`;\n            }\n            throw ctx.redirect(url);\n          }\n          let finalTokenUrl = provider.tokenUrl;\n          let finalUserInfoUrl = provider.userInfoUrl;\n          if (provider.discoveryUrl) {\n            const discovery = await betterFetch(provider.discoveryUrl, {\n              method: \"GET\",\n              headers: provider.discoveryHeaders\n            });\n            if (discovery.data) {\n              finalTokenUrl = discovery.data.token_endpoint;\n              finalUserInfoUrl = discovery.data.userinfo_endpoint;\n            }\n          }\n          try {\n            if (!finalTokenUrl) {\n              throw new APIError(\"BAD_REQUEST\", {\n                message: \"Invalid OAuth configuration.\"\n              });\n            }\n            tokens = await validateAuthorizationCode({\n              headers: provider.authorizationHeaders,\n              code,\n              codeVerifier: provider.pkce ? codeVerifier : void 0,\n              redirectURI: `${ctx.context.baseURL}/oauth2/callback/${provider.providerId}`,\n              options: {\n                clientId: provider.clientId,\n                clientSecret: provider.clientSecret,\n                redirectURI: provider.redirectURI\n              },\n              tokenEndpoint: finalTokenUrl,\n              authentication: provider.authentication,\n              additionalParams: provider.tokenUrlParams\n            });\n          } catch (e) {\n            ctx.context.logger.error(\n              e && typeof e === \"object\" && \"name\" in e ? e.name : \"\",\n              e\n            );\n            throw redirectOnError(\"oauth_code_verification_failed\");\n          }\n          if (!tokens) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: \"Invalid OAuth configuration.\"\n            });\n          }\n          const userInfo = provider.getUserInfo ? await provider.getUserInfo(tokens) : await getUserInfo(tokens, finalUserInfoUrl);\n          if (!userInfo) {\n            throw redirectOnError(\"user_info_is_missing\");\n          }\n          const mapUser = provider.mapProfileToUser ? await provider.mapProfileToUser(userInfo) : userInfo;\n          if (!mapUser?.email) {\n            ctx.context.logger.error(\"Unable to get user info\", userInfo);\n            throw redirectOnError(\"email_is_missing\");\n          }\n          if (link) {\n            if (ctx.context.options.account?.accountLinking?.allowDifferentEmails !== true && link.email !== mapUser.email.toLowerCase()) {\n              return redirectOnError(\"email_doesn't_match\");\n            }\n            const existingAccount = await ctx.context.internalAdapter.findAccountByProviderId(\n              userInfo.id,\n              provider.providerId\n            );\n            if (existingAccount) {\n              if (existingAccount.userId !== link.userId) {\n                return redirectOnError(\n                  \"account_already_linked_to_different_user\"\n                );\n              }\n              const updateData = Object.fromEntries(\n                Object.entries({\n                  accessToken: tokens.accessToken,\n                  idToken: tokens.idToken,\n                  refreshToken: tokens.refreshToken,\n                  accessTokenExpiresAt: tokens.accessTokenExpiresAt,\n                  refreshTokenExpiresAt: tokens.refreshTokenExpiresAt,\n                  scope: tokens.scopes?.join(\",\")\n                }).filter(([_, value]) => value !== void 0)\n              );\n              await ctx.context.internalAdapter.updateAccount(\n                existingAccount.id,\n                updateData\n              );\n            } else {\n              const newAccount = await ctx.context.internalAdapter.createAccount({\n                userId: link.userId,\n                providerId: provider.providerId,\n                accountId: userInfo.id,\n                accessToken: tokens.accessToken,\n                accessTokenExpiresAt: tokens.accessTokenExpiresAt,\n                refreshTokenExpiresAt: tokens.refreshTokenExpiresAt,\n                scope: tokens.scopes?.join(\",\"),\n                refreshToken: tokens.refreshToken,\n                idToken: tokens.idToken\n              });\n              if (!newAccount) {\n                return redirectOnError(\"unable_to_link_account\");\n              }\n            }\n            let toRedirectTo2;\n            try {\n              const url = callbackURL;\n              toRedirectTo2 = url.toString();\n            } catch {\n              toRedirectTo2 = callbackURL;\n            }\n            throw ctx.redirect(toRedirectTo2);\n          }\n          const result = await handleOAuthUserInfo(ctx, {\n            userInfo: {\n              ...userInfo,\n              ...mapUser\n            },\n            account: {\n              providerId: provider.providerId,\n              accountId: userInfo.id,\n              ...tokens,\n              scope: tokens.scopes?.join(\",\")\n            },\n            callbackURL,\n            disableSignUp: provider.disableImplicitSignUp && !requestSignUp || provider.disableSignUp,\n            overrideUserInfo: provider.overrideUserInfo\n          });\n          if (result.error) {\n            return redirectOnError(result.error.split(\" \").join(\"_\"));\n          }\n          const { session, user } = result.data;\n          await setSessionCookie(ctx, {\n            session,\n            user\n          });\n          let toRedirectTo;\n          try {\n            const url = result.isRegister ? newUserURL || callbackURL : callbackURL;\n            toRedirectTo = url.toString();\n          } catch {\n            toRedirectTo = result.isRegister ? newUserURL || callbackURL : callbackURL;\n          }\n          throw ctx.redirect(toRedirectTo);\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * POST `/oauth2/link`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.oAuth2LinkAccount`\n       *\n       * **client:**\n       * `authClient.oauth2.link`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/generic-oauth#api-method-oauth2-link)\n       */\n      oAuth2LinkAccount: createAuthEndpoint(\n        \"/oauth2/link\",\n        {\n          method: \"POST\",\n          body: z.object({\n            providerId: z.string(),\n            /**\n             * Callback URL to redirect to after the user has signed in.\n             */\n            callbackURL: z.string(),\n            /**\n             * Additional scopes to request when linking the account.\n             * This is useful for requesting additional permissions when\n             * linking a social account compared to the initial authentication.\n             */\n            scopes: z.array(z.string()).meta({\n              description: \"Additional scopes to request when linking the account\"\n            }).optional(),\n            /**\n             * The URL to redirect to if there is an error during the link process.\n             */\n            errorCallbackURL: z.string().meta({\n              description: \"The URL to redirect to if there is an error during the link process\"\n            }).optional()\n          }),\n          use: [sessionMiddleware],\n          metadata: {\n            openapi: {\n              description: \"Link an OAuth2 account to the current user session\",\n              responses: {\n                \"200\": {\n                  description: \"Authorization URL generated successfully for linking an OAuth2 account\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          url: {\n                            type: \"string\",\n                            format: \"uri\",\n                            description: \"The authorization URL to redirect the user to for linking the OAuth2 account\"\n                          },\n                          redirect: {\n                            type: \"boolean\",\n                            description: \"Indicates that the client should redirect to the provided URL\",\n                            enum: [true]\n                          }\n                        },\n                        required: [\"url\", \"redirect\"]\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (c) => {\n          const session = c.context.session;\n          const provider = options.config.find(\n            (p) => p.providerId === c.body.providerId\n          );\n          if (!provider) {\n            throw new APIError(\"NOT_FOUND\", {\n              message: BASE_ERROR_CODES.PROVIDER_NOT_FOUND\n            });\n          }\n          const {\n            providerId,\n            clientId,\n            clientSecret,\n            redirectURI,\n            authorizationUrl,\n            discoveryUrl,\n            pkce,\n            scopes,\n            prompt,\n            accessType,\n            authorizationUrlParams\n          } = provider;\n          let finalAuthUrl = authorizationUrl;\n          if (!finalAuthUrl) {\n            if (!discoveryUrl) {\n              throw new APIError(\"BAD_REQUEST\", {\n                message: ERROR_CODES.INVALID_OAUTH_CONFIGURATION\n              });\n            }\n            const discovery = await betterFetch(discoveryUrl, {\n              method: \"GET\",\n              headers: provider.discoveryHeaders,\n              onError(context) {\n                c.context.logger.error(context.error.message, context.error, {\n                  discoveryUrl\n                });\n              }\n            });\n            if (discovery.data) {\n              finalAuthUrl = discovery.data.authorization_endpoint;\n            }\n          }\n          if (!finalAuthUrl) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.INVALID_OAUTH_CONFIGURATION\n            });\n          }\n          const state = await generateState(c, {\n            userId: session.user.id,\n            email: session.user.email\n          });\n          const url = await createAuthorizationURL({\n            id: providerId,\n            options: {\n              clientId,\n              redirectURI: redirectURI || `${c.context.baseURL}/oauth2/callback/${providerId}`\n            },\n            authorizationEndpoint: finalAuthUrl,\n            state: state.state,\n            codeVerifier: pkce ? state.codeVerifier : void 0,\n            scopes: c.body.scopes || scopes || [],\n            redirectURI: redirectURI || `${c.context.baseURL}/oauth2/callback/${providerId}`,\n            prompt,\n            accessType,\n            additionalParams: authorizationUrlParams\n          });\n          return c.json({\n            url: url.toString(),\n            redirect: true\n          });\n        }\n      )\n    },\n    $ERROR_CODES: ERROR_CODES\n  };\n};\n\nexport { genericOAuth };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,eAAe,YAAY,MAAM,EAAE,gBAAgB;IACjD,IAAI,OAAO,OAAO,EAAE;QAClB,MAAM,UAAU,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO;QACxC,IAAI,SAAS;YACX,IAAI,QAAQ,GAAG,IAAI,QAAQ,KAAK,EAAE;gBAChC,OAAO;oBACL,IAAI,QAAQ,GAAG;oBACf,eAAe,QAAQ,cAAc;oBACrC,OAAO,QAAQ,OAAO;oBACtB,GAAG,OAAO;gBACZ;YACF;QACF;IACF;IACA,IAAI,CAAC,kBAAkB;QACrB,OAAO;IACT;IACA,MAAM,WAAW,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB;QACnD,QAAQ;QACR,SAAS;YACP,eAAe,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE;QAC/C;IACF;IACA,OAAO;QACL,IAAI,SAAS,IAAI,EAAE;QACnB,eAAe,SAAS,IAAI,EAAE;QAC9B,OAAO,SAAS,IAAI,EAAE;QACtB,OAAO,SAAS,IAAI,EAAE;QACtB,MAAM,SAAS,IAAI,EAAE;QACrB,GAAG,SAAS,IAAI;IAClB;AACF;AACA,MAAM,eAAe,CAAC;IACpB,MAAM,cAAc;QAClB,6BAA6B;IAC/B;IACA,OAAO;QACL,IAAI;QACJ,MAAM,CAAC;YACL,MAAM,mBAAmB,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC3C,IAAI,mBAAmB,EAAE,WAAW;gBACpC,OAAO;oBACL,IAAI,EAAE,UAAU;oBAChB,MAAM,EAAE,UAAU;oBAClB,wBAAuB,IAAI;wBACzB,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;4BAC5B,IAAI,EAAE,UAAU;4BAChB,SAAS;gCACP,UAAU,EAAE,QAAQ;gCACpB,cAAc,EAAE,YAAY;gCAC5B,aAAa,EAAE,WAAW;4BAC5B;4BACA,uBAAuB,EAAE,gBAAgB;4BACzC,OAAO,KAAK,KAAK;4BACjB,cAAc,EAAE,IAAI,GAAG,KAAK,YAAY,GAAG,KAAK;4BAChD,QAAQ,EAAE,MAAM,IAAI,EAAE;4BACtB,aAAa,GAAG,IAAI,OAAO,CAAC,iBAAiB,EAAE,EAAE,UAAU,EAAE;wBAC/D;oBACF;oBACA,MAAM,2BAA0B,IAAI;wBAClC,IAAI,gBAAgB,EAAE,QAAQ;wBAC9B,IAAI,EAAE,YAAY,EAAE;4BAClB,MAAM,YAAY,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,EAAE,YAAY,EAAE;gCAClD,QAAQ;gCACR,SAAS,EAAE,gBAAgB;4BAC7B;4BACA,IAAI,UAAU,IAAI,EAAE;gCAClB,gBAAgB,UAAU,IAAI,CAAC,cAAc;gCAC7C,mBAAmB,UAAU,IAAI,CAAC,iBAAiB;4BACrD;wBACF;wBACA,IAAI,CAAC,eAAe;4BAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gCAChC,SAAS;4BACX;wBACF;wBACA,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;4BAC/B,SAAS,EAAE,oBAAoB;4BAC/B,MAAM,KAAK,IAAI;4BACf,cAAc,KAAK,YAAY;4BAC/B,aAAa,KAAK,WAAW;4BAC7B,SAAS;gCACP,UAAU,EAAE,QAAQ;gCACpB,cAAc,EAAE,YAAY;gCAC5B,aAAa,EAAE,WAAW;4BAC5B;4BACA,eAAe;4BACf,gBAAgB,EAAE,cAAc;wBAClC;oBACF;oBACA,MAAM,oBAAmB,YAAY;wBACnC,IAAI,gBAAgB,EAAE,QAAQ;wBAC9B,IAAI,EAAE,YAAY,EAAE;4BAClB,MAAM,YAAY,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,EAAE,YAAY,EAAE;gCAClD,QAAQ;gCACR,SAAS,EAAE,gBAAgB;4BAC7B;4BACA,IAAI,UAAU,IAAI,EAAE;gCAClB,gBAAgB,UAAU,IAAI,CAAC,cAAc;4BAC/C;wBACF;wBACA,IAAI,CAAC,eAAe;4BAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gCAChC,SAAS;4BACX;wBACF;wBACA,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;4BACxB;4BACA,SAAS;gCACP,UAAU,EAAE,QAAQ;gCACpB,cAAc,EAAE,YAAY;4BAC9B;4BACA,gBAAgB,EAAE,cAAc;4BAChC,eAAe;wBACjB;oBACF;oBACA,MAAM,aAAY,MAAM;wBACtB,MAAM,WAAW,EAAE,WAAW,GAAG,MAAM,EAAE,WAAW,CAAC,UAAU,MAAM,YAAY,QAAQ;wBACzF,IAAI,CAAC,UAAU;4BACb,OAAO;wBACT;wBACA,OAAO;4BACL,MAAM;gCACJ,IAAI,UAAU;gCACd,OAAO,UAAU;gCACjB,eAAe,UAAU;gCACzB,OAAO,UAAU;gCACjB,MAAM,UAAU;gCAChB,GAAG,EAAE,gBAAgB,GAAG,SAAS;4BACnC;4BACA,MAAM;wBACR;oBACF;gBACF;YACF;YACA,OAAO;gBACL,SAAS;oBACP,iBAAiB,iBAAiB,MAAM,CAAC,IAAI,eAAe;gBAC9D;YACF;QACF;QACA,WAAW;YACT;;;;;;;;;;;;;;OAcC,GACD,kBAAkB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACjC,mBACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,YAAY,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC1B,aAAa;oBACf;oBACA,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa;oBACf,GAAG,QAAQ;oBACX,kBAAkB,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAChC,aAAa;oBACf,GAAG,QAAQ;oBACX,oBAAoB,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAClC,aAAa;oBACf,GAAG,QAAQ;oBACX,iBAAiB,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,IAAI,CAAC;wBAChC,aAAa;oBACf,GAAG,QAAQ;oBACX,QAAQ,CAAA,GAAA,mLAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,KAAK,IAAI,CAAC;wBAC/B,aAAa;oBACf,GAAG,QAAQ;oBACX,eAAe,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,IAAI,CAAC;wBAC9B,aAAa;oBACf,GAAG,QAAQ;gBACb;gBACA,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,KAAK;oDACH,MAAM;gDACR;gDACA,UAAU;oDACR,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,IAAI;gBAC/B,MAAM,SAAS,QAAQ,MAAM,CAAC,IAAI,CAChC,CAAC,IAAM,EAAE,UAAU,KAAK;gBAE1B,IAAI,CAAC,QAAQ;oBACX,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,CAAC,6BAA6B,EAAE,YAAY;oBACvD;gBACF;gBACA,MAAM,EACJ,YAAY,EACZ,gBAAgB,EAChB,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,MAAM,EACN,WAAW,EACX,YAAY,EACZ,IAAI,EACJ,MAAM,EACN,UAAU,EACV,sBAAsB,EACtB,YAAY,EACZ,cAAc,EACf,GAAG;gBACJ,IAAI,eAAe;gBACnB,IAAI,gBAAgB;gBACpB,IAAI,cAAc;oBAChB,MAAM,YAAY,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,cAAc;wBAChD,QAAQ;wBACR,SAAS,OAAO,gBAAgB;wBAChC,SAAQ,OAAO;4BACb,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,KAAK,CAAC,OAAO,EAAE,QAAQ,KAAK,EAAE;gCAC7D;4BACF;wBACF;oBACF;oBACA,IAAI,UAAU,IAAI,EAAE;wBAClB,eAAe,UAAU,IAAI,CAAC,sBAAsB;wBACpD,gBAAgB,UAAU,IAAI,CAAC,cAAc;oBAC/C;gBACF;gBACA,IAAI,CAAC,gBAAgB,CAAC,eAAe;oBACnC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,2BAA2B;oBAClD;gBACF;gBACA,IAAI,wBAAwB;oBAC1B,MAAM,uBAAuB,IAAI,IAAI;oBACrC,KAAK,MAAM,CAAC,WAAW,WAAW,IAAI,OAAO,OAAO,CAClD,wBACC;wBACD,qBAAqB,YAAY,CAAC,GAAG,CAAC,WAAW;oBACnD;oBACA,eAAe,qBAAqB,QAAQ;gBAC9C;gBACA,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAa,AAAD,EAAE;gBACpD,MAAM,UAAU,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;oBAC3C,IAAI;oBACJ,SAAS;wBACP;wBACA;oBACF;oBACA,uBAAuB;oBACvB;oBACA,cAAc,OAAO,eAAe,KAAK;oBACzC,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG;2BAAI,IAAI,IAAI,CAAC,MAAM;2BAAK,UAAU,EAAE;qBAAC,GAAG,UAAU,EAAE;oBAC9E,aAAa,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,YAAY;oBACnE;oBACA;oBACA;oBACA;oBACA,kBAAkB;gBACpB;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,KAAK,QAAQ,QAAQ;oBACrB,UAAU,CAAC,IAAI,IAAI,CAAC,eAAe;gBACrC;YACF;YAEF,gBAAgB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAC/B,gCACA;gBACE,QAAQ;gBACR,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACd,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACpB,aAAa;oBACf,GAAG,QAAQ;oBACX,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACrB,aAAa;oBACf,GAAG,QAAQ;oBACX,mBAAmB,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACjC,aAAa;oBACf,GAAG,QAAQ;oBACX,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACrB,aAAa;oBACf,GAAG,QAAQ;gBACb;gBACA,UAAU;oBACR,QAAQ;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,KAAK;oDACH,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,kBAAkB,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;gBAClG,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE;oBACtC,MAAM,IAAI,QAAQ,CAChB,GAAG,gBAAgB,OAAO,EAAE,IAAI,KAAK,CAAC,KAAK,IAAI,qBAAqB,mBAAmB,EAAE,IAAI,KAAK,CAAC,iBAAiB,EAAE;gBAE1H;gBACA,MAAM,WAAW,QAAQ,MAAM,CAAC,IAAI,CAClC,CAAC,IAAM,EAAE,UAAU,KAAK,IAAI,MAAM,CAAC,UAAU;gBAE/C,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,CAAC,6BAA6B,EAAE,IAAI,MAAM,CAAC,UAAU,EAAE;oBAClE;gBACF;gBACA,IAAI,SAAS,KAAK;gBAClB,MAAM,cAAc,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD,EAAE;gBACrC,MAAM,EACJ,WAAW,EACX,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,UAAU,EACV,IAAI,EACL,GAAG;gBACJ,MAAM,OAAO,IAAI,KAAK,CAAC,IAAI;gBAC3B,SAAS,gBAAgB,KAAK;oBAC5B,MAAM,mBAAmB,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;oBACnG,IAAI,MAAM,YAAY;oBACtB,IAAI,IAAI,QAAQ,CAAC,MAAM;wBACrB,MAAM,GAAG,IAAI,OAAO,EAAE,OAAO;oBAC/B,OAAO;wBACL,MAAM,GAAG,IAAI,OAAO,EAAE,OAAO;oBAC/B;oBACA,MAAM,IAAI,QAAQ,CAAC;gBACrB;gBACA,IAAI,gBAAgB,SAAS,QAAQ;gBACrC,IAAI,mBAAmB,SAAS,WAAW;gBAC3C,IAAI,SAAS,YAAY,EAAE;oBACzB,MAAM,YAAY,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,SAAS,YAAY,EAAE;wBACzD,QAAQ;wBACR,SAAS,SAAS,gBAAgB;oBACpC;oBACA,IAAI,UAAU,IAAI,EAAE;wBAClB,gBAAgB,UAAU,IAAI,CAAC,cAAc;wBAC7C,mBAAmB,UAAU,IAAI,CAAC,iBAAiB;oBACrD;gBACF;gBACA,IAAI;oBACF,IAAI,CAAC,eAAe;wBAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,SAAS;wBACX;oBACF;oBACA,SAAS,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD,EAAE;wBACvC,SAAS,SAAS,oBAAoB;wBACtC;wBACA,cAAc,SAAS,IAAI,GAAG,eAAe,KAAK;wBAClD,aAAa,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,SAAS,UAAU,EAAE;wBAC5E,SAAS;4BACP,UAAU,SAAS,QAAQ;4BAC3B,cAAc,SAAS,YAAY;4BACnC,aAAa,SAAS,WAAW;wBACnC;wBACA,eAAe;wBACf,gBAAgB,SAAS,cAAc;wBACvC,kBAAkB,SAAS,cAAc;oBAC3C;gBACF,EAAE,OAAO,GAAG;oBACV,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CACtB,KAAK,OAAO,MAAM,YAAY,UAAU,IAAI,EAAE,IAAI,GAAG,IACrD;oBAEF,MAAM,gBAAgB;gBACxB;gBACA,IAAI,CAAC,QAAQ;oBACX,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS;oBACX;gBACF;gBACA,MAAM,WAAW,SAAS,WAAW,GAAG,MAAM,SAAS,WAAW,CAAC,UAAU,MAAM,YAAY,QAAQ;gBACvG,IAAI,CAAC,UAAU;oBACb,MAAM,gBAAgB;gBACxB;gBACA,MAAM,UAAU,SAAS,gBAAgB,GAAG,MAAM,SAAS,gBAAgB,CAAC,YAAY;gBACxF,IAAI,CAAC,SAAS,OAAO;oBACnB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B;oBACpD,MAAM,gBAAgB;gBACxB;gBACA,IAAI,MAAM;oBACR,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,gBAAgB,yBAAyB,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,CAAC,WAAW,IAAI;wBAC5H,OAAO,gBAAgB;oBACzB;oBACA,MAAM,kBAAkB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CAC/E,SAAS,EAAE,EACX,SAAS,UAAU;oBAErB,IAAI,iBAAiB;wBACnB,IAAI,gBAAgB,MAAM,KAAK,KAAK,MAAM,EAAE;4BAC1C,OAAO,gBACL;wBAEJ;wBACA,MAAM,aAAa,OAAO,WAAW,CACnC,OAAO,OAAO,CAAC;4BACb,aAAa,OAAO,WAAW;4BAC/B,SAAS,OAAO,OAAO;4BACvB,cAAc,OAAO,YAAY;4BACjC,sBAAsB,OAAO,oBAAoB;4BACjD,uBAAuB,OAAO,qBAAqB;4BACnD,OAAO,OAAO,MAAM,EAAE,KAAK;wBAC7B,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU,KAAK;wBAE3C,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7C,gBAAgB,EAAE,EAClB;oBAEJ,OAAO;wBACL,MAAM,aAAa,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC;4BACjE,QAAQ,KAAK,MAAM;4BACnB,YAAY,SAAS,UAAU;4BAC/B,WAAW,SAAS,EAAE;4BACtB,aAAa,OAAO,WAAW;4BAC/B,sBAAsB,OAAO,oBAAoB;4BACjD,uBAAuB,OAAO,qBAAqB;4BACnD,OAAO,OAAO,MAAM,EAAE,KAAK;4BAC3B,cAAc,OAAO,YAAY;4BACjC,SAAS,OAAO,OAAO;wBACzB;wBACA,IAAI,CAAC,YAAY;4BACf,OAAO,gBAAgB;wBACzB;oBACF;oBACA,IAAI;oBACJ,IAAI;wBACF,MAAM,MAAM;wBACZ,gBAAgB,IAAI,QAAQ;oBAC9B,EAAE,OAAM;wBACN,gBAAgB;oBAClB;oBACA,MAAM,IAAI,QAAQ,CAAC;gBACrB;gBACA,MAAM,SAAS,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAmB,AAAD,EAAE,KAAK;oBAC5C,UAAU;wBACR,GAAG,QAAQ;wBACX,GAAG,OAAO;oBACZ;oBACA,SAAS;wBACP,YAAY,SAAS,UAAU;wBAC/B,WAAW,SAAS,EAAE;wBACtB,GAAG,MAAM;wBACT,OAAO,OAAO,MAAM,EAAE,KAAK;oBAC7B;oBACA;oBACA,eAAe,SAAS,qBAAqB,IAAI,CAAC,iBAAiB,SAAS,aAAa;oBACzF,kBAAkB,SAAS,gBAAgB;gBAC7C;gBACA,IAAI,OAAO,KAAK,EAAE;oBAChB,OAAO,gBAAgB,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC;gBACtD;gBACA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,IAAI;gBACrC,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;oBAC1B;oBACA;gBACF;gBACA,IAAI;gBACJ,IAAI;oBACF,MAAM,MAAM,OAAO,UAAU,GAAG,cAAc,cAAc;oBAC5D,eAAe,IAAI,QAAQ;gBAC7B,EAAE,OAAM;oBACN,eAAe,OAAO,UAAU,GAAG,cAAc,cAAc;gBACjE;gBACA,MAAM,IAAI,QAAQ,CAAC;YACrB;YAEF;;;;;;;;;;;;;;OAcC,GACD,mBAAmB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAClC,gBACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,YAAY,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD;oBACnB;;aAEC,GACD,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD;oBACpB;;;;aAIC,GACD,QAAQ,CAAA,GAAA,mLAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,KAAK,IAAI,CAAC;wBAC/B,aAAa;oBACf,GAAG,QAAQ;oBACX;;aAEC,GACD,kBAAkB,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAChC,aAAa;oBACf,GAAG,QAAQ;gBACb;gBACA,KAAK;oBAAC,iLAAA,CAAA,IAAiB;iBAAC;gBACxB,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,OAAO;gCACL,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,KAAK;oDACH,MAAM;oDACN,QAAQ;oDACR,aAAa;gDACf;gDACA,UAAU;oDACR,MAAM;oDACN,aAAa;oDACb,MAAM;wDAAC;qDAAK;gDACd;4CACF;4CACA,UAAU;gDAAC;gDAAO;6CAAW;wCAC/B;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,UAAU,EAAE,OAAO,CAAC,OAAO;gBACjC,MAAM,WAAW,QAAQ,MAAM,CAAC,IAAI,CAClC,CAAC,IAAM,EAAE,UAAU,KAAK,EAAE,IAAI,CAAC,UAAU;gBAE3C,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,aAAa;wBAC9B,SAAS,iLAAA,CAAA,IAAgB,CAAC,kBAAkB;oBAC9C;gBACF;gBACA,MAAM,EACJ,UAAU,EACV,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,gBAAgB,EAChB,YAAY,EACZ,IAAI,EACJ,MAAM,EACN,MAAM,EACN,UAAU,EACV,sBAAsB,EACvB,GAAG;gBACJ,IAAI,eAAe;gBACnB,IAAI,CAAC,cAAc;oBACjB,IAAI,CAAC,cAAc;wBACjB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,SAAS,YAAY,2BAA2B;wBAClD;oBACF;oBACA,MAAM,YAAY,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,cAAc;wBAChD,QAAQ;wBACR,SAAS,SAAS,gBAAgB;wBAClC,SAAQ,OAAO;4BACb,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,KAAK,CAAC,OAAO,EAAE,QAAQ,KAAK,EAAE;gCAC3D;4BACF;wBACF;oBACF;oBACA,IAAI,UAAU,IAAI,EAAE;wBAClB,eAAe,UAAU,IAAI,CAAC,sBAAsB;oBACtD;gBACF;gBACA,IAAI,CAAC,cAAc;oBACjB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,2BAA2B;oBAClD;gBACF;gBACA,MAAM,QAAQ,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAa,AAAD,EAAE,GAAG;oBACnC,QAAQ,QAAQ,IAAI,CAAC,EAAE;oBACvB,OAAO,QAAQ,IAAI,CAAC,KAAK;gBAC3B;gBACA,MAAM,MAAM,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;oBACvC,IAAI;oBACJ,SAAS;wBACP;wBACA,aAAa,eAAe,GAAG,EAAE,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,YAAY;oBAClF;oBACA,uBAAuB;oBACvB,OAAO,MAAM,KAAK;oBAClB,cAAc,OAAO,MAAM,YAAY,GAAG,KAAK;oBAC/C,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,UAAU,EAAE;oBACrC,aAAa,eAAe,GAAG,EAAE,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,YAAY;oBAChF;oBACA;oBACA,kBAAkB;gBACpB;gBACA,OAAO,EAAE,IAAI,CAAC;oBACZ,KAAK,IAAI,QAAQ;oBACjB,UAAU;gBACZ;YACF;QAEJ;QACA,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/multi-session/index.mjs"], "sourcesContent": ["import * as z from 'zod/v4';\nimport { APIError } from 'better-call';\nimport { i as createAuthMiddleware, j as createAuthEndpoint, l as sessionMiddleware } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport { parseSetCookieHeader, parseCookies, setSessionCookie, deleteSessionCookie } from '../../cookies/index.mjs';\nimport '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '@better-auth/utils/hash';\nimport '@better-auth/utils/base64';\nimport '../../crypto/index.mjs';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-auth/utils/random';\nimport '@better-fetch/fetch';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport 'jose/errors';\n\nconst multiSession = (options) => {\n  const opts = {\n    maximumSessions: 5,\n    ...options\n  };\n  const isMultiSessionCookie = (key) => key.includes(\"_multi-\");\n  const ERROR_CODES = {\n    INVALID_SESSION_TOKEN: \"Invalid session token\"\n  };\n  return {\n    id: \"multi-session\",\n    endpoints: {\n      /**\n       * ### Endpoint\n       *\n       * GET `/multi-session/list-device-sessions`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.listDeviceSessions`\n       *\n       * **client:**\n       * `authClient.multiSession.listDeviceSessions`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/multi-session#api-method-multi-session-list-device-sessions)\n       */\n      listDeviceSessions: createAuthEndpoint(\n        \"/multi-session/list-device-sessions\",\n        {\n          method: \"GET\",\n          requireHeaders: true\n        },\n        async (ctx) => {\n          const cookieHeader = ctx.headers?.get(\"cookie\");\n          if (!cookieHeader) return ctx.json([]);\n          const cookies = Object.fromEntries(parseCookies(cookieHeader));\n          const sessionTokens = (await Promise.all(\n            Object.entries(cookies).filter(([key]) => isMultiSessionCookie(key)).map(\n              async ([key]) => await ctx.getSignedCookie(key, ctx.context.secret)\n            )\n          )).filter((v) => v !== null);\n          if (!sessionTokens.length) return ctx.json([]);\n          const sessions = await ctx.context.internalAdapter.findSessions(sessionTokens);\n          const validSessions = sessions.filter(\n            (session) => session && session.session.expiresAt > /* @__PURE__ */ new Date()\n          );\n          const uniqueUserSessions = validSessions.reduce(\n            (acc, session) => {\n              if (!acc.find((s) => s.user.id === session.user.id)) {\n                acc.push(session);\n              }\n              return acc;\n            },\n            []\n          );\n          return ctx.json(uniqueUserSessions);\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * POST `/multi-session/set-active`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.setActiveSession`\n       *\n       * **client:**\n       * `authClient.multiSession.setActive`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/multi-session#api-method-multi-session-set-active)\n       */\n      setActiveSession: createAuthEndpoint(\n        \"/multi-session/set-active\",\n        {\n          method: \"POST\",\n          body: z.object({\n            sessionToken: z.string().meta({\n              description: \"The session token to set as active\"\n            })\n          }),\n          requireHeaders: true,\n          use: [sessionMiddleware],\n          metadata: {\n            openapi: {\n              description: \"Set the active session\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          session: {\n                            $ref: \"#/components/schemas/Session\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const sessionToken = ctx.body.sessionToken;\n          const multiSessionCookieName = `${ctx.context.authCookies.sessionToken.name}_multi-${sessionToken.toLowerCase()}`;\n          const sessionCookie = await ctx.getSignedCookie(\n            multiSessionCookieName,\n            ctx.context.secret\n          );\n          if (!sessionCookie) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: ERROR_CODES.INVALID_SESSION_TOKEN\n            });\n          }\n          const session = await ctx.context.internalAdapter.findSession(sessionToken);\n          if (!session || session.session.expiresAt < /* @__PURE__ */ new Date()) {\n            ctx.setCookie(multiSessionCookieName, \"\", {\n              ...ctx.context.authCookies.sessionToken.options,\n              maxAge: 0\n            });\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: ERROR_CODES.INVALID_SESSION_TOKEN\n            });\n          }\n          await setSessionCookie(ctx, session);\n          return ctx.json(session);\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * POST `/multi-session/revoke`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.revokeDeviceSession`\n       *\n       * **client:**\n       * `authClient.multiSession.revoke`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/multi-session#api-method-multi-session-revoke)\n       */\n      revokeDeviceSession: createAuthEndpoint(\n        \"/multi-session/revoke\",\n        {\n          method: \"POST\",\n          body: z.object({\n            sessionToken: z.string().meta({\n              description: \"The session token to revoke\"\n            })\n          }),\n          requireHeaders: true,\n          use: [sessionMiddleware],\n          metadata: {\n            openapi: {\n              description: \"Revoke a device session\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          status: {\n                            type: \"boolean\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const sessionToken = ctx.body.sessionToken;\n          const multiSessionCookieName = `${ctx.context.authCookies.sessionToken.name}_multi-${sessionToken.toLowerCase()}`;\n          const sessionCookie = await ctx.getSignedCookie(\n            multiSessionCookieName,\n            ctx.context.secret\n          );\n          if (!sessionCookie) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: ERROR_CODES.INVALID_SESSION_TOKEN\n            });\n          }\n          await ctx.context.internalAdapter.deleteSession(sessionToken);\n          ctx.setCookie(multiSessionCookieName, \"\", {\n            ...ctx.context.authCookies.sessionToken.options,\n            maxAge: 0\n          });\n          const isActive = ctx.context.session?.session.token === sessionToken;\n          if (!isActive) return ctx.json({ status: true });\n          const cookieHeader = ctx.headers?.get(\"cookie\");\n          if (cookieHeader) {\n            const cookies = Object.fromEntries(parseCookies(cookieHeader));\n            const sessionTokens = (await Promise.all(\n              Object.entries(cookies).filter(([key]) => isMultiSessionCookie(key)).map(\n                async ([key]) => await ctx.getSignedCookie(key, ctx.context.secret)\n              )\n            )).filter((v) => v !== void 0);\n            const internalAdapter = ctx.context.internalAdapter;\n            if (sessionTokens.length > 0) {\n              const sessions = await internalAdapter.findSessions(sessionTokens);\n              const validSessions = sessions.filter(\n                (session) => session && session.session.expiresAt > /* @__PURE__ */ new Date()\n              );\n              if (validSessions.length > 0) {\n                const nextSession = validSessions[0];\n                await setSessionCookie(ctx, nextSession);\n              } else {\n                deleteSessionCookie(ctx);\n              }\n            } else {\n              deleteSessionCookie(ctx);\n            }\n          } else {\n            deleteSessionCookie(ctx);\n          }\n          return ctx.json({\n            status: true\n          });\n        }\n      )\n    },\n    hooks: {\n      after: [\n        {\n          matcher: () => true,\n          handler: createAuthMiddleware(async (ctx) => {\n            const cookieString = ctx.context.responseHeaders?.get(\"set-cookie\");\n            if (!cookieString) return;\n            const setCookies = parseSetCookieHeader(cookieString);\n            const sessionCookieConfig = ctx.context.authCookies.sessionToken;\n            const sessionToken = ctx.context.newSession?.session.token;\n            if (!sessionToken) return;\n            const cookies = parseCookies(ctx.headers?.get(\"cookie\") || \"\");\n            const cookieName = `${sessionCookieConfig.name}_multi-${sessionToken.toLowerCase()}`;\n            if (setCookies.get(cookieName) || cookies.get(cookieName)) return;\n            const currentMultiSessions = Object.keys(Object.fromEntries(cookies)).filter(\n              isMultiSessionCookie\n            ).length + (cookieString.includes(\"session_token\") ? 1 : 0);\n            if (currentMultiSessions >= opts.maximumSessions) {\n              return;\n            }\n            await ctx.setSignedCookie(\n              cookieName,\n              sessionToken,\n              ctx.context.secret,\n              sessionCookieConfig.options\n            );\n          })\n        },\n        {\n          matcher: (context) => context.path === \"/sign-out\",\n          handler: createAuthMiddleware(async (ctx) => {\n            const cookieHeader = ctx.headers?.get(\"cookie\");\n            if (!cookieHeader) return;\n            const cookies = Object.fromEntries(parseCookies(cookieHeader));\n            const ids = Object.keys(cookies).map((key) => {\n              if (isMultiSessionCookie(key)) {\n                ctx.setCookie(key.toLowerCase(), \"\", {\n                  ...ctx.context.authCookies.sessionToken.options,\n                  maxAge: 0\n                });\n                const token = cookies[key].split(\".\")[0];\n                return token;\n              }\n              return null;\n            }).filter((v) => v !== null);\n            await ctx.context.internalAdapter.deleteSessions(ids);\n          })\n        }\n      ]\n    },\n    $ERROR_CODES: ERROR_CODES\n  };\n};\n\nexport { multiSession };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,eAAe,CAAC;IACpB,MAAM,OAAO;QACX,iBAAiB;QACjB,GAAG,OAAO;IACZ;IACA,MAAM,uBAAuB,CAAC,MAAQ,IAAI,QAAQ,CAAC;IACnD,MAAM,cAAc;QAClB,uBAAuB;IACzB;IACA,OAAO;QACL,IAAI;QACJ,WAAW;YACT;;;;;;;;;;;;;;OAcC,GACD,oBAAoB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACnC,uCACA;gBACE,QAAQ;gBACR,gBAAgB;YAClB,GACA,OAAO;gBACL,MAAM,eAAe,IAAI,OAAO,EAAE,IAAI;gBACtC,IAAI,CAAC,cAAc,OAAO,IAAI,IAAI,CAAC,EAAE;gBACrC,MAAM,UAAU,OAAO,WAAW,CAAC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;gBAChD,MAAM,gBAAgB,CAAC,MAAM,QAAQ,GAAG,CACtC,OAAO,OAAO,CAAC,SAAS,MAAM,CAAC,CAAC,CAAC,IAAI,GAAK,qBAAqB,MAAM,GAAG,CACtE,OAAO,CAAC,IAAI,GAAK,MAAM,IAAI,eAAe,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,GAErE,EAAE,MAAM,CAAC,CAAC,IAAM,MAAM;gBACvB,IAAI,CAAC,cAAc,MAAM,EAAE,OAAO,IAAI,IAAI,CAAC,EAAE;gBAC7C,MAAM,WAAW,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC;gBAChE,MAAM,gBAAgB,SAAS,MAAM,CACnC,CAAC,UAAY,WAAW,QAAQ,OAAO,CAAC,SAAS,GAAG,aAAa,GAAG,IAAI;gBAE1E,MAAM,qBAAqB,cAAc,MAAM,CAC7C,CAAC,KAAK;oBACJ,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,CAAC,EAAE,KAAK,QAAQ,IAAI,CAAC,EAAE,GAAG;wBACnD,IAAI,IAAI,CAAC;oBACX;oBACA,OAAO;gBACT,GACA,EAAE;gBAEJ,OAAO,IAAI,IAAI,CAAC;YAClB;YAEF;;;;;;;;;;;;;;OAcC,GACD,kBAAkB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACjC,6BACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,cAAc,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC5B,aAAa;oBACf;gBACF;gBACA,gBAAgB;gBAChB,KAAK;oBAAC,iLAAA,CAAA,IAAiB;iBAAC;gBACxB,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,SAAS;oDACP,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,eAAe,IAAI,IAAI,CAAC,YAAY;gBAC1C,MAAM,yBAAyB,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,WAAW,IAAI;gBACjH,MAAM,gBAAgB,MAAM,IAAI,eAAe,CAC7C,wBACA,IAAI,OAAO,CAAC,MAAM;gBAEpB,IAAI,CAAC,eAAe;oBAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,YAAY,qBAAqB;oBAC5C;gBACF;gBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;gBAC9D,IAAI,CAAC,WAAW,QAAQ,OAAO,CAAC,SAAS,GAAG,aAAa,GAAG,IAAI,QAAQ;oBACtE,IAAI,SAAS,CAAC,wBAAwB,IAAI;wBACxC,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO;wBAC/C,QAAQ;oBACV;oBACA,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,YAAY,qBAAqB;oBAC5C;gBACF;gBACA,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;gBAC5B,OAAO,IAAI,IAAI,CAAC;YAClB;YAEF;;;;;;;;;;;;;;OAcC,GACD,qBAAqB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACpC,yBACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,cAAc,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC5B,aAAa;oBACf;gBACF;gBACA,gBAAgB;gBAChB,KAAK;oBAAC,iLAAA,CAAA,IAAiB;iBAAC;gBACxB,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,QAAQ;oDACN,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,eAAe,IAAI,IAAI,CAAC,YAAY;gBAC1C,MAAM,yBAAyB,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,WAAW,IAAI;gBACjH,MAAM,gBAAgB,MAAM,IAAI,eAAe,CAC7C,wBACA,IAAI,OAAO,CAAC,MAAM;gBAEpB,IAAI,CAAC,eAAe;oBAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,SAAS,YAAY,qBAAqB;oBAC5C;gBACF;gBACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAAC;gBAChD,IAAI,SAAS,CAAC,wBAAwB,IAAI;oBACxC,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO;oBAC/C,QAAQ;gBACV;gBACA,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO,EAAE,QAAQ,UAAU;gBACxD,IAAI,CAAC,UAAU,OAAO,IAAI,IAAI,CAAC;oBAAE,QAAQ;gBAAK;gBAC9C,MAAM,eAAe,IAAI,OAAO,EAAE,IAAI;gBACtC,IAAI,cAAc;oBAChB,MAAM,UAAU,OAAO,WAAW,CAAC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;oBAChD,MAAM,gBAAgB,CAAC,MAAM,QAAQ,GAAG,CACtC,OAAO,OAAO,CAAC,SAAS,MAAM,CAAC,CAAC,CAAC,IAAI,GAAK,qBAAqB,MAAM,GAAG,CACtE,OAAO,CAAC,IAAI,GAAK,MAAM,IAAI,eAAe,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,GAErE,EAAE,MAAM,CAAC,CAAC,IAAM,MAAM,KAAK;oBAC5B,MAAM,kBAAkB,IAAI,OAAO,CAAC,eAAe;oBACnD,IAAI,cAAc,MAAM,GAAG,GAAG;wBAC5B,MAAM,WAAW,MAAM,gBAAgB,YAAY,CAAC;wBACpD,MAAM,gBAAgB,SAAS,MAAM,CACnC,CAAC,UAAY,WAAW,QAAQ,OAAO,CAAC,SAAS,GAAG,aAAa,GAAG,IAAI;wBAE1E,IAAI,cAAc,MAAM,GAAG,GAAG;4BAC5B,MAAM,cAAc,aAAa,CAAC,EAAE;4BACpC,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;wBAC9B,OAAO;4BACL,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;wBACtB;oBACF,OAAO;wBACL,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;oBACtB;gBACF,OAAO;oBACL,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;gBACtB;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,QAAQ;gBACV;YACF;QAEJ;QACA,OAAO;YACL,OAAO;gBACL;oBACE,SAAS,IAAM;oBACf,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,eAAe,IAAI,OAAO,CAAC,eAAe,EAAE,IAAI;wBACtD,IAAI,CAAC,cAAc;wBACnB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,uBAAoB,AAAD,EAAE;wBACxC,MAAM,sBAAsB,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY;wBAChE,MAAM,eAAe,IAAI,OAAO,CAAC,UAAU,EAAE,QAAQ;wBACrD,IAAI,CAAC,cAAc;wBACnB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,IAAI,OAAO,EAAE,IAAI,aAAa;wBAC3D,MAAM,aAAa,GAAG,oBAAoB,IAAI,CAAC,OAAO,EAAE,aAAa,WAAW,IAAI;wBACpF,IAAI,WAAW,GAAG,CAAC,eAAe,QAAQ,GAAG,CAAC,aAAa;wBAC3D,MAAM,uBAAuB,OAAO,IAAI,CAAC,OAAO,WAAW,CAAC,UAAU,MAAM,CAC1E,sBACA,MAAM,GAAG,CAAC,aAAa,QAAQ,CAAC,mBAAmB,IAAI,CAAC;wBAC1D,IAAI,wBAAwB,KAAK,eAAe,EAAE;4BAChD;wBACF;wBACA,MAAM,IAAI,eAAe,CACvB,YACA,cACA,IAAI,OAAO,CAAC,MAAM,EAClB,oBAAoB,OAAO;oBAE/B;gBACF;gBACA;oBACE,SAAS,CAAC,UAAY,QAAQ,IAAI,KAAK;oBACvC,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,eAAe,IAAI,OAAO,EAAE,IAAI;wBACtC,IAAI,CAAC,cAAc;wBACnB,MAAM,UAAU,OAAO,WAAW,CAAC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE;wBAChD,MAAM,MAAM,OAAO,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;4BACpC,IAAI,qBAAqB,MAAM;gCAC7B,IAAI,SAAS,CAAC,IAAI,WAAW,IAAI,IAAI;oCACnC,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO;oCAC/C,QAAQ;gCACV;gCACA,MAAM,QAAQ,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gCACxC,OAAO;4BACT;4BACA,OAAO;wBACT,GAAG,MAAM,CAAC,CAAC,IAAM,MAAM;wBACvB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,cAAc,CAAC;oBACnD;gBACF;aACD;QACH;QACA,cAAc;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4549, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/email-otp/index.mjs"], "sourcesContent": ["import * as z from 'zod/v4';\nimport { APIError } from 'better-call';\nimport { j as createAuthEndpoint, i as createAuthMiddleware } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport { setSessionCookie } from '../../cookies/index.mjs';\nimport '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport { symmetricDecrypt, symmetricEncrypt } from '../../crypto/index.mjs';\nimport { g as getDate } from '../../shared/better-auth.CW6D9eSx.mjs';\nimport { g as getEndpointResponse } from '../../shared/better-auth.DQI8AD7d.mjs';\nimport { createHash } from '@better-auth/utils/hash';\nimport { base64Url } from '@better-auth/utils/base64';\nimport { g as generateRandomString } from '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-fetch/fetch';\nimport 'jose';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport 'jose/errors';\nimport '@better-auth/utils/random';\n\nconst defaultKeyHasher = async (otp) => {\n  const hash = await createHash(\"SHA-256\").digest(\n    new TextEncoder().encode(otp)\n  );\n  const hashed = base64Url.encode(new Uint8Array(hash), {\n    padding: false\n  });\n  return hashed;\n};\nfunction splitAtLastColon(input) {\n  const idx = input.lastIndexOf(\":\");\n  if (idx === -1) {\n    return [input, \"\"];\n  }\n  return [input.slice(0, idx), input.slice(idx + 1)];\n}\n\nconst types = [\"email-verification\", \"sign-in\", \"forget-password\"];\nconst emailOTP = (options) => {\n  const opts = {\n    expiresIn: 5 * 60,\n    generateOTP: () => generateRandomString(options.otpLength ?? 6, \"0-9\"),\n    storeOTP: \"plain\",\n    ...options\n  };\n  const ERROR_CODES = {\n    OTP_EXPIRED: \"otp expired\",\n    INVALID_OTP: \"Invalid OTP\",\n    INVALID_EMAIL: \"Invalid email\",\n    USER_NOT_FOUND: \"User not found\",\n    TOO_MANY_ATTEMPTS: \"Too many attempts\"\n  };\n  async function storeOTP(ctx, otp) {\n    if (opts.storeOTP === \"encrypted\") {\n      return await symmetricEncrypt({\n        key: ctx.context.secret,\n        data: otp\n      });\n    }\n    if (opts.storeOTP === \"hashed\") {\n      return await defaultKeyHasher(otp);\n    }\n    if (typeof opts.storeOTP === \"object\" && \"hash\" in opts.storeOTP) {\n      return await opts.storeOTP.hash(otp);\n    }\n    if (typeof opts.storeOTP === \"object\" && \"encrypt\" in opts.storeOTP) {\n      return await opts.storeOTP.encrypt(otp);\n    }\n    return otp;\n  }\n  async function verifyStoredOTP(ctx, storedOtp, otp) {\n    if (opts.storeOTP === \"encrypted\") {\n      return await symmetricDecrypt({\n        key: ctx.context.secret,\n        data: storedOtp\n      }) === otp;\n    }\n    if (opts.storeOTP === \"hashed\") {\n      const hashedOtp = await defaultKeyHasher(otp);\n      return hashedOtp === storedOtp;\n    }\n    if (typeof opts.storeOTP === \"object\" && \"hash\" in opts.storeOTP) {\n      const hashedOtp = await opts.storeOTP.hash(otp);\n      return hashedOtp === storedOtp;\n    }\n    if (typeof opts.storeOTP === \"object\" && \"decrypt\" in opts.storeOTP) {\n      const decryptedOtp = await opts.storeOTP.decrypt(storedOtp);\n      return decryptedOtp === otp;\n    }\n    return otp === storedOtp;\n  }\n  const endpoints = {\n    /**\n     * ### Endpoint\n     *\n     * POST `/email-otp/send-verification-otp`\n     *\n     * ### API Methods\n     *\n     * **server:**\n     * `auth.api.sendVerificationOTP`\n     *\n     * **client:**\n     * `authClient.emailOtp.sendVerificationOtp`\n     *\n     * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/email-otp#api-method-email-otp-send-verification-otp)\n     */\n    sendVerificationOTP: createAuthEndpoint(\n      \"/email-otp/send-verification-otp\",\n      {\n        method: \"POST\",\n        body: z.object({\n          email: z.string({}).meta({\n            description: \"Email address to send the OTP\"\n          }),\n          type: z.enum(types).meta({\n            description: \"Type of the OTP\"\n          })\n        }),\n        metadata: {\n          openapi: {\n            description: \"Send verification OTP\",\n            responses: {\n              200: {\n                description: \"Success\",\n                content: {\n                  \"application/json\": {\n                    schema: {\n                      type: \"object\",\n                      properties: {\n                        success: {\n                          type: \"boolean\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      },\n      async (ctx) => {\n        if (!options?.sendVerificationOTP) {\n          ctx.context.logger.error(\n            \"send email verification is not implemented\"\n          );\n          throw new APIError(\"BAD_REQUEST\", {\n            message: \"send email verification is not implemented\"\n          });\n        }\n        const email = ctx.body.email;\n        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n        if (!emailRegex.test(email)) {\n          throw ctx.error(\"BAD_REQUEST\", {\n            message: ERROR_CODES.INVALID_EMAIL\n          });\n        }\n        if (opts.disableSignUp) {\n          const user = await ctx.context.internalAdapter.findUserByEmail(email);\n          if (!user) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.USER_NOT_FOUND\n            });\n          }\n        } else if (ctx.body.type === \"forget-password\") {\n          const user = await ctx.context.internalAdapter.findUserByEmail(email);\n          if (!user) {\n            return ctx.json({\n              success: true\n            });\n          }\n        }\n        let otp = opts.generateOTP({ email, type: ctx.body.type }, ctx.request);\n        let storedOTP = await storeOTP(ctx, otp);\n        await ctx.context.internalAdapter.createVerificationValue(\n          {\n            value: `${storedOTP}:0`,\n            identifier: `${ctx.body.type}-otp-${email}`,\n            expiresAt: getDate(opts.expiresIn, \"sec\")\n          },\n          ctx\n        ).catch(async (error) => {\n          await ctx.context.internalAdapter.deleteVerificationByIdentifier(\n            `${ctx.body.type}-otp-${email}`\n          );\n          await ctx.context.internalAdapter.createVerificationValue(\n            {\n              value: `${storedOTP}:0`,\n              identifier: `${ctx.body.type}-otp-${email}`,\n              expiresAt: getDate(opts.expiresIn, \"sec\")\n            },\n            ctx\n          );\n        });\n        await options.sendVerificationOTP(\n          {\n            email,\n            otp,\n            type: ctx.body.type\n          },\n          ctx.request\n        );\n        return ctx.json({\n          success: true\n        });\n      }\n    )\n  };\n  return {\n    id: \"email-otp\",\n    init(ctx) {\n      return {\n        options: {\n          emailVerification: {\n            ...opts.overrideDefaultEmailVerification ? {\n              async sendVerificationEmail(data, request) {\n                await endpoints.sendVerificationOTP({\n                  //@ts-expect-error - we need to pass the context\n                  context: ctx,\n                  request,\n                  body: {\n                    email: data.user.email,\n                    type: \"email-verification\"\n                  },\n                  ctx\n                });\n              }\n            } : {}\n          }\n        }\n      };\n    },\n    endpoints: {\n      ...endpoints,\n      createVerificationOTP: createAuthEndpoint(\n        \"/email-otp/create-verification-otp\",\n        {\n          method: \"POST\",\n          body: z.object({\n            email: z.string({}).meta({\n              description: \"Email address to send the OTP\"\n            }),\n            type: z.enum(types).meta({\n              required: true,\n              description: \"Type of the OTP\"\n            })\n          }),\n          metadata: {\n            SERVER_ONLY: true,\n            openapi: {\n              description: \"Create verification OTP\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"string\"\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const email = ctx.body.email;\n          const otp = opts.generateOTP(\n            { email, type: ctx.body.type },\n            ctx.request\n          );\n          let storedOTP = await storeOTP(ctx, otp);\n          await ctx.context.internalAdapter.createVerificationValue(\n            {\n              value: `${storedOTP}:0`,\n              identifier: `${ctx.body.type}-otp-${email}`,\n              expiresAt: getDate(opts.expiresIn, \"sec\")\n            },\n            ctx\n          );\n          return otp;\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * GET `/email-otp/get-verification-otp`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.getVerificationOTP`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/email-otp#api-method-email-otp-get-verification-otp)\n       */\n      getVerificationOTP: createAuthEndpoint(\n        \"/email-otp/get-verification-otp\",\n        {\n          method: \"GET\",\n          query: z.object({\n            email: z.string({}).meta({\n              description: \"Email address to get the OTP\"\n            }),\n            type: z.enum(types).meta({\n              required: true,\n              description: \"Type of the OTP\"\n            })\n          }),\n          metadata: {\n            SERVER_ONLY: true,\n            openapi: {\n              description: \"Get verification OTP\",\n              responses: {\n                \"200\": {\n                  description: \"OTP retrieved successfully or not found/expired\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          otp: {\n                            type: \"string\",\n                            nullable: true,\n                            description: \"The stored OTP, or null if not found or expired\"\n                          }\n                        },\n                        required: [\"otp\"]\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const email = ctx.query.email;\n          const verificationValue = await ctx.context.internalAdapter.findVerificationValue(\n            `${ctx.query.type}-otp-${email}`\n          );\n          if (!verificationValue || verificationValue.expiresAt < /* @__PURE__ */ new Date()) {\n            return ctx.json({\n              otp: null\n            });\n          }\n          if (opts.storeOTP === \"hashed\" || typeof opts.storeOTP === \"object\" && \"hash\" in opts.storeOTP) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: \"OTP is hashed, cannot return the plain text OTP\"\n            });\n          }\n          let [storedOtp, _attempts] = splitAtLastColon(\n            verificationValue.value\n          );\n          let otp = storedOtp;\n          if (opts.storeOTP === \"encrypted\") {\n            otp = await symmetricDecrypt({\n              key: ctx.context.secret,\n              data: storedOtp\n            });\n          }\n          if (typeof opts.storeOTP === \"object\" && \"decrypt\" in opts.storeOTP) {\n            otp = await opts.storeOTP.decrypt(storedOtp);\n          }\n          return ctx.json({\n            otp\n          });\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * POST `/email-otp/verify-email`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.verifyEmailOTP`\n       *\n       * **client:**\n       * `authClient.emailOtp.verifyEmail`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/email-otp#api-method-email-otp-verify-email)\n       */\n      verifyEmailOTP: createAuthEndpoint(\n        \"/email-otp/verify-email\",\n        {\n          method: \"POST\",\n          body: z.object({\n            email: z.string({}).meta({\n              description: \"Email address to verify\"\n            }),\n            otp: z.string().meta({\n              required: true,\n              description: \"OTP to verify\"\n            })\n          }),\n          metadata: {\n            openapi: {\n              description: \"Verify email OTP\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          status: {\n                            type: \"boolean\",\n                            description: \"Indicates if the verification was successful\",\n                            enum: [true]\n                          },\n                          token: {\n                            type: \"string\",\n                            nullable: true,\n                            description: \"Session token if autoSignInAfterVerification is enabled, otherwise null\"\n                          },\n                          user: {\n                            $ref: \"#/components/schemas/User\"\n                          },\n                          required: [\"status\", \"token\", \"user\"]\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const email = ctx.body.email;\n          const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n          if (!emailRegex.test(email)) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.INVALID_EMAIL\n            });\n          }\n          const verificationValue = await ctx.context.internalAdapter.findVerificationValue(\n            `email-verification-otp-${email}`\n          );\n          if (!verificationValue) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.INVALID_OTP\n            });\n          }\n          if (verificationValue.expiresAt < /* @__PURE__ */ new Date()) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.OTP_EXPIRED\n            });\n          }\n          const [otpValue, attempts] = splitAtLastColon(\n            verificationValue.value\n          );\n          const allowedAttempts = options?.allowedAttempts || 3;\n          if (attempts && parseInt(attempts) >= allowedAttempts) {\n            await ctx.context.internalAdapter.deleteVerificationValue(\n              verificationValue.id\n            );\n            throw new APIError(\"FORBIDDEN\", {\n              message: ERROR_CODES.TOO_MANY_ATTEMPTS\n            });\n          }\n          const verified = await verifyStoredOTP(ctx, otpValue, ctx.body.otp);\n          if (!verified) {\n            await ctx.context.internalAdapter.updateVerificationValue(\n              verificationValue.id,\n              {\n                value: `${otpValue}:${parseInt(attempts || \"0\") + 1}`\n              }\n            );\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.INVALID_OTP\n            });\n          }\n          await ctx.context.internalAdapter.deleteVerificationValue(\n            verificationValue.id\n          );\n          const user = await ctx.context.internalAdapter.findUserByEmail(email);\n          if (!user) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.USER_NOT_FOUND\n            });\n          }\n          const updatedUser = await ctx.context.internalAdapter.updateUser(\n            user.user.id,\n            {\n              email,\n              emailVerified: true\n            },\n            ctx\n          );\n          await ctx.context.options.emailVerification?.onEmailVerification?.(\n            updatedUser,\n            ctx.request\n          );\n          if (ctx.context.options.emailVerification?.autoSignInAfterVerification) {\n            const session = await ctx.context.internalAdapter.createSession(\n              updatedUser.id,\n              ctx\n            );\n            await setSessionCookie(ctx, {\n              session,\n              user: updatedUser\n            });\n            return ctx.json({\n              status: true,\n              token: session.token,\n              user: {\n                id: updatedUser.id,\n                email: updatedUser.email,\n                emailVerified: updatedUser.emailVerified,\n                name: updatedUser.name,\n                image: updatedUser.image,\n                createdAt: updatedUser.createdAt,\n                updatedAt: updatedUser.updatedAt\n              }\n            });\n          }\n          return ctx.json({\n            status: true,\n            token: null,\n            user: {\n              id: updatedUser.id,\n              email: updatedUser.email,\n              emailVerified: updatedUser.emailVerified,\n              name: updatedUser.name,\n              image: updatedUser.image,\n              createdAt: updatedUser.createdAt,\n              updatedAt: updatedUser.updatedAt\n            }\n          });\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * POST `/sign-in/email-otp`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.signInEmailOTP`\n       *\n       * **client:**\n       * `authClient.signIn.emailOtp`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/email-otp#api-method-sign-in-email-otp)\n       */\n      signInEmailOTP: createAuthEndpoint(\n        \"/sign-in/email-otp\",\n        {\n          method: \"POST\",\n          body: z.object({\n            email: z.string({}).meta({\n              description: \"Email address to sign in\"\n            }),\n            otp: z.string().meta({\n              required: true,\n              description: \"OTP sent to the email\"\n            })\n          }),\n          metadata: {\n            openapi: {\n              description: \"Sign in with email OTP\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          token: {\n                            type: \"string\",\n                            description: \"Session token for the authenticated session\"\n                          },\n                          user: {\n                            $ref: \"#/components/schemas/User\"\n                          }\n                        },\n                        required: [\"token\", \"user\"]\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const email = ctx.body.email;\n          const verificationValue = await ctx.context.internalAdapter.findVerificationValue(\n            `sign-in-otp-${email}`\n          );\n          if (!verificationValue) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.INVALID_OTP\n            });\n          }\n          if (verificationValue.expiresAt < /* @__PURE__ */ new Date()) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.OTP_EXPIRED\n            });\n          }\n          const [otpValue, attempts] = splitAtLastColon(\n            verificationValue.value\n          );\n          const allowedAttempts = options?.allowedAttempts || 3;\n          if (attempts && parseInt(attempts) >= allowedAttempts) {\n            await ctx.context.internalAdapter.deleteVerificationValue(\n              verificationValue.id\n            );\n            throw new APIError(\"FORBIDDEN\", {\n              message: ERROR_CODES.TOO_MANY_ATTEMPTS\n            });\n          }\n          const verified = await verifyStoredOTP(ctx, otpValue, ctx.body.otp);\n          if (!verified) {\n            await ctx.context.internalAdapter.updateVerificationValue(\n              verificationValue.id,\n              {\n                value: `${otpValue}:${parseInt(attempts || \"0\") + 1}`\n              }\n            );\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.INVALID_OTP\n            });\n          }\n          await ctx.context.internalAdapter.deleteVerificationValue(\n            verificationValue.id\n          );\n          const user = await ctx.context.internalAdapter.findUserByEmail(email);\n          if (!user) {\n            if (opts.disableSignUp) {\n              throw new APIError(\"BAD_REQUEST\", {\n                message: ERROR_CODES.USER_NOT_FOUND\n              });\n            }\n            const newUser = await ctx.context.internalAdapter.createUser(\n              {\n                email,\n                emailVerified: true,\n                name: \"\"\n              },\n              ctx\n            );\n            const session2 = await ctx.context.internalAdapter.createSession(\n              newUser.id,\n              ctx\n            );\n            await setSessionCookie(ctx, {\n              session: session2,\n              user: newUser\n            });\n            return ctx.json({\n              token: session2.token,\n              user: {\n                id: newUser.id,\n                email: newUser.email,\n                emailVerified: newUser.emailVerified,\n                name: newUser.name,\n                image: newUser.image,\n                createdAt: newUser.createdAt,\n                updatedAt: newUser.updatedAt\n              }\n            });\n          }\n          if (!user.user.emailVerified) {\n            await ctx.context.internalAdapter.updateUser(\n              user.user.id,\n              {\n                emailVerified: true\n              },\n              ctx\n            );\n          }\n          const session = await ctx.context.internalAdapter.createSession(\n            user.user.id,\n            ctx\n          );\n          await setSessionCookie(ctx, {\n            session,\n            user: user.user\n          });\n          return ctx.json({\n            token: session.token,\n            user: {\n              id: user.user.id,\n              email: user.user.email,\n              emailVerified: user.user.emailVerified,\n              name: user.user.name,\n              image: user.user.image,\n              createdAt: user.user.createdAt,\n              updatedAt: user.user.updatedAt\n            }\n          });\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * POST `/forget-password/email-otp`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.forgetPasswordEmailOTP`\n       *\n       * **client:**\n       * `authClient.forgetPassword.emailOtp`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/email-otp#api-method-forget-password-email-otp)\n       */\n      forgetPasswordEmailOTP: createAuthEndpoint(\n        \"/forget-password/email-otp\",\n        {\n          method: \"POST\",\n          body: z.object({\n            email: z.string().meta({\n              description: \"Email address to send the OTP\"\n            })\n          }),\n          metadata: {\n            openapi: {\n              description: \"Forget password with email OTP\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          success: {\n                            type: \"boolean\",\n                            description: \"Indicates if the OTP was sent successfully\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const email = ctx.body.email;\n          const user = await ctx.context.internalAdapter.findUserByEmail(email);\n          if (!user) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.USER_NOT_FOUND\n            });\n          }\n          const otp = opts.generateOTP(\n            { email, type: \"forget-password\" },\n            ctx.request\n          );\n          let storedOTP = await storeOTP(ctx, otp);\n          await ctx.context.internalAdapter.createVerificationValue(\n            {\n              value: `${storedOTP}:0`,\n              identifier: `forget-password-otp-${email}`,\n              expiresAt: getDate(opts.expiresIn, \"sec\")\n            },\n            ctx\n          );\n          await options.sendVerificationOTP(\n            {\n              email,\n              otp,\n              type: \"forget-password\"\n            },\n            ctx.request\n          );\n          return ctx.json({\n            success: true\n          });\n        }\n      ),\n      /**\n       * ### Endpoint\n       *\n       * POST `/email-otp/reset-password`\n       *\n       * ### API Methods\n       *\n       * **server:**\n       * `auth.api.resetPasswordEmailOTP`\n       *\n       * **client:**\n       * `authClient.emailOtp.resetPassword`\n       *\n       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/email-otp#api-method-email-otp-reset-password)\n       */\n      resetPasswordEmailOTP: createAuthEndpoint(\n        \"/email-otp/reset-password\",\n        {\n          method: \"POST\",\n          body: z.object({\n            email: z.string().meta({\n              description: \"Email address to reset the password\"\n            }),\n            otp: z.string().meta({\n              description: \"OTP sent to the email\"\n            }),\n            password: z.string().meta({\n              description: \"New password\"\n            })\n          }),\n          metadata: {\n            openapi: {\n              description: \"Reset password with email OTP\",\n              responses: {\n                200: {\n                  description: \"Success\",\n                  contnt: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          success: {\n                            type: \"boolean\"\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const email = ctx.body.email;\n          const user = await ctx.context.internalAdapter.findUserByEmail(\n            email,\n            {\n              includeAccounts: true\n            }\n          );\n          if (!user) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.USER_NOT_FOUND\n            });\n          }\n          const verificationValue = await ctx.context.internalAdapter.findVerificationValue(\n            `forget-password-otp-${email}`\n          );\n          if (!verificationValue) {\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.INVALID_OTP\n            });\n          }\n          if (verificationValue.expiresAt < /* @__PURE__ */ new Date()) {\n            await ctx.context.internalAdapter.deleteVerificationValue(\n              verificationValue.id\n            );\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.OTP_EXPIRED\n            });\n          }\n          const [otpValue, attempts] = splitAtLastColon(\n            verificationValue.value\n          );\n          const allowedAttempts = options?.allowedAttempts || 3;\n          if (attempts && parseInt(attempts) >= allowedAttempts) {\n            await ctx.context.internalAdapter.deleteVerificationValue(\n              verificationValue.id\n            );\n            throw new APIError(\"FORBIDDEN\", {\n              message: ERROR_CODES.TOO_MANY_ATTEMPTS\n            });\n          }\n          const verified = await verifyStoredOTP(ctx, otpValue, ctx.body.otp);\n          if (!verified) {\n            await ctx.context.internalAdapter.updateVerificationValue(\n              verificationValue.id,\n              {\n                value: `${otpValue}:${parseInt(attempts || \"0\") + 1}`\n              }\n            );\n            throw new APIError(\"BAD_REQUEST\", {\n              message: ERROR_CODES.INVALID_OTP\n            });\n          }\n          await ctx.context.internalAdapter.deleteVerificationValue(\n            verificationValue.id\n          );\n          const passwordHash = await ctx.context.password.hash(\n            ctx.body.password\n          );\n          const account = user.accounts.find(\n            (account2) => account2.providerId === \"credential\"\n          );\n          if (!account) {\n            await ctx.context.internalAdapter.createAccount(\n              {\n                userId: user.user.id,\n                providerId: \"credential\",\n                accountId: user.user.id,\n                password: passwordHash\n              },\n              ctx\n            );\n          } else {\n            await ctx.context.internalAdapter.updatePassword(\n              user.user.id,\n              passwordHash,\n              ctx\n            );\n          }\n          if (!user.user.emailVerified) {\n            await ctx.context.internalAdapter.updateUser(\n              user.user.id,\n              {\n                emailVerified: true\n              },\n              ctx\n            );\n          }\n          return ctx.json({\n            success: true\n          });\n        }\n      )\n    },\n    hooks: {\n      after: [\n        {\n          matcher(context) {\n            return !!(context.path?.startsWith(\"/sign-up\") && opts.sendVerificationOnSignUp);\n          },\n          handler: createAuthMiddleware(async (ctx) => {\n            const response = await getEndpointResponse(ctx);\n            const email = response?.user.email;\n            if (email) {\n              const otp = opts.generateOTP(\n                { email, type: ctx.body.type },\n                ctx.request\n              );\n              let storedOTP = await storeOTP(ctx, otp);\n              await ctx.context.internalAdapter.createVerificationValue(\n                {\n                  value: `${storedOTP}:0`,\n                  identifier: `email-verification-otp-${email}`,\n                  expiresAt: getDate(opts.expiresIn, \"sec\")\n                },\n                ctx\n              );\n              await options.sendVerificationOTP(\n                {\n                  email,\n                  otp,\n                  type: \"email-verification\"\n                },\n                ctx.request\n              );\n            }\n          })\n        }\n      ]\n    },\n    $ERROR_CODES: ERROR_CODES,\n    rateLimit: [\n      {\n        pathMatcher(path) {\n          return path === \"/email-otp/send-verification-otp\";\n        },\n        window: 60,\n        max: 3\n      },\n      {\n        pathMatcher(path) {\n          return path === \"/email-otp/verify-email\";\n        },\n        window: 60,\n        max: 3\n      },\n      {\n        pathMatcher(path) {\n          return path === \"/sign-in/email-otp\";\n        },\n        window: 60,\n        max: 3\n      }\n    ]\n  };\n};\n\nexport { emailOTP };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,mBAAmB,OAAO;IAC9B,MAAM,OAAO,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,MAAM,CAC7C,IAAI,cAAc,MAAM,CAAC;IAE3B,MAAM,SAAS,8JAAA,CAAA,YAAS,CAAC,MAAM,CAAC,IAAI,WAAW,OAAO;QACpD,SAAS;IACX;IACA,OAAO;AACT;AACA,SAAS,iBAAiB,KAAK;IAC7B,MAAM,MAAM,MAAM,WAAW,CAAC;IAC9B,IAAI,QAAQ,CAAC,GAAG;QACd,OAAO;YAAC;YAAO;SAAG;IACpB;IACA,OAAO;QAAC,MAAM,KAAK,CAAC,GAAG;QAAM,MAAM,KAAK,CAAC,MAAM;KAAG;AACpD;AAEA,MAAM,QAAQ;IAAC;IAAsB;IAAW;CAAkB;AAClE,MAAM,WAAW,CAAC;IAChB,MAAM,OAAO;QACX,WAAW,IAAI;QACf,aAAa,IAAM,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,QAAQ,SAAS,IAAI,GAAG;QAChE,UAAU;QACV,GAAG,OAAO;IACZ;IACA,MAAM,cAAc;QAClB,aAAa;QACb,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,mBAAmB;IACrB;IACA,eAAe,SAAS,GAAG,EAAE,GAAG;QAC9B,IAAI,KAAK,QAAQ,KAAK,aAAa;YACjC,OAAO,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC5B,KAAK,IAAI,OAAO,CAAC,MAAM;gBACvB,MAAM;YACR;QACF;QACA,IAAI,KAAK,QAAQ,KAAK,UAAU;YAC9B,OAAO,MAAM,iBAAiB;QAChC;QACA,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,UAAU,KAAK,QAAQ,EAAE;YAChE,OAAO,MAAM,KAAK,QAAQ,CAAC,IAAI,CAAC;QAClC;QACA,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,aAAa,KAAK,QAAQ,EAAE;YACnE,OAAO,MAAM,KAAK,QAAQ,CAAC,OAAO,CAAC;QACrC;QACA,OAAO;IACT;IACA,eAAe,gBAAgB,GAAG,EAAE,SAAS,EAAE,GAAG;QAChD,IAAI,KAAK,QAAQ,KAAK,aAAa;YACjC,OAAO,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC5B,KAAK,IAAI,OAAO,CAAC,MAAM;gBACvB,MAAM;YACR,OAAO;QACT;QACA,IAAI,KAAK,QAAQ,KAAK,UAAU;YAC9B,MAAM,YAAY,MAAM,iBAAiB;YACzC,OAAO,cAAc;QACvB;QACA,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,UAAU,KAAK,QAAQ,EAAE;YAChE,MAAM,YAAY,MAAM,KAAK,QAAQ,CAAC,IAAI,CAAC;YAC3C,OAAO,cAAc;QACvB;QACA,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,aAAa,KAAK,QAAQ,EAAE;YACnE,MAAM,eAAe,MAAM,KAAK,QAAQ,CAAC,OAAO,CAAC;YACjD,OAAO,iBAAiB;QAC1B;QACA,OAAO,QAAQ;IACjB;IACA,MAAM,YAAY;QAChB;;;;;;;;;;;;;;KAcC,GACD,qBAAqB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACpC,oCACA;YACE,QAAQ;YACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;gBACb,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC;oBACvB,aAAa;gBACf;gBACA,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAM,AAAD,EAAE,OAAO,IAAI,CAAC;oBACvB,aAAa;gBACf;YACF;YACA,UAAU;gBACR,SAAS;oBACP,aAAa;oBACb,WAAW;wBACT,KAAK;4BACH,aAAa;4BACb,SAAS;gCACP,oBAAoB;oCAClB,QAAQ;wCACN,MAAM;wCACN,YAAY;4CACV,SAAS;gDACP,MAAM;4CACR;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF,GACA,OAAO;YACL,IAAI,CAAC,SAAS,qBAAqB;gBACjC,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CACtB;gBAEF,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;oBAChC,SAAS;gBACX;YACF;YACA,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK;YAC5B,MAAM,aAAa;YACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;gBAC3B,MAAM,IAAI,KAAK,CAAC,eAAe;oBAC7B,SAAS,YAAY,aAAa;gBACpC;YACF;YACA,IAAI,KAAK,aAAa,EAAE;gBACtB,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC;gBAC/D,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,cAAc;oBACrC;gBACF;YACF,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,mBAAmB;gBAC9C,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC;gBAC/D,IAAI,CAAC,MAAM;oBACT,OAAO,IAAI,IAAI,CAAC;wBACd,SAAS;oBACX;gBACF;YACF;YACA,IAAI,MAAM,KAAK,WAAW,CAAC;gBAAE;gBAAO,MAAM,IAAI,IAAI,CAAC,IAAI;YAAC,GAAG,IAAI,OAAO;YACtE,IAAI,YAAY,MAAM,SAAS,KAAK;YACpC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;gBACE,OAAO,GAAG,UAAU,EAAE,CAAC;gBACvB,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;gBAC3C,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EAAE,KAAK,SAAS,EAAE;YACrC,GACA,KACA,KAAK,CAAC,OAAO;gBACb,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,8BAA8B,CAC9D,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;gBAEjC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;oBACE,OAAO,GAAG,UAAU,EAAE,CAAC;oBACvB,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;oBAC3C,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EAAE,KAAK,SAAS,EAAE;gBACrC,GACA;YAEJ;YACA,MAAM,QAAQ,mBAAmB,CAC/B;gBACE;gBACA;gBACA,MAAM,IAAI,IAAI,CAAC,IAAI;YACrB,GACA,IAAI,OAAO;YAEb,OAAO,IAAI,IAAI,CAAC;gBACd,SAAS;YACX;QACF;IAEJ;IACA,OAAO;QACL,IAAI;QACJ,MAAK,GAAG;YACN,OAAO;gBACL,SAAS;oBACP,mBAAmB;wBACjB,GAAG,KAAK,gCAAgC,GAAG;4BACzC,MAAM,uBAAsB,IAAI,EAAE,OAAO;gCACvC,MAAM,UAAU,mBAAmB,CAAC;oCAClC,gDAAgD;oCAChD,SAAS;oCACT;oCACA,MAAM;wCACJ,OAAO,KAAK,IAAI,CAAC,KAAK;wCACtB,MAAM;oCACR;oCACA;gCACF;4BACF;wBACF,IAAI,CAAC,CAAC;oBACR;gBACF;YACF;QACF;QACA,WAAW;YACT,GAAG,SAAS;YACZ,uBAAuB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACtC,sCACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC;wBACvB,aAAa;oBACf;oBACA,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAM,AAAD,EAAE,OAAO,IAAI,CAAC;wBACvB,UAAU;wBACV,aAAa;oBACf;gBACF;gBACA,UAAU;oBACR,aAAa;oBACb,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;wCACR;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK;gBAC5B,MAAM,MAAM,KAAK,WAAW,CAC1B;oBAAE;oBAAO,MAAM,IAAI,IAAI,CAAC,IAAI;gBAAC,GAC7B,IAAI,OAAO;gBAEb,IAAI,YAAY,MAAM,SAAS,KAAK;gBACpC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;oBACE,OAAO,GAAG,UAAU,EAAE,CAAC;oBACvB,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;oBAC3C,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EAAE,KAAK,SAAS,EAAE;gBACrC,GACA;gBAEF,OAAO;YACT;YAEF;;;;;;;;;;;OAWC,GACD,oBAAoB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACnC,mCACA;gBACE,QAAQ;gBACR,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACd,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC;wBACvB,aAAa;oBACf;oBACA,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAM,AAAD,EAAE,OAAO,IAAI,CAAC;wBACvB,UAAU;wBACV,aAAa;oBACf;gBACF;gBACA,UAAU;oBACR,aAAa;oBACb,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,OAAO;gCACL,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,KAAK;oDACH,MAAM;oDACN,UAAU;oDACV,aAAa;gDACf;4CACF;4CACA,UAAU;gDAAC;6CAAM;wCACnB;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,QAAQ,IAAI,KAAK,CAAC,KAAK;gBAC7B,MAAM,oBAAoB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,qBAAqB,CAC/E,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO;gBAElC,IAAI,CAAC,qBAAqB,kBAAkB,SAAS,GAAG,aAAa,GAAG,IAAI,QAAQ;oBAClF,OAAO,IAAI,IAAI,CAAC;wBACd,KAAK;oBACP;gBACF;gBACA,IAAI,KAAK,QAAQ,KAAK,YAAY,OAAO,KAAK,QAAQ,KAAK,YAAY,UAAU,KAAK,QAAQ,EAAE;oBAC9F,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS;oBACX;gBACF;gBACA,IAAI,CAAC,WAAW,UAAU,GAAG,iBAC3B,kBAAkB,KAAK;gBAEzB,IAAI,MAAM;gBACV,IAAI,KAAK,QAAQ,KAAK,aAAa;oBACjC,MAAM,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;wBAC3B,KAAK,IAAI,OAAO,CAAC,MAAM;wBACvB,MAAM;oBACR;gBACF;gBACA,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,aAAa,KAAK,QAAQ,EAAE;oBACnE,MAAM,MAAM,KAAK,QAAQ,CAAC,OAAO,CAAC;gBACpC;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd;gBACF;YACF;YAEF;;;;;;;;;;;;;;OAcC,GACD,gBAAgB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAC/B,2BACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC;wBACvB,aAAa;oBACf;oBACA,KAAK,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACnB,UAAU;wBACV,aAAa;oBACf;gBACF;gBACA,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,QAAQ;oDACN,MAAM;oDACN,aAAa;oDACb,MAAM;wDAAC;qDAAK;gDACd;gDACA,OAAO;oDACL,MAAM;oDACN,UAAU;oDACV,aAAa;gDACf;gDACA,MAAM;oDACJ,MAAM;gDACR;gDACA,UAAU;oDAAC;oDAAU;oDAAS;iDAAO;4CACvC;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK;gBAC5B,MAAM,aAAa;gBACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;oBAC3B,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,aAAa;oBACpC;gBACF;gBACA,MAAM,oBAAoB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,qBAAqB,CAC/E,CAAC,uBAAuB,EAAE,OAAO;gBAEnC,IAAI,CAAC,mBAAmB;oBACtB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,WAAW;oBAClC;gBACF;gBACA,IAAI,kBAAkB,SAAS,GAAG,aAAa,GAAG,IAAI,QAAQ;oBAC5D,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,WAAW;oBAClC;gBACF;gBACA,MAAM,CAAC,UAAU,SAAS,GAAG,iBAC3B,kBAAkB,KAAK;gBAEzB,MAAM,kBAAkB,SAAS,mBAAmB;gBACpD,IAAI,YAAY,SAAS,aAAa,iBAAiB;oBACrD,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE;oBAEtB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,aAAa;wBAC9B,SAAS,YAAY,iBAAiB;oBACxC;gBACF;gBACA,MAAM,WAAW,MAAM,gBAAgB,KAAK,UAAU,IAAI,IAAI,CAAC,GAAG;gBAClE,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE,EACpB;wBACE,OAAO,GAAG,SAAS,CAAC,EAAE,SAAS,YAAY,OAAO,GAAG;oBACvD;oBAEF,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,WAAW;oBAClC;gBACF;gBACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE;gBAEtB,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC;gBAC/D,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,cAAc;oBACrC;gBACF;gBACA,MAAM,cAAc,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAC9D,KAAK,IAAI,CAAC,EAAE,EACZ;oBACE;oBACA,eAAe;gBACjB,GACA;gBAEF,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,sBAC3C,aACA,IAAI,OAAO;gBAEb,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,6BAA6B;oBACtE,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7D,YAAY,EAAE,EACd;oBAEF,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;wBAC1B;wBACA,MAAM;oBACR;oBACA,OAAO,IAAI,IAAI,CAAC;wBACd,QAAQ;wBACR,OAAO,QAAQ,KAAK;wBACpB,MAAM;4BACJ,IAAI,YAAY,EAAE;4BAClB,OAAO,YAAY,KAAK;4BACxB,eAAe,YAAY,aAAa;4BACxC,MAAM,YAAY,IAAI;4BACtB,OAAO,YAAY,KAAK;4BACxB,WAAW,YAAY,SAAS;4BAChC,WAAW,YAAY,SAAS;wBAClC;oBACF;gBACF;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,QAAQ;oBACR,OAAO;oBACP,MAAM;wBACJ,IAAI,YAAY,EAAE;wBAClB,OAAO,YAAY,KAAK;wBACxB,eAAe,YAAY,aAAa;wBACxC,MAAM,YAAY,IAAI;wBACtB,OAAO,YAAY,KAAK;wBACxB,WAAW,YAAY,SAAS;wBAChC,WAAW,YAAY,SAAS;oBAClC;gBACF;YACF;YAEF;;;;;;;;;;;;;;OAcC,GACD,gBAAgB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAC/B,sBACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC;wBACvB,aAAa;oBACf;oBACA,KAAK,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACnB,UAAU;wBACV,aAAa;oBACf;gBACF;gBACA,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,OAAO;oDACL,MAAM;oDACN,aAAa;gDACf;gDACA,MAAM;oDACJ,MAAM;gDACR;4CACF;4CACA,UAAU;gDAAC;gDAAS;6CAAO;wCAC7B;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK;gBAC5B,MAAM,oBAAoB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,qBAAqB,CAC/E,CAAC,YAAY,EAAE,OAAO;gBAExB,IAAI,CAAC,mBAAmB;oBACtB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,WAAW;oBAClC;gBACF;gBACA,IAAI,kBAAkB,SAAS,GAAG,aAAa,GAAG,IAAI,QAAQ;oBAC5D,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,WAAW;oBAClC;gBACF;gBACA,MAAM,CAAC,UAAU,SAAS,GAAG,iBAC3B,kBAAkB,KAAK;gBAEzB,MAAM,kBAAkB,SAAS,mBAAmB;gBACpD,IAAI,YAAY,SAAS,aAAa,iBAAiB;oBACrD,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE;oBAEtB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,aAAa;wBAC9B,SAAS,YAAY,iBAAiB;oBACxC;gBACF;gBACA,MAAM,WAAW,MAAM,gBAAgB,KAAK,UAAU,IAAI,IAAI,CAAC,GAAG;gBAClE,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE,EACpB;wBACE,OAAO,GAAG,SAAS,CAAC,EAAE,SAAS,YAAY,OAAO,GAAG;oBACvD;oBAEF,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,WAAW;oBAClC;gBACF;gBACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE;gBAEtB,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC;gBAC/D,IAAI,CAAC,MAAM;oBACT,IAAI,KAAK,aAAa,EAAE;wBACtB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,SAAS,YAAY,cAAc;wBACrC;oBACF;oBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAC1D;wBACE;wBACA,eAAe;wBACf,MAAM;oBACR,GACA;oBAEF,MAAM,WAAW,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC9D,QAAQ,EAAE,EACV;oBAEF,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;wBAC1B,SAAS;wBACT,MAAM;oBACR;oBACA,OAAO,IAAI,IAAI,CAAC;wBACd,OAAO,SAAS,KAAK;wBACrB,MAAM;4BACJ,IAAI,QAAQ,EAAE;4BACd,OAAO,QAAQ,KAAK;4BACpB,eAAe,QAAQ,aAAa;4BACpC,MAAM,QAAQ,IAAI;4BAClB,OAAO,QAAQ,KAAK;4BACpB,WAAW,QAAQ,SAAS;4BAC5B,WAAW,QAAQ,SAAS;wBAC9B;oBACF;gBACF;gBACA,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa,EAAE;oBAC5B,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAC1C,KAAK,IAAI,CAAC,EAAE,EACZ;wBACE,eAAe;oBACjB,GACA;gBAEJ;gBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7D,KAAK,IAAI,CAAC,EAAE,EACZ;gBAEF,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;oBAC1B;oBACA,MAAM,KAAK,IAAI;gBACjB;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,OAAO,QAAQ,KAAK;oBACpB,MAAM;wBACJ,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB,eAAe,KAAK,IAAI,CAAC,aAAa;wBACtC,MAAM,KAAK,IAAI,CAAC,IAAI;wBACpB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB,WAAW,KAAK,IAAI,CAAC,SAAS;wBAC9B,WAAW,KAAK,IAAI,CAAC,SAAS;oBAChC;gBACF;YACF;YAEF;;;;;;;;;;;;;;OAcC,GACD,wBAAwB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACvC,8BACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACrB,aAAa;oBACf;gBACF;gBACA,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,SAAS;oDACP,MAAM;oDACN,aAAa;gDACf;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK;gBAC5B,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC;gBAC/D,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,cAAc;oBACrC;gBACF;gBACA,MAAM,MAAM,KAAK,WAAW,CAC1B;oBAAE;oBAAO,MAAM;gBAAkB,GACjC,IAAI,OAAO;gBAEb,IAAI,YAAY,MAAM,SAAS,KAAK;gBACpC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;oBACE,OAAO,GAAG,UAAU,EAAE,CAAC;oBACvB,YAAY,CAAC,oBAAoB,EAAE,OAAO;oBAC1C,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EAAE,KAAK,SAAS,EAAE;gBACrC,GACA;gBAEF,MAAM,QAAQ,mBAAmB,CAC/B;oBACE;oBACA;oBACA,MAAM;gBACR,GACA,IAAI,OAAO;gBAEb,OAAO,IAAI,IAAI,CAAC;oBACd,SAAS;gBACX;YACF;YAEF;;;;;;;;;;;;;;OAcC,GACD,uBAAuB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACtC,6BACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACrB,aAAa;oBACf;oBACA,KAAK,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACnB,aAAa;oBACf;oBACA,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACxB,aAAa;oBACf;gBACF;gBACA,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,QAAQ;oCACN,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,SAAS;oDACP,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK;gBAC5B,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAC5D,OACA;oBACE,iBAAiB;gBACnB;gBAEF,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,cAAc;oBACrC;gBACF;gBACA,MAAM,oBAAoB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,qBAAqB,CAC/E,CAAC,oBAAoB,EAAE,OAAO;gBAEhC,IAAI,CAAC,mBAAmB;oBACtB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,WAAW;oBAClC;gBACF;gBACA,IAAI,kBAAkB,SAAS,GAAG,aAAa,GAAG,IAAI,QAAQ;oBAC5D,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE;oBAEtB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,WAAW;oBAClC;gBACF;gBACA,MAAM,CAAC,UAAU,SAAS,GAAG,iBAC3B,kBAAkB,KAAK;gBAEzB,MAAM,kBAAkB,SAAS,mBAAmB;gBACpD,IAAI,YAAY,SAAS,aAAa,iBAAiB;oBACrD,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE;oBAEtB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,aAAa;wBAC9B,SAAS,YAAY,iBAAiB;oBACxC;gBACF;gBACA,MAAM,WAAW,MAAM,gBAAgB,KAAK,UAAU,IAAI,IAAI,CAAC,GAAG;gBAClE,IAAI,CAAC,UAAU;oBACb,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE,EACpB;wBACE,OAAO,GAAG,SAAS,CAAC,EAAE,SAAS,YAAY,OAAO,GAAG;oBACvD;oBAEF,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS,YAAY,WAAW;oBAClC;gBACF;gBACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE;gBAEtB,MAAM,eAAe,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAClD,IAAI,IAAI,CAAC,QAAQ;gBAEnB,MAAM,UAAU,KAAK,QAAQ,CAAC,IAAI,CAChC,CAAC,WAAa,SAAS,UAAU,KAAK;gBAExC,IAAI,CAAC,SAAS;oBACZ,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7C;wBACE,QAAQ,KAAK,IAAI,CAAC,EAAE;wBACpB,YAAY;wBACZ,WAAW,KAAK,IAAI,CAAC,EAAE;wBACvB,UAAU;oBACZ,GACA;gBAEJ,OAAO;oBACL,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,cAAc,CAC9C,KAAK,IAAI,CAAC,EAAE,EACZ,cACA;gBAEJ;gBACA,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa,EAAE;oBAC5B,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,UAAU,CAC1C,KAAK,IAAI,CAAC,EAAE,EACZ;wBACE,eAAe;oBACjB,GACA;gBAEJ;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,SAAS;gBACX;YACF;QAEJ;QACA,OAAO;YACL,OAAO;gBACL;oBACE,SAAQ,OAAO;wBACb,OAAO,CAAC,CAAC,CAAC,QAAQ,IAAI,EAAE,WAAW,eAAe,KAAK,wBAAwB;oBACjF;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,WAAW,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAmB,AAAD,EAAE;wBAC3C,MAAM,QAAQ,UAAU,KAAK;wBAC7B,IAAI,OAAO;4BACT,MAAM,MAAM,KAAK,WAAW,CAC1B;gCAAE;gCAAO,MAAM,IAAI,IAAI,CAAC,IAAI;4BAAC,GAC7B,IAAI,OAAO;4BAEb,IAAI,YAAY,MAAM,SAAS,KAAK;4BACpC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;gCACE,OAAO,GAAG,UAAU,EAAE,CAAC;gCACvB,YAAY,CAAC,uBAAuB,EAAE,OAAO;gCAC7C,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAO,AAAD,EAAE,KAAK,SAAS,EAAE;4BACrC,GACA;4BAEF,MAAM,QAAQ,mBAAmB,CAC/B;gCACE;gCACA;gCACA,MAAM;4BACR,GACA,IAAI,OAAO;wBAEf;oBACF;gBACF;aACD;QACH;QACA,cAAc;QACd,WAAW;YACT;gBACE,aAAY,IAAI;oBACd,OAAO,SAAS;gBAClB;gBACA,QAAQ;gBACR,KAAK;YACP;YACA;gBACE,aAAY,IAAI;oBACd,OAAO,SAAS;gBAClB;gBACA,QAAQ;gBACR,KAAK;YACP;YACA;gBACE,aAAY,IAAI;oBACd,OAAO,SAAS;gBAClB;gBACA,QAAQ;gBACR,KAAK;YACP;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5464, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/one-tap/index.mjs"], "sourcesContent": ["import * as z from 'zod/v4';\nimport { APIError } from 'better-call';\nimport { j as createAuthEndpoint } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport { setSessionCookie } from '../../cookies/index.mjs';\nimport '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport { createRemoteJWKSet, jwtVerify } from 'jose';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '@better-auth/utils/hash';\nimport '@better-auth/utils/base64';\nimport '../../crypto/index.mjs';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-auth/utils/random';\nimport '@better-fetch/fetch';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport 'jose/errors';\n\nfunction toBoolean(value) {\n  return value === \"true\" || value === true;\n}\n\nconst oneTap = (options) => ({\n  id: \"one-tap\",\n  endpoints: {\n    oneTapCallback: createAuthEndpoint(\n      \"/one-tap/callback\",\n      {\n        method: \"POST\",\n        body: z.object({\n          idToken: z.string().meta({\n            description: \"Google ID token, which the client obtains from the One Tap API\"\n          })\n        }),\n        metadata: {\n          openapi: {\n            summary: \"One tap callback\",\n            description: \"Use this endpoint to authenticate with Google One Tap\",\n            responses: {\n              200: {\n                description: \"Successful response\",\n                content: {\n                  \"application/json\": {\n                    schema: {\n                      type: \"object\",\n                      properties: {\n                        session: {\n                          $ref: \"#/components/schemas/Session\"\n                        },\n                        user: {\n                          $ref: \"#/components/schemas/User\"\n                        }\n                      }\n                    }\n                  }\n                }\n              },\n              400: {\n                description: \"Invalid token\"\n              }\n            }\n          }\n        }\n      },\n      async (ctx) => {\n        const { idToken } = ctx.body;\n        let payload;\n        try {\n          const JWKS = createRemoteJWKSet(\n            new URL(\"https://www.googleapis.com/oauth2/v3/certs\")\n          );\n          const { payload: verifiedPayload } = await jwtVerify(\n            idToken,\n            JWKS,\n            {\n              issuer: [\"https://accounts.google.com\", \"accounts.google.com\"],\n              audience: options?.clientId || ctx.context.options.socialProviders?.google?.clientId\n            }\n          );\n          payload = verifiedPayload;\n        } catch (error) {\n          throw new APIError(\"BAD_REQUEST\", {\n            message: \"invalid id token\"\n          });\n        }\n        const { email, email_verified, name, picture, sub } = payload;\n        if (!email) {\n          return ctx.json({ error: \"Email not available in token\" });\n        }\n        const user = await ctx.context.internalAdapter.findUserByEmail(email);\n        if (!user) {\n          if (options?.disableSignup) {\n            throw new APIError(\"BAD_GATEWAY\", {\n              message: \"User not found\"\n            });\n          }\n          const newUser = await ctx.context.internalAdapter.createOAuthUser(\n            {\n              email,\n              emailVerified: typeof email_verified === \"boolean\" ? email_verified : toBoolean(email_verified),\n              name,\n              image: picture\n            },\n            {\n              providerId: \"google\",\n              accountId: sub\n            },\n            ctx\n          );\n          if (!newUser) {\n            throw new APIError(\"INTERNAL_SERVER_ERROR\", {\n              message: \"Could not create user\"\n            });\n          }\n          const session2 = await ctx.context.internalAdapter.createSession(\n            newUser.user.id,\n            ctx\n          );\n          await setSessionCookie(ctx, {\n            user: newUser.user,\n            session: session2\n          });\n          return ctx.json({\n            token: session2.token,\n            user: {\n              id: newUser.user.id,\n              email: newUser.user.email,\n              emailVerified: newUser.user.emailVerified,\n              name: newUser.user.name,\n              image: newUser.user.image,\n              createdAt: newUser.user.createdAt,\n              updatedAt: newUser.user.updatedAt\n            }\n          });\n        }\n        const account = await ctx.context.internalAdapter.findAccount(sub);\n        if (!account) {\n          const accountLinking = ctx.context.options.account?.accountLinking;\n          const shouldLinkAccount = accountLinking?.enabled && (accountLinking.trustedProviders?.includes(\"google\") || email_verified);\n          if (shouldLinkAccount) {\n            await ctx.context.internalAdapter.linkAccount({\n              userId: user.user.id,\n              providerId: \"google\",\n              accountId: sub,\n              scope: \"openid,profile,email\",\n              idToken\n            });\n          } else {\n            throw new APIError(\"UNAUTHORIZED\", {\n              message: \"Google sub doesn't match\"\n            });\n          }\n        }\n        const session = await ctx.context.internalAdapter.createSession(\n          user.user.id,\n          ctx\n        );\n        await setSessionCookie(ctx, {\n          user: user.user,\n          session\n        });\n        return ctx.json({\n          token: session.token,\n          user: {\n            id: user.user.id,\n            email: user.user.email,\n            emailVerified: user.user.emailVerified,\n            name: user.user.name,\n            image: user.user.image,\n            createdAt: user.user.createdAt,\n            updatedAt: user.user.updatedAt\n          }\n        });\n      }\n    )\n  }\n});\n\nexport { oneTap };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAKA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS,UAAU,KAAK;IACtB,OAAO,UAAU,UAAU,UAAU;AACvC;AAEA,MAAM,SAAS,CAAC,UAAY,CAAC;QAC3B,IAAI;QACJ,WAAW;YACT,gBAAgB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAC/B,qBACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,SAAS,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACvB,aAAa;oBACf;gBACF;gBACA,UAAU;oBACR,SAAS;wBACP,SAAS;wBACT,aAAa;wBACb,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,SAAS;oDACP,MAAM;gDACR;gDACA,MAAM;oDACJ,MAAM;gDACR;4CACF;wCACF;oCACF;gCACF;4BACF;4BACA,KAAK;gCACH,aAAa;4BACf;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,IAAI;gBAC5B,IAAI;gBACJ,IAAI;oBACF,MAAM,OAAO,CAAA,GAAA,+JAAA,CAAA,qBAAkB,AAAD,EAC5B,IAAI,IAAI;oBAEV,MAAM,EAAE,SAAS,eAAe,EAAE,GAAG,MAAM,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EACjD,SACA,MACA;wBACE,QAAQ;4BAAC;4BAA+B;yBAAsB;wBAC9D,UAAU,SAAS,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE,QAAQ;oBAC9E;oBAEF,UAAU;gBACZ,EAAE,OAAO,OAAO;oBACd,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,SAAS;oBACX;gBACF;gBACA,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG;gBACtD,IAAI,CAAC,OAAO;oBACV,OAAO,IAAI,IAAI,CAAC;wBAAE,OAAO;oBAA+B;gBAC1D;gBACA,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAAC;gBAC/D,IAAI,CAAC,MAAM;oBACT,IAAI,SAAS,eAAe;wBAC1B,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,SAAS;wBACX;oBACF;oBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,CAC/D;wBACE;wBACA,eAAe,OAAO,mBAAmB,YAAY,iBAAiB,UAAU;wBAChF;wBACA,OAAO;oBACT,GACA;wBACE,YAAY;wBACZ,WAAW;oBACb,GACA;oBAEF,IAAI,CAAC,SAAS;wBACZ,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,yBAAyB;4BAC1C,SAAS;wBACX;oBACF;oBACA,MAAM,WAAW,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC9D,QAAQ,IAAI,CAAC,EAAE,EACf;oBAEF,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;wBAC1B,MAAM,QAAQ,IAAI;wBAClB,SAAS;oBACX;oBACA,OAAO,IAAI,IAAI,CAAC;wBACd,OAAO,SAAS,KAAK;wBACrB,MAAM;4BACJ,IAAI,QAAQ,IAAI,CAAC,EAAE;4BACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;4BACzB,eAAe,QAAQ,IAAI,CAAC,aAAa;4BACzC,MAAM,QAAQ,IAAI,CAAC,IAAI;4BACvB,OAAO,QAAQ,IAAI,CAAC,KAAK;4BACzB,WAAW,QAAQ,IAAI,CAAC,SAAS;4BACjC,WAAW,QAAQ,IAAI,CAAC,SAAS;wBACnC;oBACF;gBACF;gBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;gBAC9D,IAAI,CAAC,SAAS;oBACZ,MAAM,iBAAiB,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;oBACpD,MAAM,oBAAoB,gBAAgB,WAAW,CAAC,eAAe,gBAAgB,EAAE,SAAS,aAAa,cAAc;oBAC3H,IAAI,mBAAmB;wBACrB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;4BAC5C,QAAQ,KAAK,IAAI,CAAC,EAAE;4BACpB,YAAY;4BACZ,WAAW;4BACX,OAAO;4BACP;wBACF;oBACF,OAAO;wBACL,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;4BACjC,SAAS;wBACX;oBACF;gBACF;gBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,aAAa,CAC7D,KAAK,IAAI,CAAC,EAAE,EACZ;gBAEF,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;oBAC1B,MAAM,KAAK,IAAI;oBACf;gBACF;gBACA,OAAO,IAAI,IAAI,CAAC;oBACd,OAAO,QAAQ,KAAK;oBACpB,MAAM;wBACJ,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB,eAAe,KAAK,IAAI,CAAC,aAAa;wBACtC,MAAM,KAAK,IAAI,CAAC,IAAI;wBACpB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB,WAAW,KAAK,IAAI,CAAC,SAAS;wBAC9B,WAAW,KAAK,IAAI,CAAC,SAAS;oBAChC;gBACF;YACF;QAEJ;IACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5670, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/oauth-proxy/index.mjs"], "sourcesContent": ["import * as z from 'zod/v4';\nimport 'better-call';\nimport { i as createAuthMiddleware, j as createAuthEndpoint, o as originCheck } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport { e as env } from '../../shared/better-auth.8zoxzg-F.mjs';\nimport '@better-auth/utils/base64';\nimport '@better-auth/utils/hmac';\nimport { g as getOrigin } from '../../shared/better-auth.VTXNLFMT.mjs';\nimport '@better-auth/utils/binary';\nimport '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport { symmetricEncrypt, symmetricDecrypt } from '../../crypto/index.mjs';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '@better-auth/utils/hash';\nimport '@better-fetch/fetch';\nimport 'jose';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-auth/utils/random';\nimport '../../cookies/index.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport 'jose/errors';\n\nfunction getVenderBaseURL() {\n  const vercel = env.VERCEL_URL ? `https://${env.VERCEL_URL}` : void 0;\n  const netlify = env.NETLIFY_URL;\n  const render = env.RENDER_URL;\n  const aws = env.AWS_LAMBDA_FUNCTION_NAME;\n  const google = env.GOOGLE_CLOUD_FUNCTION_NAME;\n  const azure = env.AZURE_FUNCTION_NAME;\n  return vercel || netlify || render || aws || google || azure;\n}\nconst oAuthProxy = (opts) => {\n  const resolveCurrentURL = (ctx) => {\n    return new URL(\n      opts?.currentURL || ctx.request?.url || getVenderBaseURL() || ctx.context.baseURL\n    );\n  };\n  return {\n    id: \"oauth-proxy\",\n    endpoints: {\n      oAuthProxy: createAuthEndpoint(\n        \"/oauth-proxy-callback\",\n        {\n          method: \"GET\",\n          query: z.object({\n            callbackURL: z.string().meta({\n              description: \"The URL to redirect to after the proxy\"\n            }),\n            cookies: z.string().meta({\n              description: \"The cookies to set after the proxy\"\n            })\n          }),\n          use: [originCheck((ctx) => ctx.query.callbackURL)],\n          metadata: {\n            openapi: {\n              description: \"OAuth Proxy Callback\",\n              parameters: [\n                {\n                  in: \"query\",\n                  name: \"callbackURL\",\n                  required: true,\n                  description: \"The URL to redirect to after the proxy\"\n                },\n                {\n                  in: \"query\",\n                  name: \"cookies\",\n                  required: true,\n                  description: \"The cookies to set after the proxy\"\n                }\n              ],\n              responses: {\n                302: {\n                  description: \"Redirect\",\n                  headers: {\n                    Location: {\n                      description: \"The URL to redirect to\",\n                      schema: {\n                        type: \"string\"\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const cookies = ctx.query.cookies;\n          const decryptedCookies = await symmetricDecrypt({\n            key: ctx.context.secret,\n            data: cookies\n          }).catch((e) => {\n            ctx.context.logger.error(e);\n            return null;\n          });\n          const error = ctx.context.options.onAPIError?.errorURL || `${ctx.context.options.baseURL}/api/auth/error`;\n          if (!decryptedCookies) {\n            throw ctx.redirect(\n              `${error}?error=OAuthProxy - Invalid cookies or secret`\n            );\n          }\n          const isSecureContext = resolveCurrentURL(ctx).protocol === \"https:\";\n          const prefix = ctx.context.options.advanced?.cookiePrefix || \"better-auth\";\n          const cookieToSet = isSecureContext ? decryptedCookies : decryptedCookies.replace(\"Secure;\", \"\").replace(`__Secure-${prefix}`, prefix);\n          ctx.setHeader(\"set-cookie\", cookieToSet);\n          throw ctx.redirect(ctx.query.callbackURL);\n        }\n      )\n    },\n    hooks: {\n      after: [\n        {\n          matcher(context) {\n            return context.path?.startsWith(\"/callback\") || context.path?.startsWith(\"/oauth2/callback\");\n          },\n          handler: createAuthMiddleware(async (ctx) => {\n            const headers = ctx.context.responseHeaders;\n            const location = headers?.get(\"location\");\n            if (location?.includes(\"/oauth-proxy-callback?callbackURL\")) {\n              if (!location.startsWith(\"http\")) {\n                return;\n              }\n              const locationURL = new URL(location);\n              const origin = locationURL.origin;\n              if (origin === getOrigin(ctx.context.baseURL)) {\n                const newLocation = locationURL.searchParams.get(\"callbackURL\");\n                if (!newLocation) {\n                  return;\n                }\n                ctx.setHeader(\"location\", newLocation);\n                return;\n              }\n              const setCookies = headers?.get(\"set-cookie\");\n              if (!setCookies) {\n                return;\n              }\n              const encryptedCookies = await symmetricEncrypt({\n                key: ctx.context.secret,\n                data: setCookies\n              });\n              const locationWithCookies = `${location}&cookies=${encodeURIComponent(\n                encryptedCookies\n              )}`;\n              ctx.setHeader(\"location\", locationWithCookies);\n            }\n          })\n        }\n      ],\n      before: [\n        {\n          matcher(context) {\n            return context.path?.startsWith(\"/sign-in/social\") || context.path?.startsWith(\"/sign-in/oauth2\");\n          },\n          handler: createAuthMiddleware(async (ctx) => {\n            const skipProxy = ctx.request?.headers.get(\"x-skip-oauth-proxy\");\n            if (skipProxy) {\n              return;\n            }\n            const url = resolveCurrentURL(ctx);\n            const productionURL = opts?.productionURL || env.BETTER_AUTH_URL;\n            if (productionURL === ctx.context.options.baseURL) {\n              return;\n            }\n            ctx.body.callbackURL = `${url.origin}${ctx.context.options.basePath || \"/api/auth\"}/oauth-proxy-callback?callbackURL=${encodeURIComponent(\n              ctx.body.callbackURL || ctx.context.baseURL\n            )}`;\n            return {\n              context: ctx\n            };\n          })\n        }\n      ]\n    }\n  };\n};\n\nexport { oAuthProxy };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS;IACP,MAAM,SAAS,oLAAA,CAAA,IAAG,CAAC,UAAU,GAAG,CAAC,QAAQ,EAAE,oLAAA,CAAA,IAAG,CAAC,UAAU,EAAE,GAAG,KAAK;IACnE,MAAM,UAAU,oLAAA,CAAA,IAAG,CAAC,WAAW;IAC/B,MAAM,SAAS,oLAAA,CAAA,IAAG,CAAC,UAAU;IAC7B,MAAM,MAAM,oLAAA,CAAA,IAAG,CAAC,wBAAwB;IACxC,MAAM,SAAS,oLAAA,CAAA,IAAG,CAAC,0BAA0B;IAC7C,MAAM,QAAQ,oLAAA,CAAA,IAAG,CAAC,mBAAmB;IACrC,OAAO,UAAU,WAAW,UAAU,OAAO,UAAU;AACzD;AACA,MAAM,aAAa,CAAC;IAClB,MAAM,oBAAoB,CAAC;QACzB,OAAO,IAAI,IACT,MAAM,cAAc,IAAI,OAAO,EAAE,OAAO,sBAAsB,IAAI,OAAO,CAAC,OAAO;IAErF;IACA,OAAO;QACL,IAAI;QACJ,WAAW;YACT,YAAY,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAC3B,yBACA;gBACE,QAAQ;gBACR,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACd,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBAC3B,aAAa;oBACf;oBACA,SAAS,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,CAAC;wBACvB,aAAa;oBACf;gBACF;gBACA,KAAK;oBAAC,CAAA,GAAA,iLAAA,CAAA,IAAW,AAAD,EAAE,CAAC,MAAQ,IAAI,KAAK,CAAC,WAAW;iBAAE;gBAClD,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,YAAY;4BACV;gCACE,IAAI;gCACJ,MAAM;gCACN,UAAU;gCACV,aAAa;4BACf;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,UAAU;gCACV,aAAa;4BACf;yBACD;wBACD,WAAW;4BACT,KAAK;gCACH,aAAa;gCACb,SAAS;oCACP,UAAU;wCACR,aAAa;wCACb,QAAQ;4CACN,MAAM;wCACR;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,UAAU,IAAI,KAAK,CAAC,OAAO;gBACjC,MAAM,mBAAmB,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC9C,KAAK,IAAI,OAAO,CAAC,MAAM;oBACvB,MAAM;gBACR,GAAG,KAAK,CAAC,CAAC;oBACR,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;oBACzB,OAAO;gBACT;gBACA,MAAM,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC;gBACzG,IAAI,CAAC,kBAAkB;oBACrB,MAAM,IAAI,QAAQ,CAChB,GAAG,MAAM,6CAA6C,CAAC;gBAE3D;gBACA,MAAM,kBAAkB,kBAAkB,KAAK,QAAQ,KAAK;gBAC5D,MAAM,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,gBAAgB;gBAC7D,MAAM,cAAc,kBAAkB,mBAAmB,iBAAiB,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,CAAC,SAAS,EAAE,QAAQ,EAAE;gBAC/H,IAAI,SAAS,CAAC,cAAc;gBAC5B,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,WAAW;YAC1C;QAEJ;QACA,OAAO;YACL,OAAO;gBACL;oBACE,SAAQ,OAAO;wBACb,OAAO,QAAQ,IAAI,EAAE,WAAW,gBAAgB,QAAQ,IAAI,EAAE,WAAW;oBAC3E;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,UAAU,IAAI,OAAO,CAAC,eAAe;wBAC3C,MAAM,WAAW,SAAS,IAAI;wBAC9B,IAAI,UAAU,SAAS,sCAAsC;4BAC3D,IAAI,CAAC,SAAS,UAAU,CAAC,SAAS;gCAChC;4BACF;4BACA,MAAM,cAAc,IAAI,IAAI;4BAC5B,MAAM,SAAS,YAAY,MAAM;4BACjC,IAAI,WAAW,CAAA,GAAA,iLAAA,CAAA,IAAS,AAAD,EAAE,IAAI,OAAO,CAAC,OAAO,GAAG;gCAC7C,MAAM,cAAc,YAAY,YAAY,CAAC,GAAG,CAAC;gCACjD,IAAI,CAAC,aAAa;oCAChB;gCACF;gCACA,IAAI,SAAS,CAAC,YAAY;gCAC1B;4BACF;4BACA,MAAM,aAAa,SAAS,IAAI;4BAChC,IAAI,CAAC,YAAY;gCACf;4BACF;4BACA,MAAM,mBAAmB,MAAM,CAAA,GAAA,4KAAA,CAAA,mBAAgB,AAAD,EAAE;gCAC9C,KAAK,IAAI,OAAO,CAAC,MAAM;gCACvB,MAAM;4BACR;4BACA,MAAM,sBAAsB,GAAG,SAAS,SAAS,EAAE,mBACjD,mBACC;4BACH,IAAI,SAAS,CAAC,YAAY;wBAC5B;oBACF;gBACF;aACD;YACD,QAAQ;gBACN;oBACE,SAAQ,OAAO;wBACb,OAAO,QAAQ,IAAI,EAAE,WAAW,sBAAsB,QAAQ,IAAI,EAAE,WAAW;oBACjF;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,YAAY,IAAI,OAAO,EAAE,QAAQ,IAAI;wBAC3C,IAAI,WAAW;4BACb;wBACF;wBACA,MAAM,MAAM,kBAAkB;wBAC9B,MAAM,gBAAgB,MAAM,iBAAiB,oLAAA,CAAA,IAAG,CAAC,eAAe;wBAChE,IAAI,kBAAkB,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;4BACjD;wBACF;wBACA,IAAI,IAAI,CAAC,WAAW,GAAG,GAAG,IAAI,MAAM,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,IAAI,YAAY,kCAAkC,EAAE,mBACrH,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,OAAO,CAAC,OAAO,GAC1C;wBACH,OAAO;4BACL,SAAS;wBACX;oBACF;gBACF;aACD;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5875, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/custom-session/index.mjs"], "sourcesContent": ["import * as z from 'zod/v4';\nimport 'better-call';\nimport { j as createAuthEndpoint, m as getSession } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '@better-auth/utils/base64';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '@better-auth/utils/hash';\nimport '../../crypto/index.mjs';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-auth/utils/random';\nimport '@better-fetch/fetch';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../cookies/index.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport 'jose/errors';\n\nconst customSession = (fn, options) => {\n  return {\n    id: \"custom-session\",\n    endpoints: {\n      getSession: createAuthEndpoint(\n        \"/get-session\",\n        {\n          method: \"GET\",\n          query: z.optional(\n            z.object({\n              /**\n               * If cookie cache is enabled, it will disable the cache\n               * and fetch the session from the database\n               */\n              disableCookieCache: z.boolean().meta({\n                description: \"Disable cookie cache and fetch session from database\"\n              }).or(z.string().transform((v) => v === \"true\")).optional(),\n              disableRefresh: z.boolean().meta({\n                description: \"Disable session refresh. Useful for checking session status, without updating the session\"\n              }).optional()\n            })\n          ),\n          metadata: {\n            CUSTOM_SESSION: true,\n            openapi: {\n              description: \"Get custom session data\",\n              responses: {\n                \"200\": {\n                  description: \"Success\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"array\",\n                        nullable: true,\n                        items: {\n                          $ref: \"#/components/schemas/Session\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          requireHeaders: true\n        },\n        async (ctx) => {\n          const session = await getSession()({\n            ...ctx,\n            asResponse: false,\n            headers: ctx.headers,\n            returnHeaders: true\n          }).catch((e) => {\n            return null;\n          });\n          if (!session?.response) {\n            return ctx.json(null);\n          }\n          const fnResult = await fn(session.response, ctx);\n          session.headers.forEach((value, key) => {\n            ctx.setHeader(key, value);\n          });\n          return ctx.json(fnResult);\n        }\n      )\n    }\n  };\n};\n\nexport { customSession };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,gBAAgB,CAAC,IAAI;IACzB,OAAO;QACL,IAAI;QACJ,WAAW;YACT,YAAY,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAC3B,gBACA;gBACE,QAAQ;gBACR,OAAO,CAAA,GAAA,mLAAA,CAAA,WAAU,AAAD,EACd,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACP;;;eAGC,GACD,oBAAoB,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,IAAI,CAAC;wBACnC,aAAa;oBACf,GAAG,EAAE,CAAC,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,SAAS,CAAC,CAAC,IAAM,MAAM,SAAS,QAAQ;oBACzD,gBAAgB,CAAA,GAAA,mLAAA,CAAA,UAAS,AAAD,IAAI,IAAI,CAAC;wBAC/B,aAAa;oBACf,GAAG,QAAQ;gBACb;gBAEF,UAAU;oBACR,gBAAgB;oBAChB,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,OAAO;gCACL,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,UAAU;4CACV,OAAO;gDACL,MAAM;4CACR;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;gBACA,gBAAgB;YAClB,GACA,OAAO;gBACL,MAAM,UAAU,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD,IAAI;oBACjC,GAAG,GAAG;oBACN,YAAY;oBACZ,SAAS,IAAI,OAAO;oBACpB,eAAe;gBACjB,GAAG,KAAK,CAAC,CAAC;oBACR,OAAO;gBACT;gBACA,IAAI,CAAC,SAAS,UAAU;oBACtB,OAAO,IAAI,IAAI,CAAC;gBAClB;gBACA,MAAM,WAAW,MAAM,GAAG,QAAQ,QAAQ,EAAE;gBAC5C,QAAQ,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;oBAC9B,IAAI,SAAS,CAAC,KAAK;gBACrB;gBACA,OAAO,IAAI,IAAI,CAAC;YAClB;QAEJ;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5997, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/open-api/index.mjs"], "sourcesContent": ["import { ZodObject, ZodType, ZodOptional, z, ZodString, ZodNumber, ZodBoolean, ZodArray } from 'zod/v4';\nimport { getEndpoints } from '../../api/index.mjs';\nimport '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '@better-auth/utils/random';\nimport { APIError } from 'better-call';\nimport '@better-auth/utils/hash';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport '@better-auth/utils/base64';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport { g as getAuthTables } from '../../shared/better-auth.DORkW_Ge.mjs';\nimport 'kysely';\nimport { j as createAuthEndpoint } from '../../shared/better-auth.D4HhkCZJ.mjs';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport 'defu';\nimport '../../cookies/index.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.DcfNPS8q.mjs';\nimport '../../crypto/index.mjs';\nimport '@better-fetch/fetch';\nimport 'jose/errors';\n\nfunction getTypeFromZodType(zodType) {\n  if (zodType instanceof ZodString) {\n    return \"string\";\n  } else if (zodType instanceof ZodNumber) {\n    return \"number\";\n  } else if (zodType instanceof ZodBoolean) {\n    return \"boolean\";\n  } else if (zodType instanceof ZodObject) {\n    return \"object\";\n  } else if (zodType instanceof ZodArray) {\n    return \"array\";\n  }\n  return \"string\";\n}\nfunction getFieldSchema(field) {\n  const schema = {\n    type: field.type === \"date\" ? \"string\" : field.type\n  };\n  if (field.defaultValue !== void 0) {\n    schema.default = typeof field.defaultValue === \"function\" ? \"Generated at runtime\" : field.defaultValue;\n  }\n  if (field.input === false) {\n    schema.readOnly = true;\n  }\n  return schema;\n}\nfunction getParameters(options) {\n  const parameters = [];\n  if (options.metadata?.openapi?.parameters) {\n    parameters.push(...options.metadata.openapi.parameters);\n    return parameters;\n  }\n  if (options.query instanceof ZodObject) {\n    Object.entries(options.query.shape).forEach(([key, value]) => {\n      if (value instanceof ZodType) {\n        parameters.push({\n          name: key,\n          in: \"query\",\n          schema: {\n            type: getTypeFromZodType(value),\n            ...\"minLength\" in value && value.minLength ? {\n              minLength: value.minLength\n            } : {},\n            description: value.description\n          }\n        });\n      }\n    });\n  }\n  return parameters;\n}\nfunction getRequestBody(options) {\n  if (options.metadata?.openapi?.requestBody) {\n    return options.metadata.openapi.requestBody;\n  }\n  if (!options.body) return void 0;\n  if (options.body instanceof ZodObject || options.body instanceof ZodOptional) {\n    const shape = options.body.shape;\n    if (!shape) return void 0;\n    const properties = {};\n    const required = [];\n    Object.entries(shape).forEach(([key, value]) => {\n      if (value instanceof ZodType) {\n        properties[key] = {\n          type: getTypeFromZodType(value),\n          description: value.description\n        };\n        if (!(value instanceof z.ZodOptional)) {\n          required.push(key);\n        }\n      }\n    });\n    return {\n      required: options.body instanceof ZodOptional ? false : options.body ? true : false,\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties,\n            required\n          }\n        }\n      }\n    };\n  }\n  return void 0;\n}\nfunction getResponse(responses) {\n  return {\n    \"400\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            },\n            required: [\"message\"]\n          }\n        }\n      },\n      description: \"Bad Request. Usually due to missing parameters, or invalid parameters.\"\n    },\n    \"401\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            },\n            required: [\"message\"]\n          }\n        }\n      },\n      description: \"Unauthorized. Due to missing or invalid authentication.\"\n    },\n    \"403\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            }\n          }\n        }\n      },\n      description: \"Forbidden. You do not have permission to access this resource or to perform this action.\"\n    },\n    \"404\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            }\n          }\n        }\n      },\n      description: \"Not Found. The requested resource was not found.\"\n    },\n    \"429\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            }\n          }\n        }\n      },\n      description: \"Too Many Requests. You have exceeded the rate limit. Try again later.\"\n    },\n    \"500\": {\n      content: {\n        \"application/json\": {\n          schema: {\n            type: \"object\",\n            properties: {\n              message: {\n                type: \"string\"\n              }\n            }\n          }\n        }\n      },\n      description: \"Internal Server Error. This is a problem with the server that you cannot fix.\"\n    },\n    ...responses\n  };\n}\nfunction toOpenApiPath(path) {\n  return path.split(\"/\").map((part) => part.startsWith(\":\") ? `{${part.slice(1)}}` : part).join(\"/\");\n}\nasync function generator(ctx, options) {\n  const baseEndpoints = getEndpoints(ctx, {\n    ...options,\n    plugins: []\n  });\n  const tables = getAuthTables(options);\n  const models = Object.entries(tables).reduce((acc, [key, value]) => {\n    const modelName = key.charAt(0).toUpperCase() + key.slice(1);\n    const fields = value.fields;\n    const required = [];\n    const properties = {\n      id: { type: \"string\" }\n    };\n    Object.entries(fields).forEach(([fieldKey, fieldValue]) => {\n      if (!fieldValue) return;\n      properties[fieldKey] = getFieldSchema(fieldValue);\n      if (fieldValue.required && fieldValue.input !== false) {\n        required.push(fieldKey);\n      }\n    });\n    acc[modelName] = {\n      type: \"object\",\n      properties,\n      ...required.length > 0 ? { required } : {}\n    };\n    return acc;\n  }, {});\n  const components = {\n    schemas: {\n      ...models\n    }\n  };\n  const paths = {};\n  Object.entries(baseEndpoints.api).forEach(([_, value]) => {\n    if (ctx.options.disabledPaths?.includes(value.path)) return;\n    const options2 = value.options;\n    if (options2.metadata?.SERVER_ONLY) return;\n    const path = toOpenApiPath(value.path);\n    if (options2.method === \"GET\") {\n      paths[path] = {\n        get: {\n          tags: [\"Default\", ...options2.metadata?.openapi?.tags || []],\n          description: options2.metadata?.openapi?.description,\n          operationId: options2.metadata?.openapi?.operationId,\n          security: [\n            {\n              bearerAuth: []\n            }\n          ],\n          parameters: getParameters(options2),\n          responses: getResponse(options2.metadata?.openapi?.responses)\n        }\n      };\n    }\n    if (options2.method === \"POST\") {\n      const body = getRequestBody(options2);\n      paths[path] = {\n        post: {\n          tags: [\"Default\", ...options2.metadata?.openapi?.tags || []],\n          description: options2.metadata?.openapi?.description,\n          operationId: options2.metadata?.openapi?.operationId,\n          security: [\n            {\n              bearerAuth: []\n            }\n          ],\n          parameters: getParameters(options2),\n          ...body ? { requestBody: body } : {\n            requestBody: {\n              //set body none\n              content: {\n                \"application/json\": {\n                  schema: {\n                    type: \"object\",\n                    properties: {}\n                  }\n                }\n              }\n            }\n          },\n          responses: getResponse(options2.metadata?.openapi?.responses)\n        }\n      };\n    }\n  });\n  for (const plugin of options.plugins || []) {\n    if (plugin.id === \"open-api\") {\n      continue;\n    }\n    const pluginEndpoints = getEndpoints(ctx, {\n      ...options,\n      plugins: [plugin]\n    });\n    const api = Object.keys(pluginEndpoints.api).map((key) => {\n      if (baseEndpoints.api[key] === void 0) {\n        return pluginEndpoints.api[key];\n      }\n      return null;\n    }).filter((x) => x !== null);\n    Object.entries(api).forEach(([key, value]) => {\n      if (ctx.options.disabledPaths?.includes(value.path)) return;\n      const options2 = value.options;\n      if (options2.metadata?.SERVER_ONLY) return;\n      const path = toOpenApiPath(value.path);\n      if (options2.method === \"GET\") {\n        paths[path] = {\n          get: {\n            tags: options2.metadata?.openapi?.tags || [\n              plugin.id.charAt(0).toUpperCase() + plugin.id.slice(1)\n            ],\n            description: options2.metadata?.openapi?.description,\n            operationId: options2.metadata?.openapi?.operationId,\n            security: [\n              {\n                bearerAuth: []\n              }\n            ],\n            parameters: getParameters(options2),\n            responses: getResponse(options2.metadata?.openapi?.responses)\n          }\n        };\n      }\n      if (options2.method === \"POST\") {\n        paths[path] = {\n          post: {\n            tags: options2.metadata?.openapi?.tags || [\n              plugin.id.charAt(0).toUpperCase() + plugin.id.slice(1)\n            ],\n            description: options2.metadata?.openapi?.description,\n            operationId: options2.metadata?.openapi?.operationId,\n            security: [\n              {\n                bearerAuth: []\n              }\n            ],\n            parameters: getParameters(options2),\n            requestBody: getRequestBody(options2),\n            responses: getResponse(options2.metadata?.openapi?.responses)\n          }\n        };\n      }\n    });\n  }\n  const res = {\n    openapi: \"3.1.1\",\n    info: {\n      title: \"Better Auth\",\n      description: \"API Reference for your Better Auth Instance\",\n      version: \"1.1.0\"\n    },\n    components: {\n      ...components,\n      securitySchemes: {\n        apiKeyCookie: {\n          type: \"apiKey\",\n          in: \"cookie\",\n          name: \"apiKeyCookie\",\n          description: \"API Key authentication via cookie\"\n        },\n        bearerAuth: {\n          type: \"http\",\n          scheme: \"bearer\",\n          description: \"Bearer token authentication\"\n        }\n      }\n    },\n    security: [\n      {\n        apiKeyCookie: [],\n        bearerAuth: []\n      }\n    ],\n    servers: [\n      {\n        url: ctx.baseURL\n      }\n    ],\n    tags: [\n      {\n        name: \"Default\",\n        description: \"Default endpoints that are included with Better Auth by default. These endpoints are not part of any plugin.\"\n      }\n    ],\n    paths\n  };\n  return res;\n}\n\nconst logo = `<svg width=\"75\" height=\"75\" viewBox=\"0 0 75 75\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n<rect width=\"75\" height=\"75\" fill=\"url(#pattern0_21_12)\"/>\n<defs>\n<pattern id=\"pattern0_21_12\" patternContentUnits=\"objectBoundingBox\" width=\"1\" height=\"1\">\n<use xlink:href=\"#image0_21_12\" transform=\"scale(0.00094697)\"/>\n</pattern>\n<image id=\"image0_21_12\" width=\"1056\" height=\"1056\" xlink:href=\"data:image/jpeg;base64,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\"/>\n</defs>\n</svg>\n`;\n\nconst getHTML = (apiReference) => `<!doctype html>\n<html>\n  <head>\n    <title>Scalar API Reference</title>\n    <meta charset=\"utf-8\" />\n    <meta\n      name=\"viewport\"\n      content=\"width=device-width, initial-scale=1\" />\n  </head>\n  <body>\n    <script\n      id=\"api-reference\"\n      type=\"application/json\">\n    ${JSON.stringify(apiReference)}\n    <\\/script>\n\t <script>\n      var configuration = {\n\t  \tfavicon: \"data:image/svg+xml;utf8,${encodeURIComponent(logo)}\",\n\t   \ttheme: \"saturn\",\n        metaData: {\n\t\t\ttitle: \"Better Auth API\",\n\t\t\tdescription: \"API Reference for your Better Auth Instance\",\n\t\t}\n      }\n\n      document.getElementById('api-reference').dataset.configuration =\n        JSON.stringify(configuration)\n    <\\/script>\n\t  <script src=\"https://cdn.jsdelivr.net/npm/@scalar/api-reference\"><\\/script>\n  </body>\n</html>`;\nconst openAPI = (options) => {\n  const path = options?.path ?? \"/reference\";\n  return {\n    id: \"open-api\",\n    endpoints: {\n      generateOpenAPISchema: createAuthEndpoint(\n        \"/open-api/generate-schema\",\n        {\n          method: \"GET\"\n        },\n        async (ctx) => {\n          const schema = await generator(ctx.context, ctx.context.options);\n          return ctx.json(schema);\n        }\n      ),\n      openAPIReference: createAuthEndpoint(\n        path,\n        {\n          method: \"GET\",\n          metadata: {\n            isAction: false\n          }\n        },\n        async (ctx) => {\n          if (options?.disableDefaultReference) {\n            throw new APIError(\"NOT_FOUND\");\n          }\n          const schema = await generator(ctx.context, ctx.context.options);\n          return new Response(getHTML(schema), {\n            headers: {\n              \"Content-Type\": \"text/html\"\n            }\n          });\n        }\n      )\n    }\n  };\n};\n\nexport { openAPI };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS,mBAAmB,OAAO;IACjC,IAAI,mBAAmB,mLAAA,CAAA,YAAS,EAAE;QAChC,OAAO;IACT,OAAO,IAAI,mBAAmB,mLAAA,CAAA,YAAS,EAAE;QACvC,OAAO;IACT,OAAO,IAAI,mBAAmB,mLAAA,CAAA,aAAU,EAAE;QACxC,OAAO;IACT,OAAO,IAAI,mBAAmB,mLAAA,CAAA,YAAS,EAAE;QACvC,OAAO;IACT,OAAO,IAAI,mBAAmB,mLAAA,CAAA,WAAQ,EAAE;QACtC,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,eAAe,KAAK;IAC3B,MAAM,SAAS;QACb,MAAM,MAAM,IAAI,KAAK,SAAS,WAAW,MAAM,IAAI;IACrD;IACA,IAAI,MAAM,YAAY,KAAK,KAAK,GAAG;QACjC,OAAO,OAAO,GAAG,OAAO,MAAM,YAAY,KAAK,aAAa,yBAAyB,MAAM,YAAY;IACzG;IACA,IAAI,MAAM,KAAK,KAAK,OAAO;QACzB,OAAO,QAAQ,GAAG;IACpB;IACA,OAAO;AACT;AACA,SAAS,cAAc,OAAO;IAC5B,MAAM,aAAa,EAAE;IACrB,IAAI,QAAQ,QAAQ,EAAE,SAAS,YAAY;QACzC,WAAW,IAAI,IAAI,QAAQ,QAAQ,CAAC,OAAO,CAAC,UAAU;QACtD,OAAO;IACT;IACA,IAAI,QAAQ,KAAK,YAAY,mLAAA,CAAA,YAAS,EAAE;QACtC,OAAO,OAAO,CAAC,QAAQ,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACvD,IAAI,iBAAiB,mLAAA,CAAA,UAAO,EAAE;gBAC5B,WAAW,IAAI,CAAC;oBACd,MAAM;oBACN,IAAI;oBACJ,QAAQ;wBACN,MAAM,mBAAmB;wBACzB,GAAG,eAAe,SAAS,MAAM,SAAS,GAAG;4BAC3C,WAAW,MAAM,SAAS;wBAC5B,IAAI,CAAC,CAAC;wBACN,aAAa,MAAM,WAAW;oBAChC;gBACF;YACF;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,eAAe,OAAO;IAC7B,IAAI,QAAQ,QAAQ,EAAE,SAAS,aAAa;QAC1C,OAAO,QAAQ,QAAQ,CAAC,OAAO,CAAC,WAAW;IAC7C;IACA,IAAI,CAAC,QAAQ,IAAI,EAAE,OAAO,KAAK;IAC/B,IAAI,QAAQ,IAAI,YAAY,mLAAA,CAAA,YAAS,IAAI,QAAQ,IAAI,YAAY,mLAAA,CAAA,cAAW,EAAE;QAC5E,MAAM,QAAQ,QAAQ,IAAI,CAAC,KAAK;QAChC,IAAI,CAAC,OAAO,OAAO,KAAK;QACxB,MAAM,aAAa,CAAC;QACpB,MAAM,WAAW,EAAE;QACnB,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACzC,IAAI,iBAAiB,mLAAA,CAAA,UAAO,EAAE;gBAC5B,UAAU,CAAC,IAAI,GAAG;oBAChB,MAAM,mBAAmB;oBACzB,aAAa,MAAM,WAAW;gBAChC;gBACA,IAAI,CAAC,CAAC,iBAAiB,iNAAA,CAAA,IAAC,CAAC,WAAW,GAAG;oBACrC,SAAS,IAAI,CAAC;gBAChB;YACF;QACF;QACA,OAAO;YACL,UAAU,QAAQ,IAAI,YAAY,mLAAA,CAAA,cAAW,GAAG,QAAQ,QAAQ,IAAI,GAAG,OAAO;YAC9E,SAAS;gBACP,oBAAoB;oBAClB,QAAQ;wBACN,MAAM;wBACN;wBACA;oBACF;gBACF;YACF;QACF;IACF;IACA,OAAO,KAAK;AACd;AACA,SAAS,YAAY,SAAS;IAC5B,OAAO;QACL,OAAO;YACL,SAAS;gBACP,oBAAoB;oBAClB,QAAQ;wBACN,MAAM;wBACN,YAAY;4BACV,SAAS;gCACP,MAAM;4BACR;wBACF;wBACA,UAAU;4BAAC;yBAAU;oBACvB;gBACF;YACF;YACA,aAAa;QACf;QACA,OAAO;YACL,SAAS;gBACP,oBAAoB;oBAClB,QAAQ;wBACN,MAAM;wBACN,YAAY;4BACV,SAAS;gCACP,MAAM;4BACR;wBACF;wBACA,UAAU;4BAAC;yBAAU;oBACvB;gBACF;YACF;YACA,aAAa;QACf;QACA,OAAO;YACL,SAAS;gBACP,oBAAoB;oBAClB,QAAQ;wBACN,MAAM;wBACN,YAAY;4BACV,SAAS;gCACP,MAAM;4BACR;wBACF;oBACF;gBACF;YACF;YACA,aAAa;QACf;QACA,OAAO;YACL,SAAS;gBACP,oBAAoB;oBAClB,QAAQ;wBACN,MAAM;wBACN,YAAY;4BACV,SAAS;gCACP,MAAM;4BACR;wBACF;oBACF;gBACF;YACF;YACA,aAAa;QACf;QACA,OAAO;YACL,SAAS;gBACP,oBAAoB;oBAClB,QAAQ;wBACN,MAAM;wBACN,YAAY;4BACV,SAAS;gCACP,MAAM;4BACR;wBACF;oBACF;gBACF;YACF;YACA,aAAa;QACf;QACA,OAAO;YACL,SAAS;gBACP,oBAAoB;oBAClB,QAAQ;wBACN,MAAM;wBACN,YAAY;4BACV,SAAS;gCACP,MAAM;4BACR;wBACF;oBACF;gBACF;YACF;YACA,aAAa;QACf;QACA,GAAG,SAAS;IACd;AACF;AACA,SAAS,cAAc,IAAI;IACzB,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,OAAS,KAAK,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC;AAChG;AACA,eAAe,UAAU,GAAG,EAAE,OAAO;IACnC,MAAM,gBAAgB,CAAA,GAAA,yKAAA,CAAA,eAAY,AAAD,EAAE,KAAK;QACtC,GAAG,OAAO;QACV,SAAS,EAAE;IACb;IACA,MAAM,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAa,AAAD,EAAE;IAC7B,MAAM,SAAS,OAAO,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM;QAC7D,MAAM,YAAY,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;QAC1D,MAAM,SAAS,MAAM,MAAM;QAC3B,MAAM,WAAW,EAAE;QACnB,MAAM,aAAa;YACjB,IAAI;gBAAE,MAAM;YAAS;QACvB;QACA,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,UAAU,WAAW;YACpD,IAAI,CAAC,YAAY;YACjB,UAAU,CAAC,SAAS,GAAG,eAAe;YACtC,IAAI,WAAW,QAAQ,IAAI,WAAW,KAAK,KAAK,OAAO;gBACrD,SAAS,IAAI,CAAC;YAChB;QACF;QACA,GAAG,CAAC,UAAU,GAAG;YACf,MAAM;YACN;YACA,GAAG,SAAS,MAAM,GAAG,IAAI;gBAAE;YAAS,IAAI,CAAC,CAAC;QAC5C;QACA,OAAO;IACT,GAAG,CAAC;IACJ,MAAM,aAAa;QACjB,SAAS;YACP,GAAG,MAAM;QACX;IACF;IACA,MAAM,QAAQ,CAAC;IACf,OAAO,OAAO,CAAC,cAAc,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM;QACnD,IAAI,IAAI,OAAO,CAAC,aAAa,EAAE,SAAS,MAAM,IAAI,GAAG;QACrD,MAAM,WAAW,MAAM,OAAO;QAC9B,IAAI,SAAS,QAAQ,EAAE,aAAa;QACpC,MAAM,OAAO,cAAc,MAAM,IAAI;QACrC,IAAI,SAAS,MAAM,KAAK,OAAO;YAC7B,KAAK,CAAC,KAAK,GAAG;gBACZ,KAAK;oBACH,MAAM;wBAAC;2BAAc,SAAS,QAAQ,EAAE,SAAS,QAAQ,EAAE;qBAAC;oBAC5D,aAAa,SAAS,QAAQ,EAAE,SAAS;oBACzC,aAAa,SAAS,QAAQ,EAAE,SAAS;oBACzC,UAAU;wBACR;4BACE,YAAY,EAAE;wBAChB;qBACD;oBACD,YAAY,cAAc;oBAC1B,WAAW,YAAY,SAAS,QAAQ,EAAE,SAAS;gBACrD;YACF;QACF;QACA,IAAI,SAAS,MAAM,KAAK,QAAQ;YAC9B,MAAM,OAAO,eAAe;YAC5B,KAAK,CAAC,KAAK,GAAG;gBACZ,MAAM;oBACJ,MAAM;wBAAC;2BAAc,SAAS,QAAQ,EAAE,SAAS,QAAQ,EAAE;qBAAC;oBAC5D,aAAa,SAAS,QAAQ,EAAE,SAAS;oBACzC,aAAa,SAAS,QAAQ,EAAE,SAAS;oBACzC,UAAU;wBACR;4BACE,YAAY,EAAE;wBAChB;qBACD;oBACD,YAAY,cAAc;oBAC1B,GAAG,OAAO;wBAAE,aAAa;oBAAK,IAAI;wBAChC,aAAa;4BACX,eAAe;4BACf,SAAS;gCACP,oBAAoB;oCAClB,QAAQ;wCACN,MAAM;wCACN,YAAY,CAAC;oCACf;gCACF;4BACF;wBACF;oBACF,CAAC;oBACD,WAAW,YAAY,SAAS,QAAQ,EAAE,SAAS;gBACrD;YACF;QACF;IACF;IACA,KAAK,MAAM,UAAU,QAAQ,OAAO,IAAI,EAAE,CAAE;QAC1C,IAAI,OAAO,EAAE,KAAK,YAAY;YAC5B;QACF;QACA,MAAM,kBAAkB,CAAA,GAAA,yKAAA,CAAA,eAAY,AAAD,EAAE,KAAK;YACxC,GAAG,OAAO;YACV,SAAS;gBAAC;aAAO;QACnB;QACA,MAAM,MAAM,OAAO,IAAI,CAAC,gBAAgB,GAAG,EAAE,GAAG,CAAC,CAAC;YAChD,IAAI,cAAc,GAAG,CAAC,IAAI,KAAK,KAAK,GAAG;gBACrC,OAAO,gBAAgB,GAAG,CAAC,IAAI;YACjC;YACA,OAAO;QACT,GAAG,MAAM,CAAC,CAAC,IAAM,MAAM;QACvB,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACvC,IAAI,IAAI,OAAO,CAAC,aAAa,EAAE,SAAS,MAAM,IAAI,GAAG;YACrD,MAAM,WAAW,MAAM,OAAO;YAC9B,IAAI,SAAS,QAAQ,EAAE,aAAa;YACpC,MAAM,OAAO,cAAc,MAAM,IAAI;YACrC,IAAI,SAAS,MAAM,KAAK,OAAO;gBAC7B,KAAK,CAAC,KAAK,GAAG;oBACZ,KAAK;wBACH,MAAM,SAAS,QAAQ,EAAE,SAAS,QAAQ;4BACxC,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,EAAE,CAAC,KAAK,CAAC;yBACrD;wBACD,aAAa,SAAS,QAAQ,EAAE,SAAS;wBACzC,aAAa,SAAS,QAAQ,EAAE,SAAS;wBACzC,UAAU;4BACR;gCACE,YAAY,EAAE;4BAChB;yBACD;wBACD,YAAY,cAAc;wBAC1B,WAAW,YAAY,SAAS,QAAQ,EAAE,SAAS;oBACrD;gBACF;YACF;YACA,IAAI,SAAS,MAAM,KAAK,QAAQ;gBAC9B,KAAK,CAAC,KAAK,GAAG;oBACZ,MAAM;wBACJ,MAAM,SAAS,QAAQ,EAAE,SAAS,QAAQ;4BACxC,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,EAAE,CAAC,KAAK,CAAC;yBACrD;wBACD,aAAa,SAAS,QAAQ,EAAE,SAAS;wBACzC,aAAa,SAAS,QAAQ,EAAE,SAAS;wBACzC,UAAU;4BACR;gCACE,YAAY,EAAE;4BAChB;yBACD;wBACD,YAAY,cAAc;wBAC1B,aAAa,eAAe;wBAC5B,WAAW,YAAY,SAAS,QAAQ,EAAE,SAAS;oBACrD;gBACF;YACF;QACF;IACF;IACA,MAAM,MAAM;QACV,SAAS;QACT,MAAM;YACJ,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,YAAY;YACV,GAAG,UAAU;YACb,iBAAiB;gBACf,cAAc;oBACZ,MAAM;oBACN,IAAI;oBACJ,MAAM;oBACN,aAAa;gBACf;gBACA,YAAY;oBACV,MAAM;oBACN,QAAQ;oBACR,aAAa;gBACf;YACF;QACF;QACA,UAAU;YACR;gBACE,cAAc,EAAE;gBAChB,YAAY,EAAE;YAChB;SACD;QACD,SAAS;YACP;gBACE,KAAK,IAAI,OAAO;YAClB;SACD;QACD,MAAM;YACJ;gBACE,MAAM;gBACN,aAAa;YACf;SACD;QACD;IACF;IACA,OAAO;AACT;AAEA,MAAM,OAAO,CAAC;;;;;;;;;AASd,CAAC;AAED,MAAM,UAAU,CAAC,eAAiB,CAAC;;;;;;;;;;;;;IAa/B,EAAE,KAAK,SAAS,CAAC,cAAc;;;;sCAIG,EAAE,mBAAmB,MAAM;;;;;;;;;;;;;OAa1D,CAAC;AACR,MAAM,UAAU,CAAC;IACf,MAAM,OAAO,SAAS,QAAQ;IAC9B,OAAO;QACL,IAAI;QACJ,WAAW;YACT,uBAAuB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACtC,6BACA;gBACE,QAAQ;YACV,GACA,OAAO;gBACL,MAAM,SAAS,MAAM,UAAU,IAAI,OAAO,EAAE,IAAI,OAAO,CAAC,OAAO;gBAC/D,OAAO,IAAI,IAAI,CAAC;YAClB;YAEF,kBAAkB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EACjC,MACA;gBACE,QAAQ;gBACR,UAAU;oBACR,UAAU;gBACZ;YACF,GACA,OAAO;gBACL,IAAI,SAAS,yBAAyB;oBACpC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC;gBACrB;gBACA,MAAM,SAAS,MAAM,UAAU,IAAI,OAAO,EAAE,IAAI,OAAO,CAAC,OAAO;gBAC/D,OAAO,IAAI,SAAS,QAAQ,SAAS;oBACnC,SAAS;wBACP,gBAAgB;oBAClB;gBACF;YACF;QAEJ;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6530, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/captcha/index.mjs"], "sourcesContent": ["import { betterFetch } from '@better-fetch/fetch';\n\nconst defaultEndpoints = [\n  \"/sign-up/email\",\n  \"/sign-in/email\",\n  \"/forget-password\"\n];\nconst Providers = {\n  CLOUDFLARE_TURNSTILE: \"cloudflare-turnstile\",\n  GOOGLE_RECAPTCHA: \"google-recaptcha\",\n  HCAPTCHA: \"hcaptcha\"\n};\nconst siteVerifyMap = {\n  [Providers.CLOUDFLARE_TURNSTILE]: \"https://challenges.cloudflare.com/turnstile/v0/siteverify\",\n  [Providers.GOOGLE_RECAPTCHA]: \"https://www.google.com/recaptcha/api/siteverify\",\n  [Providers.HCAPTCHA]: \"https://api.hcaptcha.com/siteverify\"\n};\n\nconst EXTERNAL_ERROR_CODES = {\n  VERIFICATION_FAILED: \"Captcha verification failed\",\n  MISSING_RESPONSE: \"Missing CAPTCHA response\",\n  UNKNOWN_ERROR: \"Something went wrong\"\n};\nconst INTERNAL_ERROR_CODES = {\n  MISSING_SECRET_KEY: \"Missing secret key\",\n  SERVICE_UNAVAILABLE: \"CAPTCHA service unavailable\"\n};\n\nconst middlewareResponse = ({ message, status }) => ({\n  response: new Response(\n    JSON.stringify({\n      message\n    }),\n    {\n      status\n    }\n  )\n});\n\nconst cloudflareTurnstile = async ({\n  siteVerifyURL,\n  captchaResponse,\n  secretKey,\n  remoteIP\n}) => {\n  const response = await betterFetch(siteVerifyURL, {\n    method: \"POST\",\n    headers: { \"Content-Type\": \"application/json\" },\n    body: JSON.stringify({\n      secret: secretKey,\n      response: captchaResponse,\n      ...remoteIP && { remoteip: remoteIP }\n    })\n  });\n  if (!response.data || response.error) {\n    throw new Error(INTERNAL_ERROR_CODES.SERVICE_UNAVAILABLE);\n  }\n  if (!response.data.success) {\n    return middlewareResponse({\n      message: EXTERNAL_ERROR_CODES.VERIFICATION_FAILED,\n      status: 403\n    });\n  }\n  return void 0;\n};\n\nconst encodeToURLParams = (obj) => {\n  if (typeof obj !== \"object\" || obj === null || Array.isArray(obj)) {\n    throw new Error(\"Input must be a non-null object.\");\n  }\n  const params = new URLSearchParams();\n  for (const [key, value] of Object.entries(obj)) {\n    if (value !== void 0 && value !== null) {\n      params.append(key, String(value));\n    }\n  }\n  return params.toString();\n};\n\nconst isV3 = (response) => {\n  return \"score\" in response && typeof response.score === \"number\";\n};\nconst googleRecaptcha = async ({\n  siteVerifyURL,\n  captchaResponse,\n  secretKey,\n  minScore = 0.5,\n  remoteIP\n}) => {\n  const response = await betterFetch(\n    siteVerifyURL,\n    {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/x-www-form-urlencoded\" },\n      body: encodeToURLParams({\n        secret: secretKey,\n        response: captchaResponse,\n        ...remoteIP && { remoteip: remoteIP }\n      })\n    }\n  );\n  if (!response.data || response.error) {\n    throw new Error(INTERNAL_ERROR_CODES.SERVICE_UNAVAILABLE);\n  }\n  if (!response.data.success || isV3(response.data) && response.data.score < minScore) {\n    return middlewareResponse({\n      message: EXTERNAL_ERROR_CODES.VERIFICATION_FAILED,\n      status: 403\n    });\n  }\n  return void 0;\n};\n\nconst hCaptcha = async ({\n  siteVerifyURL,\n  captchaResponse,\n  secretKey,\n  siteKey,\n  remoteIP\n}) => {\n  const response = await betterFetch(siteVerifyURL, {\n    method: \"POST\",\n    headers: { \"Content-Type\": \"application/x-www-form-urlencoded\" },\n    body: encodeToURLParams({\n      secret: secretKey,\n      response: captchaResponse,\n      ...siteKey && { sitekey: siteKey },\n      ...remoteIP && { remoteip: remoteIP }\n    })\n  });\n  if (!response.data || response.error) {\n    throw new Error(INTERNAL_ERROR_CODES.SERVICE_UNAVAILABLE);\n  }\n  if (!response.data.success) {\n    return middlewareResponse({\n      message: EXTERNAL_ERROR_CODES.VERIFICATION_FAILED,\n      status: 403\n    });\n  }\n  return void 0;\n};\n\nconst captcha = (options) => ({\n  id: \"captcha\",\n  onRequest: async (request, ctx) => {\n    try {\n      const endpoints = options.endpoints?.length ? options.endpoints : defaultEndpoints;\n      if (!endpoints.some((endpoint) => request.url.includes(endpoint)))\n        return void 0;\n      if (!options.secretKey) {\n        throw new Error(INTERNAL_ERROR_CODES.MISSING_SECRET_KEY);\n      }\n      const captchaResponse = request.headers.get(\"x-captcha-response\");\n      const remoteUserIP = request.headers.get(\"x-captcha-user-remote-ip\") ?? void 0;\n      if (!captchaResponse) {\n        return middlewareResponse({\n          message: EXTERNAL_ERROR_CODES.MISSING_RESPONSE,\n          status: 400\n        });\n      }\n      const siteVerifyURL = options.siteVerifyURLOverride || siteVerifyMap[options.provider];\n      const handlerParams = {\n        siteVerifyURL,\n        captchaResponse,\n        secretKey: options.secretKey,\n        remoteIP: remoteUserIP\n      };\n      if (options.provider === Providers.CLOUDFLARE_TURNSTILE) {\n        return await cloudflareTurnstile(handlerParams);\n      }\n      if (options.provider === Providers.GOOGLE_RECAPTCHA) {\n        return await googleRecaptcha({\n          ...handlerParams,\n          minScore: options.minScore\n        });\n      }\n      if (options.provider === Providers.HCAPTCHA) {\n        return await hCaptcha({\n          ...handlerParams,\n          siteKey: options.siteKey\n        });\n      }\n    } catch (_error) {\n      const errorMessage = _error instanceof Error ? _error.message : void 0;\n      ctx.logger.error(errorMessage ?? \"Unknown error\", {\n        endpoint: request.url,\n        message: _error\n      });\n      return middlewareResponse({\n        message: EXTERNAL_ERROR_CODES.UNKNOWN_ERROR,\n        status: 500\n      });\n    }\n  }\n});\n\nexport { captcha };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,mBAAmB;IACvB;IACA;IACA;CACD;AACD,MAAM,YAAY;IAChB,sBAAsB;IACtB,kBAAkB;IAClB,UAAU;AACZ;AACA,MAAM,gBAAgB;IACpB,CAAC,UAAU,oBAAoB,CAAC,EAAE;IAClC,CAAC,UAAU,gBAAgB,CAAC,EAAE;IAC9B,CAAC,UAAU,QAAQ,CAAC,EAAE;AACxB;AAEA,MAAM,uBAAuB;IAC3B,qBAAqB;IACrB,kBAAkB;IAClB,eAAe;AACjB;AACA,MAAM,uBAAuB;IAC3B,oBAAoB;IACpB,qBAAqB;AACvB;AAEA,MAAM,qBAAqB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,GAAK,CAAC;QACnD,UAAU,IAAI,SACZ,KAAK,SAAS,CAAC;YACb;QACF,IACA;YACE;QACF;IAEJ,CAAC;AAED,MAAM,sBAAsB,OAAO,EACjC,aAAa,EACb,eAAe,EACf,SAAS,EACT,QAAQ,EACT;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,eAAe;QAChD,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;YACnB,QAAQ;YACR,UAAU;YACV,GAAG,YAAY;gBAAE,UAAU;YAAS,CAAC;QACvC;IACF;IACA,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,KAAK,EAAE;QACpC,MAAM,IAAI,MAAM,qBAAqB,mBAAmB;IAC1D;IACA,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE;QAC1B,OAAO,mBAAmB;YACxB,SAAS,qBAAqB,mBAAmB;YACjD,QAAQ;QACV;IACF;IACA,OAAO,KAAK;AACd;AAEA,MAAM,oBAAoB,CAAC;IACzB,IAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,MAAM,OAAO,CAAC,MAAM;QACjE,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,SAAS,IAAI;IACnB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;QAC9C,IAAI,UAAU,KAAK,KAAK,UAAU,MAAM;YACtC,OAAO,MAAM,CAAC,KAAK,OAAO;QAC5B;IACF;IACA,OAAO,OAAO,QAAQ;AACxB;AAEA,MAAM,OAAO,CAAC;IACZ,OAAO,WAAW,YAAY,OAAO,SAAS,KAAK,KAAK;AAC1D;AACA,MAAM,kBAAkB,OAAO,EAC7B,aAAa,EACb,eAAe,EACf,SAAS,EACT,WAAW,GAAG,EACd,QAAQ,EACT;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC/B,eACA;QACE,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAoC;QAC/D,MAAM,kBAAkB;YACtB,QAAQ;YACR,UAAU;YACV,GAAG,YAAY;gBAAE,UAAU;YAAS,CAAC;QACvC;IACF;IAEF,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,KAAK,EAAE;QACpC,MAAM,IAAI,MAAM,qBAAqB,mBAAmB;IAC1D;IACA,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,KAAK,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC,KAAK,GAAG,UAAU;QACnF,OAAO,mBAAmB;YACxB,SAAS,qBAAqB,mBAAmB;YACjD,QAAQ;QACV;IACF;IACA,OAAO,KAAK;AACd;AAEA,MAAM,WAAW,OAAO,EACtB,aAAa,EACb,eAAe,EACf,SAAS,EACT,OAAO,EACP,QAAQ,EACT;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAAE,eAAe;QAChD,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAoC;QAC/D,MAAM,kBAAkB;YACtB,QAAQ;YACR,UAAU;YACV,GAAG,WAAW;gBAAE,SAAS;YAAQ,CAAC;YAClC,GAAG,YAAY;gBAAE,UAAU;YAAS,CAAC;QACvC;IACF;IACA,IAAI,CAAC,SAAS,IAAI,IAAI,SAAS,KAAK,EAAE;QACpC,MAAM,IAAI,MAAM,qBAAqB,mBAAmB;IAC1D;IACA,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE;QAC1B,OAAO,mBAAmB;YACxB,SAAS,qBAAqB,mBAAmB;YACjD,QAAQ;QACV;IACF;IACA,OAAO,KAAK;AACd;AAEA,MAAM,UAAU,CAAC,UAAY,CAAC;QAC5B,IAAI;QACJ,WAAW,OAAO,SAAS;YACzB,IAAI;gBACF,MAAM,YAAY,QAAQ,SAAS,EAAE,SAAS,QAAQ,SAAS,GAAG;gBAClE,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,WAAa,QAAQ,GAAG,CAAC,QAAQ,CAAC,YACrD,OAAO,KAAK;gBACd,IAAI,CAAC,QAAQ,SAAS,EAAE;oBACtB,MAAM,IAAI,MAAM,qBAAqB,kBAAkB;gBACzD;gBACA,MAAM,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC;gBAC5C,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC,+BAA+B,KAAK;gBAC7E,IAAI,CAAC,iBAAiB;oBACpB,OAAO,mBAAmB;wBACxB,SAAS,qBAAqB,gBAAgB;wBAC9C,QAAQ;oBACV;gBACF;gBACA,MAAM,gBAAgB,QAAQ,qBAAqB,IAAI,aAAa,CAAC,QAAQ,QAAQ,CAAC;gBACtF,MAAM,gBAAgB;oBACpB;oBACA;oBACA,WAAW,QAAQ,SAAS;oBAC5B,UAAU;gBACZ;gBACA,IAAI,QAAQ,QAAQ,KAAK,UAAU,oBAAoB,EAAE;oBACvD,OAAO,MAAM,oBAAoB;gBACnC;gBACA,IAAI,QAAQ,QAAQ,KAAK,UAAU,gBAAgB,EAAE;oBACnD,OAAO,MAAM,gBAAgB;wBAC3B,GAAG,aAAa;wBAChB,UAAU,QAAQ,QAAQ;oBAC5B;gBACF;gBACA,IAAI,QAAQ,QAAQ,KAAK,UAAU,QAAQ,EAAE;oBAC3C,OAAO,MAAM,SAAS;wBACpB,GAAG,aAAa;wBAChB,SAAS,QAAQ,OAAO;oBAC1B;gBACF;YACF,EAAE,OAAO,QAAQ;gBACf,MAAM,eAAe,kBAAkB,QAAQ,OAAO,OAAO,GAAG,KAAK;gBACrE,IAAI,MAAM,CAAC,KAAK,CAAC,gBAAgB,iBAAiB;oBAChD,UAAU,QAAQ,GAAG;oBACrB,SAAS;gBACX;gBACA,OAAO,mBAAmB;oBACxB,SAAS,qBAAqB,aAAa;oBAC3C,QAAQ;gBACV;YACF;QACF;IACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6718, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/haveibeenpwned/index.mjs"], "sourcesContent": ["import { APIError } from 'better-call';\nimport '../../shared/better-auth.D4HhkCZJ.mjs';\nimport 'zod/v4';\nimport '../../shared/better-auth.8zoxzg-F.mjs';\nimport '@better-auth/utils/base64';\nimport '@better-auth/utils/hmac';\nimport '@better-auth/utils/binary';\nimport '../../shared/better-auth.n2KFGwjY.mjs';\nimport '../../shared/better-auth.DBGfIDnh.mjs';\nimport 'defu';\nimport { createHash } from '@better-auth/utils/hash';\nimport { betterFetch } from '@better-fetch/fetch';\nimport '../../shared/better-auth.CW6D9eSx.mjs';\nimport '../../crypto/index.mjs';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport 'jose';\nimport '@noble/hashes/scrypt';\nimport '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport '../../shared/better-auth.B4Qoxdgc.mjs';\nimport '@better-auth/utils/random';\nimport '../../shared/better-auth.VTXNLFMT.mjs';\nimport '../../shared/better-auth.DdzSJf-n.mjs';\nimport '../../cookies/index.mjs';\nimport '../../shared/better-auth.tB5eU6EY.mjs';\nimport 'jose/errors';\n\nconst ERROR_CODES = {\n  PASSWORD_COMPROMISED: \"The password you entered has been compromised. Please choose a different password.\"\n};\nasync function checkPasswordCompromise(password, customMessage) {\n  if (!password) return;\n  const sha1Hash = (await createHash(\"SHA-1\", \"hex\").digest(password)).toUpperCase();\n  const prefix = sha1Hash.substring(0, 5);\n  const suffix = sha1Hash.substring(5);\n  try {\n    const { data, error } = await betterFetch(\n      `https://api.pwnedpasswords.com/range/${prefix}`,\n      {\n        headers: {\n          \"Add-Padding\": \"true\",\n          \"User-Agent\": \"BetterAuth Password Checker\"\n        }\n      }\n    );\n    if (error) {\n      throw new APIError(\"INTERNAL_SERVER_ERROR\", {\n        message: `Failed to check password. Status: ${error.status}`\n      });\n    }\n    const lines = data.split(\"\\n\");\n    const found = lines.some(\n      (line) => line.split(\":\")[0].toUpperCase() === suffix.toUpperCase()\n    );\n    if (found) {\n      throw new APIError(\"BAD_REQUEST\", {\n        message: customMessage || ERROR_CODES.PASSWORD_COMPROMISED,\n        code: \"PASSWORD_COMPROMISED\"\n      });\n    }\n  } catch (error) {\n    if (error instanceof APIError) throw error;\n    throw new APIError(\"INTERNAL_SERVER_ERROR\", {\n      message: \"Failed to check password. Please try again later.\"\n    });\n  }\n}\nconst haveIBeenPwned = (options) => ({\n  id: \"haveIBeenPwned\",\n  init(ctx) {\n    return {\n      context: {\n        password: {\n          ...ctx.password,\n          async hash(password) {\n            await checkPasswordCompromise(\n              password,\n              options?.customPasswordCompromisedMessage\n            );\n            return ctx.password.hash(password);\n          }\n        }\n      }\n    };\n  },\n  $ERROR_CODES: ERROR_CODES\n});\n\nexport { haveIBeenPwned };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,cAAc;IAClB,sBAAsB;AACxB;AACA,eAAe,wBAAwB,QAAQ,EAAE,aAAa;IAC5D,IAAI,CAAC,UAAU;IACf,MAAM,WAAW,CAAC,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,SAAS,OAAO,MAAM,CAAC,SAAS,EAAE,WAAW;IAChF,MAAM,SAAS,SAAS,SAAS,CAAC,GAAG;IACrC,MAAM,SAAS,SAAS,SAAS,CAAC;IAClC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EACtC,CAAC,qCAAqC,EAAE,QAAQ,EAChD;YACE,SAAS;gBACP,eAAe;gBACf,cAAc;YAChB;QACF;QAEF,IAAI,OAAO;YACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,yBAAyB;gBAC1C,SAAS,CAAC,kCAAkC,EAAE,MAAM,MAAM,EAAE;YAC9D;QACF;QACA,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,QAAQ,MAAM,IAAI,CACtB,CAAC,OAAS,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,OAAO,OAAO,WAAW;QAEnE,IAAI,OAAO;YACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;gBAChC,SAAS,iBAAiB,YAAY,oBAAoB;gBAC1D,MAAM;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,iJAAA,CAAA,WAAQ,EAAE,MAAM;QACrC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,yBAAyB;YAC1C,SAAS;QACX;IACF;AACF;AACA,MAAM,iBAAiB,CAAC,UAAY,CAAC;QACnC,IAAI;QACJ,MAAK,GAAG;YACN,OAAO;gBACL,SAAS;oBACP,UAAU;wBACR,GAAG,IAAI,QAAQ;wBACf,MAAM,MAAK,QAAQ;4BACjB,MAAM,wBACJ,UACA,SAAS;4BAEX,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC;wBAC3B;oBACF;gBACF;YACF;QACF;QACA,cAAc;IAChB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6830, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dropbox/madhukar/robynnv3/code/firegeo/firegeo/node_modules/better-auth/dist/plugins/index.mjs"], "sourcesContent": ["export { o as organization, p as parseRoles } from '../shared/better-auth.DR6QsC6j.mjs';\nexport { TWO_FACTOR_ERROR_CODES, twoFactor } from './two-factor/index.mjs';\nexport { USERNAME_ERROR_CODES, username } from './username/index.mjs';\nexport { bearer } from './bearer/index.mjs';\nimport { k as getSessionFromCtx, j as createAuthEndpoint, i as createAuthMiddleware } from '../shared/better-auth.D4HhkCZJ.mjs';\nexport { H as HIDE_METADATA, q as optionsMiddleware } from '../shared/better-auth.D4HhkCZJ.mjs';\nexport { magicLink } from './magic-link/index.mjs';\nexport { phoneNumber } from './phone-number/index.mjs';\nexport { anonymous } from './anonymous/index.mjs';\nexport { a as admin } from '../shared/better-auth.DygEm6OX.mjs';\nexport { genericOAuth } from './generic-oauth/index.mjs';\nexport { g as generateExportedKeyPair, j as jwt } from '../shared/better-auth.D1SaVuxk.mjs';\nexport { multiSession } from './multi-session/index.mjs';\nexport { emailOTP } from './email-otp/index.mjs';\nexport { oneTap } from './one-tap/index.mjs';\nexport { oAuthProxy } from './oauth-proxy/index.mjs';\nexport { customSession } from './custom-session/index.mjs';\nexport { openAPI } from './open-api/index.mjs';\nimport { o as oidcProvider, s as schema } from '../shared/better-auth.HDgvLN7B.mjs';\nexport { g as getClient, a as getMetadata } from '../shared/better-auth.HDgvLN7B.mjs';\nexport { captcha } from './captcha/index.mjs';\nexport { A as API_KEY_TABLE_NAME, E as ERROR_CODES, a as apiKey, d as defaultKeyHasher, o as oneTimeToken } from '../shared/better-auth.Bqt8-7ls.mjs';\nexport { haveIBeenPwned } from './haveibeenpwned/index.mjs';\nimport * as z from 'zod/v4';\nimport { APIError } from 'better-call';\nimport { a as isProduction } from '../shared/better-auth.8zoxzg-F.mjs';\nimport { base64 } from '@better-auth/utils/base64';\nimport '@better-auth/utils/hmac';\nimport { a as getBaseURL } from '../shared/better-auth.VTXNLFMT.mjs';\nimport '@better-auth/utils/binary';\nimport { parseSetCookieHeader } from '../cookies/index.mjs';\nimport '../shared/better-auth.n2KFGwjY.mjs';\nimport '../shared/better-auth.DGaVMVAI.mjs';\nimport './organization/access/index.mjs';\nimport '@better-auth/utils/random';\nimport { createHash } from '@better-auth/utils/hash';\nimport '@noble/ciphers/chacha';\nimport '@noble/ciphers/utils';\nimport '@noble/ciphers/webcrypto';\nimport { SignJWT } from 'jose';\nimport '@noble/hashes/scrypt';\nimport { subtle } from '@better-auth/utils';\nimport '@better-auth/utils/hex';\nimport '@noble/hashes/utils';\nimport { g as generateRandomString } from '../shared/better-auth.B4Qoxdgc.mjs';\nimport { a as logger } from '../shared/better-auth.DBGfIDnh.mjs';\nimport 'kysely';\nimport 'defu';\nimport '@better-auth/utils/otp';\nimport './admin/access/index.mjs';\nimport '@better-fetch/fetch';\nimport '../shared/better-auth.CGrHn1Ih.mjs';\nexport { t as twoFactorClient } from '../shared/better-auth.Ddw8bVyV.mjs';\nimport '../shared/better-auth.CW6D9eSx.mjs';\nimport '../shared/better-auth.DdzSJf-n.mjs';\nimport '../shared/better-auth.ffWeg50w.mjs';\nimport '../shared/better-auth.Dt0CvI2z.mjs';\nimport '../shared/better-auth.DXqcUO8W.mjs';\nimport '../crypto/index.mjs';\nimport '../shared/better-auth.YwDQhoPc.mjs';\nimport '../shared/better-auth.tB5eU6EY.mjs';\nimport 'jose/errors';\nimport '../shared/better-auth.DQI8AD7d.mjs';\nimport '../shared/better-auth.bkwPl2G4.mjs';\nimport '../api/index.mjs';\nimport '../shared/better-auth.DcfNPS8q.mjs';\nimport '../shared/better-auth.DORkW_Ge.mjs';\nimport './access/index.mjs';\n\nfunction redirectErrorURL(url, error, description) {\n  return `${url.includes(\"?\") ? \"&\" : \"?\"}error=${error}&error_description=${description}`;\n}\nasync function authorizeMCPOAuth(ctx, options) {\n  ctx.setHeader(\"Access-Control-Allow-Origin\", \"*\");\n  ctx.setHeader(\"Access-Control-Allow-Methods\", \"POST, OPTIONS\");\n  ctx.setHeader(\"Access-Control-Allow-Headers\", \"Content-Type, Authorization\");\n  ctx.setHeader(\"Access-Control-Max-Age\", \"86400\");\n  const opts = {\n    codeExpiresIn: 600,\n    defaultScope: \"openid\",\n    ...options,\n    scopes: [\n      \"openid\",\n      \"profile\",\n      \"email\",\n      \"offline_access\",\n      ...options?.scopes || []\n    ]\n  };\n  if (!ctx.request) {\n    throw new APIError(\"UNAUTHORIZED\", {\n      error_description: \"request not found\",\n      error: \"invalid_request\"\n    });\n  }\n  const session = await getSessionFromCtx(ctx);\n  if (!session) {\n    await ctx.setSignedCookie(\n      \"oidc_login_prompt\",\n      JSON.stringify(ctx.query),\n      ctx.context.secret,\n      {\n        maxAge: 600,\n        path: \"/\",\n        sameSite: \"lax\"\n      }\n    );\n    const queryFromURL = ctx.request.url?.split(\"?\")[1];\n    throw ctx.redirect(`${options.loginPage}?${queryFromURL}`);\n  }\n  const query = ctx.query;\n  console.log(query);\n  if (!query.client_id) {\n    throw ctx.redirect(`${ctx.context.baseURL}/error?error=invalid_client`);\n  }\n  if (!query.response_type) {\n    throw ctx.redirect(\n      redirectErrorURL(\n        `${ctx.context.baseURL}/error`,\n        \"invalid_request\",\n        \"response_type is required\"\n      )\n    );\n  }\n  const client = await ctx.context.adapter.findOne({\n    model: \"oauthApplication\",\n    where: [\n      {\n        field: \"clientId\",\n        value: ctx.query.client_id\n      }\n    ]\n  }).then((res) => {\n    if (!res) {\n      return null;\n    }\n    return {\n      ...res,\n      redirectURLs: res.redirectURLs.split(\",\"),\n      metadata: res.metadata ? JSON.parse(res.metadata) : {}\n    };\n  });\n  console.log(client);\n  if (!client) {\n    throw ctx.redirect(`${ctx.context.baseURL}/error?error=invalid_client`);\n  }\n  const redirectURI = client.redirectURLs.find(\n    (url) => url === ctx.query.redirect_uri\n  );\n  if (!redirectURI || !query.redirect_uri) {\n    throw new APIError(\"BAD_REQUEST\", {\n      message: \"Invalid redirect URI\"\n    });\n  }\n  if (client.disabled) {\n    throw ctx.redirect(`${ctx.context.baseURL}/error?error=client_disabled`);\n  }\n  if (query.response_type !== \"code\") {\n    throw ctx.redirect(\n      `${ctx.context.baseURL}/error?error=unsupported_response_type`\n    );\n  }\n  const requestScope = query.scope?.split(\" \").filter((s) => s) || opts.defaultScope.split(\" \");\n  const invalidScopes = requestScope.filter((scope) => {\n    return !opts.scopes.includes(scope);\n  });\n  if (invalidScopes.length) {\n    throw ctx.redirect(\n      redirectErrorURL(\n        query.redirect_uri,\n        \"invalid_scope\",\n        `The following scopes are invalid: ${invalidScopes.join(\", \")}`\n      )\n    );\n  }\n  if ((!query.code_challenge || !query.code_challenge_method) && options.requirePKCE) {\n    throw ctx.redirect(\n      redirectErrorURL(\n        query.redirect_uri,\n        \"invalid_request\",\n        \"pkce is required\"\n      )\n    );\n  }\n  if (!query.code_challenge_method) {\n    query.code_challenge_method = \"plain\";\n  }\n  if (![\n    \"s256\",\n    options.allowPlainCodeChallengeMethod ? \"plain\" : \"s256\"\n  ].includes(query.code_challenge_method?.toLowerCase() || \"\")) {\n    throw ctx.redirect(\n      redirectErrorURL(\n        query.redirect_uri,\n        \"invalid_request\",\n        \"invalid code_challenge method\"\n      )\n    );\n  }\n  const code = generateRandomString(32, \"a-z\", \"A-Z\", \"0-9\");\n  const codeExpiresInMs = opts.codeExpiresIn * 1e3;\n  const expiresAt = new Date(Date.now() + codeExpiresInMs);\n  try {\n    await ctx.context.internalAdapter.createVerificationValue(\n      {\n        value: JSON.stringify({\n          clientId: client.clientId,\n          redirectURI: query.redirect_uri,\n          scope: requestScope,\n          userId: session.user.id,\n          authTime: session.session.createdAt.getTime(),\n          /**\n           * If the prompt is set to `consent`, then we need\n           * to require the user to consent to the scopes.\n           *\n           * This means the code now needs to be treated as a\n           * consent request.\n           *\n           * once the user consents, the code will be updated\n           * with the actual code. This is to prevent the\n           * client from using the code before the user\n           * consents.\n           */\n          requireConsent: query.prompt === \"consent\",\n          state: query.prompt === \"consent\" ? query.state : null,\n          codeChallenge: query.code_challenge,\n          codeChallengeMethod: query.code_challenge_method,\n          nonce: query.nonce\n        }),\n        identifier: code,\n        expiresAt\n      },\n      ctx\n    );\n  } catch (e) {\n    throw ctx.redirect(\n      redirectErrorURL(\n        query.redirect_uri,\n        \"server_error\",\n        \"An error occurred while processing the request\"\n      )\n    );\n  }\n  const redirectURIWithCode = new URL(redirectURI);\n  redirectURIWithCode.searchParams.set(\"code\", code);\n  redirectURIWithCode.searchParams.set(\"state\", ctx.query.state);\n  if (query.prompt !== \"consent\") {\n    throw ctx.redirect(redirectURIWithCode.toString());\n  }\n  throw ctx.redirect(redirectURIWithCode.toString());\n}\n\nconst getMCPProviderMetadata = (ctx, options) => {\n  const issuer = ctx.context.options.baseURL;\n  const baseURL = ctx.context.baseURL;\n  if (!issuer || !baseURL) {\n    throw new APIError(\"INTERNAL_SERVER_ERROR\", {\n      error: \"invalid_issuer\",\n      error_description: \"issuer or baseURL is not set. If you're the app developer, please make sure to set the `baseURL` in your auth config.\"\n    });\n  }\n  return {\n    issuer,\n    authorization_endpoint: `${baseURL}/mcp/authorize`,\n    token_endpoint: `${baseURL}/mcp/token`,\n    userinfo_endpoint: `${baseURL}/mcp/userinfo`,\n    jwks_uri: `${baseURL}/mcp/jwks`,\n    registration_endpoint: `${baseURL}/mcp/register`,\n    scopes_supported: [\"openid\", \"profile\", \"email\", \"offline_access\"],\n    response_types_supported: [\"code\"],\n    response_modes_supported: [\"query\"],\n    grant_types_supported: [\"authorization_code\", \"refresh_token\"],\n    acr_values_supported: [\n      \"urn:mace:incommon:iap:silver\",\n      \"urn:mace:incommon:iap:bronze\"\n    ],\n    subject_types_supported: [\"public\"],\n    id_token_signing_alg_values_supported: [\"RS256\", \"none\"],\n    token_endpoint_auth_methods_supported: [\n      \"client_secret_basic\",\n      \"client_secret_post\",\n      \"none\"\n    ],\n    code_challenge_methods_supported: [\"S256\"],\n    claims_supported: [\n      \"sub\",\n      \"iss\",\n      \"aud\",\n      \"exp\",\n      \"nbf\",\n      \"iat\",\n      \"jti\",\n      \"email\",\n      \"email_verified\",\n      \"name\"\n    ],\n    ...options?.metadata\n  };\n};\nconst mcp = (options) => {\n  const opts = {\n    codeExpiresIn: 600,\n    defaultScope: \"openid\",\n    accessTokenExpiresIn: 3600,\n    refreshTokenExpiresIn: 604800,\n    allowPlainCodeChallengeMethod: true,\n    ...options.oidcConfig,\n    loginPage: options.loginPage,\n    scopes: [\n      \"openid\",\n      \"profile\",\n      \"email\",\n      \"offline_access\",\n      ...options.oidcConfig?.scopes || []\n    ]\n  };\n  const modelName = {\n    oauthClient: \"oauthApplication\",\n    oauthAccessToken: \"oauthAccessToken\"};\n  oidcProvider(opts);\n  return {\n    id: \"mcp\",\n    hooks: {\n      after: [\n        {\n          matcher() {\n            return true;\n          },\n          handler: createAuthMiddleware(async (ctx) => {\n            const cookie = await ctx.getSignedCookie(\n              \"oidc_login_prompt\",\n              ctx.context.secret\n            );\n            const cookieName = ctx.context.authCookies.sessionToken.name;\n            const parsedSetCookieHeader = parseSetCookieHeader(\n              ctx.context.responseHeaders?.get(\"set-cookie\") || \"\"\n            );\n            const hasSessionToken = parsedSetCookieHeader.has(cookieName);\n            if (!cookie || !hasSessionToken) {\n              return;\n            }\n            ctx.setCookie(\"oidc_login_prompt\", \"\", {\n              maxAge: 0\n            });\n            const sessionCookie = parsedSetCookieHeader.get(cookieName)?.value;\n            const sessionToken = sessionCookie?.split(\".\")[0];\n            if (!sessionToken) {\n              return;\n            }\n            const session = await ctx.context.internalAdapter.findSession(sessionToken);\n            if (!session) {\n              return;\n            }\n            ctx.query = JSON.parse(cookie);\n            ctx.query.prompt = \"consent\";\n            ctx.context.session = session;\n            const response = await authorizeMCPOAuth(ctx, opts).catch((e) => {\n              if (e instanceof APIError) {\n                if (e.statusCode === 302) {\n                  return ctx.json({\n                    redirect: true,\n                    //@ts-expect-error\n                    url: e.headers.get(\"location\")\n                  });\n                }\n              }\n              throw e;\n            });\n            return response;\n          })\n        }\n      ]\n    },\n    endpoints: {\n      getMcpOAuthConfig: createAuthEndpoint(\n        \"/.well-known/oauth-authorization-server\",\n        {\n          method: \"GET\",\n          metadata: {\n            client: false\n          }\n        },\n        async (c) => {\n          try {\n            const metadata = getMCPProviderMetadata(c, options);\n            return c.json(metadata);\n          } catch (e) {\n            console.log(e);\n            return c.json(null);\n          }\n        }\n      ),\n      mcpOAuthAuthroize: createAuthEndpoint(\n        \"/mcp/authorize\",\n        {\n          method: \"GET\",\n          query: z.record(z.string(), z.any()),\n          metadata: {\n            openapi: {\n              description: \"Authorize an OAuth2 request using MCP\",\n              responses: {\n                \"200\": {\n                  description: \"Authorization response generated successfully\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        additionalProperties: true,\n                        description: \"Authorization response, contents depend on the authorize function implementation\"\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          return authorizeMCPOAuth(ctx, opts);\n        }\n      ),\n      mcpOAuthToken: createAuthEndpoint(\n        \"/mcp/token\",\n        {\n          method: \"POST\",\n          body: z.record(z.any(), z.any()),\n          metadata: {\n            isAction: false\n          }\n        },\n        async (ctx) => {\n          ctx.setHeader(\"Access-Control-Allow-Origin\", \"*\");\n          ctx.setHeader(\"Access-Control-Allow-Methods\", \"POST, OPTIONS\");\n          ctx.setHeader(\n            \"Access-Control-Allow-Headers\",\n            \"Content-Type, Authorization\"\n          );\n          ctx.setHeader(\"Access-Control-Max-Age\", \"86400\");\n          let { body } = ctx;\n          if (!body) {\n            throw ctx.error(\"BAD_REQUEST\", {\n              error_description: \"request body not found\",\n              error: \"invalid_request\"\n            });\n          }\n          if (body instanceof FormData) {\n            body = Object.fromEntries(body.entries());\n          }\n          if (!(body instanceof Object)) {\n            throw new APIError(\"BAD_REQUEST\", {\n              error_description: \"request body is not an object\",\n              error: \"invalid_request\"\n            });\n          }\n          let { client_id, client_secret } = body;\n          const authorization = ctx.request?.headers.get(\"authorization\") || null;\n          if (authorization && !client_id && !client_secret && authorization.startsWith(\"Basic \")) {\n            try {\n              const encoded = authorization.replace(\"Basic \", \"\");\n              const decoded = new TextDecoder().decode(base64.decode(encoded));\n              if (!decoded.includes(\":\")) {\n                throw new APIError(\"UNAUTHORIZED\", {\n                  error_description: \"invalid authorization header format\",\n                  error: \"invalid_client\"\n                });\n              }\n              const [id, secret] = decoded.split(\":\");\n              if (!id || !secret) {\n                throw new APIError(\"UNAUTHORIZED\", {\n                  error_description: \"invalid authorization header format\",\n                  error: \"invalid_client\"\n                });\n              }\n              client_id = id;\n              client_secret = secret;\n            } catch (error) {\n              throw new APIError(\"UNAUTHORIZED\", {\n                error_description: \"invalid authorization header format\",\n                error: \"invalid_client\"\n              });\n            }\n          }\n          const {\n            grant_type,\n            code,\n            redirect_uri,\n            refresh_token,\n            code_verifier\n          } = body;\n          if (grant_type === \"refresh_token\") {\n            if (!refresh_token) {\n              throw new APIError(\"BAD_REQUEST\", {\n                error_description: \"refresh_token is required\",\n                error: \"invalid_request\"\n              });\n            }\n            const token = await ctx.context.adapter.findOne({\n              model: \"oauthAccessToken\",\n              where: [\n                {\n                  field: \"refreshToken\",\n                  value: refresh_token.toString()\n                }\n              ]\n            });\n            if (!token) {\n              throw new APIError(\"UNAUTHORIZED\", {\n                error_description: \"invalid refresh token\",\n                error: \"invalid_grant\"\n              });\n            }\n            if (token.clientId !== client_id?.toString()) {\n              throw new APIError(\"UNAUTHORIZED\", {\n                error_description: \"invalid client_id\",\n                error: \"invalid_client\"\n              });\n            }\n            if (token.refreshTokenExpiresAt < /* @__PURE__ */ new Date()) {\n              throw new APIError(\"UNAUTHORIZED\", {\n                error_description: \"refresh token expired\",\n                error: \"invalid_grant\"\n              });\n            }\n            const accessToken2 = generateRandomString(32, \"a-z\", \"A-Z\");\n            const newRefreshToken = generateRandomString(32, \"a-z\", \"A-Z\");\n            const accessTokenExpiresAt2 = new Date(\n              Date.now() + opts.accessTokenExpiresIn * 1e3\n            );\n            const refreshTokenExpiresAt2 = new Date(\n              Date.now() + opts.refreshTokenExpiresIn * 1e3\n            );\n            await ctx.context.adapter.create({\n              model: modelName.oauthAccessToken,\n              data: {\n                accessToken: accessToken2,\n                refreshToken: newRefreshToken,\n                accessTokenExpiresAt: accessTokenExpiresAt2,\n                refreshTokenExpiresAt: refreshTokenExpiresAt2,\n                clientId: client_id.toString(),\n                userId: token.userId,\n                scopes: token.scopes,\n                createdAt: /* @__PURE__ */ new Date(),\n                updatedAt: /* @__PURE__ */ new Date()\n              }\n            });\n            return ctx.json({\n              access_token: accessToken2,\n              token_type: \"bearer\",\n              expires_in: opts.accessTokenExpiresIn,\n              refresh_token: newRefreshToken,\n              scope: token.scopes\n            });\n          }\n          if (!code) {\n            throw new APIError(\"BAD_REQUEST\", {\n              error_description: \"code is required\",\n              error: \"invalid_request\"\n            });\n          }\n          if (opts.requirePKCE && !code_verifier) {\n            throw new APIError(\"BAD_REQUEST\", {\n              error_description: \"code verifier is missing\",\n              error: \"invalid_request\"\n            });\n          }\n          const verificationValue = await ctx.context.internalAdapter.findVerificationValue(\n            code.toString()\n          );\n          if (!verificationValue) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              error_description: \"invalid code\",\n              error: \"invalid_grant\"\n            });\n          }\n          if (verificationValue.expiresAt < /* @__PURE__ */ new Date()) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              error_description: \"code expired\",\n              error: \"invalid_grant\"\n            });\n          }\n          await ctx.context.internalAdapter.deleteVerificationValue(\n            verificationValue.id\n          );\n          if (!client_id) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              error_description: \"client_id is required\",\n              error: \"invalid_client\"\n            });\n          }\n          if (!grant_type) {\n            throw new APIError(\"BAD_REQUEST\", {\n              error_description: \"grant_type is required\",\n              error: \"invalid_request\"\n            });\n          }\n          if (grant_type !== \"authorization_code\") {\n            throw new APIError(\"BAD_REQUEST\", {\n              error_description: \"grant_type must be 'authorization_code'\",\n              error: \"unsupported_grant_type\"\n            });\n          }\n          if (!redirect_uri) {\n            throw new APIError(\"BAD_REQUEST\", {\n              error_description: \"redirect_uri is required\",\n              error: \"invalid_request\"\n            });\n          }\n          const client = await ctx.context.adapter.findOne({\n            model: modelName.oauthClient,\n            where: [{ field: \"clientId\", value: client_id.toString() }]\n          }).then((res) => {\n            if (!res) {\n              return null;\n            }\n            return {\n              ...res,\n              redirectURLs: res.redirectURLs.split(\",\"),\n              metadata: res.metadata ? JSON.parse(res.metadata) : {}\n            };\n          });\n          if (!client) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              error_description: \"invalid client_id\",\n              error: \"invalid_client\"\n            });\n          }\n          if (client.disabled) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              error_description: \"client is disabled\",\n              error: \"invalid_client\"\n            });\n          }\n          if (client.type === \"public\") {\n            if (!code_verifier) {\n              throw new APIError(\"BAD_REQUEST\", {\n                error_description: \"code verifier is required for public clients\",\n                error: \"invalid_request\"\n              });\n            }\n          } else {\n            if (!client_secret) {\n              throw new APIError(\"UNAUTHORIZED\", {\n                error_description: \"client_secret is required for confidential clients\",\n                error: \"invalid_client\"\n              });\n            }\n            const isValidSecret = client.clientSecret === client_secret.toString();\n            if (!isValidSecret) {\n              throw new APIError(\"UNAUTHORIZED\", {\n                error_description: \"invalid client_secret\",\n                error: \"invalid_client\"\n              });\n            }\n          }\n          const value = JSON.parse(\n            verificationValue.value\n          );\n          if (value.clientId !== client_id.toString()) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              error_description: \"invalid client_id\",\n              error: \"invalid_client\"\n            });\n          }\n          if (value.redirectURI !== redirect_uri.toString()) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              error_description: \"invalid redirect_uri\",\n              error: \"invalid_client\"\n            });\n          }\n          if (value.codeChallenge && !code_verifier) {\n            throw new APIError(\"BAD_REQUEST\", {\n              error_description: \"code verifier is missing\",\n              error: \"invalid_request\"\n            });\n          }\n          const challenge = value.codeChallengeMethod === \"plain\" ? code_verifier : await createHash(\"SHA-256\", \"base64urlnopad\").digest(\n            code_verifier\n          );\n          if (challenge !== value.codeChallenge) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              error_description: \"code verification failed\",\n              error: \"invalid_request\"\n            });\n          }\n          const requestedScopes = value.scope;\n          await ctx.context.internalAdapter.deleteVerificationValue(\n            verificationValue.id\n          );\n          const accessToken = generateRandomString(32, \"a-z\", \"A-Z\");\n          const refreshToken = generateRandomString(32, \"A-Z\", \"a-z\");\n          const accessTokenExpiresAt = new Date(\n            Date.now() + opts.accessTokenExpiresIn * 1e3\n          );\n          const refreshTokenExpiresAt = new Date(\n            Date.now() + opts.refreshTokenExpiresIn * 1e3\n          );\n          await ctx.context.adapter.create({\n            model: modelName.oauthAccessToken,\n            data: {\n              accessToken,\n              refreshToken,\n              accessTokenExpiresAt,\n              refreshTokenExpiresAt,\n              clientId: client_id.toString(),\n              userId: value.userId,\n              scopes: requestedScopes.join(\" \"),\n              createdAt: /* @__PURE__ */ new Date(),\n              updatedAt: /* @__PURE__ */ new Date()\n            }\n          });\n          const user = await ctx.context.internalAdapter.findUserById(\n            value.userId\n          );\n          if (!user) {\n            throw new APIError(\"UNAUTHORIZED\", {\n              error_description: \"user not found\",\n              error: \"invalid_grant\"\n            });\n          }\n          let secretKey = {\n            alg: \"HS256\",\n            key: await subtle.generateKey(\n              {\n                name: \"HMAC\",\n                hash: \"SHA-256\"\n              },\n              true,\n              [\"sign\", \"verify\"]\n            )\n          };\n          const profile = {\n            given_name: user.name.split(\" \")[0],\n            family_name: user.name.split(\" \")[1],\n            name: user.name,\n            profile: user.image,\n            updated_at: user.updatedAt.toISOString()\n          };\n          const email = {\n            email: user.email,\n            email_verified: user.emailVerified\n          };\n          const userClaims = {\n            ...requestedScopes.includes(\"profile\") ? profile : {},\n            ...requestedScopes.includes(\"email\") ? email : {}\n          };\n          const additionalUserClaims = opts.getAdditionalUserInfoClaim ? opts.getAdditionalUserInfoClaim(user, requestedScopes) : {};\n          const idToken = await new SignJWT({\n            sub: user.id,\n            aud: client_id.toString(),\n            iat: Date.now(),\n            auth_time: ctx.context.session?.session.createdAt.getTime(),\n            nonce: value.nonce,\n            acr: \"urn:mace:incommon:iap:silver\",\n            // default to silver - ⚠︎ this should be configurable and should be validated against the client's metadata\n            ...userClaims,\n            ...additionalUserClaims\n          }).setProtectedHeader({ alg: secretKey.alg }).setIssuedAt().setExpirationTime(\n            Math.floor(Date.now() / 1e3) + opts.accessTokenExpiresIn\n          ).sign(secretKey.key);\n          return ctx.json(\n            {\n              access_token: accessToken,\n              token_type: \"Bearer\",\n              expires_in: opts.accessTokenExpiresIn,\n              refresh_token: requestedScopes.includes(\"offline_access\") ? refreshToken : void 0,\n              scope: requestedScopes.join(\" \"),\n              id_token: requestedScopes.includes(\"openid\") ? idToken : void 0\n            },\n            {\n              headers: {\n                \"Cache-Control\": \"no-store\",\n                Pragma: \"no-cache\"\n              }\n            }\n          );\n        }\n      ),\n      registerMcpClient: createAuthEndpoint(\n        \"/mcp/register\",\n        {\n          method: \"POST\",\n          body: z.object({\n            redirect_uris: z.array(z.string()),\n            token_endpoint_auth_method: z.enum([\"none\", \"client_secret_basic\", \"client_secret_post\"]).default(\"client_secret_basic\").optional(),\n            grant_types: z.array(\n              z.enum([\n                \"authorization_code\",\n                \"implicit\",\n                \"password\",\n                \"client_credentials\",\n                \"refresh_token\",\n                \"urn:ietf:params:oauth:grant-type:jwt-bearer\",\n                \"urn:ietf:params:oauth:grant-type:saml2-bearer\"\n              ])\n            ).default([\"authorization_code\"]).optional(),\n            response_types: z.array(z.enum([\"code\", \"token\"])).default([\"code\"]).optional(),\n            client_name: z.string().optional(),\n            client_uri: z.string().optional(),\n            logo_uri: z.string().optional(),\n            scope: z.string().optional(),\n            contacts: z.array(z.string()).optional(),\n            tos_uri: z.string().optional(),\n            policy_uri: z.string().optional(),\n            jwks_uri: z.string().optional(),\n            jwks: z.record(z.string(), z.any()).optional(),\n            metadata: z.record(z.any(), z.any()).optional(),\n            software_id: z.string().optional(),\n            software_version: z.string().optional(),\n            software_statement: z.string().optional()\n          }),\n          metadata: {\n            openapi: {\n              description: \"Register an OAuth2 application\",\n              responses: {\n                \"200\": {\n                  description: \"OAuth2 application registered successfully\",\n                  content: {\n                    \"application/json\": {\n                      schema: {\n                        type: \"object\",\n                        properties: {\n                          name: {\n                            type: \"string\",\n                            description: \"Name of the OAuth2 application\"\n                          },\n                          icon: {\n                            type: \"string\",\n                            nullable: true,\n                            description: \"Icon URL for the application\"\n                          },\n                          metadata: {\n                            type: \"object\",\n                            additionalProperties: true,\n                            nullable: true,\n                            description: \"Additional metadata for the application\"\n                          },\n                          clientId: {\n                            type: \"string\",\n                            description: \"Unique identifier for the client\"\n                          },\n                          clientSecret: {\n                            type: \"string\",\n                            description: \"Secret key for the client. Not included for public clients.\"\n                          },\n                          redirectURLs: {\n                            type: \"array\",\n                            items: { type: \"string\", format: \"uri\" },\n                            description: \"List of allowed redirect URLs\"\n                          },\n                          type: {\n                            type: \"string\",\n                            description: \"Type of the client\",\n                            enum: [\"web\", \"public\"]\n                          },\n                          authenticationScheme: {\n                            type: \"string\",\n                            description: \"Authentication scheme used by the client\",\n                            enum: [\"client_secret\", \"none\"]\n                          },\n                          disabled: {\n                            type: \"boolean\",\n                            description: \"Whether the client is disabled\",\n                            enum: [false]\n                          },\n                          userId: {\n                            type: \"string\",\n                            nullable: true,\n                            description: \"ID of the user who registered the client, null if registered anonymously\"\n                          },\n                          createdAt: {\n                            type: \"string\",\n                            format: \"date-time\",\n                            description: \"Creation timestamp\"\n                          },\n                          updatedAt: {\n                            type: \"string\",\n                            format: \"date-time\",\n                            description: \"Last update timestamp\"\n                          }\n                        },\n                        required: [\n                          \"name\",\n                          \"clientId\",\n                          \"redirectURLs\",\n                          \"type\",\n                          \"authenticationScheme\",\n                          \"disabled\",\n                          \"createdAt\",\n                          \"updatedAt\"\n                        ]\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        async (ctx) => {\n          const body = ctx.body;\n          const session = await getSessionFromCtx(ctx);\n          ctx.setHeader(\"Access-Control-Allow-Origin\", \"*\");\n          ctx.setHeader(\"Access-Control-Allow-Methods\", \"POST, OPTIONS\");\n          ctx.setHeader(\n            \"Access-Control-Allow-Headers\",\n            \"Content-Type, Authorization\"\n          );\n          ctx.setHeader(\"Access-Control-Max-Age\", \"86400\");\n          ctx.headers?.set(\"Access-Control-Max-Age\", \"86400\");\n          if ((!body.grant_types || body.grant_types.includes(\"authorization_code\") || body.grant_types.includes(\"implicit\")) && (!body.redirect_uris || body.redirect_uris.length === 0)) {\n            throw new APIError(\"BAD_REQUEST\", {\n              error: \"invalid_redirect_uri\",\n              error_description: \"Redirect URIs are required for authorization_code and implicit grant types\"\n            });\n          }\n          if (body.grant_types && body.response_types) {\n            if (body.grant_types.includes(\"authorization_code\") && !body.response_types.includes(\"code\")) {\n              throw new APIError(\"BAD_REQUEST\", {\n                error: \"invalid_client_metadata\",\n                error_description: \"When 'authorization_code' grant type is used, 'code' response type must be included\"\n              });\n            }\n            if (body.grant_types.includes(\"implicit\") && !body.response_types.includes(\"token\")) {\n              throw new APIError(\"BAD_REQUEST\", {\n                error: \"invalid_client_metadata\",\n                error_description: \"When 'implicit' grant type is used, 'token' response type must be included\"\n              });\n            }\n          }\n          const clientId = opts.generateClientId?.() || generateRandomString(32, \"a-z\", \"A-Z\");\n          const clientSecret = opts.generateClientSecret?.() || generateRandomString(32, \"a-z\", \"A-Z\");\n          const clientType = body.token_endpoint_auth_method === \"none\" ? \"public\" : \"web\";\n          const finalClientSecret = clientType === \"public\" ? \"\" : clientSecret;\n          await ctx.context.adapter.create({\n            model: modelName.oauthClient,\n            data: {\n              name: body.client_name,\n              icon: body.logo_uri,\n              metadata: body.metadata ? JSON.stringify(body.metadata) : null,\n              clientId,\n              clientSecret: finalClientSecret,\n              redirectURLs: body.redirect_uris.join(\",\"),\n              type: clientType,\n              authenticationScheme: body.token_endpoint_auth_method || \"client_secret_basic\",\n              disabled: false,\n              userId: session?.session.userId,\n              createdAt: /* @__PURE__ */ new Date(),\n              updatedAt: /* @__PURE__ */ new Date()\n            }\n          });\n          const responseData = {\n            client_id: clientId,\n            client_id_issued_at: Math.floor(Date.now() / 1e3),\n            redirect_uris: body.redirect_uris,\n            token_endpoint_auth_method: body.token_endpoint_auth_method || \"client_secret_basic\",\n            grant_types: body.grant_types || [\"authorization_code\"],\n            response_types: body.response_types || [\"code\"],\n            client_name: body.client_name,\n            client_uri: body.client_uri,\n            logo_uri: body.logo_uri,\n            scope: body.scope,\n            contacts: body.contacts,\n            tos_uri: body.tos_uri,\n            policy_uri: body.policy_uri,\n            jwks_uri: body.jwks_uri,\n            jwks: body.jwks,\n            software_id: body.software_id,\n            software_version: body.software_version,\n            software_statement: body.software_statement,\n            metadata: body.metadata,\n            ...clientType !== \"public\" ? {\n              client_secret: finalClientSecret,\n              client_secret_expires_at: 0\n              // 0 means it doesn't expire\n            } : {}\n          };\n          return ctx.json(responseData, {\n            status: 201,\n            headers: {\n              \"Cache-Control\": \"no-store\",\n              Pragma: \"no-cache\"\n            }\n          });\n        }\n      ),\n      getMcpSession: createAuthEndpoint(\n        \"/mcp/get-session\",\n        {\n          method: \"GET\",\n          requireHeaders: true\n        },\n        async (c) => {\n          const accessToken = c.headers?.get(\"Authorization\")?.replace(\"Bearer \", \"\");\n          if (!accessToken) {\n            c.headers?.set(\"WWW-Authenticate\", \"Bearer\");\n            return c.json(null);\n          }\n          const accessTokenData = await c.context.adapter.findOne({\n            model: modelName.oauthAccessToken,\n            where: [\n              {\n                field: \"accessToken\",\n                value: accessToken\n              }\n            ]\n          });\n          if (!accessTokenData) {\n            return c.json(null);\n          }\n          return c.json(accessTokenData);\n        }\n      )\n    },\n    schema\n  };\n};\nconst withMcpAuth = (auth, handler) => {\n  return async (req) => {\n    const baseURL = getBaseURL(auth.options.baseURL, auth.options.basePath);\n    if (!baseURL && !isProduction) {\n      logger.warn(\"Unable to get the baseURL, please check your config!\");\n    }\n    const session = await auth.api.getMcpSession({\n      headers: req.headers\n    });\n    const wwwAuthenticateValue = `Bearer resource_metadata=${baseURL}/api/auth/.well-known/oauth-authorization-server`;\n    if (!session) {\n      return Response.json(\n        {\n          jsonrpc: \"2.0\",\n          error: {\n            code: -32e3,\n            message: \"Unauthorized: Authentication required\",\n            \"www-authenticate\": wwwAuthenticateValue\n          },\n          id: null\n        },\n        {\n          status: 401,\n          headers: {\n            \"WWW-Authenticate\": wwwAuthenticateValue\n          }\n        }\n      );\n    }\n    return handler(req, session);\n  };\n};\nconst oAuthDiscoveryMetadata = (auth) => {\n  return async (request) => {\n    const res = await auth.api.getMcpOAuthConfig();\n    return new Response(JSON.stringify(res), {\n      status: 200,\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Access-Control-Allow-Origin\": \"*\",\n        \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n        \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\",\n        \"Access-Control-Max-Age\": \"86400\"\n      }\n    });\n  };\n};\n\nexport { createAuthEndpoint, createAuthMiddleware, getMCPProviderMetadata, mcp, oAuthDiscoveryMetadata, oidcProvider, withMcpAuth };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAEA;AAAA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAS,iBAAiB,GAAG,EAAE,KAAK,EAAE,WAAW;IAC/C,OAAO,GAAG,IAAI,QAAQ,CAAC,OAAO,MAAM,IAAI,MAAM,EAAE,MAAM,mBAAmB,EAAE,aAAa;AAC1F;AACA,eAAe,kBAAkB,GAAG,EAAE,OAAO;IAC3C,IAAI,SAAS,CAAC,+BAA+B;IAC7C,IAAI,SAAS,CAAC,gCAAgC;IAC9C,IAAI,SAAS,CAAC,gCAAgC;IAC9C,IAAI,SAAS,CAAC,0BAA0B;IACxC,MAAM,OAAO;QACX,eAAe;QACf,cAAc;QACd,GAAG,OAAO;QACV,QAAQ;YACN;YACA;YACA;YACA;eACG,SAAS,UAAU,EAAE;SACzB;IACH;IACA,IAAI,CAAC,IAAI,OAAO,EAAE;QAChB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;YACjC,mBAAmB;YACnB,OAAO;QACT;IACF;IACA,MAAM,UAAU,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAiB,AAAD,EAAE;IACxC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,eAAe,CACvB,qBACA,KAAK,SAAS,CAAC,IAAI,KAAK,GACxB,IAAI,OAAO,CAAC,MAAM,EAClB;YACE,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;QAEF,MAAM,eAAe,IAAI,OAAO,CAAC,GAAG,EAAE,MAAM,IAAI,CAAC,EAAE;QACnD,MAAM,IAAI,QAAQ,CAAC,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,cAAc;IAC3D;IACA,MAAM,QAAQ,IAAI,KAAK;IACvB,QAAQ,GAAG,CAAC;IACZ,IAAI,CAAC,MAAM,SAAS,EAAE;QACpB,MAAM,IAAI,QAAQ,CAAC,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC;IACxE;IACA,IAAI,CAAC,MAAM,aAAa,EAAE;QACxB,MAAM,IAAI,QAAQ,CAChB,iBACE,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAC9B,mBACA;IAGN;IACA,MAAM,SAAS,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;QAC/C,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,OAAO,IAAI,KAAK,CAAC,SAAS;YAC5B;SACD;IACH,GAAG,IAAI,CAAC,CAAC;QACP,IAAI,CAAC,KAAK;YACR,OAAO;QACT;QACA,OAAO;YACL,GAAG,GAAG;YACN,cAAc,IAAI,YAAY,CAAC,KAAK,CAAC;YACrC,UAAU,IAAI,QAAQ,GAAG,KAAK,KAAK,CAAC,IAAI,QAAQ,IAAI,CAAC;QACvD;IACF;IACA,QAAQ,GAAG,CAAC;IACZ,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,QAAQ,CAAC,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC;IACxE;IACA,MAAM,cAAc,OAAO,YAAY,CAAC,IAAI,CAC1C,CAAC,MAAQ,QAAQ,IAAI,KAAK,CAAC,YAAY;IAEzC,IAAI,CAAC,eAAe,CAAC,MAAM,YAAY,EAAE;QACvC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;YAChC,SAAS;QACX;IACF;IACA,IAAI,OAAO,QAAQ,EAAE;QACnB,MAAM,IAAI,QAAQ,CAAC,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,4BAA4B,CAAC;IACzE;IACA,IAAI,MAAM,aAAa,KAAK,QAAQ;QAClC,MAAM,IAAI,QAAQ,CAChB,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,sCAAsC,CAAC;IAElE;IACA,MAAM,eAAe,MAAM,KAAK,EAAE,MAAM,KAAK,OAAO,CAAC,IAAM,MAAM,KAAK,YAAY,CAAC,KAAK,CAAC;IACzF,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAC;QACzC,OAAO,CAAC,KAAK,MAAM,CAAC,QAAQ,CAAC;IAC/B;IACA,IAAI,cAAc,MAAM,EAAE;QACxB,MAAM,IAAI,QAAQ,CAChB,iBACE,MAAM,YAAY,EAClB,iBACA,CAAC,kCAAkC,EAAE,cAAc,IAAI,CAAC,OAAO;IAGrE;IACA,IAAI,CAAC,CAAC,MAAM,cAAc,IAAI,CAAC,MAAM,qBAAqB,KAAK,QAAQ,WAAW,EAAE;QAClF,MAAM,IAAI,QAAQ,CAChB,iBACE,MAAM,YAAY,EAClB,mBACA;IAGN;IACA,IAAI,CAAC,MAAM,qBAAqB,EAAE;QAChC,MAAM,qBAAqB,GAAG;IAChC;IACA,IAAI,CAAC;QACH;QACA,QAAQ,6BAA6B,GAAG,UAAU;KACnD,CAAC,QAAQ,CAAC,MAAM,qBAAqB,EAAE,iBAAiB,KAAK;QAC5D,MAAM,IAAI,QAAQ,CAChB,iBACE,MAAM,YAAY,EAClB,mBACA;IAGN;IACA,MAAM,OAAO,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,IAAI,OAAO,OAAO;IACpD,MAAM,kBAAkB,KAAK,aAAa,GAAG;IAC7C,MAAM,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK;IACxC,IAAI;QACF,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD;YACE,OAAO,KAAK,SAAS,CAAC;gBACpB,UAAU,OAAO,QAAQ;gBACzB,aAAa,MAAM,YAAY;gBAC/B,OAAO;gBACP,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACvB,UAAU,QAAQ,OAAO,CAAC,SAAS,CAAC,OAAO;gBAC3C;;;;;;;;;;;WAWC,GACD,gBAAgB,MAAM,MAAM,KAAK;gBACjC,OAAO,MAAM,MAAM,KAAK,YAAY,MAAM,KAAK,GAAG;gBAClD,eAAe,MAAM,cAAc;gBACnC,qBAAqB,MAAM,qBAAqB;gBAChD,OAAO,MAAM,KAAK;YACpB;YACA,YAAY;YACZ;QACF,GACA;IAEJ,EAAE,OAAO,GAAG;QACV,MAAM,IAAI,QAAQ,CAChB,iBACE,MAAM,YAAY,EAClB,gBACA;IAGN;IACA,MAAM,sBAAsB,IAAI,IAAI;IACpC,oBAAoB,YAAY,CAAC,GAAG,CAAC,QAAQ;IAC7C,oBAAoB,YAAY,CAAC,GAAG,CAAC,SAAS,IAAI,KAAK,CAAC,KAAK;IAC7D,IAAI,MAAM,MAAM,KAAK,WAAW;QAC9B,MAAM,IAAI,QAAQ,CAAC,oBAAoB,QAAQ;IACjD;IACA,MAAM,IAAI,QAAQ,CAAC,oBAAoB,QAAQ;AACjD;AAEA,MAAM,yBAAyB,CAAC,KAAK;IACnC,MAAM,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO;IAC1C,MAAM,UAAU,IAAI,OAAO,CAAC,OAAO;IACnC,IAAI,CAAC,UAAU,CAAC,SAAS;QACvB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,yBAAyB;YAC1C,OAAO;YACP,mBAAmB;QACrB;IACF;IACA,OAAO;QACL;QACA,wBAAwB,GAAG,QAAQ,cAAc,CAAC;QAClD,gBAAgB,GAAG,QAAQ,UAAU,CAAC;QACtC,mBAAmB,GAAG,QAAQ,aAAa,CAAC;QAC5C,UAAU,GAAG,QAAQ,SAAS,CAAC;QAC/B,uBAAuB,GAAG,QAAQ,aAAa,CAAC;QAChD,kBAAkB;YAAC;YAAU;YAAW;YAAS;SAAiB;QAClE,0BAA0B;YAAC;SAAO;QAClC,0BAA0B;YAAC;SAAQ;QACnC,uBAAuB;YAAC;YAAsB;SAAgB;QAC9D,sBAAsB;YACpB;YACA;SACD;QACD,yBAAyB;YAAC;SAAS;QACnC,uCAAuC;YAAC;YAAS;SAAO;QACxD,uCAAuC;YACrC;YACA;YACA;SACD;QACD,kCAAkC;YAAC;SAAO;QAC1C,kBAAkB;YAChB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,GAAG,SAAS,QAAQ;IACtB;AACF;AACA,MAAM,MAAM,CAAC;IACX,MAAM,OAAO;QACX,eAAe;QACf,cAAc;QACd,sBAAsB;QACtB,uBAAuB;QACvB,+BAA+B;QAC/B,GAAG,QAAQ,UAAU;QACrB,WAAW,QAAQ,SAAS;QAC5B,QAAQ;YACN;YACA;YACA;YACA;eACG,QAAQ,UAAU,EAAE,UAAU,EAAE;SACpC;IACH;IACA,MAAM,YAAY;QAChB,aAAa;QACb,kBAAkB;IAAkB;IACtC,CAAA,GAAA,iLAAA,CAAA,IAAY,AAAD,EAAE;IACb,OAAO;QACL,IAAI;QACJ,OAAO;YACL,OAAO;gBACL;oBACE;wBACE,OAAO;oBACT;oBACA,SAAS,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,OAAO;wBACnC,MAAM,SAAS,MAAM,IAAI,eAAe,CACtC,qBACA,IAAI,OAAO,CAAC,MAAM;wBAEpB,MAAM,aAAa,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI;wBAC5D,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,uBAAoB,AAAD,EAC/C,IAAI,OAAO,CAAC,eAAe,EAAE,IAAI,iBAAiB;wBAEpD,MAAM,kBAAkB,sBAAsB,GAAG,CAAC;wBAClD,IAAI,CAAC,UAAU,CAAC,iBAAiB;4BAC/B;wBACF;wBACA,IAAI,SAAS,CAAC,qBAAqB,IAAI;4BACrC,QAAQ;wBACV;wBACA,MAAM,gBAAgB,sBAAsB,GAAG,CAAC,aAAa;wBAC7D,MAAM,eAAe,eAAe,MAAM,IAAI,CAAC,EAAE;wBACjD,IAAI,CAAC,cAAc;4BACjB;wBACF;wBACA,MAAM,UAAU,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC;wBAC9D,IAAI,CAAC,SAAS;4BACZ;wBACF;wBACA,IAAI,KAAK,GAAG,KAAK,KAAK,CAAC;wBACvB,IAAI,KAAK,CAAC,MAAM,GAAG;wBACnB,IAAI,OAAO,CAAC,OAAO,GAAG;wBACtB,MAAM,WAAW,MAAM,kBAAkB,KAAK,MAAM,KAAK,CAAC,CAAC;4BACzD,IAAI,aAAa,iJAAA,CAAA,WAAQ,EAAE;gCACzB,IAAI,EAAE,UAAU,KAAK,KAAK;oCACxB,OAAO,IAAI,IAAI,CAAC;wCACd,UAAU;wCACV,kBAAkB;wCAClB,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC;oCACrB;gCACF;4BACF;4BACA,MAAM;wBACR;wBACA,OAAO;oBACT;gBACF;aACD;QACH;QACA,WAAW;YACT,mBAAmB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAClC,2CACA;gBACE,QAAQ;gBACR,UAAU;oBACR,QAAQ;gBACV;YACF,GACA,OAAO;gBACL,IAAI;oBACF,MAAM,WAAW,uBAAuB,GAAG;oBAC3C,OAAO,EAAE,IAAI,CAAC;gBAChB,EAAE,OAAO,GAAG;oBACV,QAAQ,GAAG,CAAC;oBACZ,OAAO,EAAE,IAAI,CAAC;gBAChB;YACF;YAEF,mBAAmB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAClC,kBACA;gBACE,QAAQ;gBACR,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,KAAK,CAAA,GAAA,mLAAA,CAAA,MAAK,AAAD;gBAChC,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,OAAO;gCACL,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,sBAAsB;4CACtB,aAAa;wCACf;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,OAAO,kBAAkB,KAAK;YAChC;YAEF,eAAe,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAC9B,cACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE,CAAA,GAAA,mLAAA,CAAA,MAAK,AAAD,KAAK,CAAA,GAAA,mLAAA,CAAA,MAAK,AAAD;gBAC5B,UAAU;oBACR,UAAU;gBACZ;YACF,GACA,OAAO;gBACL,IAAI,SAAS,CAAC,+BAA+B;gBAC7C,IAAI,SAAS,CAAC,gCAAgC;gBAC9C,IAAI,SAAS,CACX,gCACA;gBAEF,IAAI,SAAS,CAAC,0BAA0B;gBACxC,IAAI,EAAE,IAAI,EAAE,GAAG;gBACf,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,KAAK,CAAC,eAAe;wBAC7B,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,gBAAgB,UAAU;oBAC5B,OAAO,OAAO,WAAW,CAAC,KAAK,OAAO;gBACxC;gBACA,IAAI,CAAC,CAAC,gBAAgB,MAAM,GAAG;oBAC7B,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG;gBACnC,MAAM,gBAAgB,IAAI,OAAO,EAAE,QAAQ,IAAI,oBAAoB;gBACnE,IAAI,iBAAiB,CAAC,aAAa,CAAC,iBAAiB,cAAc,UAAU,CAAC,WAAW;oBACvF,IAAI;wBACF,MAAM,UAAU,cAAc,OAAO,CAAC,UAAU;wBAChD,MAAM,UAAU,IAAI,cAAc,MAAM,CAAC,8JAAA,CAAA,SAAM,CAAC,MAAM,CAAC;wBACvD,IAAI,CAAC,QAAQ,QAAQ,CAAC,MAAM;4BAC1B,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;gCACjC,mBAAmB;gCACnB,OAAO;4BACT;wBACF;wBACA,MAAM,CAAC,IAAI,OAAO,GAAG,QAAQ,KAAK,CAAC;wBACnC,IAAI,CAAC,MAAM,CAAC,QAAQ;4BAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;gCACjC,mBAAmB;gCACnB,OAAO;4BACT;wBACF;wBACA,YAAY;wBACZ,gBAAgB;oBAClB,EAAE,OAAO,OAAO;wBACd,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;4BACjC,mBAAmB;4BACnB,OAAO;wBACT;oBACF;gBACF;gBACA,MAAM,EACJ,UAAU,EACV,IAAI,EACJ,YAAY,EACZ,aAAa,EACb,aAAa,EACd,GAAG;gBACJ,IAAI,eAAe,iBAAiB;oBAClC,IAAI,CAAC,eAAe;wBAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,mBAAmB;4BACnB,OAAO;wBACT;oBACF;oBACA,MAAM,QAAQ,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;wBAC9C,OAAO;wBACP,OAAO;4BACL;gCACE,OAAO;gCACP,OAAO,cAAc,QAAQ;4BAC/B;yBACD;oBACH;oBACA,IAAI,CAAC,OAAO;wBACV,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;4BACjC,mBAAmB;4BACnB,OAAO;wBACT;oBACF;oBACA,IAAI,MAAM,QAAQ,KAAK,WAAW,YAAY;wBAC5C,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;4BACjC,mBAAmB;4BACnB,OAAO;wBACT;oBACF;oBACA,IAAI,MAAM,qBAAqB,GAAG,aAAa,GAAG,IAAI,QAAQ;wBAC5D,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;4BACjC,mBAAmB;4BACnB,OAAO;wBACT;oBACF;oBACA,MAAM,eAAe,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,IAAI,OAAO;oBACrD,MAAM,kBAAkB,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,IAAI,OAAO;oBACxD,MAAM,wBAAwB,IAAI,KAChC,KAAK,GAAG,KAAK,KAAK,oBAAoB,GAAG;oBAE3C,MAAM,yBAAyB,IAAI,KACjC,KAAK,GAAG,KAAK,KAAK,qBAAqB,GAAG;oBAE5C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;wBAC/B,OAAO,UAAU,gBAAgB;wBACjC,MAAM;4BACJ,aAAa;4BACb,cAAc;4BACd,sBAAsB;4BACtB,uBAAuB;4BACvB,UAAU,UAAU,QAAQ;4BAC5B,QAAQ,MAAM,MAAM;4BACpB,QAAQ,MAAM,MAAM;4BACpB,WAAW,aAAa,GAAG,IAAI;4BAC/B,WAAW,aAAa,GAAG,IAAI;wBACjC;oBACF;oBACA,OAAO,IAAI,IAAI,CAAC;wBACd,cAAc;wBACd,YAAY;wBACZ,YAAY,KAAK,oBAAoB;wBACrC,eAAe;wBACf,OAAO,MAAM,MAAM;oBACrB;gBACF;gBACA,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,KAAK,WAAW,IAAI,CAAC,eAAe;oBACtC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,MAAM,oBAAoB,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,qBAAqB,CAC/E,KAAK,QAAQ;gBAEf,IAAI,CAAC,mBAAmB;oBACtB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,kBAAkB,SAAS,GAAG,aAAa,GAAG,IAAI,QAAQ;oBAC5D,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE;gBAEtB,IAAI,CAAC,WAAW;oBACd,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,CAAC,YAAY;oBACf,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,eAAe,sBAAsB;oBACvC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,CAAC,cAAc;oBACjB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,MAAM,SAAS,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBAC/C,OAAO,UAAU,WAAW;oBAC5B,OAAO;wBAAC;4BAAE,OAAO;4BAAY,OAAO,UAAU,QAAQ;wBAAG;qBAAE;gBAC7D,GAAG,IAAI,CAAC,CAAC;oBACP,IAAI,CAAC,KAAK;wBACR,OAAO;oBACT;oBACA,OAAO;wBACL,GAAG,GAAG;wBACN,cAAc,IAAI,YAAY,CAAC,KAAK,CAAC;wBACrC,UAAU,IAAI,QAAQ,GAAG,KAAK,KAAK,CAAC,IAAI,QAAQ,IAAI,CAAC;oBACvD;gBACF;gBACA,IAAI,CAAC,QAAQ;oBACX,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,OAAO,QAAQ,EAAE;oBACnB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,OAAO,IAAI,KAAK,UAAU;oBAC5B,IAAI,CAAC,eAAe;wBAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,mBAAmB;4BACnB,OAAO;wBACT;oBACF;gBACF,OAAO;oBACL,IAAI,CAAC,eAAe;wBAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;4BACjC,mBAAmB;4BACnB,OAAO;wBACT;oBACF;oBACA,MAAM,gBAAgB,OAAO,YAAY,KAAK,cAAc,QAAQ;oBACpE,IAAI,CAAC,eAAe;wBAClB,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;4BACjC,mBAAmB;4BACnB,OAAO;wBACT;oBACF;gBACF;gBACA,MAAM,QAAQ,KAAK,KAAK,CACtB,kBAAkB,KAAK;gBAEzB,IAAI,MAAM,QAAQ,KAAK,UAAU,QAAQ,IAAI;oBAC3C,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,MAAM,WAAW,KAAK,aAAa,QAAQ,IAAI;oBACjD,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,MAAM,aAAa,IAAI,CAAC,eAAe;oBACzC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,MAAM,YAAY,MAAM,mBAAmB,KAAK,UAAU,gBAAgB,MAAM,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,WAAW,kBAAkB,MAAM,CAC5H;gBAEF,IAAI,cAAc,MAAM,aAAa,EAAE;oBACrC,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,MAAM,kBAAkB,MAAM,KAAK;gBACnC,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,uBAAuB,CACvD,kBAAkB,EAAE;gBAEtB,MAAM,cAAc,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,IAAI,OAAO;gBACpD,MAAM,eAAe,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,IAAI,OAAO;gBACrD,MAAM,uBAAuB,IAAI,KAC/B,KAAK,GAAG,KAAK,KAAK,oBAAoB,GAAG;gBAE3C,MAAM,wBAAwB,IAAI,KAChC,KAAK,GAAG,KAAK,KAAK,qBAAqB,GAAG;gBAE5C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;oBAC/B,OAAO,UAAU,gBAAgB;oBACjC,MAAM;wBACJ;wBACA;wBACA;wBACA;wBACA,UAAU,UAAU,QAAQ;wBAC5B,QAAQ,MAAM,MAAM;wBACpB,QAAQ,gBAAgB,IAAI,CAAC;wBAC7B,WAAW,aAAa,GAAG,IAAI;wBAC/B,WAAW,aAAa,GAAG,IAAI;oBACjC;gBACF;gBACA,MAAM,OAAO,MAAM,IAAI,OAAO,CAAC,eAAe,CAAC,YAAY,CACzD,MAAM,MAAM;gBAEd,IAAI,CAAC,MAAM;oBACT,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,gBAAgB;wBACjC,mBAAmB;wBACnB,OAAO;oBACT;gBACF;gBACA,IAAI,YAAY;oBACd,KAAK;oBACL,KAAK,MAAM,qJAAA,CAAA,SAAM,CAAC,WAAW,CAC3B;wBACE,MAAM;wBACN,MAAM;oBACR,GACA,MACA;wBAAC;wBAAQ;qBAAS;gBAEtB;gBACA,MAAM,UAAU;oBACd,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACnC,aAAa,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACpC,MAAM,KAAK,IAAI;oBACf,SAAS,KAAK,KAAK;oBACnB,YAAY,KAAK,SAAS,CAAC,WAAW;gBACxC;gBACA,MAAM,QAAQ;oBACZ,OAAO,KAAK,KAAK;oBACjB,gBAAgB,KAAK,aAAa;gBACpC;gBACA,MAAM,aAAa;oBACjB,GAAG,gBAAgB,QAAQ,CAAC,aAAa,UAAU,CAAC,CAAC;oBACrD,GAAG,gBAAgB,QAAQ,CAAC,WAAW,QAAQ,CAAC,CAAC;gBACnD;gBACA,MAAM,uBAAuB,KAAK,0BAA0B,GAAG,KAAK,0BAA0B,CAAC,MAAM,mBAAmB,CAAC;gBACzH,MAAM,UAAU,MAAM,IAAI,4JAAA,CAAA,UAAO,CAAC;oBAChC,KAAK,KAAK,EAAE;oBACZ,KAAK,UAAU,QAAQ;oBACvB,KAAK,KAAK,GAAG;oBACb,WAAW,IAAI,OAAO,CAAC,OAAO,EAAE,QAAQ,UAAU;oBAClD,OAAO,MAAM,KAAK;oBAClB,KAAK;oBACL,2GAA2G;oBAC3G,GAAG,UAAU;oBACb,GAAG,oBAAoB;gBACzB,GAAG,kBAAkB,CAAC;oBAAE,KAAK,UAAU,GAAG;gBAAC,GAAG,WAAW,GAAG,iBAAiB,CAC3E,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,OAAO,KAAK,oBAAoB,EACxD,IAAI,CAAC,UAAU,GAAG;gBACpB,OAAO,IAAI,IAAI,CACb;oBACE,cAAc;oBACd,YAAY;oBACZ,YAAY,KAAK,oBAAoB;oBACrC,eAAe,gBAAgB,QAAQ,CAAC,oBAAoB,eAAe,KAAK;oBAChF,OAAO,gBAAgB,IAAI,CAAC;oBAC5B,UAAU,gBAAgB,QAAQ,CAAC,YAAY,UAAU,KAAK;gBAChE,GACA;oBACE,SAAS;wBACP,iBAAiB;wBACjB,QAAQ;oBACV;gBACF;YAEJ;YAEF,mBAAmB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAClC,iBACA;gBACE,QAAQ;gBACR,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE;oBACb,eAAe,CAAA,GAAA,mLAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD;oBAC9B,4BAA4B,CAAA,GAAA,mLAAA,CAAA,OAAM,AAAD,EAAE;wBAAC;wBAAQ;wBAAuB;qBAAqB,EAAE,OAAO,CAAC,uBAAuB,QAAQ;oBACjI,aAAa,CAAA,GAAA,mLAAA,CAAA,QAAO,AAAD,EACjB,CAAA,GAAA,mLAAA,CAAA,OAAM,AAAD,EAAE;wBACL;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD,GACD,OAAO,CAAC;wBAAC;qBAAqB,EAAE,QAAQ;oBAC1C,gBAAgB,CAAA,GAAA,mLAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,mLAAA,CAAA,OAAM,AAAD,EAAE;wBAAC;wBAAQ;qBAAQ,GAAG,OAAO,CAAC;wBAAC;qBAAO,EAAE,QAAQ;oBAC7E,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;oBAChC,YAAY,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;oBAC/B,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;oBAC7B,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;oBAC1B,UAAU,CAAA,GAAA,mLAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,KAAK,QAAQ;oBACtC,SAAS,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;oBAC5B,YAAY,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;oBAC/B,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;oBAC7B,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,KAAK,CAAA,GAAA,mLAAA,CAAA,MAAK,AAAD,KAAK,QAAQ;oBAC5C,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,EAAE,CAAA,GAAA,mLAAA,CAAA,MAAK,AAAD,KAAK,CAAA,GAAA,mLAAA,CAAA,MAAK,AAAD,KAAK,QAAQ;oBAC7C,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;oBAChC,kBAAkB,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;oBACrC,oBAAoB,CAAA,GAAA,mLAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;gBACzC;gBACA,UAAU;oBACR,SAAS;wBACP,aAAa;wBACb,WAAW;4BACT,OAAO;gCACL,aAAa;gCACb,SAAS;oCACP,oBAAoB;wCAClB,QAAQ;4CACN,MAAM;4CACN,YAAY;gDACV,MAAM;oDACJ,MAAM;oDACN,aAAa;gDACf;gDACA,MAAM;oDACJ,MAAM;oDACN,UAAU;oDACV,aAAa;gDACf;gDACA,UAAU;oDACR,MAAM;oDACN,sBAAsB;oDACtB,UAAU;oDACV,aAAa;gDACf;gDACA,UAAU;oDACR,MAAM;oDACN,aAAa;gDACf;gDACA,cAAc;oDACZ,MAAM;oDACN,aAAa;gDACf;gDACA,cAAc;oDACZ,MAAM;oDACN,OAAO;wDAAE,MAAM;wDAAU,QAAQ;oDAAM;oDACvC,aAAa;gDACf;gDACA,MAAM;oDACJ,MAAM;oDACN,aAAa;oDACb,MAAM;wDAAC;wDAAO;qDAAS;gDACzB;gDACA,sBAAsB;oDACpB,MAAM;oDACN,aAAa;oDACb,MAAM;wDAAC;wDAAiB;qDAAO;gDACjC;gDACA,UAAU;oDACR,MAAM;oDACN,aAAa;oDACb,MAAM;wDAAC;qDAAM;gDACf;gDACA,QAAQ;oDACN,MAAM;oDACN,UAAU;oDACV,aAAa;gDACf;gDACA,WAAW;oDACT,MAAM;oDACN,QAAQ;oDACR,aAAa;gDACf;gDACA,WAAW;oDACT,MAAM;oDACN,QAAQ;oDACR,aAAa;gDACf;4CACF;4CACA,UAAU;gDACR;gDACA;gDACA;gDACA;gDACA;gDACA;gDACA;gDACA;6CACD;wCACH;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF,GACA,OAAO;gBACL,MAAM,OAAO,IAAI,IAAI;gBACrB,MAAM,UAAU,MAAM,CAAA,GAAA,iLAAA,CAAA,IAAiB,AAAD,EAAE;gBACxC,IAAI,SAAS,CAAC,+BAA+B;gBAC7C,IAAI,SAAS,CAAC,gCAAgC;gBAC9C,IAAI,SAAS,CACX,gCACA;gBAEF,IAAI,SAAS,CAAC,0BAA0B;gBACxC,IAAI,OAAO,EAAE,IAAI,0BAA0B;gBAC3C,IAAI,CAAC,CAAC,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,QAAQ,CAAC,yBAAyB,KAAK,WAAW,CAAC,QAAQ,CAAC,WAAW,KAAK,CAAC,CAAC,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,MAAM,KAAK,CAAC,GAAG;oBAC/K,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;wBAChC,OAAO;wBACP,mBAAmB;oBACrB;gBACF;gBACA,IAAI,KAAK,WAAW,IAAI,KAAK,cAAc,EAAE;oBAC3C,IAAI,KAAK,WAAW,CAAC,QAAQ,CAAC,yBAAyB,CAAC,KAAK,cAAc,CAAC,QAAQ,CAAC,SAAS;wBAC5F,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,OAAO;4BACP,mBAAmB;wBACrB;oBACF;oBACA,IAAI,KAAK,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,cAAc,CAAC,QAAQ,CAAC,UAAU;wBACnF,MAAM,IAAI,iJAAA,CAAA,WAAQ,CAAC,eAAe;4BAChC,OAAO;4BACP,mBAAmB;wBACrB;oBACF;gBACF;gBACA,MAAM,WAAW,KAAK,gBAAgB,QAAQ,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,IAAI,OAAO;gBAC9E,MAAM,eAAe,KAAK,oBAAoB,QAAQ,CAAA,GAAA,iLAAA,CAAA,IAAoB,AAAD,EAAE,IAAI,OAAO;gBACtF,MAAM,aAAa,KAAK,0BAA0B,KAAK,SAAS,WAAW;gBAC3E,MAAM,oBAAoB,eAAe,WAAW,KAAK;gBACzD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;oBAC/B,OAAO,UAAU,WAAW;oBAC5B,MAAM;wBACJ,MAAM,KAAK,WAAW;wBACtB,MAAM,KAAK,QAAQ;wBACnB,UAAU,KAAK,QAAQ,GAAG,KAAK,SAAS,CAAC,KAAK,QAAQ,IAAI;wBAC1D;wBACA,cAAc;wBACd,cAAc,KAAK,aAAa,CAAC,IAAI,CAAC;wBACtC,MAAM;wBACN,sBAAsB,KAAK,0BAA0B,IAAI;wBACzD,UAAU;wBACV,QAAQ,SAAS,QAAQ;wBACzB,WAAW,aAAa,GAAG,IAAI;wBAC/B,WAAW,aAAa,GAAG,IAAI;oBACjC;gBACF;gBACA,MAAM,eAAe;oBACnB,WAAW;oBACX,qBAAqB,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;oBAC7C,eAAe,KAAK,aAAa;oBACjC,4BAA4B,KAAK,0BAA0B,IAAI;oBAC/D,aAAa,KAAK,WAAW,IAAI;wBAAC;qBAAqB;oBACvD,gBAAgB,KAAK,cAAc,IAAI;wBAAC;qBAAO;oBAC/C,aAAa,KAAK,WAAW;oBAC7B,YAAY,KAAK,UAAU;oBAC3B,UAAU,KAAK,QAAQ;oBACvB,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,SAAS,KAAK,OAAO;oBACrB,YAAY,KAAK,UAAU;oBAC3B,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,aAAa,KAAK,WAAW;oBAC7B,kBAAkB,KAAK,gBAAgB;oBACvC,oBAAoB,KAAK,kBAAkB;oBAC3C,UAAU,KAAK,QAAQ;oBACvB,GAAG,eAAe,WAAW;wBAC3B,eAAe;wBACf,0BAA0B;oBAE5B,IAAI,CAAC,CAAC;gBACR;gBACA,OAAO,IAAI,IAAI,CAAC,cAAc;oBAC5B,QAAQ;oBACR,SAAS;wBACP,iBAAiB;wBACjB,QAAQ;oBACV;gBACF;YACF;YAEF,eAAe,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAC9B,oBACA;gBACE,QAAQ;gBACR,gBAAgB;YAClB,GACA,OAAO;gBACL,MAAM,cAAc,EAAE,OAAO,EAAE,IAAI,kBAAkB,QAAQ,WAAW;gBACxE,IAAI,CAAC,aAAa;oBAChB,EAAE,OAAO,EAAE,IAAI,oBAAoB;oBACnC,OAAO,EAAE,IAAI,CAAC;gBAChB;gBACA,MAAM,kBAAkB,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;oBACtD,OAAO,UAAU,gBAAgB;oBACjC,OAAO;wBACL;4BACE,OAAO;4BACP,OAAO;wBACT;qBACD;gBACH;gBACA,IAAI,CAAC,iBAAiB;oBACpB,OAAO,EAAE,IAAI,CAAC;gBAChB;gBACA,OAAO,EAAE,IAAI,CAAC;YAChB;QAEJ;QACA,QAAA,iLAAA,CAAA,IAAM;IACR;AACF;AACA,MAAM,cAAc,CAAC,MAAM;IACzB,OAAO,OAAO;QACZ,MAAM,UAAU,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD,EAAE,KAAK,OAAO,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC,QAAQ;QACtE,IAAI,CAAC,WAAW,CAAC,oLAAA,CAAA,IAAY,EAAE;YAC7B,iLAAA,CAAA,IAAM,CAAC,IAAI,CAAC;QACd;QACA,MAAM,UAAU,MAAM,KAAK,GAAG,CAAC,aAAa,CAAC;YAC3C,SAAS,IAAI,OAAO;QACtB;QACA,MAAM,uBAAuB,CAAC,yBAAyB,EAAE,QAAQ,gDAAgD,CAAC;QAClH,IAAI,CAAC,SAAS;YACZ,OAAO,SAAS,IAAI,CAClB;gBACE,SAAS;gBACT,OAAO;oBACL,MAAM,CAAC;oBACP,SAAS;oBACT,oBAAoB;gBACtB;gBACA,IAAI;YACN,GACA;gBACE,QAAQ;gBACR,SAAS;oBACP,oBAAoB;gBACtB;YACF;QAEJ;QACA,OAAO,QAAQ,KAAK;IACtB;AACF;AACA,MAAM,yBAAyB,CAAC;IAC9B,OAAO,OAAO;QACZ,MAAM,MAAM,MAAM,KAAK,GAAG,CAAC,iBAAiB;QAC5C,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC,MAAM;YACvC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;gBAChC,0BAA0B;YAC5B;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}]}