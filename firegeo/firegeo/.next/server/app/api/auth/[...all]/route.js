const CHUNK_PUBLIC_PATH = "server/app/api/auth/[...all]/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_d3cc8056._.js");
runtime.loadChunk("server/chunks/node_modules_next_dist_0d2a95be._.js");
runtime.loadChunk("server/chunks/node_modules_better-call_dist_index_5d513ab4.js");
runtime.loadChunk("server/chunks/3aad8_zod_v4_cdfa6116._.js");
runtime.loadChunk("server/chunks/node_modules_zod_v3_cc34a201._.js");
runtime.loadChunk("server/chunks/node_modules_better-auth_dist_shared_c92b0493._.js");
runtime.loadChunk("server/chunks/node_modules_better-auth_dist_plugins_f7e7bb65._.js");
runtime.loadChunk("server/chunks/node_modules_better-auth_dist_0ab913cb._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_esm_b6529421._.js");
runtime.loadChunk("server/chunks/node_modules_kysely_dist_esm_37ebfac0._.js");
runtime.loadChunk("server/chunks/node_modules_dc598dff._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__2b9a47b3._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/auth/[...all]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/auth/[...all]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/auth/[...all]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
