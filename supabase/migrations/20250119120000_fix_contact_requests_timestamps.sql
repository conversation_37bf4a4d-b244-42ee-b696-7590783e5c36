-- Fix contact_requests table to add created_at column and proper timestamp handling
-- This migration addresses the missing created_at column that the API code expects

-- Add created_at column with default value
ALTER TABLE contact_requests 
ADD COLUMN IF NOT EXISTS created_at timestamp with time zone NOT NULL DEFAULT now();

-- Create or replace the updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column() 
RETURNS TRIGGER AS $$ 
BEGIN 
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add trigger to automatically update updated_at column
DROP TRIGGER IF EXISTS update_contact_requests_updated_at ON contact_requests;
CREATE TRIGGER update_contact_requests_updated_at 
    BEFORE UPDATE ON contact_requests 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Set updated_at to created_at for existing records where updated_at is null
UPDATE contact_requests 
SET updated_at = created_at 
WHERE updated_at IS NULL;

-- Make updated_at NOT NULL and default to now()
ALTER TABLE contact_requests 
ALTER COLUMN updated_at SET NOT NULL,
ALTER COLUMN updated_at SET DEFAULT now();
