{"name": "robynn-workspace", "version": "0.0.1", "private": true, "scripts": {"dev": "pnpm --parallel --filter \"./apps/*\" dev", "dev:dashboard": "pnpm --filter dashboard dev", "dev:firegeo": "pnpm --filter firegeo-api dev", "dev:shared": "pnpm --filter shared dev", "build": "pnpm --filter shared build && pnpm --recursive --filter \"./apps/*\" build", "build:dashboard": "pnpm --filter shared build && pnpm --filter dashboard build", "build:firegeo": "pnpm --filter shared build && pnpm --filter firegeo-api build", "clean": "pnpm --recursive exec rm -rf node_modules .next .svelte-kit build dist", "install:all": "pnpm install", "type-check": "pnpm --recursive --filter \"./apps/*\" run check", "lint": "pnpm --recursive --filter \"./apps/*\" run lint", "test": "pnpm --recursive --filter \"./apps/*\" run test"}, "devDependencies": {"typescript": "^5.8.3"}}