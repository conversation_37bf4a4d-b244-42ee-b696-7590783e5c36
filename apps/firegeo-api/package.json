{"name": "firegeo-api", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start", "lint": "next lint", "setup": "node setup.js", "setup:autumn": "tsx scripts/setup-autumn.ts", "setup:stripe-portal": "tsx scripts/setup-stripe-portal.ts", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:drop": "drizzle-kit drop"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.23", "@ai-sdk/perplexity": "^1.1.9", "@mendable/firecrawl-js": "^1.29.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-query": "^5.82.0", "ai": "^4.3.17", "autumn-js": "^0.0.96", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "eventsource-parser": "^3.0.3", "lucide-react": "^0.525.0", "next": "15.3.5", "node-fetch": "^2.7.0", "pg": "^8.16.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "recharts": "^3.1.0", "remark-gfm": "^4.0.1", "resend": "^4.6.0", "sonner": "^2.0.6", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "zod": "^3.23.8", "shared": "workspace:*"}, "devDependencies": {"@better-auth/cli": "^1.2.12", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^17.1.0", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5"}}