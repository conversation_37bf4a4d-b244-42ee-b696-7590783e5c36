"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7086],{18513:(t,e,s)=>{s.d(e,{I:()=>T});var r=s(84589),i=s(29054),n=s(57906),u=s(24311),a=s(32025),h=s(50247),o=class extends u.Q{constructor(t,e){super(),this.options=e,this.#t=t,this.#e=null,this.#s=(0,a.T)(),this.options.experimental_prefetchInRender||this.#s.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#t;#r=void 0;#i=void 0;#n=void 0;#u;#a;#s;#e;#h;#o;#c;#l;#d;#p;#f=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#r.addObserver(this),c(this.#r,this.options)?this.#y():this.updateResult(),this.#R())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return l(this.#r,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return l(this.#r,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#b(),this.#m(),this.#r.removeObserver(this)}setOptions(t){let e=this.options,s=this.#r;if(this.options=this.#t.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,h.Eh)(this.options.enabled,this.#r))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#v(),this.#r.setOptions(this.options),e._defaulted&&!(0,h.f8)(this.options,e)&&this.#t.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#r,observer:this});let r=this.hasListeners();r&&d(this.#r,s,this.options,e)&&this.#y(),this.updateResult(),r&&(this.#r!==s||(0,h.Eh)(this.options.enabled,this.#r)!==(0,h.Eh)(e.enabled,this.#r)||(0,h.d2)(this.options.staleTime,this.#r)!==(0,h.d2)(e.staleTime,this.#r))&&this.#Q();let i=this.#O();r&&(this.#r!==s||(0,h.Eh)(this.options.enabled,this.#r)!==(0,h.Eh)(e.enabled,this.#r)||i!==this.#p)&&this.#g(i)}getOptimisticResult(t){var e,s;let r=this.#t.getQueryCache().build(this.#t,t),i=this.createResult(r,t);return e=this,s=i,(0,h.f8)(e.getCurrentResult(),s)||(this.#n=i,this.#a=this.options,this.#u=this.#r.state),i}getCurrentResult(){return this.#n}trackResult(t,e){return new Proxy(t,{get:(t,s)=>(this.trackProp(s),e?.(s),Reflect.get(t,s))})}trackProp(t){this.#f.add(t)}getCurrentQuery(){return this.#r}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){let e=this.#t.defaultQueryOptions(t),s=this.#t.getQueryCache().build(this.#t,e);return s.fetch().then(()=>this.createResult(s,e))}fetch(t){return this.#y({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#n))}#y(t){this.#v();let e=this.#r.fetch(this.options,t);return t?.throwOnError||(e=e.catch(h.lQ)),e}#Q(){this.#b();let t=(0,h.d2)(this.options.staleTime,this.#r);if(h.S$||this.#n.isStale||!(0,h.gn)(t))return;let e=(0,h.j3)(this.#n.dataUpdatedAt,t);this.#l=setTimeout(()=>{this.#n.isStale||this.updateResult()},e+1)}#O(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#r):this.options.refetchInterval)??!1}#g(t){this.#m(),this.#p=t,!h.S$&&!1!==(0,h.Eh)(this.options.enabled,this.#r)&&(0,h.gn)(this.#p)&&0!==this.#p&&(this.#d=setInterval(()=>{(this.options.refetchIntervalInBackground||r.m.isFocused())&&this.#y()},this.#p))}#R(){this.#Q(),this.#g(this.#O())}#b(){this.#l&&(clearTimeout(this.#l),this.#l=void 0)}#m(){this.#d&&(clearInterval(this.#d),this.#d=void 0)}createResult(t,e){let s,r=this.#r,i=this.options,u=this.#n,o=this.#u,l=this.#a,f=t!==r?t.state:this.#i,{state:y}=t,R={...y},b=!1;if(e._optimisticResults){let s=this.hasListeners(),u=!s&&c(t,e),a=s&&d(t,r,e,i);(u||a)&&(R={...R,...(0,n.k)(y.data,t.options)}),"isRestoring"===e._optimisticResults&&(R.fetchStatus="idle")}let{error:m,errorUpdatedAt:v,status:Q}=R;s=R.data;let O=!1;if(void 0!==e.placeholderData&&void 0===s&&"pending"===Q){let t;u?.isPlaceholderData&&e.placeholderData===l?.placeholderData?(t=u.data,O=!0):t="function"==typeof e.placeholderData?e.placeholderData(this.#c?.state.data,this.#c):e.placeholderData,void 0!==t&&(Q="success",s=(0,h.pl)(u?.data,t,e),b=!0)}if(e.select&&void 0!==s&&!O)if(u&&s===o?.data&&e.select===this.#h)s=this.#o;else try{this.#h=e.select,s=e.select(s),s=(0,h.pl)(u?.data,s,e),this.#o=s,this.#e=null}catch(t){this.#e=t}this.#e&&(m=this.#e,s=this.#o,v=Date.now(),Q="error");let g="fetching"===R.fetchStatus,I="pending"===Q,E="error"===Q,S=I&&g,C=void 0!==s,T={status:Q,fetchStatus:R.fetchStatus,isPending:I,isSuccess:"success"===Q,isError:E,isInitialLoading:S,isLoading:S,data:s,dataUpdatedAt:R.dataUpdatedAt,error:m,errorUpdatedAt:v,failureCount:R.fetchFailureCount,failureReason:R.fetchFailureReason,errorUpdateCount:R.errorUpdateCount,isFetched:R.dataUpdateCount>0||R.errorUpdateCount>0,isFetchedAfterMount:R.dataUpdateCount>f.dataUpdateCount||R.errorUpdateCount>f.errorUpdateCount,isFetching:g,isRefetching:g&&!I,isLoadingError:E&&!C,isPaused:"paused"===R.fetchStatus,isPlaceholderData:b,isRefetchError:E&&C,isStale:p(t,e),refetch:this.refetch,promise:this.#s,isEnabled:!1!==(0,h.Eh)(e.enabled,t)};if(this.options.experimental_prefetchInRender){let e=t=>{"error"===T.status?t.reject(T.error):void 0!==T.data&&t.resolve(T.data)},s=()=>{e(this.#s=T.promise=(0,a.T)())},i=this.#s;switch(i.status){case"pending":t.queryHash===r.queryHash&&e(i);break;case"fulfilled":("error"===T.status||T.data!==i.value)&&s();break;case"rejected":("error"!==T.status||T.error!==i.reason)&&s()}}return T}updateResult(){let t=this.#n,e=this.createResult(this.#r,this.options);this.#u=this.#r.state,this.#a=this.options,void 0!==this.#u.data&&(this.#c=this.#r),(0,h.f8)(e,t)||(this.#n=e,this.#I({listeners:(()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,s="function"==typeof e?e():e;if("all"===s||!s&&!this.#f.size)return!0;let r=new Set(s??this.#f);return this.options.throwOnError&&r.add("error"),Object.keys(this.#n).some(e=>this.#n[e]!==t[e]&&r.has(e))})()}))}#v(){let t=this.#t.getQueryCache().build(this.#t,this.options);if(t===this.#r)return;let e=this.#r;this.#r=t,this.#i=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#R()}#I(t){i.jG.batch(()=>{t.listeners&&this.listeners.forEach(t=>{t(this.#n)}),this.#t.getQueryCache().notify({query:this.#r,type:"observerResultsUpdated"})})}};function c(t,e){return!1!==(0,h.Eh)(e.enabled,t)&&void 0===t.state.data&&("error"!==t.state.status||!1!==e.retryOnMount)||void 0!==t.state.data&&l(t,e,e.refetchOnMount)}function l(t,e,s){if(!1!==(0,h.Eh)(e.enabled,t)&&"static"!==(0,h.d2)(e.staleTime,t)){let r="function"==typeof s?s(t):s;return"always"===r||!1!==r&&p(t,e)}return!1}function d(t,e,s,r){return(t!==e||!1===(0,h.Eh)(r.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&p(t,s)}function p(t,e){return!1!==(0,h.Eh)(e.enabled,t)&&t.isStaleByTime((0,h.d2)(e.staleTime,t))}var f=s(12359),y=s(56949);s(87183);var R=f.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),b=()=>f.useContext(R),m=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&!e.isReset()&&(t.retryOnMount=!1)},v=t=>{f.useEffect(()=>{t.clearReset()},[t])},Q=t=>{let{result:e,errorResetBoundary:s,throwOnError:r,query:i,suspense:n}=t;return e.isError&&!s.isReset()&&!e.isFetching&&i&&(n&&void 0===e.data||(0,h.GU)(r,[e.error,i]))},O=f.createContext(!1),g=()=>f.useContext(O);O.Provider;var I=t=>{if(t.suspense){let e=t=>"static"===t?t:Math.max(t??1e3,1e3),s=t.staleTime;t.staleTime="function"==typeof s?(...t)=>e(s(...t)):e(s),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3))}},E=(t,e)=>t.isLoading&&t.isFetching&&!e,S=(t,e)=>t?.suspense&&e.isPending,C=(t,e,s)=>e.fetchOptimistic(t).catch(()=>{s.clearReset()});function T(t,e){return function(t,e,s){var r,n,u,a,o;let c=g(),l=b(),d=(0,y.jE)(s),p=d.defaultQueryOptions(t);null==(n=d.getDefaultOptions().queries)||null==(r=n._experimental_beforeQuery)||r.call(n,p),p._optimisticResults=c?"isRestoring":"optimistic",I(p),m(p,l),v(l);let R=!d.getQueryCache().get(p.queryHash),[O]=f.useState(()=>new e(d,p)),T=O.getOptimisticResult(p),x=!c&&!1!==t.subscribed;if(f.useSyncExternalStore(f.useCallback(t=>{let e=x?O.subscribe(i.jG.batchCalls(t)):h.lQ;return O.updateResult(),e},[O,x]),()=>O.getCurrentResult(),()=>O.getCurrentResult()),f.useEffect(()=>{O.setOptions(p)},[p,O]),S(p,T))throw C(p,O,l);if(Q({result:T,errorResetBoundary:l,throwOnError:p.throwOnError,query:d.getQueryCache().get(p.queryHash),suspense:p.suspense}))throw T.error;if(null==(a=d.getDefaultOptions().queries)||null==(u=a._experimental_afterQuery)||u.call(a,p,T),p.experimental_prefetchInRender&&!h.S$&&E(T,c)){let t=R?C(p,O,l):null==(o=d.getQueryCache().get(p.queryHash))?void 0:o.promise;null==t||t.catch(h.lQ).finally(()=>{O.updateResult()})}return p.notifyOnChangeProps?T:O.trackResult(T)}(t,o,e)}},74290:(t,e,s)=>{s.d(e,{n:()=>c});var r=s(12359),i=s(60061),n=s(29054),u=s(24311),a=s(50247),h=class extends u.Q{#t;#n=void 0;#E;#S;constructor(t,e){super(),this.#t=t,this.setOptions(e),this.bindMethods(),this.#C()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#t.defaultMutationOptions(t),(0,a.f8)(this.options,e)||this.#t.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#E,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,a.EN)(e.mutationKey)!==(0,a.EN)(this.options.mutationKey)?this.reset():this.#E?.state.status==="pending"&&this.#E.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#E?.removeObserver(this)}onMutationUpdate(t){this.#C(),this.#I(t)}getCurrentResult(){return this.#n}reset(){this.#E?.removeObserver(this),this.#E=void 0,this.#C(),this.#I()}mutate(t,e){return this.#S=e,this.#E?.removeObserver(this),this.#E=this.#t.getMutationCache().build(this.#t,this.options),this.#E.addObserver(this),this.#E.execute(t)}#C(){let t=this.#E?.state??(0,i.$)();this.#n={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#I(t){n.jG.batch(()=>{if(this.#S&&this.hasListeners()){let e=this.#n.variables,s=this.#n.context;t?.type==="success"?(this.#S.onSuccess?.(t.data,e,s),this.#S.onSettled?.(t.data,null,e,s)):t?.type==="error"&&(this.#S.onError?.(t.error,e,s),this.#S.onSettled?.(void 0,t.error,e,s))}this.listeners.forEach(t=>{t(this.#n)})})}},o=s(56949);function c(t,e){let s=(0,o.jE)(e),[i]=r.useState(()=>new h(s,t));r.useEffect(()=>{i.setOptions(t)},[i,t]);let u=r.useSyncExternalStore(r.useCallback(t=>i.subscribe(n.jG.batchCalls(t)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),c=r.useCallback((t,e)=>{i.mutate(t,e).catch(a.lQ)},[i]);if(u.error&&(0,a.GU)(i.options.throwOnError,[u.error]))throw u.error;return{...u,mutate:c,mutateAsync:u.mutate}}}}]);