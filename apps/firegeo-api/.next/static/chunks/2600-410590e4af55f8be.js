(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2600],{4913:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useAutumn:()=>i});var n=r(14776),a=r(16254);let i=()=>(0,a.useAutumnBase)({AutumnContext:n.AutumnContext})},7597:(e,t)=>{"use strict";var r,n;Object.defineProperty(t,"__esModule",{value:!0}),t.getParsedType=t.ZodParsedType=t.objectUtil=t.util=void 0,function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),n={};for(let e of r)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(r||(t.util=r={})),(n||(t.objectUtil=n={})).mergeShapes=(e,t)=>({...e,...t}),t.ZodParsedType=r.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),t.getParsedType=e=>{switch(typeof e){case"undefined":return t.ZodParsedType.undefined;case"string":return t.ZodParsedType.string;case"number":return Number.isNaN(e)?t.ZodParsedType.nan:t.ZodParsedType.number;case"boolean":return t.ZodParsedType.boolean;case"function":return t.ZodParsedType.function;case"bigint":return t.ZodParsedType.bigint;case"symbol":return t.ZodParsedType.symbol;case"object":if(Array.isArray(e))return t.ZodParsedType.array;if(null===e)return t.ZodParsedType.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return t.ZodParsedType.promise;if("undefined"!=typeof Map&&e instanceof Map)return t.ZodParsedType.map;if("undefined"!=typeof Set&&e instanceof Set)return t.ZodParsedType.set;if("undefined"!=typeof Date&&e instanceof Date)return t.ZodParsedType.date;return t.ZodParsedType.object;default:return t.ZodParsedType.unknown}}},10360:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ZodError=t.quotelessJson=t.ZodIssueCode=void 0;let n=r(7597);t.ZodIssueCode=n.util.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),t.quotelessJson=e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:");class a extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},n=e=>{for(let a of e.issues)if("invalid_union"===a.code)a.unionErrors.map(n);else if("invalid_return_type"===a.code)n(a.returnTypeError);else if("invalid_arguments"===a.code)n(a.argumentsError);else if(0===a.path.length)r._errors.push(t(a));else{let e=r,n=0;for(;n<a.path.length;){let r=a.path[n];n===a.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(a))):e[r]=e[r]||{_errors:[]},e=e[r],n++}}};return n(this),r}static assert(e){if(!(e instanceof a))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,n.util.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let n of this.issues)if(n.path.length>0){let r=n.path[0];t[r]=t[r]||[],t[r].push(e(n))}else r.push(e(n));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}t.ZodError=a,a.create=e=>new a(e)},14540:(e,t,r)=>{"use strict";var n,a=r(37811),i=Object.create,s=Object.defineProperty,o=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,d=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of c(t))u.call(e,a)||a===r||s(e,a,{get:()=>t[a],enumerable:!(n=o(t,a))||n.enumerable});return e},p=(e,t,r)=>(r=null!=e?i(l(e)):{},d(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)),h={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(h,{useCustomerBase:()=>eZ}),e.exports=d(s({},"__esModule",{value:!0}),h);var f=p(r(59153)),m=async e=>{if(401===e.status){let t=e.clone();if((await t.json()).message.includes("Missing authorization header"))return console.error("[Autumn] Missing authorization header.\n\nUse the getBearerToken prop in <AutumnProvider /> to set the authorization header.\nhttps://docs.useautumn.com/quickstart/quickstart#5-set-up-autumnprovider"),!0}return!1},y=class e extends Error{static fromError(t){return new e({message:t.message||"Unknown error",code:t.code||"unknown_error"})}toString(){return"".concat(this.message," (code: ").concat(this.code,")")}toJSON(){return{message:this.message,code:this.code}}constructor(e){super(e.message),this.message=e.message,this.code=e.code}},g=async e=>{let{instance:t,params:r}=e;return t.post("/attach",r)},_=async e=>{let{instance:t,params:r}=e;return t.post("/setup_payment",r)},v=async e=>{let{instance:t,params:r}=e;return t.post("/cancel",r)},b=async e=>{let{instance:t,params:r}=e;return t.post("/entitled",r)},w=async e=>{let{instance:t,params:r}=e;return t.post("/events",r)},x=async e=>{let{instance:t,params:r}=e;return t.post("/track",r)},C=async e=>{let{instance:t,params:r}=e;return t.post("/usage",r)},z=async e=>{let{instance:t,params:r}=e;return t.post("/check",r)},T=(e,t,r)=>(t||(t=new G),e({instance:t,...r})),k=e=>({get:(t,r)=>T(O,e,{id:t,params:r}),create:t=>T(A,e,{params:t}),update:(t,r)=>T(P,e,{id:t,params:r}),delete:t=>T(j,e,{id:t}),billingPortal:(t,r)=>T(E,e,{id:t,params:r})}),I=e=>e?"expand=".concat(e.join(",")):"",O=async e=>{let{instance:t,id:r,params:n}=e;return r?t.get("/customers/".concat(r,"?").concat(I(null==n?void 0:n.expand))):{data:null,error:new y({message:"Customer ID is required",code:"CUSTOMER_ID_REQUIRED"})}},A=async e=>{let{instance:t,params:r}=e;return t.post("/customers?".concat(I(null==r?void 0:r.expand)),r)},P=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r),n)},j=async e=>{let{instance:t,id:r}=e;return t.delete("/customers/".concat(r))},E=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r,"/billing_portal"),n)},Z=e=>({get:(t,r,n)=>T(N,e,{customer_id:t,entity_id:r,params:n}),create:(t,r)=>T(U,e,{customer_id:t,params:r}),delete:(t,r)=>T(D,e,{customer_id:t,entity_id:r})}),S=e=>e?"expand=".concat(e.join(",")):"",N=async e=>{let{instance:t,customer_id:r,entity_id:n,params:a}=e;return t.get("/customers/".concat(r,"/entities/").concat(n,"?").concat(S(null==a?void 0:a.expand)))},U=async e=>{let{instance:t,customer_id:r,params:n}=e;return t.post("/customers/".concat(r,"/entities"),n)},D=async e=>{let{instance:t,customer_id:r,entity_id:n}=e;return t.delete("/customers/".concat(r,"/entities/").concat(n))},R=e=>({get:t=>T(F,e,{id:t}),create:t=>T(M,e,{params:t}),list:t=>T(L,e,{params:t})}),L=async e=>{let{instance:t,params:r}=e,n="/products_beta";if(r){let e=new URLSearchParams;for(let[t,n]of Object.entries(r))void 0!==n&&e.append(t,String(n));let t=e.toString();t&&(n+="?".concat(t))}return t.get(n)},F=async e=>{let{instance:t,id:r}=e;return t.get("/products/".concat(r))},M=async e=>{let{instance:t,params:r}=e;return t.post("/products",r)},B=e=>({createCode:t=>T(V,e,{params:t}),redeemCode:t=>T(K,e,{params:t})}),V=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/code",r)},K=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/redeem",r)},$=async e=>{let{response:t,logger:r,logError:n=!0}=e;if(t.status<200||t.status>=300){let e;try{e=await t.json(),n&&r.error("[Autumn] ".concat(e.message))}catch(e){throw e}return{data:null,error:new y({message:e.message,code:e.code}),statusCode:t.status}}try{return{data:await t.json(),error:null,statusCode:null==t?void 0:t.status}}catch(e){throw e}},q=p(r(83809)),W=()=>{let e=new Date().toISOString();return"[".concat(e.split("T")[1].split(".")[0],"]")},H=e=>J.indexOf(e)>=J.indexOf(Y.level),J=["debug","info","warn","error","fatal"],Y={...console,level:"info",debug:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];H("debug")&&console.log(W(),q.default.gray("DEBUG"),...t)},log:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];console.log(W(),q.default.blue("INFO"),...t)},info:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];H("info")&&console.log(W(),q.default.blue("INFO"),...t)},warn:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];H("warn")&&console.log(W(),q.default.yellow("WARN"),...t)},error:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];H("error")&&console.log(W(),q.default.red("ERROR"),...t)}},G=((n=class{async get(e){return $({response:await fetch("".concat(this.url).concat(e),{headers:this.headers}),logger:this.logger})}async post(e,t){try{let r=await fetch("".concat(this.url).concat(e),{method:"POST",headers:this.headers,body:JSON.stringify(t)});return $({response:r,logger:this.logger})}catch(e){throw console.error("Error sending request:",e),e}}async delete(e){return $({response:await fetch("".concat(this.url).concat(e),{method:"DELETE",headers:this.headers}),logger:this.logger})}async attach(e){return g({instance:this,params:e})}async setupPayment(e){return _({instance:this,params:e})}async cancel(e){return v({instance:this,params:e})}async entitled(e){return b({instance:this,params:e})}async check(e){return z({instance:this,params:e})}async event(e){return w({instance:this,params:e})}async track(e){return x({instance:this,params:e})}async usage(e){return C({instance:this,params:e})}constructor(e){this.logger=console,this.customers=k(this),this.products=R(this),this.entities=Z(this),this.referrals=B(this);try{this.secretKey=(null==e?void 0:e.secretKey)||a.env.AUTUMN_SECRET_KEY,this.publishableKey=(null==e?void 0:e.publishableKey)||a.env.AUTUMN_PUBLISHABLE_KEY}catch(e){}if(!this.secretKey&&!this.publishableKey&&!(null==e?void 0:e.headers))throw Error("Autumn secret key or publishable key is required");this.headers=(null==e?void 0:e.headers)||{Authorization:"Bearer ".concat(this.secretKey||this.publishableKey),"Content-Type":"application/json"};let t=(null==e?void 0:e.version)||"1.2";this.headers["x-api-version"]=t,this.url=(null==e?void 0:e.url)||"https://api.useautumn.com/v1",this.logger=Y,this.logger.level=(null==e?void 0:e.logLevel)||"info"}}).customers=k(),n.products=R(),n.entities=Z(),n.referrals=B(),n.attach=e=>T(g,void 0,{params:e}),n.usage=e=>T(C,void 0,{params:e}),n.setupPayment=e=>T(_,void 0,{params:e}),n.cancel=e=>T(v,void 0,{params:e}),n.entitled=e=>T(b,void 0,{params:e}),n.check=e=>T(z,void 0,{params:e}),n.event=e=>T(w,void 0,{params:e}),n.track=e=>T(x,void 0,{params:e}),n),Q=r(28935),X=Q.z.object({name:Q.z.string().optional(),feature_id:Q.z.string()}),ee=r(28935);ee.z.object({feature_id:ee.z.string(),quantity:ee.z.number()}),ee.z.object({customer_id:ee.z.string(),product_id:ee.z.string().optional(),entity_id:ee.z.string().optional(),options:ee.z.array(ee.z.object({feature_id:ee.z.string(),quantity:ee.z.number()})).optional(),product_ids:ee.z.array(ee.z.string()).optional(),free_trial:ee.z.boolean().optional(),success_url:ee.z.string().optional(),metadata:ee.z.record(ee.z.string()).optional(),force_checkout:ee.z.boolean().optional(),customer_data:ee.z.any().optional(),entity_data:ee.z.any().optional(),checkout_session_params:ee.z.record(ee.z.any()).optional(),reward:ee.z.string().optional()}),ee.z.object({checkout_url:ee.z.string().optional(),customer_id:ee.z.string(),product_ids:ee.z.array(ee.z.string()),code:ee.z.string(),message:ee.z.string(),customer_data:ee.z.any().optional()}),ee.z.object({customer_id:ee.z.string(),product_id:ee.z.string(),entity_id:ee.z.string().optional(),cancel_immediately:ee.z.boolean().optional()}),ee.z.object({success:ee.z.boolean(),customer_id:ee.z.string(),product_id:ee.z.string()}),ee.z.object({customer_id:ee.z.string(),value:ee.z.number().optional(),feature_id:ee.z.string().optional(),event_name:ee.z.string().optional(),entity_id:ee.z.string().optional(),customer_data:ee.z.any().optional(),idempotency_key:ee.z.string().optional(),entity_data:ee.z.any().optional()}),ee.z.object({id:ee.z.string(),code:ee.z.string(),customer_id:ee.z.string(),feature_id:ee.z.string().optional(),event_name:ee.z.string().optional()}),ee.z.object({customer_id:ee.z.string(),feature_id:ee.z.string().optional(),product_id:ee.z.string().optional(),entity_id:ee.z.string().optional(),customer_data:ee.z.any().optional(),required_balance:ee.z.number().optional(),send_event:ee.z.boolean().optional(),with_preview:ee.z.boolean().optional(),entity_data:X.optional()});var et=r(28935).z.enum(["invoices","rewards","trials_used","entities","referrals","payment_method"]),er=r(28935);er.z.object({id:er.z.string().nullish(),email:er.z.string().nullish(),name:er.z.string().nullish(),fingerprint:er.z.string().nullish(),metadata:er.z.record(er.z.any()).optional(),expand:er.z.array(et).optional()}),er.z.object({return_url:er.z.string().optional()});var en=r(28935);en.z.object({customer_id:en.z.string(),program_id:en.z.string()}),en.z.object({code:en.z.string(),customer_id:en.z.string()});var ea=e=>{let{method:t,backendUrl:r,path:n,error:a}=e;console.error("[Autumn] Fetch failed: ".concat(t," ").concat(r).concat(n,"\n\n1. Check that backendUrl in <AutumnProvider/> is correctly set.\n2. Check that autumnHandler is correctly registered on your backend."))};async function ei(){return await this.get("".concat(this.prefix,"/components/pricing_table"))}var es=async e=>{let{client:t,params:r}=e;return await t.post("".concat(t.prefix,"/customers"),r)},eo=(e,t)=>Array.isArray(e)?e.map(e=>eo(e,t)):null!==e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(e=>{let[r,n]=e,a=r.replace(/([a-z])([A-Z])/g,"$1_$2").replace(/[-\s]+/g,"_").toLowerCase();return t&&t.includes(r)?[a,n]:[a,eo(n,t)]})):e,ec=e=>e?"expand=".concat(e.join(",")):"";async function el(e){let t=eo(e);return await this.post("".concat(this.prefix,"/entities"),t)}async function eu(e,t){eo(t);let r=ec(null==t?void 0:t.expand);return await this.get("".concat(this.prefix,"/entities/").concat(e,"?").concat(r))}async function ed(e){return await this.delete("".concat(this.prefix,"/entities/").concat(e))}async function ep(e){let{dialog:t,...r}=e,n=eo(r,["checkoutSessionparams"]);return await this.post("".concat(this.prefix,"/attach"),n)}async function eh(e){let t=eo(e,["checkoutSessionParams"]);return await this.post("".concat(this.prefix,"/setup_payment"),t)}async function ef(e){let t=eo(e);return await this.post("".concat(this.prefix,"/cancel"),t)}async function em(e){let{dialog:t,...r}=e,n=eo(r);return await this.post("".concat(this.prefix,"/check"),n)}async function ey(e){let t=eo(e);return await this.post("".concat(this.prefix,"/track"),t)}async function eg(e){let t=eo(e||{});return await this.post("".concat(this.prefix,"/billing_portal"),t)}async function e_(){return await this.get("".concat(this.prefix,"/products"))}async function ev(e){let t=eo(e);return await this.post("".concat(this.prefix,"/referrals/code"),t)}async function eb(e){let t=eo(e);return await this.post("".concat(this.prefix,"/referrals/redeem"),t)}var ew=class{async detectCors(){var e;if(null==(e=this.prefix)?void 0:e.includes("/api/auth"))return{valid:!0,includeCredentials:!0};let t="".concat(this.backendUrl,"/api/autumn/cors");try{return await fetch(t,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!0}}catch(e){try{return await fetch(t,{method:"POST",credentials:"omit",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!1}}catch(e){return{valid:!1,includeCredentials:void 0}}}}async shouldIncludeCredentials(){if(void 0!==this.includeCredentials)return this.includeCredentials;try{let e=await this.detectCors();return e.valid&&(console.warn("[Autumn] Detected CORS credentials: ".concat(e.includeCredentials)),console.warn("[Autumn] To disable this warning, you can set includeCredentials={".concat(e.includeCredentials?"true":"false","} in <AutumnProvider />")),this.includeCredentials=e.includeCredentials),e.includeCredentials||!1}catch(e){return console.error("[Autumn] Error detecting CORS: ".concat(e.message)),!1}}async getHeaders(){let e={"Content-Type":"application/json"};if(this.getBearerToken)try{let t=await this.getBearerToken();e.Authorization="Bearer ".concat(t)}catch(e){console.error("Failed to call getToken() in AutumnProvider")}return e}async handleFetch(e){let{path:t,method:r,body:n}=e;n="POST"===r?JSON.stringify({...n,customer_data:this.customerData||void 0}):void 0;let a=await this.shouldIncludeCredentials();try{let e=await fetch("".concat(this.backendUrl).concat(t),{method:r,body:n,headers:await this.getHeaders(),credentials:a?"include":"omit"}),i=await m(e);return await $({response:e,logger:console,logError:!i})}catch(e){return ea({method:r,backendUrl:this.backendUrl||"",path:t,error:e}),{data:null,error:new y({message:e.message,code:"fetch_failed"})}}}async post(e,t){return await this.handleFetch({path:e,method:"POST",body:t})}async get(e){return await this.handleFetch({path:e,method:"GET"})}async delete(e){return await this.handleFetch({path:e,method:"DELETE"})}async createCustomer(e){return await es({client:this,params:e})}async getPricingTable(){return await ei.bind(this)()}constructor({backendUrl:e,getBearerToken:t,customerData:r,includeCredentials:n,betterAuthUrl:a}){this.attach=ep.bind(this),this.cancel=ef.bind(this),this.check=em.bind(this),this.track=ey.bind(this),this.openBillingPortal=eg.bind(this),this.setupPayment=eh.bind(this),this.entities={create:el.bind(this),get:eu.bind(this),delete:ed.bind(this)},this.referrals={createCode:ev.bind(this),redeemCode:eb.bind(this)},this.products={list:e_.bind(this)},this.backendUrl=e,this.getBearerToken=t,this.customerData=r,this.includeCredentials=n,this.prefix="/api/autumn",a&&(this.prefix="/api/auth/autumn",this.backendUrl=a)}},ex=r(12359);(0,ex.createContext)({initialized:!1,disableDialogs:!1,client:new ew({backendUrl:""}),paywallDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}},attachDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}}});var eC=e=>{let{AutumnContext:t,name:r,errorIfNotInitialized:n=!0}=e,a=(0,ex.useContext)(t);if(!a.initialized&&n)throw Error("".concat(r," must be used within <AutumnProvider />"));return a},ez=p(r(59153)),eT=(e,t)=>{if(!e)return null;if(!t)return e.map(t=>{if(t.base_variant_id){let r=e.find(e=>e.id===t.base_variant_id);if(r)return{...t,name:r.name}}return t});let r=structuredClone(e),n=[];for(let e of t){var a,i,s,o;if(!e.id){let t={},r=null==(i=e.items)?void 0:i.map(e=>({display:{primary_text:e.primaryText,secondary_text:e.secondaryText}})),a=e.price;e.price&&(t.is_free=!1,r=[{display:{primary_text:null==a?void 0:a.primaryText,secondary_text:null==a?void 0:a.secondaryText}},...r||[]]),r&&0!==r.length||(r=[{display:{primary_text:""}}]),n.push({display:{name:e.name,description:e.description,button_text:e.buttonText,recommend_text:e.recommendText,everything_from:e.everythingFrom,button_url:e.buttonUrl},items:r,properties:t});continue}let t=r.find(t=>t.id===e.id);if(!t)continue;let c=t.name,l=t.base_variant_id;if(l){let e=r.find(e=>e.id===l);e&&(c=e.name)}c=e.name||c;let u=null==(a=t.properties)?void 0:a.is_free,d=t.properties||{},p=e.items,h=e.price,f=[];if(h?(d.is_free=!1,u||void 0!==p?f.push({display:{primary_text:h.primaryText,secondary_text:h.secondaryText}}):t.items[0].display={primary_text:h.primaryText,secondary_text:h.secondaryText}):p&&!u&&f.push(t.items[0]),p)for(let e of p)if(e.featureId){let r=t.items.find(t=>t.feature_id===e.featureId);if(!r){console.error("Feature with id ".concat(e.featureId," not found for product ").concat(t.id));continue}f.push({...r,display:{primary_text:e.primaryText||(null==(s=r.display)?void 0:s.primary_text),secondary_text:e.secondaryText||(null==(o=r.display)?void 0:o.secondary_text)}})}else f.push({display:{primary_text:e.primaryText,secondary_text:e.secondaryText}});else f=t.items;let m={...t,items:f,properties:d,display:{name:c,description:e.description,button_text:e.buttonText,recommend_text:e.recommendText,everything_from:e.everythingFrom,button_url:e.buttonUrl}};n.push(m)}return n},ek={refreshInterval:0},eI=e=>{let{AutumnContext:t,params:r,authClient:n}=e,a=eC({AutumnContext:t,name:"usePricingTable",errorIfNotInitialized:!n}),i=n?n.autumn:a.client,s=async()=>{try{let{data:e,error:t}=await i.products.list();if(t)throw t;return(null==e?void 0:e.list)||[]}catch(e){throw new y({message:"Failed to fetch pricing table products",code:"failed_to_fetch_pricing_table_products"})}},{data:o,error:c,mutate:l}=(0,ez.default)("pricing-table",s,{...ek});return{products:eT(o||[],null==r?void 0:r.productDetails),isLoading:!c&&!o,error:c,refetch:l}},eO=e=>{let{AutumnContext:t,authClient:r}=e,n=eC({AutumnContext:t,name:"useAutumn",errorIfNotInitialized:!r}),{attachDialog:a,paywallDialog:i}=n,s=r?r.autumn:n.client,o=!!r,{refetch:c}=eI({AutumnContext:t,authClient:r}),{open:l,setProps:u,setOpen:d,setComponent:p}=a,{setProps:h,setOpen:f,setComponent:m}=i,y=async e=>{let t=await s.attach(e);if(t.error)return t;let r=t.data;return(null==r?void 0:r.checkout_url)&&"undefined"!=typeof window&&(e.openInNewTab?window.open(r.checkout_url,"_blank"):window.location.href=r.checkout_url),await c(),d&&d(!1),t},g=async e=>{let{dialog:t,...r}=e,{productId:n,entityId:a,entityData:i}=e,o=await s.check({productId:n,entityId:a,entityData:i,withPreview:!0});if(o.error)return o;let c=o.data.preview;return c?(u({preview:c,attachParams:r}),d(!0),o):await y(r)};return{attach:async e=>{let{dialog:t,openInNewTab:r}=e;return t&&o?void console.error("[Autumn] Attach dialog cannot be used with better auth plugin. To use this, please switch to <AutumnProvider /> and autumnHandler. Learn more here: https://docs.useautumn.com/quickstart/quickstart"):t&&!l?(p(t),await g(e)):await y(e)},check:async e=>{let{dialog:t,withPreview:r}=e;if(t&&o)return void console.error("[Autumn] Check dialog cannot be used with better auth plugin. To use this, please switch to <AutumnProvider /> and autumnHandler. Learn more here: https://docs.useautumn.com/quickstart/quickstart");t&&m(t);let n=await s.check({...e,withPreview:!!r||!!t});if(n.error)return n;let a=n.data;return a&&a.preview&&t&&(h({preview:a.preview}),f(!0)),n},track:async e=>{let t=await s.track(e);return t.error,t},cancel:async e=>{let t=await s.cancel(e);return t.error,t},openBillingPortal:async e=>{let t={openInNewTab:!1,...e},r=await s.openBillingPortal(t);if(r.error)return r;let n=r.data;return(null==n?void 0:n.url)&&"undefined"!=typeof window&&(t.openInNewTab?window.open(n.url,"_blank"):window.open(n.url,"_self")),r},setupPayment:async e=>{var t;let r={openInNewTab:!1,...e||{}},n=await s.setupPayment(r);return(null==(t=n.data)?void 0:t.url)&&"undefined"!=typeof window&&(r.openInNewTab?window.open(n.data.url,"_blank"):window.open(n.data.url,"_self")),n}}},eA=e=>{let{customer:t,featureId:r,requiredBalance:n=1}=e,a=Object.values(t.features).find(e=>e.credit_schema&&e.credit_schema.some(e=>e.feature_id===r));if(a){var i;let e=null==(i=a.credit_schema)?void 0:i.find(e=>e.feature_id===r);return{feature:a,requiredBalance:e.credit_amount*n}}return{cusFeature:t.features[r],requiredBalance:n}},eP=e=>{let{customer:t,params:r}=e,{cusFeature:n,requiredBalance:a}=eA({customer:t,featureId:r.featureId});if(!n)return!1;if("static"==n.type||n.unlimited||n.overage_allowed)return!0;if(n.usage_limit){let e=(n.usage_limit||0)-(n.included_usage||0);return(n.balance||0)+e>=a}return(n.balance||0)>=a},ej=e=>{let{customer:t,params:r}=e;if(!t)return!1;if(!r.featureId&&!r.productId)throw Error("allowed() requires either featureId or productId");if(r.featureId)return eP({customer:t,params:r});if(r.productId){let e=t.products.find(e=>e.id==r.productId);return!!e&&"scheduled"!=e.status}return!1},eE={attach:"",check:"",track:"",cancel:"",openBillingPortal:"",setupPayment:""},eZ=e=>{let t,{params:r,AutumnContext:n,client:a}=e;n&&(t=eC({AutumnContext:n,name:"useCustomer"})),a||(a=t.client);let i=["customer",(null==a?void 0:a.backendUrl)||"",null==r?void 0:r.expand],s=async()=>{let{data:e,error:t}=await a.createCustomer({errorOnNotFound:null==r?void 0:r.errorOnNotFound,expand:null==r?void 0:r.expand});if(t)throw t;return e||null},{data:o,error:c,isLoading:l,mutate:u}=(0,f.default)(i,s,{fallbackData:null,...null==r?void 0:r.swrConfig}),d=eE;return n&&(d=eO({AutumnContext:n})),{customer:c?null:o,isLoading:l,error:c,refetch:u,...d,createEntity:a.entities.create,createReferralCode:a.referrals.createCode,redeemReferralCode:a.referrals.redeemCode,allowed:e=>ej({customer:o,params:e})}}},14776:(e,t,r)=>{"use strict";var n,a=r(37811),i=Object.create,s=Object.defineProperty,o=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,d=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of c(t))u.call(e,a)||a===r||s(e,a,{get:()=>t[a],enumerable:!(n=o(t,a))||n.enumerable});return e},p={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(p,{AutumnContext:()=>ew,useAutumnContext:()=>ex}),e.exports=d(s({},"__esModule",{value:!0}),p);var h=async e=>{if(401===e.status){let t=e.clone();if((await t.json()).message.includes("Missing authorization header"))return console.error("[Autumn] Missing authorization header.\n\nUse the getBearerToken prop in <AutumnProvider /> to set the authorization header.\nhttps://docs.useautumn.com/quickstart/quickstart#5-set-up-autumnprovider"),!0}return!1},f=class e extends Error{static fromError(t){return new e({message:t.message||"Unknown error",code:t.code||"unknown_error"})}toString(){return"".concat(this.message," (code: ").concat(this.code,")")}toJSON(){return{message:this.message,code:this.code}}constructor(e){super(e.message),this.message=e.message,this.code=e.code}},m=async e=>{let{instance:t,params:r}=e;return t.post("/attach",r)},y=async e=>{let{instance:t,params:r}=e;return t.post("/setup_payment",r)},g=async e=>{let{instance:t,params:r}=e;return t.post("/cancel",r)},_=async e=>{let{instance:t,params:r}=e;return t.post("/entitled",r)},v=async e=>{let{instance:t,params:r}=e;return t.post("/events",r)},b=async e=>{let{instance:t,params:r}=e;return t.post("/track",r)},w=async e=>{let{instance:t,params:r}=e;return t.post("/usage",r)},x=async e=>{let{instance:t,params:r}=e;return t.post("/check",r)},C=(e,t,r)=>(t||(t=new J),e({instance:t,...r})),z=e=>({get:(t,r)=>C(k,e,{id:t,params:r}),create:t=>C(I,e,{params:t}),update:(t,r)=>C(O,e,{id:t,params:r}),delete:t=>C(A,e,{id:t}),billingPortal:(t,r)=>C(P,e,{id:t,params:r})}),T=e=>e?"expand=".concat(e.join(",")):"",k=async e=>{let{instance:t,id:r,params:n}=e;return r?t.get("/customers/".concat(r,"?").concat(T(null==n?void 0:n.expand))):{data:null,error:new f({message:"Customer ID is required",code:"CUSTOMER_ID_REQUIRED"})}},I=async e=>{let{instance:t,params:r}=e;return t.post("/customers?".concat(T(null==r?void 0:r.expand)),r)},O=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r),n)},A=async e=>{let{instance:t,id:r}=e;return t.delete("/customers/".concat(r))},P=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r,"/billing_portal"),n)},j=e=>({get:(t,r,n)=>C(Z,e,{customer_id:t,entity_id:r,params:n}),create:(t,r)=>C(S,e,{customer_id:t,params:r}),delete:(t,r)=>C(N,e,{customer_id:t,entity_id:r})}),E=e=>e?"expand=".concat(e.join(",")):"",Z=async e=>{let{instance:t,customer_id:r,entity_id:n,params:a}=e;return t.get("/customers/".concat(r,"/entities/").concat(n,"?").concat(E(null==a?void 0:a.expand)))},S=async e=>{let{instance:t,customer_id:r,params:n}=e;return t.post("/customers/".concat(r,"/entities"),n)},N=async e=>{let{instance:t,customer_id:r,entity_id:n}=e;return t.delete("/customers/".concat(r,"/entities/").concat(n))},U=e=>({get:t=>C(R,e,{id:t}),create:t=>C(L,e,{params:t}),list:t=>C(D,e,{params:t})}),D=async e=>{let{instance:t,params:r}=e,n="/products_beta";if(r){let e=new URLSearchParams;for(let[t,n]of Object.entries(r))void 0!==n&&e.append(t,String(n));let t=e.toString();t&&(n+="?".concat(t))}return t.get(n)},R=async e=>{let{instance:t,id:r}=e;return t.get("/products/".concat(r))},L=async e=>{let{instance:t,params:r}=e;return t.post("/products",r)},F=e=>({createCode:t=>C(M,e,{params:t}),redeemCode:t=>C(B,e,{params:t})}),M=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/code",r)},B=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/redeem",r)},V=async e=>{let{response:t,logger:r,logError:n=!0}=e;if(t.status<200||t.status>=300){let e;try{e=await t.json(),n&&r.error("[Autumn] ".concat(e.message))}catch(e){throw e}return{data:null,error:new f({message:e.message,code:e.code}),statusCode:t.status}}try{return{data:await t.json(),error:null,statusCode:null==t?void 0:t.status}}catch(e){throw e}},K=((e,t,r)=>(r=null!=e?i(l(e)):{},d(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(83809)),$=()=>{let e=new Date().toISOString();return"[".concat(e.split("T")[1].split(".")[0],"]")},q=e=>W.indexOf(e)>=W.indexOf(H.level),W=["debug","info","warn","error","fatal"],H={...console,level:"info",debug:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];q("debug")&&console.log($(),K.default.gray("DEBUG"),...t)},log:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];console.log($(),K.default.blue("INFO"),...t)},info:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];q("info")&&console.log($(),K.default.blue("INFO"),...t)},warn:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];q("warn")&&console.log($(),K.default.yellow("WARN"),...t)},error:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];q("error")&&console.log($(),K.default.red("ERROR"),...t)}},J=((n=class{async get(e){return V({response:await fetch("".concat(this.url).concat(e),{headers:this.headers}),logger:this.logger})}async post(e,t){try{let r=await fetch("".concat(this.url).concat(e),{method:"POST",headers:this.headers,body:JSON.stringify(t)});return V({response:r,logger:this.logger})}catch(e){throw console.error("Error sending request:",e),e}}async delete(e){return V({response:await fetch("".concat(this.url).concat(e),{method:"DELETE",headers:this.headers}),logger:this.logger})}async attach(e){return m({instance:this,params:e})}async setupPayment(e){return y({instance:this,params:e})}async cancel(e){return g({instance:this,params:e})}async entitled(e){return _({instance:this,params:e})}async check(e){return x({instance:this,params:e})}async event(e){return v({instance:this,params:e})}async track(e){return b({instance:this,params:e})}async usage(e){return w({instance:this,params:e})}constructor(e){this.logger=console,this.customers=z(this),this.products=U(this),this.entities=j(this),this.referrals=F(this);try{this.secretKey=(null==e?void 0:e.secretKey)||a.env.AUTUMN_SECRET_KEY,this.publishableKey=(null==e?void 0:e.publishableKey)||a.env.AUTUMN_PUBLISHABLE_KEY}catch(e){}if(!this.secretKey&&!this.publishableKey&&!(null==e?void 0:e.headers))throw Error("Autumn secret key or publishable key is required");this.headers=(null==e?void 0:e.headers)||{Authorization:"Bearer ".concat(this.secretKey||this.publishableKey),"Content-Type":"application/json"};let t=(null==e?void 0:e.version)||"1.2";this.headers["x-api-version"]=t,this.url=(null==e?void 0:e.url)||"https://api.useautumn.com/v1",this.logger=H,this.logger.level=(null==e?void 0:e.logLevel)||"info"}}).customers=z(),n.products=U(),n.entities=j(),n.referrals=F(),n.attach=e=>C(m,void 0,{params:e}),n.usage=e=>C(w,void 0,{params:e}),n.setupPayment=e=>C(y,void 0,{params:e}),n.cancel=e=>C(g,void 0,{params:e}),n.entitled=e=>C(_,void 0,{params:e}),n.check=e=>C(x,void 0,{params:e}),n.event=e=>C(v,void 0,{params:e}),n.track=e=>C(b,void 0,{params:e}),n),Y=r(28935),G=Y.z.object({name:Y.z.string().optional(),feature_id:Y.z.string()}),Q=r(28935);Q.z.object({feature_id:Q.z.string(),quantity:Q.z.number()}),Q.z.object({customer_id:Q.z.string(),product_id:Q.z.string().optional(),entity_id:Q.z.string().optional(),options:Q.z.array(Q.z.object({feature_id:Q.z.string(),quantity:Q.z.number()})).optional(),product_ids:Q.z.array(Q.z.string()).optional(),free_trial:Q.z.boolean().optional(),success_url:Q.z.string().optional(),metadata:Q.z.record(Q.z.string()).optional(),force_checkout:Q.z.boolean().optional(),customer_data:Q.z.any().optional(),entity_data:Q.z.any().optional(),checkout_session_params:Q.z.record(Q.z.any()).optional(),reward:Q.z.string().optional()}),Q.z.object({checkout_url:Q.z.string().optional(),customer_id:Q.z.string(),product_ids:Q.z.array(Q.z.string()),code:Q.z.string(),message:Q.z.string(),customer_data:Q.z.any().optional()}),Q.z.object({customer_id:Q.z.string(),product_id:Q.z.string(),entity_id:Q.z.string().optional(),cancel_immediately:Q.z.boolean().optional()}),Q.z.object({success:Q.z.boolean(),customer_id:Q.z.string(),product_id:Q.z.string()}),Q.z.object({customer_id:Q.z.string(),value:Q.z.number().optional(),feature_id:Q.z.string().optional(),event_name:Q.z.string().optional(),entity_id:Q.z.string().optional(),customer_data:Q.z.any().optional(),idempotency_key:Q.z.string().optional(),entity_data:Q.z.any().optional()}),Q.z.object({id:Q.z.string(),code:Q.z.string(),customer_id:Q.z.string(),feature_id:Q.z.string().optional(),event_name:Q.z.string().optional()}),Q.z.object({customer_id:Q.z.string(),feature_id:Q.z.string().optional(),product_id:Q.z.string().optional(),entity_id:Q.z.string().optional(),customer_data:Q.z.any().optional(),required_balance:Q.z.number().optional(),send_event:Q.z.boolean().optional(),with_preview:Q.z.boolean().optional(),entity_data:G.optional()});var X=r(28935).z.enum(["invoices","rewards","trials_used","entities","referrals","payment_method"]),ee=r(28935);ee.z.object({id:ee.z.string().nullish(),email:ee.z.string().nullish(),name:ee.z.string().nullish(),fingerprint:ee.z.string().nullish(),metadata:ee.z.record(ee.z.any()).optional(),expand:ee.z.array(X).optional()}),ee.z.object({return_url:ee.z.string().optional()});var et=r(28935);et.z.object({customer_id:et.z.string(),program_id:et.z.string()}),et.z.object({code:et.z.string(),customer_id:et.z.string()});var er=e=>{let{method:t,backendUrl:r,path:n,error:a}=e;console.error("[Autumn] Fetch failed: ".concat(t," ").concat(r).concat(n,"\n\n1. Check that backendUrl in <AutumnProvider/> is correctly set.\n2. Check that autumnHandler is correctly registered on your backend."))};async function en(){return await this.get("".concat(this.prefix,"/components/pricing_table"))}var ea=async e=>{let{client:t,params:r}=e;return await t.post("".concat(t.prefix,"/customers"),r)},ei=(e,t)=>Array.isArray(e)?e.map(e=>ei(e,t)):null!==e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(e=>{let[r,n]=e,a=r.replace(/([a-z])([A-Z])/g,"$1_$2").replace(/[-\s]+/g,"_").toLowerCase();return t&&t.includes(r)?[a,n]:[a,ei(n,t)]})):e,es=e=>e?"expand=".concat(e.join(",")):"";async function eo(e){let t=ei(e);return await this.post("".concat(this.prefix,"/entities"),t)}async function ec(e,t){ei(t);let r=es(null==t?void 0:t.expand);return await this.get("".concat(this.prefix,"/entities/").concat(e,"?").concat(r))}async function el(e){return await this.delete("".concat(this.prefix,"/entities/").concat(e))}async function eu(e){let{dialog:t,...r}=e,n=ei(r,["checkoutSessionparams"]);return await this.post("".concat(this.prefix,"/attach"),n)}async function ed(e){let t=ei(e,["checkoutSessionParams"]);return await this.post("".concat(this.prefix,"/setup_payment"),t)}async function ep(e){let t=ei(e);return await this.post("".concat(this.prefix,"/cancel"),t)}async function eh(e){let{dialog:t,...r}=e,n=ei(r);return await this.post("".concat(this.prefix,"/check"),n)}async function ef(e){let t=ei(e);return await this.post("".concat(this.prefix,"/track"),t)}async function em(e){let t=ei(e||{});return await this.post("".concat(this.prefix,"/billing_portal"),t)}async function ey(){return await this.get("".concat(this.prefix,"/products"))}async function eg(e){let t=ei(e);return await this.post("".concat(this.prefix,"/referrals/code"),t)}async function e_(e){let t=ei(e);return await this.post("".concat(this.prefix,"/referrals/redeem"),t)}var ev=class{async detectCors(){var e;if(null==(e=this.prefix)?void 0:e.includes("/api/auth"))return{valid:!0,includeCredentials:!0};let t="".concat(this.backendUrl,"/api/autumn/cors");try{return await fetch(t,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!0}}catch(e){try{return await fetch(t,{method:"POST",credentials:"omit",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!1}}catch(e){return{valid:!1,includeCredentials:void 0}}}}async shouldIncludeCredentials(){if(void 0!==this.includeCredentials)return this.includeCredentials;try{let e=await this.detectCors();return e.valid&&(console.warn("[Autumn] Detected CORS credentials: ".concat(e.includeCredentials)),console.warn("[Autumn] To disable this warning, you can set includeCredentials={".concat(e.includeCredentials?"true":"false","} in <AutumnProvider />")),this.includeCredentials=e.includeCredentials),e.includeCredentials||!1}catch(e){return console.error("[Autumn] Error detecting CORS: ".concat(e.message)),!1}}async getHeaders(){let e={"Content-Type":"application/json"};if(this.getBearerToken)try{let t=await this.getBearerToken();e.Authorization="Bearer ".concat(t)}catch(e){console.error("Failed to call getToken() in AutumnProvider")}return e}async handleFetch(e){let{path:t,method:r,body:n}=e;n="POST"===r?JSON.stringify({...n,customer_data:this.customerData||void 0}):void 0;let a=await this.shouldIncludeCredentials();try{let e=await fetch("".concat(this.backendUrl).concat(t),{method:r,body:n,headers:await this.getHeaders(),credentials:a?"include":"omit"}),i=await h(e);return await V({response:e,logger:console,logError:!i})}catch(e){return er({method:r,backendUrl:this.backendUrl||"",path:t,error:e}),{data:null,error:new f({message:e.message,code:"fetch_failed"})}}}async post(e,t){return await this.handleFetch({path:e,method:"POST",body:t})}async get(e){return await this.handleFetch({path:e,method:"GET"})}async delete(e){return await this.handleFetch({path:e,method:"DELETE"})}async createCustomer(e){return await ea({client:this,params:e})}async getPricingTable(){return await en.bind(this)()}constructor({backendUrl:e,getBearerToken:t,customerData:r,includeCredentials:n,betterAuthUrl:a}){this.attach=eu.bind(this),this.cancel=ep.bind(this),this.check=eh.bind(this),this.track=ef.bind(this),this.openBillingPortal=em.bind(this),this.setupPayment=ed.bind(this),this.entities={create:eo.bind(this),get:ec.bind(this),delete:el.bind(this)},this.referrals={createCode:eg.bind(this),redeemCode:e_.bind(this)},this.products={list:ey.bind(this)},this.backendUrl=e,this.getBearerToken=t,this.customerData=r,this.includeCredentials=n,this.prefix="/api/autumn",a&&(this.prefix="/api/auth/autumn",this.backendUrl=a)}},eb=r(12359),ew=(0,eb.createContext)({initialized:!1,disableDialogs:!1,client:new ev({backendUrl:""}),paywallDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}},attachDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}}}),ex=e=>{let{AutumnContext:t,name:r,errorIfNotInitialized:n=!0}=e,a=(0,eb.useContext)(t);if(!a.initialized&&n)throw Error("".concat(r," must be used within <AutumnProvider />"));return a}},15232:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.isAsync=t.isValid=t.isDirty=t.isAborted=t.OK=t.DIRTY=t.INVALID=t.ParseStatus=t.EMPTY_PATH=t.makeIssue=void 0,t.addIssueToContext=function(e,r){let n=(0,a.getErrorMap)(),s=(0,t.makeIssue)({issueData:r,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===i.default?void 0:i.default].filter(e=>!!e)});e.common.issues.push(s)};let a=r(66742),i=n(r(91286));t.makeIssue=e=>{let{data:t,path:r,errorMaps:n,issueData:a}=e,i=[...r,...a.path||[]],s={...a,path:i};if(void 0!==a.message)return{...a,path:i,message:a.message};let o="";for(let e of n.filter(e=>!!e).slice().reverse())o=e(s,{data:t,defaultError:o}).message;return{...a,path:i,message:o}},t.EMPTY_PATH=[];class s{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,r){let n=[];for(let a of r){if("aborted"===a.status)return t.INVALID;"dirty"===a.status&&e.dirty(),n.push(a.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,n=await e.value;r.push({key:t,value:n})}return s.mergeObjectSync(e,r)}static mergeObjectSync(e,r){let n={};for(let a of r){let{key:r,value:i}=a;if("aborted"===r.status||"aborted"===i.status)return t.INVALID;"dirty"===r.status&&e.dirty(),"dirty"===i.status&&e.dirty(),"__proto__"!==r.value&&(void 0!==i.value||a.alwaysSet)&&(n[r.value]=i.value)}return{status:e.value,value:n}}}t.ParseStatus=s,t.INVALID=Object.freeze({status:"aborted"}),t.DIRTY=e=>({status:"dirty",value:e}),t.OK=e=>({status:"valid",value:e}),t.isAborted=e=>"aborted"===e.status,t.isDirty=e=>"dirty"===e.status,t.isValid=e=>"valid"===e.status,t.isAsync=e=>"undefined"!=typeof Promise&&e instanceof Promise},16254:(e,t,r)=>{"use strict";var n,a=r(37811),i=Object.create,s=Object.defineProperty,o=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,d=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of c(t))u.call(e,a)||a===r||s(e,a,{get:()=>t[a],enumerable:!(n=o(t,a))||n.enumerable});return e},p=(e,t,r)=>(r=null!=e?i(l(e)):{},d(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)),h={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(h,{useAutumnBase:()=>eI}),e.exports=d(s({},"__esModule",{value:!0}),h);var f=async e=>{if(401===e.status){let t=e.clone();if((await t.json()).message.includes("Missing authorization header"))return console.error("[Autumn] Missing authorization header.\n\nUse the getBearerToken prop in <AutumnProvider /> to set the authorization header.\nhttps://docs.useautumn.com/quickstart/quickstart#5-set-up-autumnprovider"),!0}return!1},m=class e extends Error{static fromError(t){return new e({message:t.message||"Unknown error",code:t.code||"unknown_error"})}toString(){return"".concat(this.message," (code: ").concat(this.code,")")}toJSON(){return{message:this.message,code:this.code}}constructor(e){super(e.message),this.message=e.message,this.code=e.code}},y=async e=>{let{instance:t,params:r}=e;return t.post("/attach",r)},g=async e=>{let{instance:t,params:r}=e;return t.post("/setup_payment",r)},_=async e=>{let{instance:t,params:r}=e;return t.post("/cancel",r)},v=async e=>{let{instance:t,params:r}=e;return t.post("/entitled",r)},b=async e=>{let{instance:t,params:r}=e;return t.post("/events",r)},w=async e=>{let{instance:t,params:r}=e;return t.post("/track",r)},x=async e=>{let{instance:t,params:r}=e;return t.post("/usage",r)},C=async e=>{let{instance:t,params:r}=e;return t.post("/check",r)},z=(e,t,r)=>(t||(t=new Y),e({instance:t,...r})),T=e=>({get:(t,r)=>z(I,e,{id:t,params:r}),create:t=>z(O,e,{params:t}),update:(t,r)=>z(A,e,{id:t,params:r}),delete:t=>z(P,e,{id:t}),billingPortal:(t,r)=>z(j,e,{id:t,params:r})}),k=e=>e?"expand=".concat(e.join(",")):"",I=async e=>{let{instance:t,id:r,params:n}=e;return r?t.get("/customers/".concat(r,"?").concat(k(null==n?void 0:n.expand))):{data:null,error:new m({message:"Customer ID is required",code:"CUSTOMER_ID_REQUIRED"})}},O=async e=>{let{instance:t,params:r}=e;return t.post("/customers?".concat(k(null==r?void 0:r.expand)),r)},A=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r),n)},P=async e=>{let{instance:t,id:r}=e;return t.delete("/customers/".concat(r))},j=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r,"/billing_portal"),n)},E=e=>({get:(t,r,n)=>z(S,e,{customer_id:t,entity_id:r,params:n}),create:(t,r)=>z(N,e,{customer_id:t,params:r}),delete:(t,r)=>z(U,e,{customer_id:t,entity_id:r})}),Z=e=>e?"expand=".concat(e.join(",")):"",S=async e=>{let{instance:t,customer_id:r,entity_id:n,params:a}=e;return t.get("/customers/".concat(r,"/entities/").concat(n,"?").concat(Z(null==a?void 0:a.expand)))},N=async e=>{let{instance:t,customer_id:r,params:n}=e;return t.post("/customers/".concat(r,"/entities"),n)},U=async e=>{let{instance:t,customer_id:r,entity_id:n}=e;return t.delete("/customers/".concat(r,"/entities/").concat(n))},D=e=>({get:t=>z(L,e,{id:t}),create:t=>z(F,e,{params:t}),list:t=>z(R,e,{params:t})}),R=async e=>{let{instance:t,params:r}=e,n="/products_beta";if(r){let e=new URLSearchParams;for(let[t,n]of Object.entries(r))void 0!==n&&e.append(t,String(n));let t=e.toString();t&&(n+="?".concat(t))}return t.get(n)},L=async e=>{let{instance:t,id:r}=e;return t.get("/products/".concat(r))},F=async e=>{let{instance:t,params:r}=e;return t.post("/products",r)},M=e=>({createCode:t=>z(B,e,{params:t}),redeemCode:t=>z(V,e,{params:t})}),B=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/code",r)},V=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/redeem",r)},K=async e=>{let{response:t,logger:r,logError:n=!0}=e;if(t.status<200||t.status>=300){let e;try{e=await t.json(),n&&r.error("[Autumn] ".concat(e.message))}catch(e){throw e}return{data:null,error:new m({message:e.message,code:e.code}),statusCode:t.status}}try{return{data:await t.json(),error:null,statusCode:null==t?void 0:t.status}}catch(e){throw e}},$=p(r(83809)),q=()=>{let e=new Date().toISOString();return"[".concat(e.split("T")[1].split(".")[0],"]")},W=e=>H.indexOf(e)>=H.indexOf(J.level),H=["debug","info","warn","error","fatal"],J={...console,level:"info",debug:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];W("debug")&&console.log(q(),$.default.gray("DEBUG"),...t)},log:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];console.log(q(),$.default.blue("INFO"),...t)},info:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];W("info")&&console.log(q(),$.default.blue("INFO"),...t)},warn:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];W("warn")&&console.log(q(),$.default.yellow("WARN"),...t)},error:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];W("error")&&console.log(q(),$.default.red("ERROR"),...t)}},Y=((n=class{async get(e){return K({response:await fetch("".concat(this.url).concat(e),{headers:this.headers}),logger:this.logger})}async post(e,t){try{let r=await fetch("".concat(this.url).concat(e),{method:"POST",headers:this.headers,body:JSON.stringify(t)});return K({response:r,logger:this.logger})}catch(e){throw console.error("Error sending request:",e),e}}async delete(e){return K({response:await fetch("".concat(this.url).concat(e),{method:"DELETE",headers:this.headers}),logger:this.logger})}async attach(e){return y({instance:this,params:e})}async setupPayment(e){return g({instance:this,params:e})}async cancel(e){return _({instance:this,params:e})}async entitled(e){return v({instance:this,params:e})}async check(e){return C({instance:this,params:e})}async event(e){return b({instance:this,params:e})}async track(e){return w({instance:this,params:e})}async usage(e){return x({instance:this,params:e})}constructor(e){this.logger=console,this.customers=T(this),this.products=D(this),this.entities=E(this),this.referrals=M(this);try{this.secretKey=(null==e?void 0:e.secretKey)||a.env.AUTUMN_SECRET_KEY,this.publishableKey=(null==e?void 0:e.publishableKey)||a.env.AUTUMN_PUBLISHABLE_KEY}catch(e){}if(!this.secretKey&&!this.publishableKey&&!(null==e?void 0:e.headers))throw Error("Autumn secret key or publishable key is required");this.headers=(null==e?void 0:e.headers)||{Authorization:"Bearer ".concat(this.secretKey||this.publishableKey),"Content-Type":"application/json"};let t=(null==e?void 0:e.version)||"1.2";this.headers["x-api-version"]=t,this.url=(null==e?void 0:e.url)||"https://api.useautumn.com/v1",this.logger=J,this.logger.level=(null==e?void 0:e.logLevel)||"info"}}).customers=T(),n.products=D(),n.entities=E(),n.referrals=M(),n.attach=e=>z(y,void 0,{params:e}),n.usage=e=>z(x,void 0,{params:e}),n.setupPayment=e=>z(g,void 0,{params:e}),n.cancel=e=>z(_,void 0,{params:e}),n.entitled=e=>z(v,void 0,{params:e}),n.check=e=>z(C,void 0,{params:e}),n.event=e=>z(b,void 0,{params:e}),n.track=e=>z(w,void 0,{params:e}),n),G=r(28935),Q=G.z.object({name:G.z.string().optional(),feature_id:G.z.string()}),X=r(28935);X.z.object({feature_id:X.z.string(),quantity:X.z.number()}),X.z.object({customer_id:X.z.string(),product_id:X.z.string().optional(),entity_id:X.z.string().optional(),options:X.z.array(X.z.object({feature_id:X.z.string(),quantity:X.z.number()})).optional(),product_ids:X.z.array(X.z.string()).optional(),free_trial:X.z.boolean().optional(),success_url:X.z.string().optional(),metadata:X.z.record(X.z.string()).optional(),force_checkout:X.z.boolean().optional(),customer_data:X.z.any().optional(),entity_data:X.z.any().optional(),checkout_session_params:X.z.record(X.z.any()).optional(),reward:X.z.string().optional()}),X.z.object({checkout_url:X.z.string().optional(),customer_id:X.z.string(),product_ids:X.z.array(X.z.string()),code:X.z.string(),message:X.z.string(),customer_data:X.z.any().optional()}),X.z.object({customer_id:X.z.string(),product_id:X.z.string(),entity_id:X.z.string().optional(),cancel_immediately:X.z.boolean().optional()}),X.z.object({success:X.z.boolean(),customer_id:X.z.string(),product_id:X.z.string()}),X.z.object({customer_id:X.z.string(),value:X.z.number().optional(),feature_id:X.z.string().optional(),event_name:X.z.string().optional(),entity_id:X.z.string().optional(),customer_data:X.z.any().optional(),idempotency_key:X.z.string().optional(),entity_data:X.z.any().optional()}),X.z.object({id:X.z.string(),code:X.z.string(),customer_id:X.z.string(),feature_id:X.z.string().optional(),event_name:X.z.string().optional()}),X.z.object({customer_id:X.z.string(),feature_id:X.z.string().optional(),product_id:X.z.string().optional(),entity_id:X.z.string().optional(),customer_data:X.z.any().optional(),required_balance:X.z.number().optional(),send_event:X.z.boolean().optional(),with_preview:X.z.boolean().optional(),entity_data:Q.optional()});var ee=r(28935).z.enum(["invoices","rewards","trials_used","entities","referrals","payment_method"]),et=r(28935);et.z.object({id:et.z.string().nullish(),email:et.z.string().nullish(),name:et.z.string().nullish(),fingerprint:et.z.string().nullish(),metadata:et.z.record(et.z.any()).optional(),expand:et.z.array(ee).optional()}),et.z.object({return_url:et.z.string().optional()});var er=r(28935);er.z.object({customer_id:er.z.string(),program_id:er.z.string()}),er.z.object({code:er.z.string(),customer_id:er.z.string()});var en=e=>{let{method:t,backendUrl:r,path:n,error:a}=e;console.error("[Autumn] Fetch failed: ".concat(t," ").concat(r).concat(n,"\n\n1. Check that backendUrl in <AutumnProvider/> is correctly set.\n2. Check that autumnHandler is correctly registered on your backend."))};async function ea(){return await this.get("".concat(this.prefix,"/components/pricing_table"))}var ei=async e=>{let{client:t,params:r}=e;return await t.post("".concat(t.prefix,"/customers"),r)},es=(e,t)=>Array.isArray(e)?e.map(e=>es(e,t)):null!==e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(e=>{let[r,n]=e,a=r.replace(/([a-z])([A-Z])/g,"$1_$2").replace(/[-\s]+/g,"_").toLowerCase();return t&&t.includes(r)?[a,n]:[a,es(n,t)]})):e,eo=e=>e?"expand=".concat(e.join(",")):"";async function ec(e){let t=es(e);return await this.post("".concat(this.prefix,"/entities"),t)}async function el(e,t){es(t);let r=eo(null==t?void 0:t.expand);return await this.get("".concat(this.prefix,"/entities/").concat(e,"?").concat(r))}async function eu(e){return await this.delete("".concat(this.prefix,"/entities/").concat(e))}async function ed(e){let{dialog:t,...r}=e,n=es(r,["checkoutSessionparams"]);return await this.post("".concat(this.prefix,"/attach"),n)}async function ep(e){let t=es(e,["checkoutSessionParams"]);return await this.post("".concat(this.prefix,"/setup_payment"),t)}async function eh(e){let t=es(e);return await this.post("".concat(this.prefix,"/cancel"),t)}async function ef(e){let{dialog:t,...r}=e,n=es(r);return await this.post("".concat(this.prefix,"/check"),n)}async function em(e){let t=es(e);return await this.post("".concat(this.prefix,"/track"),t)}async function ey(e){let t=es(e||{});return await this.post("".concat(this.prefix,"/billing_portal"),t)}async function eg(){return await this.get("".concat(this.prefix,"/products"))}async function e_(e){let t=es(e);return await this.post("".concat(this.prefix,"/referrals/code"),t)}async function ev(e){let t=es(e);return await this.post("".concat(this.prefix,"/referrals/redeem"),t)}var eb=class{async detectCors(){var e;if(null==(e=this.prefix)?void 0:e.includes("/api/auth"))return{valid:!0,includeCredentials:!0};let t="".concat(this.backendUrl,"/api/autumn/cors");try{return await fetch(t,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!0}}catch(e){try{return await fetch(t,{method:"POST",credentials:"omit",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!1}}catch(e){return{valid:!1,includeCredentials:void 0}}}}async shouldIncludeCredentials(){if(void 0!==this.includeCredentials)return this.includeCredentials;try{let e=await this.detectCors();return e.valid&&(console.warn("[Autumn] Detected CORS credentials: ".concat(e.includeCredentials)),console.warn("[Autumn] To disable this warning, you can set includeCredentials={".concat(e.includeCredentials?"true":"false","} in <AutumnProvider />")),this.includeCredentials=e.includeCredentials),e.includeCredentials||!1}catch(e){return console.error("[Autumn] Error detecting CORS: ".concat(e.message)),!1}}async getHeaders(){let e={"Content-Type":"application/json"};if(this.getBearerToken)try{let t=await this.getBearerToken();e.Authorization="Bearer ".concat(t)}catch(e){console.error("Failed to call getToken() in AutumnProvider")}return e}async handleFetch(e){let{path:t,method:r,body:n}=e;n="POST"===r?JSON.stringify({...n,customer_data:this.customerData||void 0}):void 0;let a=await this.shouldIncludeCredentials();try{let e=await fetch("".concat(this.backendUrl).concat(t),{method:r,body:n,headers:await this.getHeaders(),credentials:a?"include":"omit"}),i=await f(e);return await K({response:e,logger:console,logError:!i})}catch(e){return en({method:r,backendUrl:this.backendUrl||"",path:t,error:e}),{data:null,error:new m({message:e.message,code:"fetch_failed"})}}}async post(e,t){return await this.handleFetch({path:e,method:"POST",body:t})}async get(e){return await this.handleFetch({path:e,method:"GET"})}async delete(e){return await this.handleFetch({path:e,method:"DELETE"})}async createCustomer(e){return await ei({client:this,params:e})}async getPricingTable(){return await ea.bind(this)()}constructor({backendUrl:e,getBearerToken:t,customerData:r,includeCredentials:n,betterAuthUrl:a}){this.attach=ed.bind(this),this.cancel=eh.bind(this),this.check=ef.bind(this),this.track=em.bind(this),this.openBillingPortal=ey.bind(this),this.setupPayment=ep.bind(this),this.entities={create:ec.bind(this),get:el.bind(this),delete:eu.bind(this)},this.referrals={createCode:e_.bind(this),redeemCode:ev.bind(this)},this.products={list:eg.bind(this)},this.backendUrl=e,this.getBearerToken=t,this.customerData=r,this.includeCredentials=n,this.prefix="/api/autumn",a&&(this.prefix="/api/auth/autumn",this.backendUrl=a)}},ew=r(12359);(0,ew.createContext)({initialized:!1,disableDialogs:!1,client:new eb({backendUrl:""}),paywallDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}},attachDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}}});var ex=e=>{let{AutumnContext:t,name:r,errorIfNotInitialized:n=!0}=e,a=(0,ew.useContext)(t);if(!a.initialized&&n)throw Error("".concat(r," must be used within <AutumnProvider />"));return a},eC=p(r(59153)),ez=(e,t)=>{if(!e)return null;if(!t)return e.map(t=>{if(t.base_variant_id){let r=e.find(e=>e.id===t.base_variant_id);if(r)return{...t,name:r.name}}return t});let r=structuredClone(e),n=[];for(let e of t){var a,i,s,o;if(!e.id){let t={},r=null==(i=e.items)?void 0:i.map(e=>({display:{primary_text:e.primaryText,secondary_text:e.secondaryText}})),a=e.price;e.price&&(t.is_free=!1,r=[{display:{primary_text:null==a?void 0:a.primaryText,secondary_text:null==a?void 0:a.secondaryText}},...r||[]]),r&&0!==r.length||(r=[{display:{primary_text:""}}]),n.push({display:{name:e.name,description:e.description,button_text:e.buttonText,recommend_text:e.recommendText,everything_from:e.everythingFrom,button_url:e.buttonUrl},items:r,properties:t});continue}let t=r.find(t=>t.id===e.id);if(!t)continue;let c=t.name,l=t.base_variant_id;if(l){let e=r.find(e=>e.id===l);e&&(c=e.name)}c=e.name||c;let u=null==(a=t.properties)?void 0:a.is_free,d=t.properties||{},p=e.items,h=e.price,f=[];if(h?(d.is_free=!1,u||void 0!==p?f.push({display:{primary_text:h.primaryText,secondary_text:h.secondaryText}}):t.items[0].display={primary_text:h.primaryText,secondary_text:h.secondaryText}):p&&!u&&f.push(t.items[0]),p)for(let e of p)if(e.featureId){let r=t.items.find(t=>t.feature_id===e.featureId);if(!r){console.error("Feature with id ".concat(e.featureId," not found for product ").concat(t.id));continue}f.push({...r,display:{primary_text:e.primaryText||(null==(s=r.display)?void 0:s.primary_text),secondary_text:e.secondaryText||(null==(o=r.display)?void 0:o.secondary_text)}})}else f.push({display:{primary_text:e.primaryText,secondary_text:e.secondaryText}});else f=t.items;let m={...t,items:f,properties:d,display:{name:c,description:e.description,button_text:e.buttonText,recommend_text:e.recommendText,everything_from:e.everythingFrom,button_url:e.buttonUrl}};n.push(m)}return n},eT={refreshInterval:0},ek=e=>{let{AutumnContext:t,params:r,authClient:n}=e,a=ex({AutumnContext:t,name:"usePricingTable",errorIfNotInitialized:!n}),i=n?n.autumn:a.client,s=async()=>{try{let{data:e,error:t}=await i.products.list();if(t)throw t;return(null==e?void 0:e.list)||[]}catch(e){throw new m({message:"Failed to fetch pricing table products",code:"failed_to_fetch_pricing_table_products"})}},{data:o,error:c,mutate:l}=(0,eC.default)("pricing-table",s,{...eT});return{products:ez(o||[],null==r?void 0:r.productDetails),isLoading:!c&&!o,error:c,refetch:l}},eI=e=>{let{AutumnContext:t,authClient:r}=e,n=ex({AutumnContext:t,name:"useAutumn",errorIfNotInitialized:!r}),{attachDialog:a,paywallDialog:i}=n,s=r?r.autumn:n.client,o=!!r,{refetch:c}=ek({AutumnContext:t,authClient:r}),{open:l,setProps:u,setOpen:d,setComponent:p}=a,{setProps:h,setOpen:f,setComponent:m}=i,y=async e=>{let t=await s.attach(e);if(t.error)return t;let r=t.data;return(null==r?void 0:r.checkout_url)&&"undefined"!=typeof window&&(e.openInNewTab?window.open(r.checkout_url,"_blank"):window.location.href=r.checkout_url),await c(),d&&d(!1),t},g=async e=>{let{dialog:t,...r}=e,{productId:n,entityId:a,entityData:i}=e,o=await s.check({productId:n,entityId:a,entityData:i,withPreview:!0});if(o.error)return o;let c=o.data.preview;return c?(u({preview:c,attachParams:r}),d(!0),o):await y(r)};return{attach:async e=>{let{dialog:t,openInNewTab:r}=e;return t&&o?void console.error("[Autumn] Attach dialog cannot be used with better auth plugin. To use this, please switch to <AutumnProvider /> and autumnHandler. Learn more here: https://docs.useautumn.com/quickstart/quickstart"):t&&!l?(p(t),await g(e)):await y(e)},check:async e=>{let{dialog:t,withPreview:r}=e;if(t&&o)return void console.error("[Autumn] Check dialog cannot be used with better auth plugin. To use this, please switch to <AutumnProvider /> and autumnHandler. Learn more here: https://docs.useautumn.com/quickstart/quickstart");t&&m(t);let n=await s.check({...e,withPreview:!!r||!!t});if(n.error)return n;let a=n.data;return a&&a.preview&&t&&(h({preview:a.preview}),f(!0)),n},track:async e=>{let t=await s.track(e);return t.error,t},cancel:async e=>{let t=await s.cancel(e);return t.error,t},openBillingPortal:async e=>{let t={openInNewTab:!1,...e},r=await s.openBillingPortal(t);if(r.error)return r;let n=r.data;return(null==n?void 0:n.url)&&"undefined"!=typeof window&&(t.openInNewTab?window.open(n.url,"_blank"):window.open(n.url,"_self")),r},setupPayment:async e=>{var t;let r={openInNewTab:!1,...e||{}},n=await s.setupPayment(r);return(null==(t=n.data)?void 0:t.url)&&"undefined"!=typeof window&&(r.openInNewTab?window.open(n.data.url,"_blank"):window.open(n.data.url,"_self")),n}}}},20323:(e,t,r)=>{"use strict";var n,a=r(37811),i=Object.create,s=Object.defineProperty,o=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,d=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of c(t))u.call(e,a)||a===r||s(e,a,{get:()=>t[a],enumerable:!(n=o(t,a))||n.enumerable});return e},p=(e,t,r)=>(r=null!=e?i(l(e)):{},d(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)),h={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(h,{useEntityBase:()=>eZ}),e.exports=d(s({},"__esModule",{value:!0}),h);var f=p(r(59153)),m=r(12359),y=e=>{let{customer:t,featureId:r,requiredBalance:n=1}=e,a=Object.values(t.features).find(e=>e.credit_schema&&e.credit_schema.some(e=>e.feature_id===r));if(a){var i;let e=null==(i=a.credit_schema)?void 0:i.find(e=>e.feature_id===r);return{feature:a,requiredBalance:e.credit_amount*n}}return{cusFeature:t.features[r],requiredBalance:n}},g=e=>{let{customer:t,params:r}=e,{cusFeature:n,requiredBalance:a}=y({customer:t,featureId:r.featureId});if(!n)return!1;if("static"==n.type||n.unlimited||n.overage_allowed)return!0;if(n.usage_limit){let e=(n.usage_limit||0)-(n.included_usage||0);return(n.balance||0)+e>=a}return(n.balance||0)>=a},_=e=>{let{customer:t,params:r}=e;if(!t)return!1;if(!r.featureId&&!r.productId)throw Error("allowed() requires either featureId or productId");if(r.featureId)return g({customer:t,params:r});if(r.productId){let e=t.products.find(e=>e.id==r.productId);return!!e&&"scheduled"!=e.status}return!1},v=async e=>{if(401===e.status){let t=e.clone();if((await t.json()).message.includes("Missing authorization header"))return console.error("[Autumn] Missing authorization header.\n\nUse the getBearerToken prop in <AutumnProvider /> to set the authorization header.\nhttps://docs.useautumn.com/quickstart/quickstart#5-set-up-autumnprovider"),!0}return!1},b=class e extends Error{static fromError(t){return new e({message:t.message||"Unknown error",code:t.code||"unknown_error"})}toString(){return"".concat(this.message," (code: ").concat(this.code,")")}toJSON(){return{message:this.message,code:this.code}}constructor(e){super(e.message),this.message=e.message,this.code=e.code}},w=async e=>{let{instance:t,params:r}=e;return t.post("/attach",r)},x=async e=>{let{instance:t,params:r}=e;return t.post("/setup_payment",r)},C=async e=>{let{instance:t,params:r}=e;return t.post("/cancel",r)},z=async e=>{let{instance:t,params:r}=e;return t.post("/entitled",r)},T=async e=>{let{instance:t,params:r}=e;return t.post("/events",r)},k=async e=>{let{instance:t,params:r}=e;return t.post("/track",r)},I=async e=>{let{instance:t,params:r}=e;return t.post("/usage",r)},O=async e=>{let{instance:t,params:r}=e;return t.post("/check",r)},A=(e,t,r)=>(t||(t=new et),e({instance:t,...r})),P=e=>({get:(t,r)=>A(E,e,{id:t,params:r}),create:t=>A(Z,e,{params:t}),update:(t,r)=>A(S,e,{id:t,params:r}),delete:t=>A(N,e,{id:t}),billingPortal:(t,r)=>A(U,e,{id:t,params:r})}),j=e=>e?"expand=".concat(e.join(",")):"",E=async e=>{let{instance:t,id:r,params:n}=e;return r?t.get("/customers/".concat(r,"?").concat(j(null==n?void 0:n.expand))):{data:null,error:new b({message:"Customer ID is required",code:"CUSTOMER_ID_REQUIRED"})}},Z=async e=>{let{instance:t,params:r}=e;return t.post("/customers?".concat(j(null==r?void 0:r.expand)),r)},S=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r),n)},N=async e=>{let{instance:t,id:r}=e;return t.delete("/customers/".concat(r))},U=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r,"/billing_portal"),n)},D=e=>({get:(t,r,n)=>A(L,e,{customer_id:t,entity_id:r,params:n}),create:(t,r)=>A(F,e,{customer_id:t,params:r}),delete:(t,r)=>A(M,e,{customer_id:t,entity_id:r})}),R=e=>e?"expand=".concat(e.join(",")):"",L=async e=>{let{instance:t,customer_id:r,entity_id:n,params:a}=e;return t.get("/customers/".concat(r,"/entities/").concat(n,"?").concat(R(null==a?void 0:a.expand)))},F=async e=>{let{instance:t,customer_id:r,params:n}=e;return t.post("/customers/".concat(r,"/entities"),n)},M=async e=>{let{instance:t,customer_id:r,entity_id:n}=e;return t.delete("/customers/".concat(r,"/entities/").concat(n))},B=e=>({get:t=>A(K,e,{id:t}),create:t=>A($,e,{params:t}),list:t=>A(V,e,{params:t})}),V=async e=>{let{instance:t,params:r}=e,n="/products_beta";if(r){let e=new URLSearchParams;for(let[t,n]of Object.entries(r))void 0!==n&&e.append(t,String(n));let t=e.toString();t&&(n+="?".concat(t))}return t.get(n)},K=async e=>{let{instance:t,id:r}=e;return t.get("/products/".concat(r))},$=async e=>{let{instance:t,params:r}=e;return t.post("/products",r)},q=e=>({createCode:t=>A(W,e,{params:t}),redeemCode:t=>A(H,e,{params:t})}),W=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/code",r)},H=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/redeem",r)},J=async e=>{let{response:t,logger:r,logError:n=!0}=e;if(t.status<200||t.status>=300){let e;try{e=await t.json(),n&&r.error("[Autumn] ".concat(e.message))}catch(e){throw e}return{data:null,error:new b({message:e.message,code:e.code}),statusCode:t.status}}try{return{data:await t.json(),error:null,statusCode:null==t?void 0:t.status}}catch(e){throw e}},Y=p(r(83809)),G=()=>{let e=new Date().toISOString();return"[".concat(e.split("T")[1].split(".")[0],"]")},Q=e=>X.indexOf(e)>=X.indexOf(ee.level),X=["debug","info","warn","error","fatal"],ee={...console,level:"info",debug:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];Q("debug")&&console.log(G(),Y.default.gray("DEBUG"),...t)},log:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];console.log(G(),Y.default.blue("INFO"),...t)},info:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];Q("info")&&console.log(G(),Y.default.blue("INFO"),...t)},warn:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];Q("warn")&&console.log(G(),Y.default.yellow("WARN"),...t)},error:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];Q("error")&&console.log(G(),Y.default.red("ERROR"),...t)}},et=((n=class{async get(e){return J({response:await fetch("".concat(this.url).concat(e),{headers:this.headers}),logger:this.logger})}async post(e,t){try{let r=await fetch("".concat(this.url).concat(e),{method:"POST",headers:this.headers,body:JSON.stringify(t)});return J({response:r,logger:this.logger})}catch(e){throw console.error("Error sending request:",e),e}}async delete(e){return J({response:await fetch("".concat(this.url).concat(e),{method:"DELETE",headers:this.headers}),logger:this.logger})}async attach(e){return w({instance:this,params:e})}async setupPayment(e){return x({instance:this,params:e})}async cancel(e){return C({instance:this,params:e})}async entitled(e){return z({instance:this,params:e})}async check(e){return O({instance:this,params:e})}async event(e){return T({instance:this,params:e})}async track(e){return k({instance:this,params:e})}async usage(e){return I({instance:this,params:e})}constructor(e){this.logger=console,this.customers=P(this),this.products=B(this),this.entities=D(this),this.referrals=q(this);try{this.secretKey=(null==e?void 0:e.secretKey)||a.env.AUTUMN_SECRET_KEY,this.publishableKey=(null==e?void 0:e.publishableKey)||a.env.AUTUMN_PUBLISHABLE_KEY}catch(e){}if(!this.secretKey&&!this.publishableKey&&!(null==e?void 0:e.headers))throw Error("Autumn secret key or publishable key is required");this.headers=(null==e?void 0:e.headers)||{Authorization:"Bearer ".concat(this.secretKey||this.publishableKey),"Content-Type":"application/json"};let t=(null==e?void 0:e.version)||"1.2";this.headers["x-api-version"]=t,this.url=(null==e?void 0:e.url)||"https://api.useautumn.com/v1",this.logger=ee,this.logger.level=(null==e?void 0:e.logLevel)||"info"}}).customers=P(),n.products=B(),n.entities=D(),n.referrals=q(),n.attach=e=>A(w,void 0,{params:e}),n.usage=e=>A(I,void 0,{params:e}),n.setupPayment=e=>A(x,void 0,{params:e}),n.cancel=e=>A(C,void 0,{params:e}),n.entitled=e=>A(z,void 0,{params:e}),n.check=e=>A(O,void 0,{params:e}),n.event=e=>A(T,void 0,{params:e}),n.track=e=>A(k,void 0,{params:e}),n),er=r(28935),en=er.z.object({name:er.z.string().optional(),feature_id:er.z.string()}),ea=r(28935);ea.z.object({feature_id:ea.z.string(),quantity:ea.z.number()}),ea.z.object({customer_id:ea.z.string(),product_id:ea.z.string().optional(),entity_id:ea.z.string().optional(),options:ea.z.array(ea.z.object({feature_id:ea.z.string(),quantity:ea.z.number()})).optional(),product_ids:ea.z.array(ea.z.string()).optional(),free_trial:ea.z.boolean().optional(),success_url:ea.z.string().optional(),metadata:ea.z.record(ea.z.string()).optional(),force_checkout:ea.z.boolean().optional(),customer_data:ea.z.any().optional(),entity_data:ea.z.any().optional(),checkout_session_params:ea.z.record(ea.z.any()).optional(),reward:ea.z.string().optional()}),ea.z.object({checkout_url:ea.z.string().optional(),customer_id:ea.z.string(),product_ids:ea.z.array(ea.z.string()),code:ea.z.string(),message:ea.z.string(),customer_data:ea.z.any().optional()}),ea.z.object({customer_id:ea.z.string(),product_id:ea.z.string(),entity_id:ea.z.string().optional(),cancel_immediately:ea.z.boolean().optional()}),ea.z.object({success:ea.z.boolean(),customer_id:ea.z.string(),product_id:ea.z.string()}),ea.z.object({customer_id:ea.z.string(),value:ea.z.number().optional(),feature_id:ea.z.string().optional(),event_name:ea.z.string().optional(),entity_id:ea.z.string().optional(),customer_data:ea.z.any().optional(),idempotency_key:ea.z.string().optional(),entity_data:ea.z.any().optional()}),ea.z.object({id:ea.z.string(),code:ea.z.string(),customer_id:ea.z.string(),feature_id:ea.z.string().optional(),event_name:ea.z.string().optional()}),ea.z.object({customer_id:ea.z.string(),feature_id:ea.z.string().optional(),product_id:ea.z.string().optional(),entity_id:ea.z.string().optional(),customer_data:ea.z.any().optional(),required_balance:ea.z.number().optional(),send_event:ea.z.boolean().optional(),with_preview:ea.z.boolean().optional(),entity_data:en.optional()});var ei=r(28935).z.enum(["invoices","rewards","trials_used","entities","referrals","payment_method"]),es=r(28935);es.z.object({id:es.z.string().nullish(),email:es.z.string().nullish(),name:es.z.string().nullish(),fingerprint:es.z.string().nullish(),metadata:es.z.record(es.z.any()).optional(),expand:es.z.array(ei).optional()}),es.z.object({return_url:es.z.string().optional()});var eo=r(28935);eo.z.object({customer_id:eo.z.string(),program_id:eo.z.string()}),eo.z.object({code:eo.z.string(),customer_id:eo.z.string()});var ec=e=>{let{method:t,backendUrl:r,path:n,error:a}=e;console.error("[Autumn] Fetch failed: ".concat(t," ").concat(r).concat(n,"\n\n1. Check that backendUrl in <AutumnProvider/> is correctly set.\n2. Check that autumnHandler is correctly registered on your backend."))};async function el(){return await this.get("".concat(this.prefix,"/components/pricing_table"))}var eu=async e=>{let{client:t,params:r}=e;return await t.post("".concat(t.prefix,"/customers"),r)},ed=(e,t)=>Array.isArray(e)?e.map(e=>ed(e,t)):null!==e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(e=>{let[r,n]=e,a=r.replace(/([a-z])([A-Z])/g,"$1_$2").replace(/[-\s]+/g,"_").toLowerCase();return t&&t.includes(r)?[a,n]:[a,ed(n,t)]})):e,ep=e=>e?"expand=".concat(e.join(",")):"";async function eh(e){let t=ed(e);return await this.post("".concat(this.prefix,"/entities"),t)}async function ef(e,t){ed(t);let r=ep(null==t?void 0:t.expand);return await this.get("".concat(this.prefix,"/entities/").concat(e,"?").concat(r))}async function em(e){return await this.delete("".concat(this.prefix,"/entities/").concat(e))}async function ey(e){let{dialog:t,...r}=e,n=ed(r,["checkoutSessionparams"]);return await this.post("".concat(this.prefix,"/attach"),n)}async function eg(e){let t=ed(e,["checkoutSessionParams"]);return await this.post("".concat(this.prefix,"/setup_payment"),t)}async function e_(e){let t=ed(e);return await this.post("".concat(this.prefix,"/cancel"),t)}async function ev(e){let{dialog:t,...r}=e,n=ed(r);return await this.post("".concat(this.prefix,"/check"),n)}async function eb(e){let t=ed(e);return await this.post("".concat(this.prefix,"/track"),t)}async function ew(e){let t=ed(e||{});return await this.post("".concat(this.prefix,"/billing_portal"),t)}async function ex(){return await this.get("".concat(this.prefix,"/products"))}async function eC(e){let t=ed(e);return await this.post("".concat(this.prefix,"/referrals/code"),t)}async function ez(e){let t=ed(e);return await this.post("".concat(this.prefix,"/referrals/redeem"),t)}var eT=class{async detectCors(){var e;if(null==(e=this.prefix)?void 0:e.includes("/api/auth"))return{valid:!0,includeCredentials:!0};let t="".concat(this.backendUrl,"/api/autumn/cors");try{return await fetch(t,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!0}}catch(e){try{return await fetch(t,{method:"POST",credentials:"omit",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!1}}catch(e){return{valid:!1,includeCredentials:void 0}}}}async shouldIncludeCredentials(){if(void 0!==this.includeCredentials)return this.includeCredentials;try{let e=await this.detectCors();return e.valid&&(console.warn("[Autumn] Detected CORS credentials: ".concat(e.includeCredentials)),console.warn("[Autumn] To disable this warning, you can set includeCredentials={".concat(e.includeCredentials?"true":"false","} in <AutumnProvider />")),this.includeCredentials=e.includeCredentials),e.includeCredentials||!1}catch(e){return console.error("[Autumn] Error detecting CORS: ".concat(e.message)),!1}}async getHeaders(){let e={"Content-Type":"application/json"};if(this.getBearerToken)try{let t=await this.getBearerToken();e.Authorization="Bearer ".concat(t)}catch(e){console.error("Failed to call getToken() in AutumnProvider")}return e}async handleFetch(e){let{path:t,method:r,body:n}=e;n="POST"===r?JSON.stringify({...n,customer_data:this.customerData||void 0}):void 0;let a=await this.shouldIncludeCredentials();try{let e=await fetch("".concat(this.backendUrl).concat(t),{method:r,body:n,headers:await this.getHeaders(),credentials:a?"include":"omit"}),i=await v(e);return await J({response:e,logger:console,logError:!i})}catch(e){return ec({method:r,backendUrl:this.backendUrl||"",path:t,error:e}),{data:null,error:new b({message:e.message,code:"fetch_failed"})}}}async post(e,t){return await this.handleFetch({path:e,method:"POST",body:t})}async get(e){return await this.handleFetch({path:e,method:"GET"})}async delete(e){return await this.handleFetch({path:e,method:"DELETE"})}async createCustomer(e){return await eu({client:this,params:e})}async getPricingTable(){return await el.bind(this)()}constructor({backendUrl:e,getBearerToken:t,customerData:r,includeCredentials:n,betterAuthUrl:a}){this.attach=ey.bind(this),this.cancel=e_.bind(this),this.check=ev.bind(this),this.track=eb.bind(this),this.openBillingPortal=ew.bind(this),this.setupPayment=eg.bind(this),this.entities={create:eh.bind(this),get:ef.bind(this),delete:em.bind(this)},this.referrals={createCode:eC.bind(this),redeemCode:ez.bind(this)},this.products={list:ex.bind(this)},this.backendUrl=e,this.getBearerToken=t,this.customerData=r,this.includeCredentials=n,this.prefix="/api/autumn",a&&(this.prefix="/api/auth/autumn",this.backendUrl=a)}},ek=r(12359);(0,ek.createContext)({initialized:!1,disableDialogs:!1,client:new eT({backendUrl:""}),paywallDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}},attachDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}}});var eI=e=>{let{AutumnContext:t,name:r,errorIfNotInitialized:n=!0}=e,a=(0,ek.useContext)(t);if(!a.initialized&&n)throw Error("".concat(r," must be used within <AutumnProvider />"));return a},eO=p(r(59153)),eA=(e,t)=>{if(!e)return null;if(!t)return e.map(t=>{if(t.base_variant_id){let r=e.find(e=>e.id===t.base_variant_id);if(r)return{...t,name:r.name}}return t});let r=structuredClone(e),n=[];for(let e of t){var a,i,s,o;if(!e.id){let t={},r=null==(i=e.items)?void 0:i.map(e=>({display:{primary_text:e.primaryText,secondary_text:e.secondaryText}})),a=e.price;e.price&&(t.is_free=!1,r=[{display:{primary_text:null==a?void 0:a.primaryText,secondary_text:null==a?void 0:a.secondaryText}},...r||[]]),r&&0!==r.length||(r=[{display:{primary_text:""}}]),n.push({display:{name:e.name,description:e.description,button_text:e.buttonText,recommend_text:e.recommendText,everything_from:e.everythingFrom,button_url:e.buttonUrl},items:r,properties:t});continue}let t=r.find(t=>t.id===e.id);if(!t)continue;let c=t.name,l=t.base_variant_id;if(l){let e=r.find(e=>e.id===l);e&&(c=e.name)}c=e.name||c;let u=null==(a=t.properties)?void 0:a.is_free,d=t.properties||{},p=e.items,h=e.price,f=[];if(h?(d.is_free=!1,u||void 0!==p?f.push({display:{primary_text:h.primaryText,secondary_text:h.secondaryText}}):t.items[0].display={primary_text:h.primaryText,secondary_text:h.secondaryText}):p&&!u&&f.push(t.items[0]),p)for(let e of p)if(e.featureId){let r=t.items.find(t=>t.feature_id===e.featureId);if(!r){console.error("Feature with id ".concat(e.featureId," not found for product ").concat(t.id));continue}f.push({...r,display:{primary_text:e.primaryText||(null==(s=r.display)?void 0:s.primary_text),secondary_text:e.secondaryText||(null==(o=r.display)?void 0:o.secondary_text)}})}else f.push({display:{primary_text:e.primaryText,secondary_text:e.secondaryText}});else f=t.items;let m={...t,items:f,properties:d,display:{name:c,description:e.description,button_text:e.buttonText,recommend_text:e.recommendText,everything_from:e.everythingFrom,button_url:e.buttonUrl}};n.push(m)}return n},eP={refreshInterval:0},ej=e=>{let{AutumnContext:t,params:r,authClient:n}=e,a=eI({AutumnContext:t,name:"usePricingTable",errorIfNotInitialized:!n}),i=n?n.autumn:a.client,s=async()=>{try{let{data:e,error:t}=await i.products.list();if(t)throw t;return(null==e?void 0:e.list)||[]}catch(e){throw new b({message:"Failed to fetch pricing table products",code:"failed_to_fetch_pricing_table_products"})}},{data:o,error:c,mutate:l}=(0,eO.default)("pricing-table",s,{...eP});return{products:eA(o||[],null==r?void 0:r.productDetails),isLoading:!c&&!o,error:c,refetch:l}},eE=e=>{let{AutumnContext:t,authClient:r}=e,n=eI({AutumnContext:t,name:"useAutumn",errorIfNotInitialized:!r}),{attachDialog:a,paywallDialog:i}=n,s=r?r.autumn:n.client,o=!!r,{refetch:c}=ej({AutumnContext:t,authClient:r}),{open:l,setProps:u,setOpen:d,setComponent:p}=a,{setProps:h,setOpen:f,setComponent:m}=i,y=async e=>{let t=await s.attach(e);if(t.error)return t;let r=t.data;return(null==r?void 0:r.checkout_url)&&"undefined"!=typeof window&&(e.openInNewTab?window.open(r.checkout_url,"_blank"):window.location.href=r.checkout_url),await c(),d&&d(!1),t},g=async e=>{let{dialog:t,...r}=e,{productId:n,entityId:a,entityData:i}=e,o=await s.check({productId:n,entityId:a,entityData:i,withPreview:!0});if(o.error)return o;let c=o.data.preview;return c?(u({preview:c,attachParams:r}),d(!0),o):await y(r)};return{attach:async e=>{let{dialog:t,openInNewTab:r}=e;return t&&o?void console.error("[Autumn] Attach dialog cannot be used with better auth plugin. To use this, please switch to <AutumnProvider /> and autumnHandler. Learn more here: https://docs.useautumn.com/quickstart/quickstart"):t&&!l?(p(t),await g(e)):await y(e)},check:async e=>{let{dialog:t,withPreview:r}=e;if(t&&o)return void console.error("[Autumn] Check dialog cannot be used with better auth plugin. To use this, please switch to <AutumnProvider /> and autumnHandler. Learn more here: https://docs.useautumn.com/quickstart/quickstart");t&&m(t);let n=await s.check({...e,withPreview:!!r||!!t});if(n.error)return n;let a=n.data;return a&&a.preview&&t&&(h({preview:a.preview}),f(!0)),n},track:async e=>{let t=await s.track(e);return t.error,t},cancel:async e=>{let t=await s.cancel(e);return t.error,t},openBillingPortal:async e=>{let t={openInNewTab:!1,...e},r=await s.openBillingPortal(t);if(r.error)return r;let n=r.data;return(null==n?void 0:n.url)&&"undefined"!=typeof window&&(t.openInNewTab?window.open(n.url,"_blank"):window.open(n.url,"_self")),r},setupPayment:async e=>{var t;let r={openInNewTab:!1,...e||{}},n=await s.setupPayment(r);return(null==(t=n.data)?void 0:t.url)&&"undefined"!=typeof window&&(r.openInNewTab?window.open(n.data.url,"_blank"):window.open(n.data.url,"_self")),n}}},eZ=e=>{let{entityId:t,params:r,AutumnContext:n}=e,{client:a}=(0,m.useContext)(n),i=["entity",t,null==r?void 0:r.expand],s=async()=>{if(!t)return null;let{data:e,error:n}=await a.entities.get(t,r);if(n)throw n;return e||null},{data:o,error:c,isLoading:l,mutate:u}=(0,f.default)(i,s,{fallbackData:null,onErrorRetry:(e,t,r)=>"entity_not_found"!=e.code}),{check:d,attach:p,cancel:h,track:y}=eE({AutumnContext:n}),g=e=>_({customer:o,params:e}),v=e=>d({...e,entityId:t||void 0}),b=e=>p({...e,entityId:t||void 0}),w=e=>h({...e,entityId:t||void 0}),x=e=>y({...e,entityId:t||void 0});return t?{entity:c?null:o,isLoading:l,error:c,refetch:u,allowed:g,check:v,attach:b,cancel:w,track:x}:{entity:null,isLoading:!1,error:null,refetch:u,allowed:g,check:v,attach:b,cancel:w,track:x}}},22365:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(40808),a=r(40670),i=r(68368),s=r(12359),o=r(85600),c=function(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,t}(a),l=function(e){return e&&e.__esModule?e:{default:e}}(s);let u=n.isWindowDefined&&window.__SWR_DEVTOOLS_USE__,d=u?window.__SWR_DEVTOOLS_USE__:[],p=e=>n.isFunction(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],h=()=>n.mergeObjects(n.defaultConfig,s.useContext(n.SWRConfigContext)),f=d.concat(e=>(t,r,a)=>{let s=r&&((...e)=>{let[a]=n.serialize(t),[,,,s]=n.SWRGlobalState.get(n.cache);if(a.startsWith(i.INFINITE_PREFIX))return r(...e);let o=s[a];return n.isUndefined(o)?r(...e):(delete s[a],o)});return e(t,s,a)});u&&(window.__SWR_DEVTOOLS_REACT__=l.default),t.IS_REACT_LEGACY=n.IS_REACT_LEGACY,t.IS_SERVER=n.IS_SERVER,t.OBJECT=n.OBJECT,t.SWRConfig=n.SWRConfig,t.SWRGlobalState=n.SWRGlobalState,t.UNDEFINED=n.UNDEFINED,t.cache=n.cache,t.compare=n.compare,t.createCacheHelper=n.createCacheHelper,t.defaultConfig=n.defaultConfig,t.defaultConfigOptions=n.defaultConfigOptions,t.getTimestamp=n.getTimestamp,t.hasRequestAnimationFrame=n.hasRequestAnimationFrame,t.initCache=n.initCache,t.internalMutate=n.internalMutate,t.isDocumentDefined=n.isDocumentDefined,t.isFunction=n.isFunction,t.isLegacyDeno=n.isLegacyDeno,t.isPromiseLike=n.isPromiseLike,t.isUndefined=n.isUndefined,t.isWindowDefined=n.isWindowDefined,t.mergeConfigs=n.mergeConfigs,t.mergeObjects=n.mergeObjects,t.mutate=n.mutate,t.noop=n.noop,t.preset=n.preset,t.rAF=n.rAF,t.serialize=n.serialize,t.slowConnection=n.slowConnection,t.stableHash=n.stableHash,t.useIsomorphicLayoutEffect=n.useIsomorphicLayoutEffect,t.revalidateEvents=c,Object.defineProperty(t,"INFINITE_PREFIX",{enumerable:!0,get:function(){return i.INFINITE_PREFIX}}),t.normalize=p,t.preload=(e,t)=>{let[r,a]=n.serialize(e),[,,,i]=n.SWRGlobalState.get(n.cache);if(i[r])return i[r];let s=t(a);return i[r]=s,s},t.subscribeCallback=(e,t,r)=>{let n=t[e]||(t[e]=[]);return n.push(r),()=>{let e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}},t.useSWRConfig=h,t.withArgs=e=>function(...t){let r=h(),[a,i,s]=p(t),o=n.mergeConfigs(r,s),c=e,{use:l}=o,u=(l||[]).concat(f);for(let e=u.length;e--;)c=u[e](c);return c(a,i||o.fetcher||null,o)},t.withMiddleware=(e,t)=>(...r)=>{let[n,a,i]=p(r),s=(i.use||[]).concat(t);return e(n,a,{...i,use:s})},Object.keys(o).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}})})},25913:(e,t,r)=>{"use strict";var n=r(12359),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,s=n.useEffect,o=n.useLayoutEffect,c=n.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!a(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),a=n[0].inst,u=n[1];return o(function(){a.value=r,a.getSnapshot=t,l(a)&&u({inst:a})},[e,r,t]),s(function(){return l(a)&&u({inst:a}),e(function(){l(a)&&u({inst:a})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:u},28935:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var a=Object.getOwnPropertyDescriptor(t,r);(!a||("get"in a?!t.__esModule:a.writable||a.configurable))&&(a={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,a)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return a(t,e),t},s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.z=void 0;let o=i(r(54594));t.z=o,s(r(54594),t),t.default=o},39843:(e,t,r)=>{"use strict";r.r(t),r.d(t,{NextAutumnProvider:()=>_});var n=r(87183),a=r(62005),i=r(45132),s=r(56289),o=r(23570),c=r(93422),l=r(7403),u=r(17474),d=r(68521),p=r(14618),h=r(21902),f=r(3026);class m extends a.AutumnClient{async createCustomer(e){return await (0,i.Q)({encryptedCustomerId:this.encryptedCustomerId,customerData:this.customerData,...e})}async getPricingTable(){return await (0,l.V)({encryptedCustomerId:this.encryptedCustomerId})}constructor({encryptedCustomerId:e,customerData:t}){super({customerData:t}),this.attach=async e=>await (0,u.C)({encryptedCustomerId:this.encryptedCustomerId,customerData:this.customerData,...e}),this.cancel=async e=>await (0,d.h)({encryptedCustomerId:this.encryptedCustomerId,...e}),this.check=async e=>await (0,p.F)({encryptedCustomerId:this.encryptedCustomerId,...e}),this.track=async e=>await (0,h.k)({encryptedCustomerId:this.encryptedCustomerId,...e}),this.openBillingPortal=async e=>await (0,f._)({encryptedCustomerId:this.encryptedCustomerId,...e||{}}),this.entities={create:async e=>await (0,s.w)({encryptedCustomerId:this.encryptedCustomerId,entity:e}),get:async(e,t)=>await (0,o.y)({encryptedCustomerId:this.encryptedCustomerId,entityId:e,...t}),delete:async e=>await (0,c.f)({encryptedCustomerId:this.encryptedCustomerId,entityId:e})},this.referrals={createCode:async e=>{throw Error("Not implemented")},redeemCode:async e=>{throw Error("Not implemented")}},this.encryptedCustomerId=e}}var y=r(14776),g=r(92345);let _=e=>{let{encryptedCustomerId:t,customerData:r,children:a}=e,i=new m({encryptedCustomerId:t,customerData:r});return(0,n.jsx)(g.BaseAutumnProvider,{client:i,AutumnContext:y.AutumnContext,children:a})}},40338:(e,t,r)=>{"use strict";var n,a=r(37811),i=Object.create,s=Object.defineProperty,o=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,d=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of c(t))u.call(e,a)||a===r||s(e,a,{get:()=>t[a],enumerable:!(n=o(t,a))||n.enumerable});return e},p=(e,t,r)=>(r=null!=e?i(l(e)):{},d(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)),h={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(h,{usePricingTableBase:()=>ek}),e.exports=d(s({},"__esModule",{value:!0}),h);var f=p(r(59153)),m=class e extends Error{static fromError(t){return new e({message:t.message||"Unknown error",code:t.code||"unknown_error"})}toString(){return"".concat(this.message," (code: ").concat(this.code,")")}toJSON(){return{message:this.message,code:this.code}}constructor(e){super(e.message),this.message=e.message,this.code=e.code}},y=async e=>{let{instance:t,params:r}=e;return t.post("/attach",r)},g=async e=>{let{instance:t,params:r}=e;return t.post("/setup_payment",r)},_=async e=>{let{instance:t,params:r}=e;return t.post("/cancel",r)},v=async e=>{let{instance:t,params:r}=e;return t.post("/entitled",r)},b=async e=>{let{instance:t,params:r}=e;return t.post("/events",r)},w=async e=>{let{instance:t,params:r}=e;return t.post("/track",r)},x=async e=>{let{instance:t,params:r}=e;return t.post("/usage",r)},C=async e=>{let{instance:t,params:r}=e;return t.post("/check",r)},z=(e,t,r)=>(t||(t=new Y),e({instance:t,...r})),T=e=>({get:(t,r)=>z(I,e,{id:t,params:r}),create:t=>z(O,e,{params:t}),update:(t,r)=>z(A,e,{id:t,params:r}),delete:t=>z(P,e,{id:t}),billingPortal:(t,r)=>z(j,e,{id:t,params:r})}),k=e=>e?"expand=".concat(e.join(",")):"",I=async e=>{let{instance:t,id:r,params:n}=e;return r?t.get("/customers/".concat(r,"?").concat(k(null==n?void 0:n.expand))):{data:null,error:new m({message:"Customer ID is required",code:"CUSTOMER_ID_REQUIRED"})}},O=async e=>{let{instance:t,params:r}=e;return t.post("/customers?".concat(k(null==r?void 0:r.expand)),r)},A=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r),n)},P=async e=>{let{instance:t,id:r}=e;return t.delete("/customers/".concat(r))},j=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r,"/billing_portal"),n)},E=e=>({get:(t,r,n)=>z(S,e,{customer_id:t,entity_id:r,params:n}),create:(t,r)=>z(N,e,{customer_id:t,params:r}),delete:(t,r)=>z(U,e,{customer_id:t,entity_id:r})}),Z=e=>e?"expand=".concat(e.join(",")):"",S=async e=>{let{instance:t,customer_id:r,entity_id:n,params:a}=e;return t.get("/customers/".concat(r,"/entities/").concat(n,"?").concat(Z(null==a?void 0:a.expand)))},N=async e=>{let{instance:t,customer_id:r,params:n}=e;return t.post("/customers/".concat(r,"/entities"),n)},U=async e=>{let{instance:t,customer_id:r,entity_id:n}=e;return t.delete("/customers/".concat(r,"/entities/").concat(n))},D=e=>({get:t=>z(L,e,{id:t}),create:t=>z(F,e,{params:t}),list:t=>z(R,e,{params:t})}),R=async e=>{let{instance:t,params:r}=e,n="/products_beta";if(r){let e=new URLSearchParams;for(let[t,n]of Object.entries(r))void 0!==n&&e.append(t,String(n));let t=e.toString();t&&(n+="?".concat(t))}return t.get(n)},L=async e=>{let{instance:t,id:r}=e;return t.get("/products/".concat(r))},F=async e=>{let{instance:t,params:r}=e;return t.post("/products",r)},M=e=>({createCode:t=>z(B,e,{params:t}),redeemCode:t=>z(V,e,{params:t})}),B=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/code",r)},V=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/redeem",r)},K=async e=>{let{response:t,logger:r,logError:n=!0}=e;if(t.status<200||t.status>=300){let e;try{e=await t.json(),n&&r.error("[Autumn] ".concat(e.message))}catch(e){throw e}return{data:null,error:new m({message:e.message,code:e.code}),statusCode:t.status}}try{return{data:await t.json(),error:null,statusCode:null==t?void 0:t.status}}catch(e){throw e}},$=p(r(83809)),q=()=>{let e=new Date().toISOString();return"[".concat(e.split("T")[1].split(".")[0],"]")},W=e=>H.indexOf(e)>=H.indexOf(J.level),H=["debug","info","warn","error","fatal"],J={...console,level:"info",debug:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];W("debug")&&console.log(q(),$.default.gray("DEBUG"),...t)},log:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];console.log(q(),$.default.blue("INFO"),...t)},info:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];W("info")&&console.log(q(),$.default.blue("INFO"),...t)},warn:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];W("warn")&&console.log(q(),$.default.yellow("WARN"),...t)},error:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];W("error")&&console.log(q(),$.default.red("ERROR"),...t)}},Y=((n=class{async get(e){return K({response:await fetch("".concat(this.url).concat(e),{headers:this.headers}),logger:this.logger})}async post(e,t){try{let r=await fetch("".concat(this.url).concat(e),{method:"POST",headers:this.headers,body:JSON.stringify(t)});return K({response:r,logger:this.logger})}catch(e){throw console.error("Error sending request:",e),e}}async delete(e){return K({response:await fetch("".concat(this.url).concat(e),{method:"DELETE",headers:this.headers}),logger:this.logger})}async attach(e){return y({instance:this,params:e})}async setupPayment(e){return g({instance:this,params:e})}async cancel(e){return _({instance:this,params:e})}async entitled(e){return v({instance:this,params:e})}async check(e){return C({instance:this,params:e})}async event(e){return b({instance:this,params:e})}async track(e){return w({instance:this,params:e})}async usage(e){return x({instance:this,params:e})}constructor(e){this.logger=console,this.customers=T(this),this.products=D(this),this.entities=E(this),this.referrals=M(this);try{this.secretKey=(null==e?void 0:e.secretKey)||a.env.AUTUMN_SECRET_KEY,this.publishableKey=(null==e?void 0:e.publishableKey)||a.env.AUTUMN_PUBLISHABLE_KEY}catch(e){}if(!this.secretKey&&!this.publishableKey&&!(null==e?void 0:e.headers))throw Error("Autumn secret key or publishable key is required");this.headers=(null==e?void 0:e.headers)||{Authorization:"Bearer ".concat(this.secretKey||this.publishableKey),"Content-Type":"application/json"};let t=(null==e?void 0:e.version)||"1.2";this.headers["x-api-version"]=t,this.url=(null==e?void 0:e.url)||"https://api.useautumn.com/v1",this.logger=J,this.logger.level=(null==e?void 0:e.logLevel)||"info"}}).customers=T(),n.products=D(),n.entities=E(),n.referrals=M(),n.attach=e=>z(y,void 0,{params:e}),n.usage=e=>z(x,void 0,{params:e}),n.setupPayment=e=>z(g,void 0,{params:e}),n.cancel=e=>z(_,void 0,{params:e}),n.entitled=e=>z(v,void 0,{params:e}),n.check=e=>z(C,void 0,{params:e}),n.event=e=>z(b,void 0,{params:e}),n.track=e=>z(w,void 0,{params:e}),n),G=r(28935),Q=G.z.object({name:G.z.string().optional(),feature_id:G.z.string()}),X=r(28935);X.z.object({feature_id:X.z.string(),quantity:X.z.number()}),X.z.object({customer_id:X.z.string(),product_id:X.z.string().optional(),entity_id:X.z.string().optional(),options:X.z.array(X.z.object({feature_id:X.z.string(),quantity:X.z.number()})).optional(),product_ids:X.z.array(X.z.string()).optional(),free_trial:X.z.boolean().optional(),success_url:X.z.string().optional(),metadata:X.z.record(X.z.string()).optional(),force_checkout:X.z.boolean().optional(),customer_data:X.z.any().optional(),entity_data:X.z.any().optional(),checkout_session_params:X.z.record(X.z.any()).optional(),reward:X.z.string().optional()}),X.z.object({checkout_url:X.z.string().optional(),customer_id:X.z.string(),product_ids:X.z.array(X.z.string()),code:X.z.string(),message:X.z.string(),customer_data:X.z.any().optional()}),X.z.object({customer_id:X.z.string(),product_id:X.z.string(),entity_id:X.z.string().optional(),cancel_immediately:X.z.boolean().optional()}),X.z.object({success:X.z.boolean(),customer_id:X.z.string(),product_id:X.z.string()}),X.z.object({customer_id:X.z.string(),value:X.z.number().optional(),feature_id:X.z.string().optional(),event_name:X.z.string().optional(),entity_id:X.z.string().optional(),customer_data:X.z.any().optional(),idempotency_key:X.z.string().optional(),entity_data:X.z.any().optional()}),X.z.object({id:X.z.string(),code:X.z.string(),customer_id:X.z.string(),feature_id:X.z.string().optional(),event_name:X.z.string().optional()}),X.z.object({customer_id:X.z.string(),feature_id:X.z.string().optional(),product_id:X.z.string().optional(),entity_id:X.z.string().optional(),customer_data:X.z.any().optional(),required_balance:X.z.number().optional(),send_event:X.z.boolean().optional(),with_preview:X.z.boolean().optional(),entity_data:Q.optional()});var ee=r(28935).z.enum(["invoices","rewards","trials_used","entities","referrals","payment_method"]),et=r(28935);et.z.object({id:et.z.string().nullish(),email:et.z.string().nullish(),name:et.z.string().nullish(),fingerprint:et.z.string().nullish(),metadata:et.z.record(et.z.any()).optional(),expand:et.z.array(ee).optional()}),et.z.object({return_url:et.z.string().optional()});var er=r(28935);er.z.object({customer_id:er.z.string(),program_id:er.z.string()}),er.z.object({code:er.z.string(),customer_id:er.z.string()});var en=async e=>{if(401===e.status){let t=e.clone();if((await t.json()).message.includes("Missing authorization header"))return console.error("[Autumn] Missing authorization header.\n\nUse the getBearerToken prop in <AutumnProvider /> to set the authorization header.\nhttps://docs.useautumn.com/quickstart/quickstart#5-set-up-autumnprovider"),!0}return!1},ea=e=>{let{method:t,backendUrl:r,path:n,error:a}=e;console.error("[Autumn] Fetch failed: ".concat(t," ").concat(r).concat(n,"\n\n1. Check that backendUrl in <AutumnProvider/> is correctly set.\n2. Check that autumnHandler is correctly registered on your backend."))};async function ei(){return await this.get("".concat(this.prefix,"/components/pricing_table"))}var es=async e=>{let{client:t,params:r}=e;return await t.post("".concat(t.prefix,"/customers"),r)},eo=(e,t)=>Array.isArray(e)?e.map(e=>eo(e,t)):null!==e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(e=>{let[r,n]=e,a=r.replace(/([a-z])([A-Z])/g,"$1_$2").replace(/[-\s]+/g,"_").toLowerCase();return t&&t.includes(r)?[a,n]:[a,eo(n,t)]})):e,ec=e=>e?"expand=".concat(e.join(",")):"";async function el(e){let t=eo(e);return await this.post("".concat(this.prefix,"/entities"),t)}async function eu(e,t){eo(t);let r=ec(null==t?void 0:t.expand);return await this.get("".concat(this.prefix,"/entities/").concat(e,"?").concat(r))}async function ed(e){return await this.delete("".concat(this.prefix,"/entities/").concat(e))}async function ep(e){let{dialog:t,...r}=e,n=eo(r,["checkoutSessionparams"]);return await this.post("".concat(this.prefix,"/attach"),n)}async function eh(e){let t=eo(e,["checkoutSessionParams"]);return await this.post("".concat(this.prefix,"/setup_payment"),t)}async function ef(e){let t=eo(e);return await this.post("".concat(this.prefix,"/cancel"),t)}async function em(e){let{dialog:t,...r}=e,n=eo(r);return await this.post("".concat(this.prefix,"/check"),n)}async function ey(e){let t=eo(e);return await this.post("".concat(this.prefix,"/track"),t)}async function eg(e){let t=eo(e||{});return await this.post("".concat(this.prefix,"/billing_portal"),t)}async function e_(){return await this.get("".concat(this.prefix,"/products"))}async function ev(e){let t=eo(e);return await this.post("".concat(this.prefix,"/referrals/code"),t)}async function eb(e){let t=eo(e);return await this.post("".concat(this.prefix,"/referrals/redeem"),t)}var ew=class{async detectCors(){var e;if(null==(e=this.prefix)?void 0:e.includes("/api/auth"))return{valid:!0,includeCredentials:!0};let t="".concat(this.backendUrl,"/api/autumn/cors");try{return await fetch(t,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!0}}catch(e){try{return await fetch(t,{method:"POST",credentials:"omit",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!1}}catch(e){return{valid:!1,includeCredentials:void 0}}}}async shouldIncludeCredentials(){if(void 0!==this.includeCredentials)return this.includeCredentials;try{let e=await this.detectCors();return e.valid&&(console.warn("[Autumn] Detected CORS credentials: ".concat(e.includeCredentials)),console.warn("[Autumn] To disable this warning, you can set includeCredentials={".concat(e.includeCredentials?"true":"false","} in <AutumnProvider />")),this.includeCredentials=e.includeCredentials),e.includeCredentials||!1}catch(e){return console.error("[Autumn] Error detecting CORS: ".concat(e.message)),!1}}async getHeaders(){let e={"Content-Type":"application/json"};if(this.getBearerToken)try{let t=await this.getBearerToken();e.Authorization="Bearer ".concat(t)}catch(e){console.error("Failed to call getToken() in AutumnProvider")}return e}async handleFetch(e){let{path:t,method:r,body:n}=e;n="POST"===r?JSON.stringify({...n,customer_data:this.customerData||void 0}):void 0;let a=await this.shouldIncludeCredentials();try{let e=await fetch("".concat(this.backendUrl).concat(t),{method:r,body:n,headers:await this.getHeaders(),credentials:a?"include":"omit"}),i=await en(e);return await K({response:e,logger:console,logError:!i})}catch(e){return ea({method:r,backendUrl:this.backendUrl||"",path:t,error:e}),{data:null,error:new m({message:e.message,code:"fetch_failed"})}}}async post(e,t){return await this.handleFetch({path:e,method:"POST",body:t})}async get(e){return await this.handleFetch({path:e,method:"GET"})}async delete(e){return await this.handleFetch({path:e,method:"DELETE"})}async createCustomer(e){return await es({client:this,params:e})}async getPricingTable(){return await ei.bind(this)()}constructor({backendUrl:e,getBearerToken:t,customerData:r,includeCredentials:n,betterAuthUrl:a}){this.attach=ep.bind(this),this.cancel=ef.bind(this),this.check=em.bind(this),this.track=ey.bind(this),this.openBillingPortal=eg.bind(this),this.setupPayment=eh.bind(this),this.entities={create:el.bind(this),get:eu.bind(this),delete:ed.bind(this)},this.referrals={createCode:ev.bind(this),redeemCode:eb.bind(this)},this.products={list:e_.bind(this)},this.backendUrl=e,this.getBearerToken=t,this.customerData=r,this.includeCredentials=n,this.prefix="/api/autumn",a&&(this.prefix="/api/auth/autumn",this.backendUrl=a)}},ex=r(12359);(0,ex.createContext)({initialized:!1,disableDialogs:!1,client:new ew({backendUrl:""}),paywallDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}},attachDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}}});var eC=e=>{let{AutumnContext:t,name:r,errorIfNotInitialized:n=!0}=e,a=(0,ex.useContext)(t);if(!a.initialized&&n)throw Error("".concat(r," must be used within <AutumnProvider />"));return a},ez=(e,t)=>{if(!e)return null;if(!t)return e.map(t=>{if(t.base_variant_id){let r=e.find(e=>e.id===t.base_variant_id);if(r)return{...t,name:r.name}}return t});let r=structuredClone(e),n=[];for(let e of t){var a,i,s,o;if(!e.id){let t={},r=null==(i=e.items)?void 0:i.map(e=>({display:{primary_text:e.primaryText,secondary_text:e.secondaryText}})),a=e.price;e.price&&(t.is_free=!1,r=[{display:{primary_text:null==a?void 0:a.primaryText,secondary_text:null==a?void 0:a.secondaryText}},...r||[]]),r&&0!==r.length||(r=[{display:{primary_text:""}}]),n.push({display:{name:e.name,description:e.description,button_text:e.buttonText,recommend_text:e.recommendText,everything_from:e.everythingFrom,button_url:e.buttonUrl},items:r,properties:t});continue}let t=r.find(t=>t.id===e.id);if(!t)continue;let c=t.name,l=t.base_variant_id;if(l){let e=r.find(e=>e.id===l);e&&(c=e.name)}c=e.name||c;let u=null==(a=t.properties)?void 0:a.is_free,d=t.properties||{},p=e.items,h=e.price,f=[];if(h?(d.is_free=!1,u||void 0!==p?f.push({display:{primary_text:h.primaryText,secondary_text:h.secondaryText}}):t.items[0].display={primary_text:h.primaryText,secondary_text:h.secondaryText}):p&&!u&&f.push(t.items[0]),p)for(let e of p)if(e.featureId){let r=t.items.find(t=>t.feature_id===e.featureId);if(!r){console.error("Feature with id ".concat(e.featureId," not found for product ").concat(t.id));continue}f.push({...r,display:{primary_text:e.primaryText||(null==(s=r.display)?void 0:s.primary_text),secondary_text:e.secondaryText||(null==(o=r.display)?void 0:o.secondary_text)}})}else f.push({display:{primary_text:e.primaryText,secondary_text:e.secondaryText}});else f=t.items;let m={...t,items:f,properties:d,display:{name:c,description:e.description,button_text:e.buttonText,recommend_text:e.recommendText,everything_from:e.everythingFrom,button_url:e.buttonUrl}};n.push(m)}return n},eT={refreshInterval:0},ek=e=>{let{AutumnContext:t,params:r,authClient:n}=e,a=eC({AutumnContext:t,name:"usePricingTable",errorIfNotInitialized:!n}),i=n?n.autumn:a.client,s=async()=>{try{let{data:e,error:t}=await i.products.list();if(t)throw t;return(null==e?void 0:e.list)||[]}catch(e){throw new m({message:"Failed to fetch pricing table products",code:"failed_to_fetch_pricing_table_products"})}},{data:o,error:c,mutate:l}=(0,f.default)("pricing-table",s,{...eT});return{products:ez(o||[],null==r?void 0:r.productDetails),isLoading:!c&&!o,error:c,refetch:l}}},40670:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ERROR_REVALIDATE_EVENT=3,t.FOCUS_EVENT=0,t.MUTATE_EVENT=2,t.RECONNECT_EVENT=1},40808:(e,t,r)=>{"use strict";var n=r(12359),a=r(40670),i=r(45663),s=function(e){return e&&e.__esModule?e:{default:e}}(n),o=function(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,t}(a);let c=new WeakMap,l=()=>{},u=l(),d=Object,p=e=>e===u,h=e=>"function"==typeof e,f=(e,t)=>({...e,...t}),m=e=>h(e.then),y={},g={},_="undefined",v=typeof window!=_,b=typeof document!=_,w=v&&"Deno"in window,x=()=>v&&typeof window.requestAnimationFrame!=_,C=(e,t)=>{let r=c.get(e);return[()=>!p(t)&&e.get(t)||y,n=>{if(!p(t)){let a=e.get(t);t in g||(g[t]=a),r[5](t,f(a,n),a||y)}},r[6],()=>!p(t)&&t in g?g[t]:!p(t)&&e.get(t)||y]},z=!0,[T,k]=v&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[l,l],I={isOnline:()=>z,isVisible:()=>{let e=b&&document.visibilityState;return p(e)||"hidden"!==e}},O={initFocus:e=>(b&&document.addEventListener("visibilitychange",e),T("focus",e),()=>{b&&document.removeEventListener("visibilitychange",e),k("focus",e)}),initReconnect:e=>{let t=()=>{z=!0,e()},r=()=>{z=!1};return T("online",t),T("offline",r),()=>{k("online",t),k("offline",r)}}},A=!s.default.useId,P=!v||w,j=P?n.useEffect:n.useLayoutEffect,E="undefined"!=typeof navigator&&navigator.connection,Z=!P&&E&&(["slow-2g","2g"].includes(E.effectiveType)||E.saveData),S=new WeakMap,N=e=>d.prototype.toString.call(e),U=(e,t)=>e==="[object ".concat(t,"]"),D=0,R=e=>{let t,r,n=typeof e,a=N(e),i=U(a,"Date"),s=U(a,"RegExp"),o=U(a,"Object");if(d(e)!==e||i||s)t=i?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=S.get(e))return t;if(t=++D+"~",S.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=R(e[r])+",";S.set(e,t)}if(o){t="#";let n=d.keys(e).sort();for(;!p(r=n.pop());)p(e[r])||(t+=r+":"+R(e[r])+",");S.set(e,t)}}return t},L=e=>{if(h(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?R(e):"",t]},F=0,M=()=>++F;async function B(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,a,i,s]=t,l=f({populateCache:!0,throwOnError:!0},"boolean"==typeof s?{revalidate:s}:s||{}),d=l.populateCache,y=l.rollbackOnError,g=l.optimisticData,_=e=>"function"==typeof y?y(e):!1!==y,v=l.throwOnError;if(h(a)){let e=[];for(let t of n.keys())!/^\$(inf|sub)\$/.test(t)&&a(n.get(t)._k)&&e.push(t);return Promise.all(e.map(b))}return b(a);async function b(e){let r,[a]=L(e);if(!a)return;let[s,f]=C(n,a),[y,b,w,x]=c.get(n),z=()=>{let t=y[a];return(h(l.revalidate)?l.revalidate(s().data,e):!1!==l.revalidate)&&(delete w[a],delete x[a],t&&t[0])?t[0](o.MUTATE_EVENT).then(()=>s().data):s().data};if(t.length<3)return z();let T=i,k=!1,I=M();b[a]=[I,0];let O=!p(g),A=s(),P=A.data,j=A._c,E=p(j)?P:j;if(O&&f({data:g=h(g)?g(E,P):g,_c:E}),h(T))try{T=T(E)}catch(e){r=e,k=!0}if(T&&m(T)){if(T=await T.catch(e=>{r=e,k=!0}),I!==b[a][0]){if(k)throw r;return T}k&&O&&_(r)&&(d=!0,f({data:E,_c:u}))}if(d&&!k&&(h(d)?f({data:d(T,E),error:u,_c:u}):f({data:T,error:u,_c:u})),b[a][1]=M(),Promise.resolve(z()).then(()=>{f({_c:u})}),k){if(v)throw r;return}return T}}let V=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},K=(e,t)=>{if(!c.has(e)){let r=f(O,t),n=Object.create(null),a=B.bind(u,e),i=l,s=Object.create(null),d=(e,t)=>{let r=s[e]||[];return s[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},p=(t,r,n)=>{e.set(t,r);let a=s[t];if(a)for(let e of a)e(r,n)},h=()=>{if(!c.has(e)&&(c.set(e,[n,Object.create(null),Object.create(null),Object.create(null),a,p,d]),!P)){let t=r.initFocus(setTimeout.bind(u,V.bind(u,n,o.FOCUS_EVENT))),a=r.initReconnect(setTimeout.bind(u,V.bind(u,n,o.RECONNECT_EVENT)));i=()=>{t&&t(),a&&a(),c.delete(e)}}};return h(),[e,a,h,i]}return[e,c.get(e)[4]]},$=i.dequal,[q,W]=K(new Map),H=f({onLoadingSlow:l,onSuccess:l,onError:l,onErrorRetry:(e,t,r,n,a)=>{let i=r.errorRetryCount,s=a.retryCount,o=~~((Math.random()+.5)*(1<<(s<8?s:8)))*r.errorRetryInterval;(p(i)||!(s>i))&&setTimeout(n,o,a)},onDiscarded:l,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:Z?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:Z?5e3:3e3,compare:$,isPaused:()=>!1,cache:q,mutate:W,fallback:{}},I),J=(e,t)=>{let r=f(e,t);if(t){let{use:n,fallback:a}=e,{use:i,fallback:s}=t;n&&i&&(r.use=n.concat(i)),a&&s&&(r.fallback=f(a,s))}return r},Y=n.createContext({});t.IS_REACT_LEGACY=A,t.IS_SERVER=P,t.OBJECT=d,t.SWRConfig=e=>{let{value:t}=e,r=n.useContext(Y),a=h(t),i=n.useMemo(()=>a?t(r):t,[a,r,t]),s=n.useMemo(()=>a?i:J(r,i),[a,r,i]),o=i&&i.provider,c=n.useRef(u);o&&!c.current&&(c.current=K(o(s.cache||q),i));let l=c.current;return l&&(s.cache=l[0],s.mutate=l[1]),j(()=>{if(l)return l[2]&&l[2](),l[3]},[]),n.createElement(Y.Provider,f(e,{value:s}))},t.SWRConfigContext=Y,t.SWRGlobalState=c,t.UNDEFINED=u,t.cache=q,t.compare=$,t.createCacheHelper=C,t.defaultConfig=H,t.defaultConfigOptions=O,t.getTimestamp=M,t.hasRequestAnimationFrame=x,t.initCache=K,t.internalMutate=B,t.isDocumentDefined=b,t.isFunction=h,t.isLegacyDeno=w,t.isPromiseLike=m,t.isUndefined=p,t.isWindowDefined=v,t.mergeConfigs=J,t.mergeObjects=f,t.mutate=W,t.noop=l,t.preset=I,t.rAF=e=>x()?window.requestAnimationFrame(e):setTimeout(e,1),t.serialize=L,t.slowConnection=Z,t.stableHash=R,t.useIsomorphicLayoutEffect=j},45663:(e,t)=>{var r=Object.prototype.hasOwnProperty;t.dequal=function e(t,n){var a,i;if(t===n)return!0;if(t&&n&&(a=t.constructor)===n.constructor){if(a===Date)return t.getTime()===n.getTime();if(a===RegExp)return t.toString()===n.toString();if(a===Array){if((i=t.length)===n.length)for(;i--&&e(t[i],n[i]););return -1===i}if(!a||"object"==typeof t){for(a in i=0,t)if(r.call(t,a)&&++i&&!r.call(n,a)||!(a in n)||!e(t[a],n[a]))return!1;return Object.keys(n).length===i}}return t!=t&&n!=n}},48066:(e,t,r)=>{"use strict";e.exports=r(25913)},54594:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var a=Object.getOwnPropertyDescriptor(t,r);(!a||("get"in a?!t.__esModule:a.writable||a.configurable))&&(a={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,a)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(66742),t),a(r(15232),t),a(r(75041),t),a(r(7597),t),a(r(71624),t),a(r(10360),t)},55853:(e,t,r)=>{"use strict";r.r(t),r.d(t,{usePricingTable:()=>i});var n=r(40338),a=r(14776);let i=e=>(0,n.usePricingTableBase)({AutumnContext:a.AutumnContext,params:e})},59153:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(12359),a=r(48066),i=r(22365),s=function(e){return e&&e.__esModule?e:{default:e}}(n);let o=()=>{},c=o(),l=Object,u=e=>e===c,d=e=>"function"==typeof e,p=new WeakMap,h=e=>l.prototype.toString.call(e),f=(e,t)=>e===`[object ${t}]`,m=0,y=e=>{let t,r,n=typeof e,a=h(e),i=f(a,"Date"),s=f(a,"RegExp"),o=f(a,"Object");if(l(e)!==e||i||s)t=i?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=p.get(e))return t;if(t=++m+"~",p.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=y(e[r])+",";p.set(e,t)}if(o){t="#";let n=l.keys(e).sort();for(;!u(r=n.pop());)u(e[r])||(t+=r+":"+y(e[r])+",");p.set(e,t)}}return t},g=e=>{if(d(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?y(e):"",t]},_=s.default.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),v={dedupe:!0},b=i.OBJECT.defineProperty(i.SWRConfig,"defaultValue",{value:i.defaultConfig}),w=i.withArgs((e,t,r)=>{let{cache:s,compare:o,suspense:c,fallbackData:l,revalidateOnMount:u,revalidateIfStale:d,refreshInterval:p,refreshWhenHidden:h,refreshWhenOffline:f,keepPreviousData:m}=r,[y,g,b,w]=i.SWRGlobalState.get(s),[x,C]=i.serialize(e),z=n.useRef(!1),T=n.useRef(!1),k=n.useRef(x),I=n.useRef(t),O=n.useRef(r),A=()=>O.current,P=()=>A().isVisible()&&A().isOnline(),[j,E,Z,S]=i.createCacheHelper(s,x),N=n.useRef({}).current,U=i.isUndefined(l)?i.isUndefined(r.fallback)?i.UNDEFINED:r.fallback[x]:l,D=(e,t)=>{for(let r in N)if("data"===r){if(!o(e[r],t[r])&&(!i.isUndefined(e[r])||!o(q,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},R=n.useMemo(()=>{let e=!!x&&!!t&&(i.isUndefined(u)?!A().isPaused()&&!c&&!1!==d:u),r=t=>{let r=i.mergeObjects(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=j(),a=S(),s=r(n),o=n===a?s:r(a),l=s;return[()=>{let e=r(j());return D(e,l)?(l.data=e.data,l.isLoading=e.isLoading,l.isValidating=e.isValidating,l.error=e.error,l):(l=e,e)},()=>o]},[s,x]),L=a.useSyncExternalStore(n.useCallback(e=>Z(x,(t,r)=>{D(r,t)||e()}),[s,x]),R[0],R[1]),F=!z.current,M=y[x]&&y[x].length>0,B=L.data,V=i.isUndefined(B)?U&&i.isPromiseLike(U)?_(U):U:B,K=L.error,$=n.useRef(V),q=m?i.isUndefined(B)?i.isUndefined($.current)?V:$.current:B:V,W=(!M||!!i.isUndefined(K))&&(F&&!i.isUndefined(u)?u:!A().isPaused()&&(c?!i.isUndefined(V)&&d:i.isUndefined(V)||d)),H=!!(x&&t&&F&&W),J=i.isUndefined(L.isValidating)?H:L.isValidating,Y=i.isUndefined(L.isLoading)?H:L.isLoading,G=n.useCallback(async e=>{let t,n,a=I.current;if(!x||!a||T.current||A().isPaused())return!1;let s=!0,c=e||{},l=!b[x]||!c.dedupe,u=()=>i.IS_REACT_LEGACY?!T.current&&x===k.current&&z.current:x===k.current,d={isValidating:!1,isLoading:!1},p=()=>{E(d)},h=()=>{let e=b[x];e&&e[1]===n&&delete b[x]},f={isValidating:!0};i.isUndefined(j().data)&&(f.isLoading=!0);try{if(l&&(E(f),r.loadingTimeout&&i.isUndefined(j().data)&&setTimeout(()=>{s&&u()&&A().onLoadingSlow(x,r)},r.loadingTimeout),b[x]=[a(C),i.getTimestamp()]),[t,n]=b[x],t=await t,l&&setTimeout(h,r.dedupingInterval),!b[x]||b[x][1]!==n)return l&&u()&&A().onDiscarded(x),!1;d.error=i.UNDEFINED;let e=g[x];if(!i.isUndefined(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return p(),l&&u()&&A().onDiscarded(x),!1;let c=j().data;d.data=o(c,t)?c:t,l&&u()&&A().onSuccess(t,x,r)}catch(r){h();let e=A(),{shouldRetryOnError:t}=e;!e.isPaused()&&(d.error=r,l&&u()&&(e.onError(r,x,e),(!0===t||i.isFunction(t)&&t(r))&&(!A().revalidateOnFocus||!A().revalidateOnReconnect||P())&&e.onErrorRetry(r,x,e,e=>{let t=y[x];t&&t[0]&&t[0](i.revalidateEvents.ERROR_REVALIDATE_EVENT,e)},{retryCount:(c.retryCount||0)+1,dedupe:!0})))}return s=!1,p(),!0},[x,s]),Q=n.useCallback((...e)=>i.internalMutate(s,k.current,...e),[]);if(i.useIsomorphicLayoutEffect(()=>{I.current=t,O.current=r,i.isUndefined(B)||($.current=B)}),i.useIsomorphicLayoutEffect(()=>{if(!x)return;let e=G.bind(i.UNDEFINED,v),t=0;A().revalidateOnFocus&&(t=Date.now()+A().focusThrottleInterval);let r=i.subscribeCallback(x,y,(r,n={})=>{if(r==i.revalidateEvents.FOCUS_EVENT){let r=Date.now();A().revalidateOnFocus&&r>t&&P()&&(t=r+A().focusThrottleInterval,e())}else if(r==i.revalidateEvents.RECONNECT_EVENT)A().revalidateOnReconnect&&P()&&e();else if(r==i.revalidateEvents.MUTATE_EVENT)return G();else if(r==i.revalidateEvents.ERROR_REVALIDATE_EVENT)return G(n)});return T.current=!1,k.current=x,z.current=!0,E({_k:C}),W&&!b[x]&&(i.isUndefined(V)||i.IS_SERVER?e():i.rAF(e)),()=>{T.current=!0,r()}},[x]),i.useIsomorphicLayoutEffect(()=>{let e;function t(){let t=i.isFunction(p)?p(j().data):p;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!j().error&&(h||A().isVisible())&&(f||A().isOnline())?G(v).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[p,h,f,x]),n.useDebugValue(q),c&&i.isUndefined(V)&&x){if(!i.IS_REACT_LEGACY&&i.IS_SERVER)throw Error("Fallback data is required when using Suspense in SSR.");I.current=t,O.current=r,T.current=!1;let e=w[x];if(i.isUndefined(e)||_(Q(e)),i.isUndefined(K)){let e=G(v);i.isUndefined(q)||(e.status="fulfilled",e.value=!0),_(e)}else throw K}return{mutate:Q,get data(){return N.data=!0,q},get error(){return N.error=!0,K},get isValidating(){return N.isValidating=!0,J},get isLoading(){return N.isLoading=!0,Y}}});Object.defineProperty(t,"mutate",{enumerable:!0,get:function(){return i.mutate}}),Object.defineProperty(t,"preload",{enumerable:!0,get:function(){return i.preload}}),Object.defineProperty(t,"useSWRConfig",{enumerable:!0,get:function(){return i.useSWRConfig}}),t.SWRConfig=b,t.default=w,t.unstable_serialize=e=>g(e)[0]},62005:(e,t,r)=>{"use strict";var n,a=r(37811),i=Object.create,s=Object.defineProperty,o=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,d=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of c(t))u.call(e,a)||a===r||s(e,a,{get:()=>t[a],enumerable:!(n=o(t,a))||n.enumerable});return e},p={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(p,{AutumnClient:()=>ev}),e.exports=d(s({},"__esModule",{value:!0}),p);var h=async e=>{if(401===e.status){let t=e.clone();if((await t.json()).message.includes("Missing authorization header"))return console.error("[Autumn] Missing authorization header.\n\nUse the getBearerToken prop in <AutumnProvider /> to set the authorization header.\nhttps://docs.useautumn.com/quickstart/quickstart#5-set-up-autumnprovider"),!0}return!1},f=class e extends Error{static fromError(t){return new e({message:t.message||"Unknown error",code:t.code||"unknown_error"})}toString(){return"".concat(this.message," (code: ").concat(this.code,")")}toJSON(){return{message:this.message,code:this.code}}constructor(e){super(e.message),this.message=e.message,this.code=e.code}},m=async e=>{let{instance:t,params:r}=e;return t.post("/attach",r)},y=async e=>{let{instance:t,params:r}=e;return t.post("/setup_payment",r)},g=async e=>{let{instance:t,params:r}=e;return t.post("/cancel",r)},_=async e=>{let{instance:t,params:r}=e;return t.post("/entitled",r)},v=async e=>{let{instance:t,params:r}=e;return t.post("/events",r)},b=async e=>{let{instance:t,params:r}=e;return t.post("/track",r)},w=async e=>{let{instance:t,params:r}=e;return t.post("/usage",r)},x=async e=>{let{instance:t,params:r}=e;return t.post("/check",r)},C=(e,t,r)=>(t||(t=new J),e({instance:t,...r})),z=e=>({get:(t,r)=>C(k,e,{id:t,params:r}),create:t=>C(I,e,{params:t}),update:(t,r)=>C(O,e,{id:t,params:r}),delete:t=>C(A,e,{id:t}),billingPortal:(t,r)=>C(P,e,{id:t,params:r})}),T=e=>e?"expand=".concat(e.join(",")):"",k=async e=>{let{instance:t,id:r,params:n}=e;return r?t.get("/customers/".concat(r,"?").concat(T(null==n?void 0:n.expand))):{data:null,error:new f({message:"Customer ID is required",code:"CUSTOMER_ID_REQUIRED"})}},I=async e=>{let{instance:t,params:r}=e;return t.post("/customers?".concat(T(null==r?void 0:r.expand)),r)},O=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r),n)},A=async e=>{let{instance:t,id:r}=e;return t.delete("/customers/".concat(r))},P=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r,"/billing_portal"),n)},j=e=>({get:(t,r,n)=>C(Z,e,{customer_id:t,entity_id:r,params:n}),create:(t,r)=>C(S,e,{customer_id:t,params:r}),delete:(t,r)=>C(N,e,{customer_id:t,entity_id:r})}),E=e=>e?"expand=".concat(e.join(",")):"",Z=async e=>{let{instance:t,customer_id:r,entity_id:n,params:a}=e;return t.get("/customers/".concat(r,"/entities/").concat(n,"?").concat(E(null==a?void 0:a.expand)))},S=async e=>{let{instance:t,customer_id:r,params:n}=e;return t.post("/customers/".concat(r,"/entities"),n)},N=async e=>{let{instance:t,customer_id:r,entity_id:n}=e;return t.delete("/customers/".concat(r,"/entities/").concat(n))},U=e=>({get:t=>C(R,e,{id:t}),create:t=>C(L,e,{params:t}),list:t=>C(D,e,{params:t})}),D=async e=>{let{instance:t,params:r}=e,n="/products_beta";if(r){let e=new URLSearchParams;for(let[t,n]of Object.entries(r))void 0!==n&&e.append(t,String(n));let t=e.toString();t&&(n+="?".concat(t))}return t.get(n)},R=async e=>{let{instance:t,id:r}=e;return t.get("/products/".concat(r))},L=async e=>{let{instance:t,params:r}=e;return t.post("/products",r)},F=e=>({createCode:t=>C(M,e,{params:t}),redeemCode:t=>C(B,e,{params:t})}),M=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/code",r)},B=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/redeem",r)},V=async e=>{let{response:t,logger:r,logError:n=!0}=e;if(t.status<200||t.status>=300){let e;try{e=await t.json(),n&&r.error("[Autumn] ".concat(e.message))}catch(e){throw e}return{data:null,error:new f({message:e.message,code:e.code}),statusCode:t.status}}try{return{data:await t.json(),error:null,statusCode:null==t?void 0:t.status}}catch(e){throw e}},K=((e,t,r)=>(r=null!=e?i(l(e)):{},d(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(83809)),$=()=>{let e=new Date().toISOString();return"[".concat(e.split("T")[1].split(".")[0],"]")},q=e=>W.indexOf(e)>=W.indexOf(H.level),W=["debug","info","warn","error","fatal"],H={...console,level:"info",debug:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];q("debug")&&console.log($(),K.default.gray("DEBUG"),...t)},log:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];console.log($(),K.default.blue("INFO"),...t)},info:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];q("info")&&console.log($(),K.default.blue("INFO"),...t)},warn:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];q("warn")&&console.log($(),K.default.yellow("WARN"),...t)},error:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];q("error")&&console.log($(),K.default.red("ERROR"),...t)}},J=((n=class{async get(e){return V({response:await fetch("".concat(this.url).concat(e),{headers:this.headers}),logger:this.logger})}async post(e,t){try{let r=await fetch("".concat(this.url).concat(e),{method:"POST",headers:this.headers,body:JSON.stringify(t)});return V({response:r,logger:this.logger})}catch(e){throw console.error("Error sending request:",e),e}}async delete(e){return V({response:await fetch("".concat(this.url).concat(e),{method:"DELETE",headers:this.headers}),logger:this.logger})}async attach(e){return m({instance:this,params:e})}async setupPayment(e){return y({instance:this,params:e})}async cancel(e){return g({instance:this,params:e})}async entitled(e){return _({instance:this,params:e})}async check(e){return x({instance:this,params:e})}async event(e){return v({instance:this,params:e})}async track(e){return b({instance:this,params:e})}async usage(e){return w({instance:this,params:e})}constructor(e){this.logger=console,this.customers=z(this),this.products=U(this),this.entities=j(this),this.referrals=F(this);try{this.secretKey=(null==e?void 0:e.secretKey)||a.env.AUTUMN_SECRET_KEY,this.publishableKey=(null==e?void 0:e.publishableKey)||a.env.AUTUMN_PUBLISHABLE_KEY}catch(e){}if(!this.secretKey&&!this.publishableKey&&!(null==e?void 0:e.headers))throw Error("Autumn secret key or publishable key is required");this.headers=(null==e?void 0:e.headers)||{Authorization:"Bearer ".concat(this.secretKey||this.publishableKey),"Content-Type":"application/json"};let t=(null==e?void 0:e.version)||"1.2";this.headers["x-api-version"]=t,this.url=(null==e?void 0:e.url)||"https://api.useautumn.com/v1",this.logger=H,this.logger.level=(null==e?void 0:e.logLevel)||"info"}}).customers=z(),n.products=U(),n.entities=j(),n.referrals=F(),n.attach=e=>C(m,void 0,{params:e}),n.usage=e=>C(w,void 0,{params:e}),n.setupPayment=e=>C(y,void 0,{params:e}),n.cancel=e=>C(g,void 0,{params:e}),n.entitled=e=>C(_,void 0,{params:e}),n.check=e=>C(x,void 0,{params:e}),n.event=e=>C(v,void 0,{params:e}),n.track=e=>C(b,void 0,{params:e}),n),Y=r(28935),G=Y.z.object({name:Y.z.string().optional(),feature_id:Y.z.string()}),Q=r(28935);Q.z.object({feature_id:Q.z.string(),quantity:Q.z.number()}),Q.z.object({customer_id:Q.z.string(),product_id:Q.z.string().optional(),entity_id:Q.z.string().optional(),options:Q.z.array(Q.z.object({feature_id:Q.z.string(),quantity:Q.z.number()})).optional(),product_ids:Q.z.array(Q.z.string()).optional(),free_trial:Q.z.boolean().optional(),success_url:Q.z.string().optional(),metadata:Q.z.record(Q.z.string()).optional(),force_checkout:Q.z.boolean().optional(),customer_data:Q.z.any().optional(),entity_data:Q.z.any().optional(),checkout_session_params:Q.z.record(Q.z.any()).optional(),reward:Q.z.string().optional()}),Q.z.object({checkout_url:Q.z.string().optional(),customer_id:Q.z.string(),product_ids:Q.z.array(Q.z.string()),code:Q.z.string(),message:Q.z.string(),customer_data:Q.z.any().optional()}),Q.z.object({customer_id:Q.z.string(),product_id:Q.z.string(),entity_id:Q.z.string().optional(),cancel_immediately:Q.z.boolean().optional()}),Q.z.object({success:Q.z.boolean(),customer_id:Q.z.string(),product_id:Q.z.string()}),Q.z.object({customer_id:Q.z.string(),value:Q.z.number().optional(),feature_id:Q.z.string().optional(),event_name:Q.z.string().optional(),entity_id:Q.z.string().optional(),customer_data:Q.z.any().optional(),idempotency_key:Q.z.string().optional(),entity_data:Q.z.any().optional()}),Q.z.object({id:Q.z.string(),code:Q.z.string(),customer_id:Q.z.string(),feature_id:Q.z.string().optional(),event_name:Q.z.string().optional()}),Q.z.object({customer_id:Q.z.string(),feature_id:Q.z.string().optional(),product_id:Q.z.string().optional(),entity_id:Q.z.string().optional(),customer_data:Q.z.any().optional(),required_balance:Q.z.number().optional(),send_event:Q.z.boolean().optional(),with_preview:Q.z.boolean().optional(),entity_data:G.optional()});var X=r(28935).z.enum(["invoices","rewards","trials_used","entities","referrals","payment_method"]),ee=r(28935);ee.z.object({id:ee.z.string().nullish(),email:ee.z.string().nullish(),name:ee.z.string().nullish(),fingerprint:ee.z.string().nullish(),metadata:ee.z.record(ee.z.any()).optional(),expand:ee.z.array(X).optional()}),ee.z.object({return_url:ee.z.string().optional()});var et=r(28935);et.z.object({customer_id:et.z.string(),program_id:et.z.string()}),et.z.object({code:et.z.string(),customer_id:et.z.string()});var er=e=>{let{method:t,backendUrl:r,path:n,error:a}=e;console.error("[Autumn] Fetch failed: ".concat(t," ").concat(r).concat(n,"\n\n1. Check that backendUrl in <AutumnProvider/> is correctly set.\n2. Check that autumnHandler is correctly registered on your backend."))};async function en(){return await this.get("".concat(this.prefix,"/components/pricing_table"))}var ea=async e=>{let{client:t,params:r}=e;return await t.post("".concat(t.prefix,"/customers"),r)},ei=(e,t)=>Array.isArray(e)?e.map(e=>ei(e,t)):null!==e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(e=>{let[r,n]=e,a=r.replace(/([a-z])([A-Z])/g,"$1_$2").replace(/[-\s]+/g,"_").toLowerCase();return t&&t.includes(r)?[a,n]:[a,ei(n,t)]})):e,es=e=>e?"expand=".concat(e.join(",")):"";async function eo(e){let t=ei(e);return await this.post("".concat(this.prefix,"/entities"),t)}async function ec(e,t){ei(t);let r=es(null==t?void 0:t.expand);return await this.get("".concat(this.prefix,"/entities/").concat(e,"?").concat(r))}async function el(e){return await this.delete("".concat(this.prefix,"/entities/").concat(e))}async function eu(e){let{dialog:t,...r}=e,n=ei(r,["checkoutSessionparams"]);return await this.post("".concat(this.prefix,"/attach"),n)}async function ed(e){let t=ei(e,["checkoutSessionParams"]);return await this.post("".concat(this.prefix,"/setup_payment"),t)}async function ep(e){let t=ei(e);return await this.post("".concat(this.prefix,"/cancel"),t)}async function eh(e){let{dialog:t,...r}=e,n=ei(r);return await this.post("".concat(this.prefix,"/check"),n)}async function ef(e){let t=ei(e);return await this.post("".concat(this.prefix,"/track"),t)}async function em(e){let t=ei(e||{});return await this.post("".concat(this.prefix,"/billing_portal"),t)}async function ey(){return await this.get("".concat(this.prefix,"/products"))}async function eg(e){let t=ei(e);return await this.post("".concat(this.prefix,"/referrals/code"),t)}async function e_(e){let t=ei(e);return await this.post("".concat(this.prefix,"/referrals/redeem"),t)}var ev=class{async detectCors(){var e;if(null==(e=this.prefix)?void 0:e.includes("/api/auth"))return{valid:!0,includeCredentials:!0};let t="".concat(this.backendUrl,"/api/autumn/cors");try{return await fetch(t,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!0}}catch(e){try{return await fetch(t,{method:"POST",credentials:"omit",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!1}}catch(e){return{valid:!1,includeCredentials:void 0}}}}async shouldIncludeCredentials(){if(void 0!==this.includeCredentials)return this.includeCredentials;try{let e=await this.detectCors();return e.valid&&(console.warn("[Autumn] Detected CORS credentials: ".concat(e.includeCredentials)),console.warn("[Autumn] To disable this warning, you can set includeCredentials={".concat(e.includeCredentials?"true":"false","} in <AutumnProvider />")),this.includeCredentials=e.includeCredentials),e.includeCredentials||!1}catch(e){return console.error("[Autumn] Error detecting CORS: ".concat(e.message)),!1}}async getHeaders(){let e={"Content-Type":"application/json"};if(this.getBearerToken)try{let t=await this.getBearerToken();e.Authorization="Bearer ".concat(t)}catch(e){console.error("Failed to call getToken() in AutumnProvider")}return e}async handleFetch(e){let{path:t,method:r,body:n}=e;n="POST"===r?JSON.stringify({...n,customer_data:this.customerData||void 0}):void 0;let a=await this.shouldIncludeCredentials();try{let e=await fetch("".concat(this.backendUrl).concat(t),{method:r,body:n,headers:await this.getHeaders(),credentials:a?"include":"omit"}),i=await h(e);return await V({response:e,logger:console,logError:!i})}catch(e){return er({method:r,backendUrl:this.backendUrl||"",path:t,error:e}),{data:null,error:new f({message:e.message,code:"fetch_failed"})}}}async post(e,t){return await this.handleFetch({path:e,method:"POST",body:t})}async get(e){return await this.handleFetch({path:e,method:"GET"})}async delete(e){return await this.handleFetch({path:e,method:"DELETE"})}async createCustomer(e){return await ea({client:this,params:e})}async getPricingTable(){return await en.bind(this)()}constructor({backendUrl:e,getBearerToken:t,customerData:r,includeCredentials:n,betterAuthUrl:a}){this.attach=eu.bind(this),this.cancel=ep.bind(this),this.check=eh.bind(this),this.track=ef.bind(this),this.openBillingPortal=em.bind(this),this.setupPayment=ed.bind(this),this.entities={create:eo.bind(this),get:ec.bind(this),delete:el.bind(this)},this.referrals={createCode:eg.bind(this),redeemCode:e_.bind(this)},this.products={list:ey.bind(this)},this.backendUrl=e,this.getBearerToken=t,this.customerData=r,this.includeCredentials=n,this.prefix="/api/autumn",a&&(this.prefix="/api/auth/autumn",this.backendUrl=a)}}},66742:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.defaultErrorMap=void 0,t.setErrorMap=function(e){i=e},t.getErrorMap=function(){return i};let a=n(r(91286));t.defaultErrorMap=a.default;let i=a.default},68368:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INFINITE_PREFIX="$inf$"},71624:(e,t,r)=>{"use strict";var n;let a;Object.defineProperty(t,"__esModule",{value:!0}),t.discriminatedUnion=t.date=t.boolean=t.bigint=t.array=t.any=t.coerce=t.ZodFirstPartyTypeKind=t.late=t.ZodSchema=t.Schema=t.ZodReadonly=t.ZodPipeline=t.ZodBranded=t.BRAND=t.ZodNaN=t.ZodCatch=t.ZodDefault=t.ZodNullable=t.ZodOptional=t.ZodTransformer=t.ZodEffects=t.ZodPromise=t.ZodNativeEnum=t.ZodEnum=t.ZodLiteral=t.ZodLazy=t.ZodFunction=t.ZodSet=t.ZodMap=t.ZodRecord=t.ZodTuple=t.ZodIntersection=t.ZodDiscriminatedUnion=t.ZodUnion=t.ZodObject=t.ZodArray=t.ZodVoid=t.ZodNever=t.ZodUnknown=t.ZodAny=t.ZodNull=t.ZodUndefined=t.ZodSymbol=t.ZodDate=t.ZodBoolean=t.ZodBigInt=t.ZodNumber=t.ZodString=t.ZodType=void 0,t.NEVER=t.void=t.unknown=t.union=t.undefined=t.tuple=t.transformer=t.symbol=t.string=t.strictObject=t.set=t.record=t.promise=t.preprocess=t.pipeline=t.ostring=t.optional=t.onumber=t.oboolean=t.object=t.number=t.nullable=t.null=t.never=t.nativeEnum=t.nan=t.map=t.literal=t.lazy=t.intersection=t.instanceof=t.function=t.enum=t.effect=void 0,t.datetimeRegex=j,t.custom=eg;let i=r(10360),s=r(66742),o=r(82071),c=r(15232),l=r(7597);class u{constructor(e,t,r,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=n}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let d=(e,t)=>{if((0,c.isValid)(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new i.ZodError(e.common.issues);return this._error=t,this._error}}};function p(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:n,description:a}=e;if(t&&(r||n))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:a}:{errorMap:(t,a)=>{let{message:i}=e;return"invalid_enum_value"===t.code?{message:i??a.defaultError}:void 0===a.data?{message:i??n??a.defaultError}:"invalid_type"!==t.code?{message:a.defaultError}:{message:i??r??a.defaultError}},description:a}}class h{get description(){return this._def.description}_getType(e){return(0,l.getParsedType)(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:(0,l.getParsedType)(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new c.ParseStatus,ctx:{common:e.parent.common,data:e.data,parsedType:(0,l.getParsedType)(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if((0,c.isAsync)(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){let r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:(0,l.getParsedType)(e)},n=this._parseSync({data:e,path:r.path,parent:r});return d(r,n)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:(0,l.getParsedType)(e)};if(!this["~standard"].async)try{let r=this._parseSync({data:e,path:[],parent:t});return(0,c.isValid)(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>(0,c.isValid)(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:(0,l.getParsedType)(e)},n=this._parse({data:e,path:r.path,parent:r});return d(r,await ((0,c.isAsync)(n)?n:Promise.resolve(n)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,n)=>{let a=e(t),s=()=>n.addIssue({code:i.ZodIssueCode.custom,...r(t)});return"undefined"!=typeof Promise&&a instanceof Promise?a.then(e=>!!e||(s(),!1)):!!a||(s(),!1)})}refinement(e,t){return this._refinement((r,n)=>!!e(r)||(n.addIssue("function"==typeof t?t(r,n):t),!1))}_refinement(e){return new eo({schema:this,typeName:n.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ec.create(this,this._def)}nullable(){return el.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return K.create(this)}promise(){return es.create(this,this._def)}or(e){return q.create([this,e],this._def)}and(e){return J.create(this,e,this._def)}transform(e){return new eo({...p(this._def),schema:this,typeName:n.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new eu({...p(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:n.ZodDefault})}brand(){return new eh({typeName:n.ZodBranded,type:this,...p(this._def)})}catch(e){return new ed({...p(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:n.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return ef.create(this,e)}readonly(){return em.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}t.ZodType=h,t.Schema=h,t.ZodSchema=h;let f=/^c[^\s-]{8,}$/i,m=/^[0-9a-z]+$/,y=/^[0-9A-HJKMNP-TV-Z]{26}$/i,g=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,_=/^[a-z0-9_-]{21}$/i,v=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,b=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,w=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,x=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,C=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,z=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,T=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,k=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,I=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,O="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",A=RegExp(`^${O}$`);function P(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}function j(e){let t=`${O}T${P(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)}class E extends h{_parse(e){var t,r,n,s;let o;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==l.ZodParsedType.string){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.string,received:t.parsedType}),c.INVALID}let u=new c.ParseStatus;for(let d of this._def.checks)if("min"===d.kind)e.data.length<d.value&&(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{code:i.ZodIssueCode.too_small,minimum:d.value,type:"string",inclusive:!0,exact:!1,message:d.message}),u.dirty());else if("max"===d.kind)e.data.length>d.value&&(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{code:i.ZodIssueCode.too_big,maximum:d.value,type:"string",inclusive:!0,exact:!1,message:d.message}),u.dirty());else if("length"===d.kind){let t=e.data.length>d.value,r=e.data.length<d.value;(t||r)&&(o=this._getOrReturnCtx(e,o),t?(0,c.addIssueToContext)(o,{code:i.ZodIssueCode.too_big,maximum:d.value,type:"string",inclusive:!0,exact:!0,message:d.message}):r&&(0,c.addIssueToContext)(o,{code:i.ZodIssueCode.too_small,minimum:d.value,type:"string",inclusive:!0,exact:!0,message:d.message}),u.dirty())}else if("email"===d.kind)w.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"email",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty());else if("emoji"===d.kind)a||(a=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),a.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"emoji",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty());else if("uuid"===d.kind)g.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"uuid",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty());else if("nanoid"===d.kind)_.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"nanoid",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty());else if("cuid"===d.kind)f.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"cuid",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty());else if("cuid2"===d.kind)m.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"cuid2",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty());else if("ulid"===d.kind)y.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"ulid",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty());else if("url"===d.kind)try{new URL(e.data)}catch{o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"url",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty()}else"regex"===d.kind?(d.regex.lastIndex=0,d.regex.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"regex",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty())):"trim"===d.kind?e.data=e.data.trim():"includes"===d.kind?e.data.includes(d.value,d.position)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{code:i.ZodIssueCode.invalid_string,validation:{includes:d.value,position:d.position},message:d.message}),u.dirty()):"toLowerCase"===d.kind?e.data=e.data.toLowerCase():"toUpperCase"===d.kind?e.data=e.data.toUpperCase():"startsWith"===d.kind?e.data.startsWith(d.value)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{code:i.ZodIssueCode.invalid_string,validation:{startsWith:d.value},message:d.message}),u.dirty()):"endsWith"===d.kind?e.data.endsWith(d.value)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{code:i.ZodIssueCode.invalid_string,validation:{endsWith:d.value},message:d.message}),u.dirty()):"datetime"===d.kind?j(d).test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{code:i.ZodIssueCode.invalid_string,validation:"datetime",message:d.message}),u.dirty()):"date"===d.kind?A.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{code:i.ZodIssueCode.invalid_string,validation:"date",message:d.message}),u.dirty()):"time"===d.kind?RegExp(`^${P(d)}$`).test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{code:i.ZodIssueCode.invalid_string,validation:"time",message:d.message}),u.dirty()):"duration"===d.kind?b.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"duration",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty()):"ip"===d.kind?(t=e.data,!(("v4"===(r=d.version)||!r)&&x.test(t)||("v6"===r||!r)&&z.test(t))&&1&&(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"ip",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty())):"jwt"===d.kind?!function(e,t){if(!v.test(e))return!1;try{let[r]=e.split(".");if(!r)return!1;let n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),a=JSON.parse(atob(n));if("object"!=typeof a||null===a||"typ"in a&&a?.typ!=="JWT"||!a.alg||t&&a.alg!==t)return!1;return!0}catch{return!1}}(e.data,d.alg)&&(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"jwt",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty()):"cidr"===d.kind?(n=e.data,!(("v4"===(s=d.version)||!s)&&C.test(n)||("v6"===s||!s)&&T.test(n))&&1&&(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"cidr",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty())):"base64"===d.kind?k.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"base64",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty()):"base64url"===d.kind?I.test(e.data)||(o=this._getOrReturnCtx(e,o),(0,c.addIssueToContext)(o,{validation:"base64url",code:i.ZodIssueCode.invalid_string,message:d.message}),u.dirty()):l.util.assertNever(d);return{status:u.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:i.ZodIssueCode.invalid_string,...o.errorUtil.errToObj(r)})}_addCheck(e){return new E({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...o.errorUtil.errToObj(e)})}url(e){return this._addCheck({kind:"url",...o.errorUtil.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...o.errorUtil.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...o.errorUtil.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...o.errorUtil.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...o.errorUtil.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...o.errorUtil.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...o.errorUtil.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...o.errorUtil.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...o.errorUtil.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...o.errorUtil.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...o.errorUtil.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...o.errorUtil.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...o.errorUtil.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...o.errorUtil.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...o.errorUtil.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...o.errorUtil.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...o.errorUtil.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...o.errorUtil.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...o.errorUtil.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...o.errorUtil.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...o.errorUtil.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...o.errorUtil.errToObj(t)})}nonempty(e){return this.min(1,o.errorUtil.errToObj(e))}trim(){return new E({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new E({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new E({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}t.ZodString=E,E.create=e=>new E({checks:[],typeName:n.ZodString,coerce:e?.coerce??!1,...p(e)});class Z extends h{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==l.ZodParsedType.number){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.number,received:t.parsedType}),c.INVALID}let r=new c.ParseStatus;for(let n of this._def.checks)"int"===n.kind?l.util.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:"integer",received:"float",message:n.message}),r.dirty()):"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(t=this._getOrReturnCtx(e,t),(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(t=this._getOrReturnCtx(e,t),(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"multipleOf"===n.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,a=r>n?r:n;return Number.parseInt(e.toFixed(a).replace(".",""))%Number.parseInt(t.toFixed(a).replace(".",""))/10**a}(e.data,n.value)&&(t=this._getOrReturnCtx(e,t),(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):"finite"===n.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.not_finite,message:n.message}),r.dirty()):l.util.assertNever(n);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,o.errorUtil.toString(t))}gt(e,t){return this.setLimit("min",e,!1,o.errorUtil.toString(t))}lte(e,t){return this.setLimit("max",e,!0,o.errorUtil.toString(t))}lt(e,t){return this.setLimit("max",e,!1,o.errorUtil.toString(t))}setLimit(e,t,r,n){return new Z({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:o.errorUtil.toString(n)}]})}_addCheck(e){return new Z({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:o.errorUtil.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:o.errorUtil.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:o.errorUtil.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:o.errorUtil.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:o.errorUtil.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:o.errorUtil.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:o.errorUtil.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:o.errorUtil.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:o.errorUtil.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&l.util.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks)if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;else"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value);return Number.isFinite(t)&&Number.isFinite(e)}}t.ZodNumber=Z,Z.create=e=>new Z({checks:[],typeName:n.ZodNumber,coerce:e?.coerce||!1,...p(e)});class S extends h{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==l.ZodParsedType.bigint)return this._getInvalidInput(e);let r=new c.ParseStatus;for(let n of this._def.checks)"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(t=this._getOrReturnCtx(e,t),(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(t=this._getOrReturnCtx(e,t),(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"multipleOf"===n.kind?e.data%n.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):l.util.assertNever(n);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.bigint,received:t.parsedType}),c.INVALID}gte(e,t){return this.setLimit("min",e,!0,o.errorUtil.toString(t))}gt(e,t){return this.setLimit("min",e,!1,o.errorUtil.toString(t))}lte(e,t){return this.setLimit("max",e,!0,o.errorUtil.toString(t))}lt(e,t){return this.setLimit("max",e,!1,o.errorUtil.toString(t))}setLimit(e,t,r,n){return new S({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:o.errorUtil.toString(n)}]})}_addCheck(e){return new S({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:o.errorUtil.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:o.errorUtil.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:o.errorUtil.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:o.errorUtil.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:o.errorUtil.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}t.ZodBigInt=S,S.create=e=>new S({checks:[],typeName:n.ZodBigInt,coerce:e?.coerce??!1,...p(e)});class N extends h{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==l.ZodParsedType.boolean){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.boolean,received:t.parsedType}),c.INVALID}return(0,c.OK)(e.data)}}t.ZodBoolean=N,N.create=e=>new N({typeName:n.ZodBoolean,coerce:e?.coerce||!1,...p(e)});class U extends h{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==l.ZodParsedType.date){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.date,received:t.parsedType}),c.INVALID}if(Number.isNaN(e.data.getTime())){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_date}),c.INVALID}let r=new c.ParseStatus;for(let n of this._def.checks)"min"===n.kind?e.data.getTime()<n.value&&(t=this._getOrReturnCtx(e,t),(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),r.dirty()):"max"===n.kind?e.data.getTime()>n.value&&(t=this._getOrReturnCtx(e,t),(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),r.dirty()):l.util.assertNever(n);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new U({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:o.errorUtil.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:o.errorUtil.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}t.ZodDate=U,U.create=e=>new U({checks:[],coerce:e?.coerce||!1,typeName:n.ZodDate,...p(e)});class D extends h{_parse(e){if(this._getType(e)!==l.ZodParsedType.symbol){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.symbol,received:t.parsedType}),c.INVALID}return(0,c.OK)(e.data)}}t.ZodSymbol=D,D.create=e=>new D({typeName:n.ZodSymbol,...p(e)});class R extends h{_parse(e){if(this._getType(e)!==l.ZodParsedType.undefined){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.undefined,received:t.parsedType}),c.INVALID}return(0,c.OK)(e.data)}}t.ZodUndefined=R,R.create=e=>new R({typeName:n.ZodUndefined,...p(e)});class L extends h{_parse(e){if(this._getType(e)!==l.ZodParsedType.null){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.null,received:t.parsedType}),c.INVALID}return(0,c.OK)(e.data)}}t.ZodNull=L,L.create=e=>new L({typeName:n.ZodNull,...p(e)});class F extends h{constructor(){super(...arguments),this._any=!0}_parse(e){return(0,c.OK)(e.data)}}t.ZodAny=F,F.create=e=>new F({typeName:n.ZodAny,...p(e)});class M extends h{constructor(){super(...arguments),this._unknown=!0}_parse(e){return(0,c.OK)(e.data)}}t.ZodUnknown=M,M.create=e=>new M({typeName:n.ZodUnknown,...p(e)});class B extends h{_parse(e){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.never,received:t.parsedType}),c.INVALID}}t.ZodNever=B,B.create=e=>new B({typeName:n.ZodNever,...p(e)});class V extends h{_parse(e){if(this._getType(e)!==l.ZodParsedType.undefined){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.void,received:t.parsedType}),c.INVALID}return(0,c.OK)(e.data)}}t.ZodVoid=V,V.create=e=>new V({typeName:n.ZodVoid,...p(e)});class K extends h{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),n=this._def;if(t.parsedType!==l.ZodParsedType.array)return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.array,received:t.parsedType}),c.INVALID;if(null!==n.exactLength){let e=t.data.length>n.exactLength.value,a=t.data.length<n.exactLength.value;(e||a)&&((0,c.addIssueToContext)(t,{code:e?i.ZodIssueCode.too_big:i.ZodIssueCode.too_small,minimum:a?n.exactLength.value:void 0,maximum:e?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),r.dirty())}if(null!==n.minLength&&t.data.length<n.minLength.value&&((0,c.addIssueToContext)(t,{code:i.ZodIssueCode.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),r.dirty()),null!==n.maxLength&&t.data.length>n.maxLength.value&&((0,c.addIssueToContext)(t,{code:i.ZodIssueCode.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>n.type._parseAsync(new u(t,e,t.path,r)))).then(e=>c.ParseStatus.mergeArray(r,e));let a=[...t.data].map((e,r)=>n.type._parseSync(new u(t,e,t.path,r)));return c.ParseStatus.mergeArray(r,a)}get element(){return this._def.type}min(e,t){return new K({...this._def,minLength:{value:e,message:o.errorUtil.toString(t)}})}max(e,t){return new K({...this._def,maxLength:{value:e,message:o.errorUtil.toString(t)}})}length(e,t){return new K({...this._def,exactLength:{value:e,message:o.errorUtil.toString(t)}})}nonempty(e){return this.min(1,e)}}t.ZodArray=K,K.create=(e,t)=>new K({type:e,minLength:null,maxLength:null,exactLength:null,typeName:n.ZodArray,...p(t)});class $ extends h{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=l.util.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==l.ZodParsedType.object){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.object,received:t.parsedType}),c.INVALID}let{status:t,ctx:r}=this._processInputParams(e),{shape:n,keys:a}=this._getCached(),s=[];if(!(this._def.catchall instanceof B&&"strip"===this._def.unknownKeys))for(let e in r.data)a.includes(e)||s.push(e);let o=[];for(let e of a){let t=n[e],a=r.data[e];o.push({key:{status:"valid",value:e},value:t._parse(new u(r,a,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof B){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of s)o.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)s.length>0&&((0,c.addIssueToContext)(r,{code:i.ZodIssueCode.unrecognized_keys,keys:s}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of s){let n=r.data[t];o.push({key:{status:"valid",value:t},value:e._parse(new u(r,n,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of o){let r=await t.key,n=await t.value;e.push({key:r,value:n,alwaysSet:t.alwaysSet})}return e}).then(e=>c.ParseStatus.mergeObjectSync(t,e)):c.ParseStatus.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return o.errorUtil.errToObj,new $({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{let n=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:o.errorUtil.errToObj(e).message??n}:{message:n}}}:{}})}strip(){return new $({...this._def,unknownKeys:"strip"})}passthrough(){return new $({...this._def,unknownKeys:"passthrough"})}extend(e){return new $({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new $({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:n.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new $({...this._def,catchall:e})}pick(e){let t={};for(let r of l.util.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new $({...this._def,shape:()=>t})}omit(e){let t={};for(let r of l.util.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new $({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof $){let r={};for(let n in t.shape){let a=t.shape[n];r[n]=ec.create(e(a))}return new $({...t._def,shape:()=>r})}if(t instanceof K)return new K({...t._def,type:e(t.element)});if(t instanceof ec)return ec.create(e(t.unwrap()));if(t instanceof el)return el.create(e(t.unwrap()));if(t instanceof Y)return Y.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let r of l.util.objectKeys(this.shape)){let n=this.shape[r];e&&!e[r]?t[r]=n:t[r]=n.optional()}return new $({...this._def,shape:()=>t})}required(e){let t={};for(let r of l.util.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof ec;)e=e._def.innerType;t[r]=e}return new $({...this._def,shape:()=>t})}keyof(){return en(l.util.objectKeys(this.shape))}}t.ZodObject=$,$.create=(e,t)=>new $({shape:()=>e,unknownKeys:"strip",catchall:B.create(),typeName:n.ZodObject,...p(t)}),$.strictCreate=(e,t)=>new $({shape:()=>e,unknownKeys:"strict",catchall:B.create(),typeName:n.ZodObject,...p(t)}),$.lazycreate=(e,t)=>new $({shape:e,unknownKeys:"strip",catchall:B.create(),typeName:n.ZodObject,...p(t)});class q extends h{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new i.ZodError(e.ctx.common.issues));return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_union,unionErrors:r}),c.INVALID});{let e,n=[];for(let a of r){let r={...t,common:{...t.common,issues:[]},parent:null},i=a._parseSync({data:t.data,path:t.path,parent:r});if("valid"===i.status)return i;"dirty"!==i.status||e||(e={result:i,ctx:r}),r.common.issues.length&&n.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let a=n.map(e=>new i.ZodError(e));return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_union,unionErrors:a}),c.INVALID}}get options(){return this._def.options}}t.ZodUnion=q,q.create=(e,t)=>new q({options:e,typeName:n.ZodUnion,...p(t)});let W=e=>{if(e instanceof et)return W(e.schema);if(e instanceof eo)return W(e.innerType());if(e instanceof er)return[e.value];if(e instanceof ea)return e.options;if(e instanceof ei)return l.util.objectValues(e.enum);else if(e instanceof eu)return W(e._def.innerType);else if(e instanceof R)return[void 0];else if(e instanceof L)return[null];else if(e instanceof ec)return[void 0,...W(e.unwrap())];else if(e instanceof el)return[null,...W(e.unwrap())];else if(e instanceof eh)return W(e.unwrap());else if(e instanceof em)return W(e.unwrap());else if(e instanceof ed)return W(e._def.innerType);else return[]};class H extends h{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==l.ZodParsedType.object)return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.object,received:t.parsedType}),c.INVALID;let r=this.discriminator,n=t.data[r],a=this.optionsMap.get(n);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):((0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),c.INVALID)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let a=new Map;for(let r of t){let t=W(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let n of t){if(a.has(n))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(n)}`);a.set(n,r)}}return new H({typeName:n.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...p(r)})}}t.ZodDiscriminatedUnion=H;class J extends h{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=(e,n)=>{if((0,c.isAborted)(e)||(0,c.isAborted)(n))return c.INVALID;let a=function e(t,r){let n=(0,l.getParsedType)(t),a=(0,l.getParsedType)(r);if(t===r)return{valid:!0,data:t};if(n===l.ZodParsedType.object&&a===l.ZodParsedType.object){let n=l.util.objectKeys(r),a=l.util.objectKeys(t).filter(e=>-1!==n.indexOf(e)),i={...t,...r};for(let n of a){let a=e(t[n],r[n]);if(!a.valid)return{valid:!1};i[n]=a.data}return{valid:!0,data:i}}if(n===l.ZodParsedType.array&&a===l.ZodParsedType.array){if(t.length!==r.length)return{valid:!1};let n=[];for(let a=0;a<t.length;a++){let i=e(t[a],r[a]);if(!i.valid)return{valid:!1};n.push(i.data)}return{valid:!0,data:n}}if(n===l.ZodParsedType.date&&a===l.ZodParsedType.date&&+t==+r)return{valid:!0,data:t};return{valid:!1}}(e.value,n.value);return a.valid?(((0,c.isDirty)(e)||(0,c.isDirty)(n))&&t.dirty(),{status:t.value,value:a.data}):((0,c.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_intersection_types}),c.INVALID)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>n(e,t)):n(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}t.ZodIntersection=J,J.create=(e,t,r)=>new J({left:e,right:t,typeName:n.ZodIntersection,...p(r)});class Y extends h{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==l.ZodParsedType.array)return(0,c.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.array,received:r.parsedType}),c.INVALID;if(r.data.length<this._def.items.length)return(0,c.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),c.INVALID;!this._def.rest&&r.data.length>this._def.items.length&&((0,c.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let n=[...r.data].map((e,t)=>{let n=this._def.items[t]||this._def.rest;return n?n._parse(new u(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(n).then(e=>c.ParseStatus.mergeArray(t,e)):c.ParseStatus.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new Y({...this._def,rest:e})}}t.ZodTuple=Y,Y.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new Y({items:e,typeName:n.ZodTuple,rest:null,...p(t)})};class G extends h{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==l.ZodParsedType.object)return(0,c.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.object,received:r.parsedType}),c.INVALID;let n=[],a=this._def.keyType,s=this._def.valueType;for(let e in r.data)n.push({key:a._parse(new u(r,e,r.path,e)),value:s._parse(new u(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?c.ParseStatus.mergeObjectAsync(t,n):c.ParseStatus.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,r){return new G(t instanceof h?{keyType:e,valueType:t,typeName:n.ZodRecord,...p(r)}:{keyType:E.create(),valueType:e,typeName:n.ZodRecord,...p(t)})}}t.ZodRecord=G;class Q extends h{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==l.ZodParsedType.map)return(0,c.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.map,received:r.parsedType}),c.INVALID;let n=this._def.keyType,a=this._def.valueType,s=[...r.data.entries()].map(([e,t],i)=>({key:n._parse(new u(r,e,r.path,[i,"key"])),value:a._parse(new u(r,t,r.path,[i,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of s){let n=await r.key,a=await r.value;if("aborted"===n.status||"aborted"===a.status)return c.INVALID;("dirty"===n.status||"dirty"===a.status)&&t.dirty(),e.set(n.value,a.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of s){let n=r.key,a=r.value;if("aborted"===n.status||"aborted"===a.status)return c.INVALID;("dirty"===n.status||"dirty"===a.status)&&t.dirty(),e.set(n.value,a.value)}return{status:t.value,value:e}}}}t.ZodMap=Q,Q.create=(e,t,r)=>new Q({valueType:t,keyType:e,typeName:n.ZodMap,...p(r)});class X extends h{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==l.ZodParsedType.set)return(0,c.addIssueToContext)(r,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.set,received:r.parsedType}),c.INVALID;let n=this._def;null!==n.minSize&&r.data.size<n.minSize.value&&((0,c.addIssueToContext)(r,{code:i.ZodIssueCode.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),null!==n.maxSize&&r.data.size>n.maxSize.value&&((0,c.addIssueToContext)(r,{code:i.ZodIssueCode.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());let a=this._def.valueType;function s(e){let r=new Set;for(let n of e){if("aborted"===n.status)return c.INVALID;"dirty"===n.status&&t.dirty(),r.add(n.value)}return{status:t.value,value:r}}let o=[...r.data.values()].map((e,t)=>a._parse(new u(r,e,r.path,t)));return r.common.async?Promise.all(o).then(e=>s(e)):s(o)}min(e,t){return new X({...this._def,minSize:{value:e,message:o.errorUtil.toString(t)}})}max(e,t){return new X({...this._def,maxSize:{value:e,message:o.errorUtil.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}t.ZodSet=X,X.create=(e,t)=>new X({valueType:e,minSize:null,maxSize:null,typeName:n.ZodSet,...p(t)});class ee extends h{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==l.ZodParsedType.function)return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.function,received:t.parsedType}),c.INVALID;function r(e,r){return(0,c.makeIssue)({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,(0,s.getErrorMap)(),s.defaultErrorMap].filter(e=>!!e),issueData:{code:i.ZodIssueCode.invalid_arguments,argumentsError:r}})}function n(e,r){return(0,c.makeIssue)({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,(0,s.getErrorMap)(),s.defaultErrorMap].filter(e=>!!e),issueData:{code:i.ZodIssueCode.invalid_return_type,returnTypeError:r}})}let a={errorMap:t.common.contextualErrorMap},o=t.data;if(this._def.returns instanceof es){let e=this;return(0,c.OK)(async function(...t){let s=new i.ZodError([]),c=await e._def.args.parseAsync(t,a).catch(e=>{throw s.addIssue(r(t,e)),s}),l=await Reflect.apply(o,this,c);return await e._def.returns._def.type.parseAsync(l,a).catch(e=>{throw s.addIssue(n(l,e)),s})})}{let e=this;return(0,c.OK)(function(...t){let s=e._def.args.safeParse(t,a);if(!s.success)throw new i.ZodError([r(t,s.error)]);let c=Reflect.apply(o,this,s.data),l=e._def.returns.safeParse(c,a);if(!l.success)throw new i.ZodError([n(c,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ee({...this._def,args:Y.create(e).rest(M.create())})}returns(e){return new ee({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new ee({args:e||Y.create([]).rest(M.create()),returns:t||M.create(),typeName:n.ZodFunction,...p(r)})}}t.ZodFunction=ee;class et extends h{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}t.ZodLazy=et,et.create=(e,t)=>new et({getter:e,typeName:n.ZodLazy,...p(t)});class er extends h{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{received:t.data,code:i.ZodIssueCode.invalid_literal,expected:this._def.value}),c.INVALID}return{status:"valid",value:e.data}}get value(){return this._def.value}}function en(e,t){return new ea({values:e,typeName:n.ZodEnum,...p(t)})}t.ZodLiteral=er,er.create=(e,t)=>new er({value:e,typeName:n.ZodLiteral,...p(t)});class ea extends h{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return(0,c.addIssueToContext)(t,{expected:l.util.joinValues(r),received:t.parsedType,code:i.ZodIssueCode.invalid_type}),c.INVALID}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return(0,c.addIssueToContext)(t,{received:t.data,code:i.ZodIssueCode.invalid_enum_value,options:r}),c.INVALID}return(0,c.OK)(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return ea.create(e,{...this._def,...t})}exclude(e,t=this._def){return ea.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}t.ZodEnum=ea,ea.create=en;class ei extends h{_parse(e){let t=l.util.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==l.ZodParsedType.string&&r.parsedType!==l.ZodParsedType.number){let e=l.util.objectValues(t);return(0,c.addIssueToContext)(r,{expected:l.util.joinValues(e),received:r.parsedType,code:i.ZodIssueCode.invalid_type}),c.INVALID}if(this._cache||(this._cache=new Set(l.util.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=l.util.objectValues(t);return(0,c.addIssueToContext)(r,{received:r.data,code:i.ZodIssueCode.invalid_enum_value,options:e}),c.INVALID}return(0,c.OK)(e.data)}get enum(){return this._def.values}}t.ZodNativeEnum=ei,ei.create=(e,t)=>new ei({values:e,typeName:n.ZodNativeEnum,...p(t)});class es extends h{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==l.ZodParsedType.promise&&!1===t.common.async)return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.promise,received:t.parsedType}),c.INVALID;let r=t.parsedType===l.ZodParsedType.promise?t.data:Promise.resolve(t.data);return(0,c.OK)(r.then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}t.ZodPromise=es,es.create=(e,t)=>new es({type:e,typeName:n.ZodPromise,...p(t)});class eo extends h{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===n.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=this._def.effect||null,a={addIssue:e=>{(0,c.addIssueToContext)(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(a.addIssue=a.addIssue.bind(a),"preprocess"===n.type){let e=n.transform(r.data,a);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return c.INVALID;let n=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===n.status?c.INVALID:"dirty"===n.status||"dirty"===t.value?(0,c.DIRTY)(n.value):n});{if("aborted"===t.value)return c.INVALID;let n=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===n.status?c.INVALID:"dirty"===n.status||"dirty"===t.value?(0,c.DIRTY)(n.value):n}}if("refinement"===n.type){let e=e=>{let t=n.refinement(e,a);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?c.INVALID:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let n=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===n.status?c.INVALID:("dirty"===n.status&&t.dirty(),e(n.value),{status:t.value,value:n.value})}}if("transform"===n.type)if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>(0,c.isValid)(e)?Promise.resolve(n.transform(e.value,a)).then(e=>({status:t.value,value:e})):c.INVALID);else{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!(0,c.isValid)(e))return c.INVALID;let i=n.transform(e.value,a);if(i instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:i}}l.util.assertNever(n)}}t.ZodEffects=eo,t.ZodTransformer=eo,eo.create=(e,t,r)=>new eo({schema:e,typeName:n.ZodEffects,effect:t,...p(r)}),eo.createWithPreprocess=(e,t,r)=>new eo({schema:t,effect:{type:"preprocess",transform:e},typeName:n.ZodEffects,...p(r)});class ec extends h{_parse(e){return this._getType(e)===l.ZodParsedType.undefined?(0,c.OK)(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}t.ZodOptional=ec,ec.create=(e,t)=>new ec({innerType:e,typeName:n.ZodOptional,...p(t)});class el extends h{_parse(e){return this._getType(e)===l.ZodParsedType.null?(0,c.OK)(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}t.ZodNullable=el,el.create=(e,t)=>new el({innerType:e,typeName:n.ZodNullable,...p(t)});class eu extends h{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===l.ZodParsedType.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}t.ZodDefault=eu,eu.create=(e,t)=>new eu({innerType:e,typeName:n.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...p(t)});class ed extends h{_parse(e){let{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return(0,c.isAsync)(n)?n.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new i.ZodError(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===n.status?n.value:this._def.catchValue({get error(){return new i.ZodError(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}t.ZodCatch=ed,ed.create=(e,t)=>new ed({innerType:e,typeName:n.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...p(t)});class ep extends h{_parse(e){if(this._getType(e)!==l.ZodParsedType.nan){let t=this._getOrReturnCtx(e);return(0,c.addIssueToContext)(t,{code:i.ZodIssueCode.invalid_type,expected:l.ZodParsedType.nan,received:t.parsedType}),c.INVALID}return{status:"valid",value:e.data}}}t.ZodNaN=ep,ep.create=e=>new ep({typeName:n.ZodNaN,...p(e)}),t.BRAND=Symbol("zod_brand");class eh extends h{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}t.ZodBranded=eh;class ef extends h{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?c.INVALID:"dirty"===e.status?(t.dirty(),(0,c.DIRTY)(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?c.INVALID:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new ef({in:e,out:t,typeName:n.ZodPipeline})}}t.ZodPipeline=ef;class em extends h{_parse(e){let t=this._def.innerType._parse(e),r=e=>((0,c.isValid)(e)&&(e.value=Object.freeze(e.value)),e);return(0,c.isAsync)(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}function ey(e,t){let r="function"==typeof e?e(t):"string"==typeof e?{message:e}:e;return"string"==typeof r?{message:r}:r}function eg(e,t={},r){return e?F.create().superRefine((n,a)=>{let i=e(n);if(i instanceof Promise)return i.then(e=>{if(!e){let e=ey(t,n),i=e.fatal??r??!0;a.addIssue({code:"custom",...e,fatal:i})}});if(!i){let e=ey(t,n),i=e.fatal??r??!0;a.addIssue({code:"custom",...e,fatal:i})}}):F.create()}t.ZodReadonly=em,em.create=(e,t)=>new em({innerType:e,typeName:n.ZodReadonly,...p(t)}),t.late={object:$.lazycreate},function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(n||(t.ZodFirstPartyTypeKind=n={})),t.instanceof=(e,t={message:`Input not instance of ${e.name}`})=>eg(t=>t instanceof e,t);let e_=E.create;t.string=e_;let ev=Z.create;t.number=ev,t.nan=ep.create,t.bigint=S.create;let eb=N.create;t.boolean=eb,t.date=U.create,t.symbol=D.create,t.undefined=R.create,t.null=L.create,t.any=F.create,t.unknown=M.create,t.never=B.create,t.void=V.create,t.array=K.create,t.object=$.create,t.strictObject=$.strictCreate,t.union=q.create,t.discriminatedUnion=H.create,t.intersection=J.create,t.tuple=Y.create,t.record=G.create,t.map=Q.create,t.set=X.create,t.function=ee.create,t.lazy=et.create,t.literal=er.create,t.enum=ea.create,t.nativeEnum=ei.create,t.promise=es.create;let ew=eo.create;t.effect=ew,t.transformer=ew,t.optional=ec.create,t.nullable=el.create,t.preprocess=eo.createWithPreprocess,t.pipeline=ef.create,t.ostring=()=>e_().optional(),t.onumber=()=>ev().optional(),t.oboolean=()=>eb().optional(),t.coerce={string:e=>E.create({...e,coerce:!0}),number:e=>Z.create({...e,coerce:!0}),boolean:e=>N.create({...e,coerce:!0}),bigint:e=>S.create({...e,coerce:!0}),date:e=>U.create({...e,coerce:!0})},t.NEVER=c.INVALID},75028:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useEntity:()=>i});var n=r(20323),a=r(14776);let i=(e,t)=>(0,n.useEntityBase)({AutumnContext:a.AutumnContext,entityId:e,params:t})},75041:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},75299:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return n.callServer},createServerReference:function(){return i},findSourceMapURL:function(){return a.findSourceMapURL}});let n=r(79328),a=r(30260),i=r(85015).createServerReference},82071:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.errorUtil=void 0,function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(r||(t.errorUtil=r={}))},83809:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Chalk:()=>x,backgroundColorNames:()=>l,backgroundColors:()=>l,chalkStderr:()=>j,colorNames:()=>u,colors:()=>u,default:()=>E,foregroundColorNames:()=>c,foregroundColors:()=>c,modifierNames:()=>o,modifiers:()=>o,supportsColor:()=>f,supportsColorStderr:()=>m});let n=(e=0)=>t=>`\u001B[${t+e}m`,a=(e=0)=>t=>`\u001B[${38+e};5;${t}m`,i=(e=0)=>(t,r,n)=>`\u001B[${38+e};2;${t};${r};${n}m`,s={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}},o=Object.keys(s.modifier),c=Object.keys(s.color),l=Object.keys(s.bgColor),u=[...c,...l],d=function(){let e=new Map;for(let[t,r]of Object.entries(s)){for(let[t,n]of Object.entries(r))s[t]={open:`\u001B[${n[0]}m`,close:`\u001B[${n[1]}m`},r[t]=s[t],e.set(n[0],n[1]);Object.defineProperty(s,t,{value:r,enumerable:!1})}return Object.defineProperty(s,"codes",{value:e,enumerable:!1}),s.color.close="\x1b[39m",s.bgColor.close="\x1b[49m",s.color.ansi=n(),s.color.ansi256=a(),s.color.ansi16m=i(),s.bgColor.ansi=n(10),s.bgColor.ansi256=a(10),s.bgColor.ansi16m=i(10),Object.defineProperties(s,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value(e){let t=/[a-f\d]{6}|[a-f\d]{3}/i.exec(e.toString(16));if(!t)return[0,0,0];let[r]=t;3===r.length&&(r=[...r].map(e=>e+e).join(""));let n=Number.parseInt(r,16);return[n>>16&255,n>>8&255,255&n]},enumerable:!1},hexToAnsi256:{value:e=>s.rgbToAnsi256(...s.hexToRgb(e)),enumerable:!1},ansi256ToAnsi:{value(e){let t,r,n;if(e<8)return 30+e;if(e<16)return 90+(e-8);if(e>=232)r=t=((e-232)*10+8)/255,n=t;else{let a=(e-=16)%36;t=Math.floor(e/36)/5,r=Math.floor(a/6)/5,n=a%6/5}let a=2*Math.max(t,r,n);if(0===a)return 30;let i=30+(Math.round(n)<<2|Math.round(r)<<1|Math.round(t));return 2===a&&(i+=60),i},enumerable:!1},rgbToAnsi:{value:(e,t,r)=>s.ansi256ToAnsi(s.rgbToAnsi256(e,t,r)),enumerable:!1},hexToAnsi:{value:e=>s.ansi256ToAnsi(s.hexToAnsi256(e)),enumerable:!1}}),s}(),p=(()=>{if(!("navigator"in globalThis))return 0;if(globalThis.navigator.userAgentData){let e=navigator.userAgentData.brands.find(({brand:e})=>"Chromium"===e);if(e&&e.version>93)return 3}return+!!/\b(Chrome|Chromium)\//.test(globalThis.navigator.userAgent)})(),h=0!==p&&{level:p,hasBasic:!0,has256:p>=2,has16m:p>=3},{stdout:f,stderr:m}={stdout:h,stderr:h},y=Symbol("GENERATOR"),g=Symbol("STYLER"),_=Symbol("IS_EMPTY"),v=["ansi","ansi","ansi256","ansi16m"],b=Object.create(null),w=(e,t={})=>{if(t.level&&!(Number.isInteger(t.level)&&t.level>=0&&t.level<=3))throw Error("The `level` option should be an integer from 0 to 3");let r=f?f.level:0;e.level=void 0===t.level?r:t.level};class x{constructor(e){return C(e)}}let C=e=>{let t=(...e)=>e.join(" ");return w(t,e),Object.setPrototypeOf(t,z.prototype),t};function z(e){return C(e)}for(let[e,t]of(Object.setPrototypeOf(z.prototype,Function.prototype),Object.entries(d)))b[e]={get(){let r=O(this,I(t.open,t.close,this[g]),this[_]);return Object.defineProperty(this,e,{value:r}),r}};b.visible={get(){let e=O(this,this[g],!0);return Object.defineProperty(this,"visible",{value:e}),e}};let T=(e,t,r,...n)=>"rgb"===e?"ansi16m"===t?d[r].ansi16m(...n):"ansi256"===t?d[r].ansi256(d.rgbToAnsi256(...n)):d[r].ansi(d.rgbToAnsi(...n)):"hex"===e?T("rgb",t,r,...d.hexToRgb(...n)):d[r][e](...n);for(let e of["rgb","hex","ansi256"])b[e]={get(){let{level:t}=this;return function(...r){return O(this,I(T(e,v[t],"color",...r),d.color.close,this[g]),this[_])}}},b["bg"+e[0].toUpperCase()+e.slice(1)]={get(){let{level:t}=this;return function(...r){return O(this,I(T(e,v[t],"bgColor",...r),d.bgColor.close,this[g]),this[_])}}};let k=Object.defineProperties(()=>{},{...b,level:{enumerable:!0,get(){return this[y].level},set(e){this[y].level=e}}}),I=(e,t,r)=>{let n,a;return void 0===r?(n=e,a=t):(n=r.openAll+e,a=t+r.closeAll),{open:e,close:t,openAll:n,closeAll:a,parent:r}},O=(e,t,r)=>{let n=(...e)=>A(n,1===e.length?""+e[0]:e.join(" "));return Object.setPrototypeOf(n,k),n[y]=e,n[g]=t,n[_]=r,n},A=(e,t)=>{if(e.level<=0||!t)return e[_]?"":t;let r=e[g];if(void 0===r)return t;let{openAll:n,closeAll:a}=r;if(t.includes("\x1b"))for(;void 0!==r;)t=function(e,t,r){let n=e.indexOf(t);if(-1===n)return e;let a=t.length,i=0,s="";do s+=e.slice(i,n)+t+r,i=n+a,n=e.indexOf(t,i);while(-1!==n);return s+e.slice(i)}(t,r.close,r.open),r=r.parent;let i=t.indexOf("\n");return -1!==i&&(t=function(e,t,r,n){let a=0,i="";do{let s="\r"===e[n-1];i+=e.slice(a,s?n-1:n)+t+(s?"\r\n":"\n")+r,a=n+1,n=e.indexOf("\n",a)}while(-1!==n);return i+e.slice(a)}(t,a,n,i)),n+t+a};Object.defineProperties(z.prototype,b);let P=C(void 0),j=C({level:m?m.level:0}),E=P},85600:()=>{},91286:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let n=r(10360),a=r(7597);t.default=(e,t)=>{let r;switch(e.code){case n.ZodIssueCode.invalid_type:r=e.received===a.ZodParsedType.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case n.ZodIssueCode.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,a.util.jsonStringifyReplacer)}`;break;case n.ZodIssueCode.unrecognized_keys:r=`Unrecognized key(s) in object: ${a.util.joinValues(e.keys,", ")}`;break;case n.ZodIssueCode.invalid_union:r="Invalid input";break;case n.ZodIssueCode.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${a.util.joinValues(e.options)}`;break;case n.ZodIssueCode.invalid_enum_value:r=`Invalid enum value. Expected ${a.util.joinValues(e.options)}, received '${e.received}'`;break;case n.ZodIssueCode.invalid_arguments:r="Invalid function arguments";break;case n.ZodIssueCode.invalid_return_type:r="Invalid function return type";break;case n.ZodIssueCode.invalid_date:r="Invalid date";break;case n.ZodIssueCode.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:a.util.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case n.ZodIssueCode.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type||"bigint"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case n.ZodIssueCode.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case n.ZodIssueCode.custom:r="Invalid input";break;case n.ZodIssueCode.invalid_intersection_types:r="Intersection results could not be merged";break;case n.ZodIssueCode.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case n.ZodIssueCode.not_finite:r="Number must be finite";break;default:r=t.defaultError,a.util.assertNever(e)}return{message:r}}},92345:(e,t,r)=>{"use strict";var n,a=r(37811),i=Object.create,s=Object.defineProperty,o=Object.getOwnPropertyDescriptor,c=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,d=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of c(t))u.call(e,a)||a===r||s(e,a,{get:()=>t[a],enumerable:!(n=o(t,a))||n.enumerable});return e},p=(e,t,r)=>(r=null!=e?i(l(e)):{},d(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)),h={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(h,{BaseAutumnProvider:()=>eR}),e.exports=d(s({},"__esModule",{value:!0}),h);var f=r(12359),m=r(12359),y=e=>{let[t,r]=(0,m.useState)(null),[n,a]=(0,m.useState)(!1);return(0,m.useEffect)(()=>{n||setTimeout(()=>{r(null)},200)},[n]),[t,r,n,a]},g=p(r(59153)),_=async e=>{if(401===e.status){let t=e.clone();if((await t.json()).message.includes("Missing authorization header"))return console.error("[Autumn] Missing authorization header.\n\nUse the getBearerToken prop in <AutumnProvider /> to set the authorization header.\nhttps://docs.useautumn.com/quickstart/quickstart#5-set-up-autumnprovider"),!0}return!1},v=class e extends Error{static fromError(t){return new e({message:t.message||"Unknown error",code:t.code||"unknown_error"})}toString(){return"".concat(this.message," (code: ").concat(this.code,")")}toJSON(){return{message:this.message,code:this.code}}constructor(e){super(e.message),this.message=e.message,this.code=e.code}},b=async e=>{let{instance:t,params:r}=e;return t.post("/attach",r)},w=async e=>{let{instance:t,params:r}=e;return t.post("/setup_payment",r)},x=async e=>{let{instance:t,params:r}=e;return t.post("/cancel",r)},C=async e=>{let{instance:t,params:r}=e;return t.post("/entitled",r)},z=async e=>{let{instance:t,params:r}=e;return t.post("/events",r)},T=async e=>{let{instance:t,params:r}=e;return t.post("/track",r)},k=async e=>{let{instance:t,params:r}=e;return t.post("/usage",r)},I=async e=>{let{instance:t,params:r}=e;return t.post("/check",r)},O=(e,t,r)=>(t||(t=new ee),e({instance:t,...r})),A=e=>({get:(t,r)=>O(j,e,{id:t,params:r}),create:t=>O(E,e,{params:t}),update:(t,r)=>O(Z,e,{id:t,params:r}),delete:t=>O(S,e,{id:t}),billingPortal:(t,r)=>O(N,e,{id:t,params:r})}),P=e=>e?"expand=".concat(e.join(",")):"",j=async e=>{let{instance:t,id:r,params:n}=e;return r?t.get("/customers/".concat(r,"?").concat(P(null==n?void 0:n.expand))):{data:null,error:new v({message:"Customer ID is required",code:"CUSTOMER_ID_REQUIRED"})}},E=async e=>{let{instance:t,params:r}=e;return t.post("/customers?".concat(P(null==r?void 0:r.expand)),r)},Z=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r),n)},S=async e=>{let{instance:t,id:r}=e;return t.delete("/customers/".concat(r))},N=async e=>{let{instance:t,id:r,params:n}=e;return t.post("/customers/".concat(r,"/billing_portal"),n)},U=e=>({get:(t,r,n)=>O(R,e,{customer_id:t,entity_id:r,params:n}),create:(t,r)=>O(L,e,{customer_id:t,params:r}),delete:(t,r)=>O(F,e,{customer_id:t,entity_id:r})}),D=e=>e?"expand=".concat(e.join(",")):"",R=async e=>{let{instance:t,customer_id:r,entity_id:n,params:a}=e;return t.get("/customers/".concat(r,"/entities/").concat(n,"?").concat(D(null==a?void 0:a.expand)))},L=async e=>{let{instance:t,customer_id:r,params:n}=e;return t.post("/customers/".concat(r,"/entities"),n)},F=async e=>{let{instance:t,customer_id:r,entity_id:n}=e;return t.delete("/customers/".concat(r,"/entities/").concat(n))},M=e=>({get:t=>O(V,e,{id:t}),create:t=>O(K,e,{params:t}),list:t=>O(B,e,{params:t})}),B=async e=>{let{instance:t,params:r}=e,n="/products_beta";if(r){let e=new URLSearchParams;for(let[t,n]of Object.entries(r))void 0!==n&&e.append(t,String(n));let t=e.toString();t&&(n+="?".concat(t))}return t.get(n)},V=async e=>{let{instance:t,id:r}=e;return t.get("/products/".concat(r))},K=async e=>{let{instance:t,params:r}=e;return t.post("/products",r)},$=e=>({createCode:t=>O(q,e,{params:t}),redeemCode:t=>O(W,e,{params:t})}),q=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/code",r)},W=async e=>{let{instance:t,params:r}=e;return t.post("/referrals/redeem",r)},H=async e=>{let{response:t,logger:r,logError:n=!0}=e;if(t.status<200||t.status>=300){let e;try{e=await t.json(),n&&r.error("[Autumn] ".concat(e.message))}catch(e){throw e}return{data:null,error:new v({message:e.message,code:e.code}),statusCode:t.status}}try{return{data:await t.json(),error:null,statusCode:null==t?void 0:t.status}}catch(e){throw e}},J=p(r(83809)),Y=()=>{let e=new Date().toISOString();return"[".concat(e.split("T")[1].split(".")[0],"]")},G=e=>Q.indexOf(e)>=Q.indexOf(X.level),Q=["debug","info","warn","error","fatal"],X={...console,level:"info",debug:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];G("debug")&&console.log(Y(),J.default.gray("DEBUG"),...t)},log:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];console.log(Y(),J.default.blue("INFO"),...t)},info:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];G("info")&&console.log(Y(),J.default.blue("INFO"),...t)},warn:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];G("warn")&&console.log(Y(),J.default.yellow("WARN"),...t)},error:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];G("error")&&console.log(Y(),J.default.red("ERROR"),...t)}},ee=((n=class{async get(e){return H({response:await fetch("".concat(this.url).concat(e),{headers:this.headers}),logger:this.logger})}async post(e,t){try{let r=await fetch("".concat(this.url).concat(e),{method:"POST",headers:this.headers,body:JSON.stringify(t)});return H({response:r,logger:this.logger})}catch(e){throw console.error("Error sending request:",e),e}}async delete(e){return H({response:await fetch("".concat(this.url).concat(e),{method:"DELETE",headers:this.headers}),logger:this.logger})}async attach(e){return b({instance:this,params:e})}async setupPayment(e){return w({instance:this,params:e})}async cancel(e){return x({instance:this,params:e})}async entitled(e){return C({instance:this,params:e})}async check(e){return I({instance:this,params:e})}async event(e){return z({instance:this,params:e})}async track(e){return T({instance:this,params:e})}async usage(e){return k({instance:this,params:e})}constructor(e){this.logger=console,this.customers=A(this),this.products=M(this),this.entities=U(this),this.referrals=$(this);try{this.secretKey=(null==e?void 0:e.secretKey)||a.env.AUTUMN_SECRET_KEY,this.publishableKey=(null==e?void 0:e.publishableKey)||a.env.AUTUMN_PUBLISHABLE_KEY}catch(e){}if(!this.secretKey&&!this.publishableKey&&!(null==e?void 0:e.headers))throw Error("Autumn secret key or publishable key is required");this.headers=(null==e?void 0:e.headers)||{Authorization:"Bearer ".concat(this.secretKey||this.publishableKey),"Content-Type":"application/json"};let t=(null==e?void 0:e.version)||"1.2";this.headers["x-api-version"]=t,this.url=(null==e?void 0:e.url)||"https://api.useautumn.com/v1",this.logger=X,this.logger.level=(null==e?void 0:e.logLevel)||"info"}}).customers=A(),n.products=M(),n.entities=U(),n.referrals=$(),n.attach=e=>O(b,void 0,{params:e}),n.usage=e=>O(k,void 0,{params:e}),n.setupPayment=e=>O(w,void 0,{params:e}),n.cancel=e=>O(x,void 0,{params:e}),n.entitled=e=>O(C,void 0,{params:e}),n.check=e=>O(I,void 0,{params:e}),n.event=e=>O(z,void 0,{params:e}),n.track=e=>O(T,void 0,{params:e}),n),et=r(28935),er=et.z.object({name:et.z.string().optional(),feature_id:et.z.string()}),en=r(28935);en.z.object({feature_id:en.z.string(),quantity:en.z.number()}),en.z.object({customer_id:en.z.string(),product_id:en.z.string().optional(),entity_id:en.z.string().optional(),options:en.z.array(en.z.object({feature_id:en.z.string(),quantity:en.z.number()})).optional(),product_ids:en.z.array(en.z.string()).optional(),free_trial:en.z.boolean().optional(),success_url:en.z.string().optional(),metadata:en.z.record(en.z.string()).optional(),force_checkout:en.z.boolean().optional(),customer_data:en.z.any().optional(),entity_data:en.z.any().optional(),checkout_session_params:en.z.record(en.z.any()).optional(),reward:en.z.string().optional()}),en.z.object({checkout_url:en.z.string().optional(),customer_id:en.z.string(),product_ids:en.z.array(en.z.string()),code:en.z.string(),message:en.z.string(),customer_data:en.z.any().optional()}),en.z.object({customer_id:en.z.string(),product_id:en.z.string(),entity_id:en.z.string().optional(),cancel_immediately:en.z.boolean().optional()}),en.z.object({success:en.z.boolean(),customer_id:en.z.string(),product_id:en.z.string()}),en.z.object({customer_id:en.z.string(),value:en.z.number().optional(),feature_id:en.z.string().optional(),event_name:en.z.string().optional(),entity_id:en.z.string().optional(),customer_data:en.z.any().optional(),idempotency_key:en.z.string().optional(),entity_data:en.z.any().optional()}),en.z.object({id:en.z.string(),code:en.z.string(),customer_id:en.z.string(),feature_id:en.z.string().optional(),event_name:en.z.string().optional()}),en.z.object({customer_id:en.z.string(),feature_id:en.z.string().optional(),product_id:en.z.string().optional(),entity_id:en.z.string().optional(),customer_data:en.z.any().optional(),required_balance:en.z.number().optional(),send_event:en.z.boolean().optional(),with_preview:en.z.boolean().optional(),entity_data:er.optional()});var ea=r(28935).z.enum(["invoices","rewards","trials_used","entities","referrals","payment_method"]),ei=r(28935);ei.z.object({id:ei.z.string().nullish(),email:ei.z.string().nullish(),name:ei.z.string().nullish(),fingerprint:ei.z.string().nullish(),metadata:ei.z.record(ei.z.any()).optional(),expand:ei.z.array(ea).optional()}),ei.z.object({return_url:ei.z.string().optional()});var es=r(28935);es.z.object({customer_id:es.z.string(),program_id:es.z.string()}),es.z.object({code:es.z.string(),customer_id:es.z.string()});var eo=e=>{let{method:t,backendUrl:r,path:n,error:a}=e;console.error("[Autumn] Fetch failed: ".concat(t," ").concat(r).concat(n,"\n\n1. Check that backendUrl in <AutumnProvider/> is correctly set.\n2. Check that autumnHandler is correctly registered on your backend."))};async function ec(){return await this.get("".concat(this.prefix,"/components/pricing_table"))}var el=async e=>{let{client:t,params:r}=e;return await t.post("".concat(t.prefix,"/customers"),r)},eu=(e,t)=>Array.isArray(e)?e.map(e=>eu(e,t)):null!==e&&"object"==typeof e?Object.fromEntries(Object.entries(e).map(e=>{let[r,n]=e,a=r.replace(/([a-z])([A-Z])/g,"$1_$2").replace(/[-\s]+/g,"_").toLowerCase();return t&&t.includes(r)?[a,n]:[a,eu(n,t)]})):e,ed=e=>e?"expand=".concat(e.join(",")):"";async function ep(e){let t=eu(e);return await this.post("".concat(this.prefix,"/entities"),t)}async function eh(e,t){eu(t);let r=ed(null==t?void 0:t.expand);return await this.get("".concat(this.prefix,"/entities/").concat(e,"?").concat(r))}async function ef(e){return await this.delete("".concat(this.prefix,"/entities/").concat(e))}async function em(e){let{dialog:t,...r}=e,n=eu(r,["checkoutSessionparams"]);return await this.post("".concat(this.prefix,"/attach"),n)}async function ey(e){let t=eu(e,["checkoutSessionParams"]);return await this.post("".concat(this.prefix,"/setup_payment"),t)}async function eg(e){let t=eu(e);return await this.post("".concat(this.prefix,"/cancel"),t)}async function e_(e){let{dialog:t,...r}=e,n=eu(r);return await this.post("".concat(this.prefix,"/check"),n)}async function ev(e){let t=eu(e);return await this.post("".concat(this.prefix,"/track"),t)}async function eb(e){let t=eu(e||{});return await this.post("".concat(this.prefix,"/billing_portal"),t)}async function ew(){return await this.get("".concat(this.prefix,"/products"))}async function ex(e){let t=eu(e);return await this.post("".concat(this.prefix,"/referrals/code"),t)}async function eC(e){let t=eu(e);return await this.post("".concat(this.prefix,"/referrals/redeem"),t)}var ez=class{async detectCors(){var e;if(null==(e=this.prefix)?void 0:e.includes("/api/auth"))return{valid:!0,includeCredentials:!0};let t="".concat(this.backendUrl,"/api/autumn/cors");try{return await fetch(t,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!0}}catch(e){try{return await fetch(t,{method:"POST",credentials:"omit",headers:{"Content-Type":"application/json"}}),{valid:!0,includeCredentials:!1}}catch(e){return{valid:!1,includeCredentials:void 0}}}}async shouldIncludeCredentials(){if(void 0!==this.includeCredentials)return this.includeCredentials;try{let e=await this.detectCors();return e.valid&&(console.warn("[Autumn] Detected CORS credentials: ".concat(e.includeCredentials)),console.warn("[Autumn] To disable this warning, you can set includeCredentials={".concat(e.includeCredentials?"true":"false","} in <AutumnProvider />")),this.includeCredentials=e.includeCredentials),e.includeCredentials||!1}catch(e){return console.error("[Autumn] Error detecting CORS: ".concat(e.message)),!1}}async getHeaders(){let e={"Content-Type":"application/json"};if(this.getBearerToken)try{let t=await this.getBearerToken();e.Authorization="Bearer ".concat(t)}catch(e){console.error("Failed to call getToken() in AutumnProvider")}return e}async handleFetch(e){let{path:t,method:r,body:n}=e;n="POST"===r?JSON.stringify({...n,customer_data:this.customerData||void 0}):void 0;let a=await this.shouldIncludeCredentials();try{let e=await fetch("".concat(this.backendUrl).concat(t),{method:r,body:n,headers:await this.getHeaders(),credentials:a?"include":"omit"}),i=await _(e);return await H({response:e,logger:console,logError:!i})}catch(e){return eo({method:r,backendUrl:this.backendUrl||"",path:t,error:e}),{data:null,error:new v({message:e.message,code:"fetch_failed"})}}}async post(e,t){return await this.handleFetch({path:e,method:"POST",body:t})}async get(e){return await this.handleFetch({path:e,method:"GET"})}async delete(e){return await this.handleFetch({path:e,method:"DELETE"})}async createCustomer(e){return await el({client:this,params:e})}async getPricingTable(){return await ec.bind(this)()}constructor({backendUrl:e,getBearerToken:t,customerData:r,includeCredentials:n,betterAuthUrl:a}){this.attach=em.bind(this),this.cancel=eg.bind(this),this.check=e_.bind(this),this.track=ev.bind(this),this.openBillingPortal=eb.bind(this),this.setupPayment=ey.bind(this),this.entities={create:ep.bind(this),get:eh.bind(this),delete:ef.bind(this)},this.referrals={createCode:ex.bind(this),redeemCode:eC.bind(this)},this.products={list:ew.bind(this)},this.backendUrl=e,this.getBearerToken=t,this.customerData=r,this.includeCredentials=n,this.prefix="/api/autumn",a&&(this.prefix="/api/auth/autumn",this.backendUrl=a)}},eT=r(12359);(0,eT.createContext)({initialized:!1,disableDialogs:!1,client:new ez({backendUrl:""}),paywallDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}},attachDialog:{props:null,setProps:()=>{},open:!1,setOpen:()=>{},setComponent:()=>{}}});var ek=e=>{let{AutumnContext:t,name:r,errorIfNotInitialized:n=!0}=e,a=(0,eT.useContext)(t);if(!a.initialized&&n)throw Error("".concat(r," must be used within <AutumnProvider />"));return a},eI=p(r(59153)),eO=(e,t)=>{if(!e)return null;if(!t)return e.map(t=>{if(t.base_variant_id){let r=e.find(e=>e.id===t.base_variant_id);if(r)return{...t,name:r.name}}return t});let r=structuredClone(e),n=[];for(let e of t){var a,i,s,o;if(!e.id){let t={},r=null==(i=e.items)?void 0:i.map(e=>({display:{primary_text:e.primaryText,secondary_text:e.secondaryText}})),a=e.price;e.price&&(t.is_free=!1,r=[{display:{primary_text:null==a?void 0:a.primaryText,secondary_text:null==a?void 0:a.secondaryText}},...r||[]]),r&&0!==r.length||(r=[{display:{primary_text:""}}]),n.push({display:{name:e.name,description:e.description,button_text:e.buttonText,recommend_text:e.recommendText,everything_from:e.everythingFrom,button_url:e.buttonUrl},items:r,properties:t});continue}let t=r.find(t=>t.id===e.id);if(!t)continue;let c=t.name,l=t.base_variant_id;if(l){let e=r.find(e=>e.id===l);e&&(c=e.name)}c=e.name||c;let u=null==(a=t.properties)?void 0:a.is_free,d=t.properties||{},p=e.items,h=e.price,f=[];if(h?(d.is_free=!1,u||void 0!==p?f.push({display:{primary_text:h.primaryText,secondary_text:h.secondaryText}}):t.items[0].display={primary_text:h.primaryText,secondary_text:h.secondaryText}):p&&!u&&f.push(t.items[0]),p)for(let e of p)if(e.featureId){let r=t.items.find(t=>t.feature_id===e.featureId);if(!r){console.error("Feature with id ".concat(e.featureId," not found for product ").concat(t.id));continue}f.push({...r,display:{primary_text:e.primaryText||(null==(s=r.display)?void 0:s.primary_text),secondary_text:e.secondaryText||(null==(o=r.display)?void 0:o.secondary_text)}})}else f.push({display:{primary_text:e.primaryText,secondary_text:e.secondaryText}});else f=t.items;let m={...t,items:f,properties:d,display:{name:c,description:e.description,button_text:e.buttonText,recommend_text:e.recommendText,everything_from:e.everythingFrom,button_url:e.buttonUrl}};n.push(m)}return n},eA={refreshInterval:0},eP=e=>{let{AutumnContext:t,params:r,authClient:n}=e,a=ek({AutumnContext:t,name:"usePricingTable",errorIfNotInitialized:!n}),i=n?n.autumn:a.client,s=async()=>{try{let{data:e,error:t}=await i.products.list();if(t)throw t;return(null==e?void 0:e.list)||[]}catch(e){throw new v({message:"Failed to fetch pricing table products",code:"failed_to_fetch_pricing_table_products"})}},{data:o,error:c,mutate:l}=(0,eI.default)("pricing-table",s,{...eA});return{products:eO(o||[],null==r?void 0:r.productDetails),isLoading:!c&&!o,error:c,refetch:l}},ej=e=>{let{AutumnContext:t,authClient:r}=e,n=ek({AutumnContext:t,name:"useAutumn",errorIfNotInitialized:!r}),{attachDialog:a,paywallDialog:i}=n,s=r?r.autumn:n.client,o=!!r,{refetch:c}=eP({AutumnContext:t,authClient:r}),{open:l,setProps:u,setOpen:d,setComponent:p}=a,{setProps:h,setOpen:f,setComponent:m}=i,y=async e=>{let t=await s.attach(e);if(t.error)return t;let r=t.data;return(null==r?void 0:r.checkout_url)&&"undefined"!=typeof window&&(e.openInNewTab?window.open(r.checkout_url,"_blank"):window.location.href=r.checkout_url),await c(),d&&d(!1),t},g=async e=>{let{dialog:t,...r}=e,{productId:n,entityId:a,entityData:i}=e,o=await s.check({productId:n,entityId:a,entityData:i,withPreview:!0});if(o.error)return o;let c=o.data.preview;return c?(u({preview:c,attachParams:r}),d(!0),o):await y(r)};return{attach:async e=>{let{dialog:t,openInNewTab:r}=e;return t&&o?void console.error("[Autumn] Attach dialog cannot be used with better auth plugin. To use this, please switch to <AutumnProvider /> and autumnHandler. Learn more here: https://docs.useautumn.com/quickstart/quickstart"):t&&!l?(p(t),await g(e)):await y(e)},check:async e=>{let{dialog:t,withPreview:r}=e;if(t&&o)return void console.error("[Autumn] Check dialog cannot be used with better auth plugin. To use this, please switch to <AutumnProvider /> and autumnHandler. Learn more here: https://docs.useautumn.com/quickstart/quickstart");t&&m(t);let n=await s.check({...e,withPreview:!!r||!!t});if(n.error)return n;let a=n.data;return a&&a.preview&&t&&(h({preview:a.preview}),f(!0)),n},track:async e=>{let t=await s.track(e);return t.error,t},cancel:async e=>{let t=await s.cancel(e);return t.error,t},openBillingPortal:async e=>{let t={openInNewTab:!1,...e},r=await s.openBillingPortal(t);if(r.error)return r;let n=r.data;return(null==n?void 0:n.url)&&"undefined"!=typeof window&&(t.openInNewTab?window.open(n.url,"_blank"):window.open(n.url,"_self")),r},setupPayment:async e=>{var t;let r={openInNewTab:!1,...e||{}},n=await s.setupPayment(r);return(null==(t=n.data)?void 0:t.url)&&"undefined"!=typeof window&&(r.openInNewTab?window.open(n.data.url,"_blank"):window.open(n.data.url,"_self")),n}}},eE=e=>{let{customer:t,featureId:r,requiredBalance:n=1}=e,a=Object.values(t.features).find(e=>e.credit_schema&&e.credit_schema.some(e=>e.feature_id===r));if(a){var i;let e=null==(i=a.credit_schema)?void 0:i.find(e=>e.feature_id===r);return{feature:a,requiredBalance:e.credit_amount*n}}return{cusFeature:t.features[r],requiredBalance:n}},eZ=e=>{let{customer:t,params:r}=e,{cusFeature:n,requiredBalance:a}=eE({customer:t,featureId:r.featureId});if(!n)return!1;if("static"==n.type||n.unlimited||n.overage_allowed)return!0;if(n.usage_limit){let e=(n.usage_limit||0)-(n.included_usage||0);return(n.balance||0)+e>=a}return(n.balance||0)>=a},eS=e=>{let{customer:t,params:r}=e;if(!t)return!1;if(!r.featureId&&!r.productId)throw Error("allowed() requires either featureId or productId");if(r.featureId)return eZ({customer:t,params:r});if(r.productId){let e=t.products.find(e=>e.id==r.productId);return!!e&&"scheduled"!=e.status}return!1},eN={attach:"",check:"",track:"",cancel:"",openBillingPortal:"",setupPayment:""},eU=e=>{let t,{params:r,AutumnContext:n,client:a}=e;n&&(t=ek({AutumnContext:n,name:"useCustomer"})),a||(a=t.client);let i=["customer",(null==a?void 0:a.backendUrl)||"",null==r?void 0:r.expand],s=async()=>{let{data:e,error:t}=await a.createCustomer({errorOnNotFound:null==r?void 0:r.errorOnNotFound,expand:null==r?void 0:r.expand});if(t)throw t;return e||null},{data:o,error:c,isLoading:l,mutate:u}=(0,g.default)(i,s,{fallbackData:null,...null==r?void 0:r.swrConfig}),d=eN;return n&&(d=ej({AutumnContext:n})),{customer:c?null:o,isLoading:l,error:c,refetch:u,...d,createEntity:a.entities.create,createReferralCode:a.referrals.createCode,redeemReferralCode:a.referrals.redeemCode,allowed:e=>eS({customer:o,params:e})}},eD=r(87183);function eR(e){let{client:t,children:r,AutumnContext:n}=e,[a,i]=(0,f.useState)({}),[s,o,c,l]=y(a.paywallDialog),[u,d,p,h]=y(a.productChangeDialog);return eU({client:t,params:{errorOnNotFound:!1}}),(0,eD.jsxs)(n.Provider,{value:{initialized:!0,client:t,paywallDialog:{props:s,setProps:o,open:c,setOpen:l,setComponent:e=>{i({...a,paywallDialog:e})}},attachDialog:{props:u,setProps:d,open:p,setOpen:h,setComponent:e=>{i({...a,productChangeDialog:e})}}},children:[a.paywallDialog&&(0,eD.jsx)(a.paywallDialog,{open:c,setOpen:l,...s}),a.productChangeDialog&&(0,eD.jsx)(a.productChangeDialog,{open:p,setOpen:h,...u}),r]})}},97627:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useCustomer:()=>i});var n=r(14776),a=r(14540);let i=e=>(0,a.useCustomerBase)({params:e,AutumnContext:n.AutumnContext})}}]);