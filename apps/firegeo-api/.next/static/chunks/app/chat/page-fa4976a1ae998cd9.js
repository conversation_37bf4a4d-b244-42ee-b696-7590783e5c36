(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8457],{53569:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>C});var t=a(87183),n=a(12359),l=a(62e3),r=a(8449),i=a(91574),c=a(9944),d=a(37418),o=a(26475),m=a(72346),u=a(15361),x=a(19856);let h=(0,x.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),g=(0,x.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var v=a(18513),y=a(56949),f=a(74290),j=a(39878),N=a(13419),b=a(46629),p=a(8256);function w(e){var s;let{session:a}=e,x=(0,r.useRouter)(),{allowed:w,customer:C,refetch:S}=(0,i.cN)(),[k,A]=(0,n.useState)(""),[E,I]=(0,n.useState)(!0),[M,P]=(0,n.useState)(null),[T,q]=(0,n.useState)(!1),[D,F]=(0,n.useState)(null),{data:O,isLoading:V}=function(){var e,s;let{data:a}=(0,l.wV)();return(0,v.I)({queryKey:["conversations",null==a||null==(e=a.user)?void 0:e.id],queryFn:async()=>{let e=await fetch("/api/chat");if(!e.ok)throw Error("Failed to fetch conversations");return e.json()},enabled:!!(null==a||null==(s=a.user)?void 0:s.id)})}(),{data:_}=function(e){var s;let{data:a}=(0,l.wV)();return(0,v.I)({queryKey:["conversation",e],queryFn:async()=>{let s=await fetch("/api/chat?conversationId=".concat(e));if(!s.ok)throw Error("Failed to fetch conversation");return s.json()},enabled:!!(null==a||null==(s=a.user)?void 0:s.id)&&!!e})}(M),K=function(){let e=(0,y.jE)(),{data:s}=(0,l.wV)();return(0,f.n)({mutationFn:async e=>{let{message:s,conversationId:a}=e,t=await fetch(j.Sn.CHAT,{method:j.y8.POST,headers:{"Content-Type":j.yw.JSON},body:JSON.stringify({message:s,conversationId:a})});return(0,N.HC)(t)},onSuccess:a=>{var t;e.invalidateQueries({queryKey:[j._l.CONVERSATIONS,null==s||null==(t=s.user)?void 0:t.id]}),a.conversationId&&e.invalidateQueries({queryKey:["conversation",a.conversationId]})}})}(),L=function(){let e=(0,y.jE)(),{data:s}=(0,l.wV)();return(0,f.n)({mutationFn:async e=>{let s=await fetch("/api/chat/".concat(e),{method:"DELETE"});if(!s.ok)throw Error("Failed to delete conversation");return s.json()},onSuccess:()=>{var a;e.invalidateQueries({queryKey:["conversations",null==s||null==(a=s.user)?void 0:a.id]})}})}(),z=null==C||null==(s=C.features)?void 0:s.messages,$=z&&z.balance||0,G=$>0,H=!C&&!a,Q=async()=>{if(k.trim()&&!K.isPending&&w({featureId:"messages"}))try{let e=await K.mutateAsync({conversationId:M,message:k});A(""),!M&&e.conversationId&&P(e.conversationId),await S()}catch(e){console.error("Failed to send message:",e)}},R=async e=>{F(e),q(!0)},J=async()=>{D&&(await L.mutateAsync(D),M===D&&P(null),F(null))};return(0,t.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,t.jsxs)("div",{className:"".concat(E?"w-64":"w-0"," bg-white border-r overflow-hidden flex flex-col transition-all duration-200"),children:[(0,t.jsx)("div",{className:"p-4 border-b",children:(0,t.jsxs)(c.$,{onClick:()=>{P(null)},className:"w-full btn-firecrawl-orange",children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"New Chat"]})}),(0,t.jsx)("div",{className:"overflow-y-auto flex-1",children:V?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Loading conversations..."}):(null==O?void 0:O.length)===0?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"No conversations yet"}):(0,t.jsx)("div",{className:"space-y-1 p-2",children:null==O?void 0:O.map(e=>(0,t.jsx)("div",{className:"p-3 rounded-lg cursor-pointer hover:bg-gray-100 ".concat(M===e.id?"bg-gray-100":""),onClick:()=>P(e.id),children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"font-medium truncate",children:e.title||"Untitled Conversation"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e.lastMessageAt&&(0,b.GP)(new Date(e.lastMessageAt),"MMM d, h:mm a")})]}),(0,t.jsx)(c.$,{size:"sm",variant:"ghost",onClick:s=>{s.stopPropagation(),R(e.id)},children:(0,t.jsx)(o.A,{className:"w-4 h-4"})})]})},e.id))})}),(0,t.jsx)("div",{className:"p-4 border-t bg-gray-50",children:(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("p",{children:"Messages remaining:"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:$})]})})]}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,t.jsx)("div",{className:"bg-white border-b px-4 py-3 flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>I(!E),children:E?(0,t.jsx)(m.A,{className:"w-5 h-5"}):(0,t.jsx)(u.A,{className:"w-5 h-5"})}),(0,t.jsx)("h1",{className:"font-semibold",children:(null==_?void 0:_.title)||"New Conversation"})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:H?(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading your account data..."})]})}):G?(null==_?void 0:_.messages)&&_.messages.length>0?(0,t.jsxs)("div",{className:"space-y-4 mb-20",children:[_.messages.map(e=>(0,t.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,t.jsxs)("div",{className:"max-w-[70%] rounded-lg px-4 py-2 ".concat("user"===e.role?"bg-orange-500 text-white":"bg-gray-100 text-gray-900"),children:[(0,t.jsx)("p",{className:"whitespace-pre-wrap",children:e.content}),(0,t.jsx)("p",{className:"text-xs mt-1 ".concat("user"===e.role?"text-orange-100":"text-gray-500"),children:(0,b.GP)(new Date(e.createdAt),"h:mm a")})]})},e.id)),K.isPending&&(0,t.jsx)("div",{className:"flex justify-start",children:(0,t.jsx)("div",{className:"bg-gray-100 text-gray-900 rounded-lg px-4 py-2",children:(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})})]}):(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(h,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Start a Conversation"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Send a message to begin chatting with AI"})]})}):(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"text-center max-w-md",children:[(0,t.jsx)(h,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Credit-Based Messaging"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"This is a demonstration of the credit-based messaging system. Each message consumes credits from your account balance."}),(0,t.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4",children:(0,t.jsxs)("p",{className:"text-sm text-orange-800",children:["You currently have ",(0,t.jsx)("span",{className:"font-bold",children:$})," message credits available."]})}),(0,t.jsx)(c.$,{onClick:()=>x.push("/plans"),className:"btn-firecrawl-orange",children:"Get More Credits"})]})})}),(0,t.jsx)("div",{className:"border-t bg-white p-4",children:(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),Q()},className:"flex gap-2",children:[(0,t.jsx)("input",{type:"text",value:k,onChange:e=>A(e.target.value),placeholder:G?"Type your message...":"No messages available",disabled:!G||K.isPending,className:"flex-1 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:bg-gray-100 disabled:text-gray-500"}),(0,t.jsx)(c.$,{type:"submit",disabled:!G||!k.trim()||K.isPending,className:"btn-firecrawl-orange",children:(0,t.jsx)(g,{className:"w-4 h-4"})})]})})]}),(0,t.jsx)(p.K,{open:T,onOpenChange:q,title:"Delete Conversation",description:"Are you sure you want to delete this conversation? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",onConfirm:J,isLoading:L.isPending})]})}function C(){let{data:e,isPending:s}=(0,l.wV)(),a=(0,r.useRouter)();return((0,n.useEffect)(()=>{s||e||a.push("/login")},[e,s,a]),s||!e)?(0,t.jsx)("div",{className:"flex h-screen items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):(0,t.jsx)(w,{session:e})}},79202:(e,s,a)=>{Promise.resolve().then(a.bind(a,53569))}},e=>{var s=s=>e(e.s=s);e.O(0,[4350,3677,6887,73,6504,4834,7086,1750,7497,6703,588,7358],()=>s(79202)),_N_E=e.O()}]);