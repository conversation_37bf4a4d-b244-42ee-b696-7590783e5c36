(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{1591:(e,a,s)=>{"use strict";s.d(a,{cn:()=>n});var t=s(4119),l=s(27291);function n(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,l.QP)((0,t.$)(a))}},9944:(e,a,s)=>{"use strict";s.d(a,{$:()=>o});var t=s(87183);s(12359);var l=s(70879),n=s(48287),i=s(1591);let r=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",{variants:{variant:{default:"bg-zinc-900 text-white hover:bg-zinc-800 [box-shadow:inset_0px_-2px_0px_0px_#18181b,_0px_1px_6px_0px_rgba(24,_24,_27,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#18181b,_0px_1px_3px_0px_rgba(24,_24,_27,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#18181b,_0px_1px_2px_0px_rgba(24,_24,_27,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",destructive:"bg-red-500 text-white hover:bg-red-600 [box-shadow:inset_0px_-2px_0px_0px_#dc2626,_0px_1px_6px_0px_rgba(239,_68,_68,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#dc2626,_0px_1px_3px_0px_rgba(239,_68,_68,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#dc2626,_0px_1px_2px_0px_rgba(239,_68,_68,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",outline:"border border-zinc-300 bg-transparent hover:bg-zinc-50 text-zinc-900 [box-shadow:inset_0px_-2px_0px_0px_#e4e4e7,_0px_1px_6px_0px_rgba(228,_228,_231,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#e4e4e7,_0px_1px_3px_0px_rgba(228,_228,_231,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#e4e4e7,_0px_1px_2px_0px_rgba(228,_228,_231,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",secondary:"bg-zinc-100 text-zinc-900 hover:bg-zinc-200 [box-shadow:inset_0px_-2px_0px_0px_#d4d4d8,_0px_1px_6px_0px_rgba(161,_161,_170,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#d4d4d8,_0px_1px_3px_0px_rgba(161,_161,_170,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#d4d4d8,_0px_1px_2px_0px_rgba(161,_161,_170,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",orange:"bg-orange-500 text-white hover:bg-orange-600 [box-shadow:inset_0px_-2px_0px_0px_#c2410c,_0px_1px_6px_0px_rgba(234,_88,_12,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#c2410c,_0px_1px_3px_0px_rgba(234,_88,_12,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#c2410c,_0px_1px_2px_0px_rgba(234,_88,_12,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-[8px] gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-11 rounded-[12px] px-6 has-[>svg]:px-4",icon:"size-10"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:a,variant:s,size:n,asChild:o=!1,...d}=e,c=o?l.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,i.cn)(r({variant:s,size:n,className:a})),...d})}},10180:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19856).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},19479:(e,a,s)=>{"use strict";s.d(a,{A:()=>d});var t=s(87183),l=s(12359),n=s(55640),i=s(9944),r=s(91574),o=s(23736);let d=e=>{var a;let{open:s,setOpen:d,preview:c}=e,[x,p]=(0,l.useState)(!1),{attach:u,refetch:m}=(0,r.cN)(),h=async()=>{if(c.product_id){p(!0);try{await u({productId:c.product_id}),await m(),d(!1)}catch(e){console.error("Error attaching product:",e)}finally{p(!1)}}};return(0,t.jsx)(n.lG,{open:s,onOpenChange:d,children:(0,t.jsxs)(n.Cf,{className:"sm:max-w-md",children:[(0,t.jsxs)(n.c7,{children:[(0,t.jsx)(n.L3,{children:(null==c?void 0:c.title)||"Confirm Plan Change"}),(null==c?void 0:c.message)&&(0,t.jsx)(n.rr,{children:c.message})]}),(null==c||null==(a=c.due_today)?void 0:a.price)!==void 0&&(0,t.jsx)("div",{className:"py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Due today:"}),(0,t.jsx)("span",{className:"text-lg font-semibold",children:function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return e?new Intl.NumberFormat("en-US",{style:"currency",currency:a}).format(e/100):null}(c.due_today.price,c.due_today.currency)})]})}),(0,t.jsxs)(n.Es,{className:"flex-col sm:flex-row gap-2",children:[(0,t.jsx)(i.$,{variant:"outline",onClick:()=>d(!1),disabled:x,children:"Cancel"}),(0,t.jsx)(i.$,{onClick:h,disabled:x,children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):"Confirm"})]})]})})}},23736:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(19856).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55640:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>c,Es:()=>p,L3:()=>u,c7:()=>x,lG:()=>r,rr:()=>m});var t=s(87183);s(12359);var l=s(9087),n=s(72346),i=s(1591);function r(e){let{...a}=e;return(0,t.jsx)(l.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,t.jsx)(l.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(l.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s})}function c(e){let{className:a,children:s,showCloseButton:r=!0,...c}=e;return(0,t.jsxs)(o,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(l.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...c,children:[s,r&&(0,t.jsxs)(l.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(n.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",a),...s})}function p(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...s})}function u(e){let{className:a,...s}=e;return(0,t.jsx)(l.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",a),...s})}function m(e){let{className:a,...s}=e;return(0,t.jsx)(l.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...s})}},61292:(e,a,s)=>{Promise.resolve().then(s.bind(s,67380))},62e3:(e,a,s)=>{"use strict";s.d(a,{CI:()=>i,Hh:()=>n,Jv:()=>l,wV:()=>r,yK:()=>t});let t=(0,s(33677).MB)({baseURL:"http://localhost:3000",fetchOptions:{credentials:"include"}}),{signIn:l,signUp:n,signOut:i,useSession:r}=t},67380:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>k});var t=s(87183),l=s(69228),n=s(62e3),i=s(8449),r=s(12359),o=s(19856);let d=(0,o.A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),c=(0,o.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var x=s(72346);let p=(0,o.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),u=(0,o.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),m=(0,o.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var h=s(10180);let _=(0,o.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var g=s(23736);let f=(0,o.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var v=s(9944),b=s(19479),y=s(18513),j=s(56949),N=s(74290);function w(e){var a,s,i,o,w;let{session:k}=e,{customer:C,attach:A}=(0,l.cN)(),{products:z}=(0,l.AC)(),[E,P]=(0,r.useState)(null),{data:S}=function(){var e,a;let{data:s}=(0,n.wV)();return(0,y.I)({queryKey:["profile",null==s||null==(e=s.user)?void 0:e.id],queryFn:async()=>{let e=await fetch("/api/user/profile");if(!e.ok)throw Error("Failed to fetch profile");return e.json()},enabled:!!(null==s||null==(a=s.user)?void 0:a.id)})}(),D=function(){let e=(0,j.jE)(),{data:a}=(0,n.wV)();return(0,N.n)({mutationFn:async e=>{let a=await fetch("/api/user/profile",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error("Failed to update profile");return a.json()},onSuccess:()=>{var s;e.invalidateQueries({queryKey:["profile",null==a||null==(s=a.user)?void 0:s.id]})}})}(),{data:F}=function(){var e,a;let{data:s}=(0,n.wV)();return(0,y.I)({queryKey:["settings",null==s||null==(e=s.user)?void 0:e.id],queryFn:async()=>{let e=await fetch("/api/user/settings");if(!e.ok)throw Error("Failed to fetch settings");return e.json()},enabled:!!(null==s||null==(a=s.user)?void 0:a.id)})}(),L=function(){let e=(0,j.jE)(),{data:a}=(0,n.wV)();return(0,N.n)({mutationFn:async e=>{let a=await fetch("/api/user/settings",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error("Failed to update settings");return a.json()},onSuccess:()=>{var s;e.invalidateQueries({queryKey:["settings",null==a||null==(s=a.user)?void 0:s.id]})}})}(),[M,V]=(0,r.useState)(!1),[U,q]=(0,r.useState)({displayName:"",bio:"",phone:""});(0,r.useEffect)(()=>{(null==S?void 0:S.profile)&&q({displayName:S.profile.displayName||"",bio:S.profile.bio||"",phone:S.profile.phone||""})},[S]);let O=async()=>{await D.mutateAsync(U),V(!1)},$=async(e,a)=>{await L.mutateAsync({[e]:a})},I=(null==C?void 0:C.products)||[],H=(null==C?void 0:C.features)||{},R=I.find(e=>"active"===e.status||"trialing"===e.status||"past_due"===e.status),J=I.find(e=>"scheduled"===e.status||e.started_at&&new Date(e.started_at)>new Date),K=async e=>{try{P(e),await A({productId:e,dialog:b.A,returnUrl:window.location.origin+"/dashboard",successUrl:window.location.origin+"/dashboard",cancelUrl:window.location.origin+"/dashboard"})}finally{P(null)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Dashboard"}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"Profile Information"}),M?(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(v.$,{onClick:O,size:"sm",variant:"default",disabled:D.isPending,children:[(0,t.jsx)(c,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,t.jsxs)(v.$,{onClick:()=>{V(!1),(null==S?void 0:S.profile)&&q({displayName:S.profile.displayName||"",bio:S.profile.bio||"",phone:S.profile.phone||""})},size:"sm",variant:"outline",disabled:D.isPending,children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Cancel"]})]}):(0,t.jsxs)(v.$,{onClick:()=>V(!0),size:"sm",className:"bg-black text-white hover:bg-gray-800",children:[(0,t.jsx)(d,{className:"h-4 w-4 mr-2"}),"Edit Profile"]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[(0,t.jsx)(p,{className:"inline-block h-4 w-4 mr-1"}),"Email"]}),(0,t.jsx)("p",{className:"text-gray-900",children:null==(a=k.user)?void 0:a.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[(0,t.jsx)(u,{className:"inline-block h-4 w-4 mr-1"}),"Display Name"]}),M?(0,t.jsx)("input",{type:"text",value:U.displayName,onChange:e=>q({...U,displayName:e.target.value}),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter your display name"}):(0,t.jsx)("p",{className:"text-gray-900",children:(null==S||null==(s=S.profile)?void 0:s.displayName)||"Not set"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[(0,t.jsx)(m,{className:"inline-block h-4 w-4 mr-1"}),"Phone"]}),M?(0,t.jsx)("input",{type:"tel",value:U.phone,onChange:e=>q({...U,phone:e.target.value}),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter your phone number"}):(0,t.jsx)("p",{className:"text-gray-900",children:(null==S||null==(i=S.profile)?void 0:i.phone)||"Not set"})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bio"}),M?(0,t.jsx)("textarea",{value:U.bio,onChange:e=>q({...U,bio:e.target.value}),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",rows:3,placeholder:"Tell us about yourself"}):(0,t.jsx)("p",{className:"text-gray-900",children:(null==S||null==(o=S.profile)?void 0:o.bio)||"Not set"})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Settings"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Email Notifications"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Receive email notifications for important updates"})]}),(0,t.jsx)("button",{onClick:()=>$("emailNotifications",!(null==F?void 0:F.emailNotifications)),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat((null==F?void 0:F.emailNotifications)?"bg-orange-500":"bg-gray-200"),disabled:L.isPending,children:(0,t.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat((null==F?void 0:F.emailNotifications)?"translate-x-6":"translate-x-1")})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Marketing Emails"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Receive emails about new features and offers"})]}),(0,t.jsx)("button",{onClick:()=>$("marketingEmails",!(null==F?void 0:F.marketingEmails)),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat((null==F?void 0:F.marketingEmails)?"bg-orange-500":"bg-gray-200"),disabled:L.isPending,children:(0,t.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat((null==F?void 0:F.marketingEmails)?"translate-x-6":"translate-x-1")})})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Account Information"}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Email"}),(0,t.jsx)("p",{className:"font-medium",children:null==(w=k.user)?void 0:w.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Current Plan"}),(0,t.jsx)("p",{className:"font-medium flex items-center",children:R?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-green-500 mr-1"}),R.name||R.id,J&&(0,t.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:["(Changing to ",J.name||J.id," on ",new Date(J.started_at||J.current_period_end).toLocaleDateString(),")"]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(_,{className:"h-4 w-4 text-yellow-500 mr-1"}),"Free Plan"]})})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Usage Statistics"}),Object.keys(H).length>0?(0,t.jsx)("div",{className:"space-y-4",children:Object.entries(H).map(e=>{let[a,s]=e;return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h3",{className:"font-medium mb-2 capitalize",children:a.replace(/_/g," ")}),(0,t.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,t.jsx)("span",{children:"Used"}),(0,t.jsxs)("span",{children:[s.usage||0," / ",s.included_usage||s.balance+(s.usage||0)]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-orange-500 h-2 rounded-full transition-all",style:{width:"".concat(Math.min((s.usage||0)/(s.included_usage||s.balance+(s.usage||0)||1)*100,100),"%")}})})]}),s.next_reset_at&&(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Resets on: ",new Date(s.next_reset_at).toLocaleDateString()]})]},a)})}):(0,t.jsx)("p",{className:"text-gray-500",children:"No usage data available"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Available Plans"}),z?(0,t.jsx)("div",{className:"space-y-4",children:z.map(e=>{var a,s,l,n,i;let r=(null==R?void 0:R.id)===e.id,o=(null==J?void 0:J.id)===e.id,d=(null==(a=e.properties)?void 0:a.is_free)?e.items:(null==(s=e.items)?void 0:s.slice(1))||[];return(0,t.jsx)("div",{className:"border rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("h3",{className:"font-medium text-lg",children:[(null==(l=e.display)?void 0:l.name)||e.name,r&&(0,t.jsx)("span",{className:"ml-2 text-sm text-green-600",children:"(Current Plan)"}),o&&(0,t.jsx)("span",{className:"ml-2 text-sm text-orange-600",children:"(Scheduled)"})]}),(null==(n=e.display)?void 0:n.description)&&(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.display.description}),(0,t.jsx)("ul",{className:"mt-3 space-y-1",children:d.slice(0,3).map((e,a)=>{var s;return(0,t.jsxs)("li",{className:"flex items-start text-sm",children:[r?(0,t.jsx)(h.A,{className:"h-4 w-4 text-green-500 mr-2 flex-shrink-0 mt-0.5"}):(0,t.jsx)(f,{className:"h-4 w-4 text-gray-400 mr-2 flex-shrink-0 mt-0.5"}),(0,t.jsx)("span",{className:r?"":"text-gray-500",children:null==(s=e.display)?void 0:s.primary_text})]},a)})})]}),!r&&!o&&(0,t.jsx)(v.$,{onClick:()=>K(e.id),size:"sm",variant:"outline",disabled:null!==E,children:E===e.id?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):(null==(i=e.properties)?void 0:i.is_free)?"Downgrade":"Upgrade"}),o&&(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["Starts ",new Date(J.started_at||J.current_period_end).toLocaleDateString()]})]})},e.id)})}):(0,t.jsx)("div",{className:"flex justify-center py-8",children:(0,t.jsx)(g.A,{className:"h-6 w-6 animate-spin text-gray-400"})})]})]})})}function k(){let{data:e,isPending:a}=(0,n.wV)(),s=(0,i.useRouter)();return((0,r.useEffect)(()=>{a||e||s.push("/login")},[e,a,s]),a||!e)?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):(0,t.jsx)(w,{session:e})}},91574:(e,a,s)=>{"use strict";s.d(a,{DE:()=>d,GJ:()=>r,cN:()=>o});var t=s(87183),l=s(12359),n=s(69228);let i=(0,l.createContext)(null);function r(e){let{children:a}=e,{refetch:s}=(0,n.cN)({skip:!0}),r=(0,l.useCallback)(async()=>{await s()},[s]);return(0,t.jsx)(i.Provider,{value:{refetchCustomer:r},children:a})}function o(e){let a=(0,n.cN)(e),s=(0,l.useContext)(i),t=(0,l.useCallback)(async()=>{let e=await a.refetch();return(null==s?void 0:s.refetchCustomer)&&await s.refetchCustomer(),e},[a,s]);return{...a,refetch:t}}function d(){let e=(0,l.useContext)(i);return e?e.refetchCustomer:async()=>{console.warn("useRefreshCustomer called outside of AutumnCustomerProvider")}}}},e=>{var a=a=>e(e.s=a);e.O(0,[4350,3677,6887,73,6504,4834,7086,6703,588,7358],()=>a(61292)),_N_E=e.O()}]);