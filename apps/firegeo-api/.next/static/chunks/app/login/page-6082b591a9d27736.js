(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{8449:(e,r,t)=>{"use strict";var s=t(42221);t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},10463:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(87183),a=t(12359),l=t(8449),n=t(34540),i=t.n(n),o=t(57220),c=t(62e3);function d(){let[e,r]=(0,a.useState)(""),[t,n]=(0,a.useState)(""),[d,m]=(0,a.useState)(""),[u,x]=(0,a.useState)(""),[g,h]=(0,a.useState)(!1),b=(0,l.useSearchParams)();(0,a.useEffect)(()=>{"success"===b.get("reset")&&x("Password reset successfully. You can now login with your new password.");let e=b.get("email");e&&r(e)},[b]);let f=async r=>{r.preventDefault(),m(""),h(!0);try{let r=await c.Jv.email({email:e,password:t});if(r.error){m(r.error.message||"Failed to login"),h(!1);return}let s=b.get("from")||"/dashboard";window.location.replace(s)}catch(e){m(e.message||"Failed to login"),h(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen flex",children:[(0,s.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 p-12 items-center justify-center relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-400/90 via-orange-500/90 to-orange-600/90"}),(0,s.jsxs)("div",{className:"relative z-10 max-w-md text-white",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Welcome back!"}),(0,s.jsx)("p",{className:"text-lg opacity-90",children:"Sign in to continue building amazing things with our powerful API."})]}),(0,s.jsx)("div",{className:"absolute top-20 right-20 w-64 h-64 bg-orange-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"}),(0,s.jsx)("div",{className:"absolute bottom-20 left-20 w-64 h-64 bg-orange-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"})]}),(0,s.jsx)("div",{className:"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-white",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"lg:hidden mb-8 flex justify-center",children:(0,s.jsx)(o.default,{src:"/firecrawl-logo-with-fire.webp",alt:"Firecrawl",width:180,height:37,priority:!0})}),(0,s.jsx)("h2",{className:"text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,s.jsx)(i(),{href:"/register",className:"font-medium text-orange-600 hover:text-orange-500",children:"create a new account"})]})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:f,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm",placeholder:"Enter your email"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:t,onChange:e=>n(e.target.value),className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm",placeholder:"Enter your password"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),(0,s.jsx)(i(),{href:"/forgot-password",className:"text-sm text-orange-600 hover:text-orange-500",children:"Forgot your password?"})]}),u&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg",children:u}),d&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:d}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:g,className:"btn-firecrawl-default w-full inline-flex items-center justify-center whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 h-10 px-4",children:g?"Signing in...":"Sign in"})})]})]})})]})}function m(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:"Loading..."}),children:(0,s.jsx)(d,{})})}},62e3:(e,r,t)=>{"use strict";t.d(r,{CI:()=>n,Hh:()=>l,Jv:()=>a,wV:()=>i,yK:()=>s});let s=(0,t(33677).MB)({baseURL:"http://localhost:3000",fetchOptions:{credentials:"include"}}),{signIn:a,signUp:l,signOut:n,useSession:i}=s},97187:(e,r,t)=>{Promise.resolve().then(t.bind(t,10463))}},e=>{var r=r=>e(e.s=r);e.O(0,[3677,7220,4540,6703,588,7358],()=>r(97187)),_N_E=e.O()}]);