(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9033],{4894:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(87183),l=t(91574),n=t(62e3),a=t(12359);function c(e){var s,t,n,c;let{session:i}=e,{customer:d,isLoading:o,error:u}=(0,l.cN)();return(0,a.useEffect)(()=>{d&&console.log("Autumn Customer:",d)},[d]),(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Autumn Integration Verification"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Auth Session Status"}),i?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-green-600",children:"✓ Logged in"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"User ID:"})," ",null==(s=i.user)?void 0:s.id]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Email:"})," ",null==(t=i.user)?void 0:t.email]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Name:"})," ",(null==(n=i.user)?void 0:n.name)||"Not set"]})]}):(0,r.jsx)("p",{className:"text-red-600",children:"✗ Not logged in"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Autumn Customer Status"}),o?(0,r.jsx)("p",{className:"text-gray-500",children:"Loading..."}):u?(0,r.jsxs)("div",{className:"text-red-600",children:[(0,r.jsx)("p",{children:"✗ Error loading customer"}),(0,r.jsx)("pre",{className:"mt-2 text-sm bg-red-50 p-2 rounded",children:JSON.stringify(u,null,2)})]}):d?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-green-600",children:"✓ Customer found in Autumn"}),(0,r.jsxs)("details",{className:"mt-4",children:[(0,r.jsx)("summary",{className:"cursor-pointer text-blue-600 hover:underline",children:"View customer data (click to expand)"}),(0,r.jsx)("pre",{className:"mt-2 text-sm bg-gray-50 p-4 rounded overflow-auto",children:JSON.stringify(d,null,2)})]})]}):(0,r.jsxs)("div",{className:"text-yellow-600",children:[(0,r.jsx)("p",{children:"⚠ No customer data"}),(0,r.jsx)("p",{className:"text-sm mt-2",children:i?"Customer may not be synced to Autumn yet.":"Please log in first."})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"Next Steps:"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm",children:[(0,r.jsx)("li",{children:'Check the browser console for the logged "Autumn Customer" object'}),(0,r.jsxs)("li",{children:["Verify in your Autumn dashboard at ",(0,r.jsx)("a",{href:"https://app.useautumn.com/sandbox/customers",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"https://app.useautumn.com/sandbox/customers"})," that the customer was created"]}),(0,r.jsxs)("li",{children:["The customer ID in Autumn should match your auth user ID: ",(0,r.jsx)("code",{className:"bg-white px-2 py-1 rounded",children:(null==i||null==(c=i.user)?void 0:c.id)||"Not logged in"})]})]})]})]})]})})}function i(){let{data:e,isPending:s}=(0,n.wV)();return s?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):(0,r.jsx)(c,{session:e})}},13268:(e,s,t)=>{Promise.resolve().then(t.bind(t,4894))},62e3:(e,s,t)=>{"use strict";t.d(s,{CI:()=>a,Hh:()=>n,Jv:()=>l,wV:()=>c,yK:()=>r});let r=(0,t(33677).MB)({baseURL:"http://localhost:3000",fetchOptions:{credentials:"include"}}),{signIn:l,signUp:n,signOut:a,useSession:c}=r},91574:(e,s,t)=>{"use strict";t.d(s,{DE:()=>d,GJ:()=>c,cN:()=>i});var r=t(87183),l=t(12359),n=t(69228);let a=(0,l.createContext)(null);function c(e){let{children:s}=e,{refetch:t}=(0,n.cN)({skip:!0}),c=(0,l.useCallback)(async()=>{await t()},[t]);return(0,r.jsx)(a.Provider,{value:{refetchCustomer:c},children:s})}function i(e){let s=(0,n.cN)(e),t=(0,l.useContext)(a),r=(0,l.useCallback)(async()=>{let e=await s.refetch();return(null==t?void 0:t.refetchCustomer)&&await t.refetchCustomer(),e},[s,t]);return{...s,refetch:r}}function d(){let e=(0,l.useContext)(a);return e?e.refetchCustomer:async()=>{console.warn("useRefreshCustomer called outside of AutumnCustomerProvider")}}}},e=>{var s=s=>e(e.s=s);e.O(0,[4350,3677,6887,6703,588,7358],()=>s(13268)),_N_E=e.O()}]);