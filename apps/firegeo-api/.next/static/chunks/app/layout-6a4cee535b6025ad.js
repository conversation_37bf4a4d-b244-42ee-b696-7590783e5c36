(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{8449:(e,t,s)=>{"use strict";var r=s(42221);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},10057:(e,t,s)=>{"use strict";s.d(t,{Navbar:()=>d});var r=s(87183),i=s(34540),a=s.n(i),n=s(57220),u=s(62e3),l=s(8449),o=s(12359),h=s(91574);function c(){var e;let{customer:t}=(0,h.cN)(),s=null==t||null==(e=t.features)?void 0:e.messages,i=s&&s.balance||0;return(0,r.jsxs)("div",{className:"flex items-center text-sm font-medium text-gray-700",children:[(0,r.jsx)("span",{children:i}),(0,r.jsx)("span",{className:"ml-1",children:"credits"})]})}function d(){let{data:e,isPending:t}=(0,u.wV)(),[s,i]=(0,o.useState)(!1),h=(0,l.useRouter)(),d=async()=>{i(!0);try{await (0,u.CI)(),setTimeout(()=>{h.refresh(),i(!1)},100)}catch(e){console.error("Logout error:",e),i(!1)}};return(0,r.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(a(),{href:"/",className:"flex items-center",children:(0,r.jsx)(n.default,{src:"/firecrawl-logo-with-fire.webp",alt:"Firecrawl",width:120,height:25,priority:!0})})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a(),{href:"/chat",className:"px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900",children:"Basic Chat"}),(0,r.jsx)(a(),{href:"/brand-monitor",className:"px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900",children:"Brand Monitor"})]}),(0,r.jsx)(a(),{href:"/plans",className:"px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900",children:"Plans"}),e&&(0,r.jsx)(c,{}),t?(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"Loading..."}):e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a(),{href:"/dashboard",className:"btn-firecrawl-orange inline-flex items-center justify-center whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 h-8 px-3",children:"Dashboard"}),(0,r.jsx)("button",{onClick:d,disabled:s,className:"btn-firecrawl-default inline-flex items-center justify-center whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 h-8 px-3",children:s?"Logging out...":"Logout"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a(),{href:"/login",className:"bg-black text-white hover:bg-gray-800 inline-flex items-center justify-center whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 h-8 px-3 shadow-sm hover:shadow-md",children:"Login"}),(0,r.jsx)(a(),{href:"/register",className:"btn-firecrawl-orange inline-flex items-center justify-center whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 h-8 px-3",children:"Register"})]})]})]})})})}},23410:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,44538,23)),Promise.resolve().then(s.bind(s,10057)),Promise.resolve().then(s.bind(s,92621)),Promise.resolve().then(s.t.bind(s,34540,23)),Promise.resolve().then(s.t.bind(s,98797,23)),Promise.resolve().then(s.t.bind(s,95458,23)),Promise.resolve().then(s.t.bind(s,98686,23))},44538:()=>{},62e3:(e,t,s)=>{"use strict";s.d(t,{CI:()=>n,Hh:()=>a,Jv:()=>i,wV:()=>u,yK:()=>r});let r=(0,s(33677).MB)({baseURL:"http://localhost:3000",fetchOptions:{credentials:"include"}}),{signIn:i,signUp:a,signOut:n,useSession:u}=r},91574:(e,t,s)=>{"use strict";s.d(t,{DE:()=>o,GJ:()=>u,cN:()=>l});var r=s(87183),i=s(12359),a=s(69228);let n=(0,i.createContext)(null);function u(e){let{children:t}=e,{refetch:s}=(0,a.cN)({skip:!0}),u=(0,i.useCallback)(async()=>{await s()},[s]);return(0,r.jsx)(n.Provider,{value:{refetchCustomer:u},children:t})}function l(e){let t=(0,a.cN)(e),s=(0,i.useContext)(n),r=(0,i.useCallback)(async()=>{let e=await t.refetch();return(null==s?void 0:s.refetchCustomer)&&await s.refetchCustomer(),e},[t,s]);return{...t,refetch:r}}function o(){let e=(0,i.useContext)(n);return e?e.refetchCustomer:async()=>{console.warn("useRefreshCustomer called outside of AutumnCustomerProvider")}}},92621:(e,t,s)=>{"use strict";s.d(t,{Providers:()=>j});var r=s(87183),i=s(69228),a=s(50247),n=s(57906),u=s(29054),l=s(24311),o=class extends l.Q{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,s){let r=t.queryKey,i=t.queryHash??(0,a.F$)(r,t),u=this.get(i);return u||(u=new n.X({client:e,queryKey:r,queryHash:i,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(r)}),this.add(u)),u}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){u.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,a.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,a.MK)(e,t)):t}notify(e){u.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){u.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){u.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},h=s(60061),c=class extends l.Q{constructor(e={}){super(),this.config=e,this.#t=new Set,this.#s=new Map,this.#r=0}#t;#s;#r;build(e,t,s){let r=new h.s({mutationCache:this,mutationId:++this.#r,options:e.defaultMutationOptions(t),state:s});return this.add(r),r}add(e){this.#t.add(e);let t=d(e);if("string"==typeof t){let s=this.#s.get(t);s?s.push(e):this.#s.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#t.delete(e)){let t=d(e);if("string"==typeof t){let s=this.#s.get(t);if(s)if(s.length>1){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}else s[0]===e&&this.#s.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=d(e);if("string"!=typeof t)return!0;{let s=this.#s.get(t),r=s?.find(e=>"pending"===e.state.status);return!r||r===e}}runNext(e){let t=d(e);if("string"!=typeof t)return Promise.resolve();{let s=this.#s.get(t)?.find(t=>t!==e&&t.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){u.jG.batch(()=>{this.#t.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#t.clear(),this.#s.clear()})}getAll(){return Array.from(this.#t)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,a.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,a.nJ)(e,t))}notify(e){u.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return u.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(a.lQ))))}};function d(e){return e.options.scope?.id}var f=s(84589),m=s(15964);function y(e){return{onFetch:(t,s)=>{let r=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],u=t.state.data?.pageParams||[],l={pages:[],pageParams:[]},o=0,h=async()=>{let s=!1,h=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?s=!0:t.signal.addEventListener("abort",()=>{s=!0}),t.signal)})},c=(0,a.ZM)(t.options,t.fetchOptions),d=async(e,r,i)=>{if(s)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);let n=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:r,direction:i?"backward":"forward",meta:t.options.meta};return h(e),e})(),u=await c(n),{maxPages:l}=t.options,o=i?a.ZZ:a.y9;return{pages:o(e.pages,u,l),pageParams:o(e.pageParams,r,l)}};if(i&&n.length){let e="backward"===i,t={pages:n,pageParams:u},s=(e?function(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}:p)(r,t);l=await d(t,s,e)}else{let t=e??n.length;do{let e=0===o?u[0]??r.initialPageParam:p(r,l);if(o>0&&null==e)break;l=await d(l,e),o++}while(o<t)}return l};t.options.persister?t.fetchFn=()=>t.options.persister?.(h,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=h}}}function p(e,{pages:t,pageParams:s}){let r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,s[r],s):void 0}var g=class{#i;#a;#n;#u;#l;#o;#h;#c;constructor(e={}){this.#i=e.queryCache||new o,this.#a=e.mutationCache||new c,this.#n=e.defaultOptions||{},this.#u=new Map,this.#l=new Map,this.#o=0}mount(){this.#o++,1===this.#o&&(this.#h=f.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#i.onFocus())}),this.#c=m.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#i.onOnline())}))}unmount(){this.#o--,0===this.#o&&(this.#h?.(),this.#h=void 0,this.#c?.(),this.#c=void 0)}isFetching(e){return this.#i.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#a.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#i.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),s=this.#i.build(this,t),r=s.state.data;return void 0===r?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime((0,a.d2)(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return this.#i.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,s){let r=this.defaultQueryOptions({queryKey:e}),i=this.#i.get(r.queryHash),n=i?.state.data,u=(0,a.Zw)(t,n);if(void 0!==u)return this.#i.build(this,r).setData(u,{...s,manual:!0})}setQueriesData(e,t,s){return u.jG.batch(()=>this.#i.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,s)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#i.get(t.queryHash)?.state}removeQueries(e){let t=this.#i;u.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let s=this.#i;return u.jG.batch(()=>(s.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let s={revert:!0,...t};return Promise.all(u.jG.batch(()=>this.#i.findAll(e).map(e=>e.cancel(s)))).then(a.lQ).catch(a.lQ)}invalidateQueries(e,t={}){return u.jG.batch(()=>(this.#i.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let s={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(u.jG.batch(()=>this.#i.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(a.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(a.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let s=this.#i.build(this,t);return s.isStaleByTime((0,a.d2)(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(a.lQ).catch(a.lQ)}fetchInfiniteQuery(e){return e.behavior=y(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(a.lQ).catch(a.lQ)}ensureInfiniteQueryData(e){return e.behavior=y(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return m.t.isOnline()?this.#a.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#i}getMutationCache(){return this.#a}getDefaultOptions(){return this.#n}setDefaultOptions(e){this.#n=e}setQueryDefaults(e,t){this.#u.set((0,a.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#u.values()],s={};return t.forEach(t=>{(0,a.Cp)(e,t.queryKey)&&Object.assign(s,t.defaultOptions)}),s}setMutationDefaults(e,t){this.#l.set((0,a.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#l.values()],s={};return t.forEach(t=>{(0,a.Cp)(e,t.mutationKey)&&Object.assign(s,t.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#n.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,a.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===a.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#n.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#i.clear(),this.#a.clear()}},b=s(56949),x=s(12359);function v(e){let{children:t}=e,[s]=(0,x.useState)(()=>new g({defaultOptions:{queries:{staleTime:6e4,gcTime:3e5}}}));return(0,r.jsx)(b.Ht,{client:s,children:t})}var w=s(91574),q=s(62e3);function C(e){let{children:t}=e,{data:s}=(0,q.wV)();return s?(0,r.jsx)(i.qz,{backendUrl:"/api/auth/autumn",betterAuthUrl:"http://localhost:3000",allowAnonymous:!1,skipInitialFetch:!1,children:(0,r.jsx)(w.GJ,{children:t})}):(0,r.jsx)(r.Fragment,{children:t})}function j(e){let{children:t}=e;return(0,r.jsx)(v,{children:(0,r.jsx)(C,{children:t})})}},95458:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},98686:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}}},e=>{var t=t=>e(e.s=t);e.O(0,[2451,4350,3677,6887,7220,4540,4834,6703,588,7358],()=>t(23410)),_N_E=e.O()}]);