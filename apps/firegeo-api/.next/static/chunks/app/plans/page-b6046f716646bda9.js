(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[481],{1591:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(4119),a=r(27291);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},6642:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19856).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},9944:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var s=r(87183);r(12359);var a=r(70879),n=r(48287),l=r(1591);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",{variants:{variant:{default:"bg-zinc-900 text-white hover:bg-zinc-800 [box-shadow:inset_0px_-2px_0px_0px_#18181b,_0px_1px_6px_0px_rgba(24,_24,_27,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#18181b,_0px_1px_3px_0px_rgba(24,_24,_27,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#18181b,_0px_1px_2px_0px_rgba(24,_24,_27,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",destructive:"bg-red-500 text-white hover:bg-red-600 [box-shadow:inset_0px_-2px_0px_0px_#dc2626,_0px_1px_6px_0px_rgba(239,_68,_68,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#dc2626,_0px_1px_3px_0px_rgba(239,_68,_68,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#dc2626,_0px_1px_2px_0px_rgba(239,_68,_68,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",outline:"border border-zinc-300 bg-transparent hover:bg-zinc-50 text-zinc-900 [box-shadow:inset_0px_-2px_0px_0px_#e4e4e7,_0px_1px_6px_0px_rgba(228,_228,_231,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#e4e4e7,_0px_1px_3px_0px_rgba(228,_228,_231,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#e4e4e7,_0px_1px_2px_0px_rgba(228,_228,_231,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",secondary:"bg-zinc-100 text-zinc-900 hover:bg-zinc-200 [box-shadow:inset_0px_-2px_0px_0px_#d4d4d8,_0px_1px_6px_0px_rgba(161,_161,_170,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#d4d4d8,_0px_1px_3px_0px_rgba(161,_161,_170,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#d4d4d8,_0px_1px_2px_0px_rgba(161,_161,_170,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",orange:"bg-orange-500 text-white hover:bg-orange-600 [box-shadow:inset_0px_-2px_0px_0px_#c2410c,_0px_1px_6px_0px_rgba(234,_88,_12,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#c2410c,_0px_1px_3px_0px_rgba(234,_88,_12,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#c2410c,_0px_1px_2px_0px_rgba(234,_88,_12,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-[8px] gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-11 rounded-[12px] px-6 has-[>svg]:px-4",icon:"size-10"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:n,asChild:o=!1,...d}=e,c=o?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,l.cn)(i({variant:r,size:n,className:t})),...d})}},23736:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19856).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55640:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>c,Es:()=>p,L3:()=>u,c7:()=>x,lG:()=>i,rr:()=>m});var s=r(87183);r(12359);var a=r(9087),n=r(72346),l=r(1591);function i(e){let{...t}=e;return(0,s.jsx)(a.bL,{"data-slot":"dialog",...t})}function o(e){let{...t}=e;return(0,s.jsx)(a.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{className:t,...r}=e;return(0,s.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...r})}function c(e){let{className:t,children:r,showCloseButton:i=!0,...c}=e;return(0,s.jsxs)(o,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...c,children:[r,i&&(0,s.jsxs)(a.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...r})}function p(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...r})}function u(e){let{className:t,...r}=e;return(0,s.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",t),...r})}function m(e){let{className:t,...r}=e;return(0,s.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...r})}},55752:(e,t,r)=>{Promise.resolve().then(r.bind(r,94794))},62e3:(e,t,r)=>{"use strict";r.d(t,{CI:()=>l,Hh:()=>n,Jv:()=>a,wV:()=>i,yK:()=>s});let s=(0,r(33677).MB)({baseURL:"http://localhost:3000",fetchOptions:{credentials:"include"}}),{signIn:a,signUp:n,signOut:l,useSession:i}=s},91574:(e,t,r)=>{"use strict";r.d(t,{DE:()=>d,GJ:()=>i,cN:()=>o});var s=r(87183),a=r(12359),n=r(69228);let l=(0,a.createContext)(null);function i(e){let{children:t}=e,{refetch:r}=(0,n.cN)({skip:!0}),i=(0,a.useCallback)(async()=>{await r()},[r]);return(0,s.jsx)(l.Provider,{value:{refetchCustomer:i},children:t})}function o(e){let t=(0,n.cN)(e),r=(0,a.useContext)(l),s=(0,a.useCallback)(async()=>{let e=await t.refetch();return(null==r?void 0:r.refetchCustomer)&&await r.refetchCustomer(),e},[t,r]);return{...t,refetch:s}}function d(){let e=(0,a.useContext)(l);return e?e.refetchCustomer:async()=>{console.warn("useRefreshCustomer called outside of AutumnCustomerProvider")}}},94794:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>W});var s=r(87183),a=r(12359),n=r(69228),l=r(91574),i=r(1591),o=r(15693),d=r(18299),c=r(19725),x=r(25672),p=r(9568),u=r(98271),m="Switch",[h,f]=(0,c.A)(m),[g,b]=h(m),v=a.forwardRef((e,t)=>{let{__scopeSwitch:r,name:n,checked:l,defaultChecked:i,required:c,disabled:p,value:h="on",onCheckedChange:f,form:b,...v}=e,[_,w]=a.useState(null),N=(0,d.s)(t,e=>w(e)),k=a.useRef(!1),T=!_||b||!!_.closest("form"),[C,z]=(0,x.i)({prop:l,defaultProp:null!=i&&i,onChange:f,caller:m});return(0,s.jsxs)(g,{scope:r,checked:C,disabled:p,children:[(0,s.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":C,"aria-required":c,"data-state":j(C),"data-disabled":p?"":void 0,disabled:p,value:h,...v,ref:N,onClick:(0,o.m)(e.onClick,e=>{z(e=>!e),T&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),T&&(0,s.jsx)(y,{control:_,bubbles:!k.current,name:n,value:h,checked:C,required:c,disabled:p,form:b,style:{transform:"translateX(-100%)"}})]})});v.displayName=m;var _="SwitchThumb",w=a.forwardRef((e,t)=>{let{__scopeSwitch:r,...a}=e,n=b(_,r);return(0,s.jsx)(u.sG.span,{"data-state":j(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:t})});w.displayName=_;var y=a.forwardRef((e,t)=>{let{__scopeSwitch:r,control:n,checked:l,bubbles:i=!0,...o}=e,c=a.useRef(null),x=(0,d.s)(c,t),u=function(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(l),m=function(e){let[t,r]=a.useState(void 0);return(0,p.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let s,a;if(!Array.isArray(t)||!t.length)return;let n=t[0];if("borderBoxSize"in n){let e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;s=t.inlineSize,a=t.blockSize}else s=e.offsetWidth,a=e.offsetHeight;r({width:s,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(n);return a.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==l&&t){let r=new Event("click",{bubbles:i});t.call(e,l),e.dispatchEvent(r)}},[u,l,i]),(0,s.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...o,tabIndex:-1,ref:x,style:{...o.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}function N(e){let{className:t,...r}=e;return(0,s.jsx)(v,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-orange-500 data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...r,children:(0,s.jsx)(w,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-white pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}y.displayName="SwitchBubbleInput";var k=r(23736),T=r(6642),C=r(9944),z=r(55640);let A=e=>{let{scenario:t,product_name:r,recurring:a,current_product_name:n,next_cycle_at:l}=e,i=l?new Date(l).toLocaleDateString():void 0;switch(t){case"scheduled":return{title:(0,s.jsxs)("p",{children:[r," product already scheduled"]}),message:(0,s.jsxs)("p",{children:["You are currently on product ",n," and are scheduled to start ",r," on ",i,"."]})};case"active":return{title:(0,s.jsx)("p",{children:"Product already active"}),message:(0,s.jsx)("p",{children:"You are already subscribed to this product."})};case"new":if(a)return{title:(0,s.jsxs)("p",{children:["Subscribe to ",r]}),message:(0,s.jsxs)("p",{children:["By clicking confirm, you will be subscribed to ",r," and your card will be charged immediately."]})};return{title:(0,s.jsxs)("p",{children:["Purchase ",r]}),message:(0,s.jsxs)("p",{children:["By clicking confirm, you will purchase ",r," and your card will be charged immediately."]})};case"renew":return{title:(0,s.jsx)("p",{children:"Renew"}),message:(0,s.jsxs)("p",{children:["By clicking confirm, you will renew your subscription to"," ",r,"."]})};case"upgrade":return{title:(0,s.jsxs)("p",{children:["Upgrade to ",r]}),message:(0,s.jsxs)("p",{children:["By clicking confirm, you will upgrade to ",r," and your payment method will be charged immediately."]})};case"downgrade":return{title:(0,s.jsxs)("p",{children:["Downgrade to ",r]}),message:(0,s.jsxs)("p",{children:["By clicking confirm, your current subscription to"," ",n," will be cancelled and a new subscription to"," ",r," will begin on ",i,"."]})};case"cancel":return{title:(0,s.jsx)("p",{children:"Cancel"}),message:(0,s.jsxs)("p",{children:["By clicking confirm, your subscription to ",n," ","will end on ",i,"."]})};default:return{title:(0,s.jsx)("p",{children:"Change Subscription"}),message:(0,s.jsx)("p",{children:"You are about to change your subscription."})}}};function S(e){var t,r;let n,{attach:o,refetch:d}=(0,l.cN)(),[c,x]=(0,a.useState)(!1),[p,u]=(0,a.useState)((null==e||null==(t=e.preview)?void 0:t.options)||[]);if((0,a.useEffect)(()=>{var t;u((null==e||null==(t=e.preview)?void 0:t.options)||[])},[null==e||null==(r=e.preview)?void 0:r.options]),!e||!e.preview)return(0,s.jsx)(s.Fragment,{});let{open:m,setOpen:h,preview:f}=e,{items:g,due_today:b}=f,{title:v,message:_}=A(f);return(0,s.jsx)(z.lG,{open:m,onOpenChange:h,children:(0,s.jsxs)(z.Cf,{className:(0,i.cn)("p-0 pt-4 gap-0 text-foreground overflow-hidden text-sm"),children:[(0,s.jsx)(z.L3,{className:(0,i.cn)("px-6 mb-1 "),children:v}),(0,s.jsx)("div",{className:(0,i.cn)("px-6 mt-1 mb-4 text-muted-foreground"),children:_}),(g||p.length>0)&&(0,s.jsxs)("div",{className:"mb-6 px-6",children:[null==g?void 0:g.map(e=>(0,s.jsxs)(P,{children:[(0,s.jsx)("span",{className:"truncate flex-1",children:e.description}),(0,s.jsx)("span",{children:e.price})]},e.description)),null==p?void 0:p.map((e,t)=>(0,s.jsx)(E,{option:e,optionsInput:p,setOptionsInput:u,index:t},e.feature_name))]}),(0,s.jsxs)(z.Es,{className:"flex flex-col sm:flex-row justify-between gap-x-4 py-2 pl-6 pr-3 bg-secondary border-t shadow-inner",children:[b&&(0,s.jsxs)(B,{children:[(0,s.jsx)("span",{children:"Due Today"}),(0,s.jsx)("span",{children:new Intl.NumberFormat("en-US",{style:"currency",currency:b.currency}).format((n=(null==b?void 0:b.price)||0,p.forEach(e=>{e.price&&e.quantity&&(n+=e.price*(e.quantity/e.billing_units))}),n))})]}),(0,s.jsx)("button",{onClick:async()=>{x(!0);try{await o({productId:f.product_id,options:p.map(e=>({featureId:e.feature_id,quantity:e.quantity||0})),returnUrl:window.location.origin+"/dashboard",successUrl:window.location.origin+"/dashboard",cancelUrl:window.location.origin+"/plans"}),h(!1),await d(),"downgrade"===f.scenario?(alert("Downgrade scheduled! Your plan will change to ".concat(f.product_name," on ").concat(new Date(f.next_cycle_at).toLocaleDateString(),".")),window.location.href="/dashboard"):"upgrade"===f.scenario&&(alert("Upgrade successful! You're now on the ".concat(f.product_name," plan.")),window.location.href="/dashboard")}catch(e){console.error("Error attaching product:",e),alert("An error occurred. Please try again or contact support.")}finally{x(!1)}},disabled:c,className:"btn-firecrawl-orange inline-flex items-center justify-center whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 h-8 px-4 min-w-16 gap-2 disabled:pointer-events-none disabled:opacity-50",children:c?(0,s.jsx)(k.A,{className:"w-4 h-4 animate-spin"}):(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("span",{className:"whitespace-nowrap flex gap-1",children:"Confirm"})})})]})]})})}let P=e=>{let{children:t,className:r,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col pb-4 sm:pb-0 gap-1 sm:flex-row justify-between sm:h-7 sm:gap-2 sm:items-center",r),...a,children:t})},E=e=>{let{className:t,option:r,optionsInput:a,setOptionsInput:n,index:l,...i}=e,{feature_name:o,billing_units:d,quantity:c,price:x}=r;return(0,s.jsxs)(P,{children:[(0,s.jsx)("span",{children:o}),(0,s.jsx)(I,{value:c?c/d:"",onChange:e=>{let t=[...a];t[l].quantity=parseInt(e.target.value)*d,n(t)},children:(0,s.jsxs)("span",{className:"",children:["\xd7 $",x," per ",1===d?" ":d," ",o]})},o)]},o)},I=e=>{let{children:t,onChange:r,value:a,className:n,...l}=e,o=Number(a)||0,d=e=>{r({target:{value:String(e)}})};return(0,s.jsxs)("div",{className:(0,i.cn)(n,"flex flex-row items-center gap-4"),...l,children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(C.$,{variant:"outline",size:"icon",onClick:()=>o>0&&d(o-1),disabled:o<=0,className:"h-6 w-6 pb-0.5",children:"-"}),(0,s.jsx)("span",{className:"w-8 text-center text-foreground",children:o}),(0,s.jsx)(C.$,{variant:"outline",size:"icon",onClick:()=>d(o+1),className:"h-6 w-6 pb-0.5",children:"+"})]}),t]})},B=e=>{let{children:t}=e;return(0,s.jsx)("div",{className:"w-full font-semibold flex justify-between items-center",children:t})},F=e=>{let{scenario:t,free_trial:r}=e;if(r&&r.trial_available)return{buttonText:(0,s.jsx)("p",{children:"Start Free Trial"})};switch(t){case"scheduled":return{buttonText:(0,s.jsx)("p",{children:"Plan Scheduled"})};case"active":return{buttonText:(0,s.jsx)("p",{children:"Current Plan"})};case"new":var a;if(null==(a=e.properties)?void 0:a.is_one_off)return{buttonText:(0,s.jsx)("p",{children:"Purchase"})};return{buttonText:(0,s.jsx)("p",{children:"Get started"})};case"renew":return{buttonText:(0,s.jsx)("p",{children:"Renew"})};case"upgrade":return{buttonText:(0,s.jsx)("p",{children:"Upgrade"})};case"downgrade":return{buttonText:(0,s.jsx)("p",{children:"Downgrade"})};case"cancel":return{buttonText:(0,s.jsx)("p",{children:"Cancel Plan"})};default:return{buttonText:(0,s.jsx)("p",{children:"Get Started"})}}};function R(e){let{productDetails:t}=e,{attach:r}=(0,l.cN)(),[o,d]=(0,a.useState)(!1),{products:c,isLoading:x,error:p}=(0,n.AC)({productDetails:t});if(x)return(0,s.jsx)("div",{className:"w-full h-full flex justify-center items-center min-h-[300px]",children:(0,s.jsx)(k.A,{className:"w-6 h-6 text-zinc-400 animate-spin"})});if(p)return(0,s.jsx)("div",{children:" Something went wrong..."});let u=Array.from(new Set(null==c?void 0:c.map(e=>{var t;return null==(t=e.properties)?void 0:t.interval_group}).filter(e=>!!e))).length>1;return(0,s.jsx)("div",{className:(0,i.cn)("root"),children:c&&(0,s.jsx)(G,{products:c,isAnnualToggle:o,setIsAnnualToggle:d,multiInterval:u,children:c.filter(e=>{var t,r,s;return(null==(t=e.properties)?!void 0:!t.interval_group)||!u||(o?(null==(r=e.properties)?void 0:r.interval_group)==="year":(null==(s=e.properties)?void 0:s.interval_group)==="month")}).map((e,t)=>(0,s.jsx)(L,{productId:e.id,buttonProps:{disabled:"active"===e.scenario||"scheduled"===e.scenario,onClick:async()=>{var t,s;e.id?await r({productId:e.id,dialog:S,returnUrl:window.location.origin+"/dashboard",successUrl:window.location.origin+"/dashboard",cancelUrl:window.location.origin+"/pricing"}):(null==(t=e.display)?void 0:t.button_url)&&window.open(null==(s=e.display)?void 0:s.button_url,"_blank")}}},t))})})}let U=(0,a.createContext)({isAnnualToggle:!1,setIsAnnualToggle:()=>{},products:[],showFeatures:!0}),D=e=>{let t=(0,a.useContext)(U);if(void 0===t)throw Error("".concat(e," must be used within <PricingTable />"));return t},G=e=>{let{children:t,products:r,showFeatures:a=!0,className:n,isAnnualToggle:l,setIsAnnualToggle:o,multiInterval:d}=e;if(!r)throw Error("products is required in <PricingTable />");if(0===r.length)return(0,s.jsx)(s.Fragment,{});let c=null==r?void 0:r.some(e=>{var t;return null==(t=e.display)?void 0:t.recommend_text});return(0,s.jsx)(U.Provider,{value:{isAnnualToggle:l,setIsAnnualToggle:o,products:r,showFeatures:a},children:(0,s.jsxs)("div",{className:(0,i.cn)("flex items-center flex-col",c&&"!py-10"),children:[d&&(0,s.jsx)("div",{className:(0,i.cn)(r.some(e=>{var t;return null==(t=e.display)?void 0:t.recommend_text})&&"mb-8"),children:(0,s.jsx)(M,{isAnnualToggle:l,setIsAnnualToggle:o})}),(0,s.jsx)("div",{className:(0,i.cn)("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-[repeat(auto-fit,minmax(200px,1fr))] w-full gap-2",n),children:t})]})})},L=e=>{var t,r,a;let{productId:n,className:l,buttonProps:o}=e,{products:d,showFeatures:c}=D("PricingCard"),x=d.find(e=>e.id===n);if(!x)throw Error("Product with id ".concat(n," not found"));let{name:p,display:u,items:m}=x,{buttonText:h}=F(x),f=null!=u&&!!u.recommend_text,g=(null==(t=x.properties)?void 0:t.is_free)?{primary_text:"Free"}:x.items[0].display,b=(null==(r=x.properties)?void 0:r.is_free)?x.items:x.items.slice(1);return(0,s.jsxs)("div",{className:(0,i.cn)(" w-full h-full py-6 text-foreground border rounded-lg shadow-sm max-w-xl",f&&"lg:-translate-y-6 lg:shadow-lg dark:shadow-zinc-800/80 lg:h-[calc(100%+48px)] bg-secondary/40",l),children:[(null==u?void 0:u.recommend_text)&&(0,s.jsx)(Y,{recommended:null==u?void 0:u.recommend_text}),(0,s.jsxs)("div",{className:(0,i.cn)("flex flex-col h-full flex-grow",f&&"lg:translate-y-6"),children:[(0,s.jsxs)("div",{className:"h-full",children:[(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"pb-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold px-6 truncate",children:(null==u?void 0:u.name)||p}),(null==u?void 0:u.description)&&(0,s.jsx)("div",{className:"text-sm text-muted-foreground px-6 h-8",children:(0,s.jsx)("p",{className:"line-clamp-2",children:null==u?void 0:u.description})})]}),(0,s.jsx)("div",{className:"mb-2",children:(0,s.jsx)("h3",{className:"font-semibold h-16 flex px-6 items-center border-y mb-4 bg-secondary/40",children:(0,s.jsxs)("div",{className:"line-clamp-2",children:[null==g?void 0:g.primary_text," ",(null==g?void 0:g.secondary_text)&&(0,s.jsx)("span",{className:"font-normal text-muted-foreground mt-1",children:null==g?void 0:g.secondary_text})]})})})]}),c&&b.length>0&&(0,s.jsx)("div",{className:"flex-grow px-6 mb-6",children:(0,s.jsx)(q,{items:b,showIcon:!0,everythingFrom:null==(a=x.display)?void 0:a.everything_from})})]}),(0,s.jsx)("div",{className:(0,i.cn)(" px-6 ",f&&"lg:-translate-y-12"),children:(0,s.jsx)(O,{recommended:null!=u&&!!u.recommend_text,...o,children:(null==u?void 0:u.button_text)||h})})]})]})},q=e=>{let{items:t,showIcon:r=!0,everythingFrom:a,className:n}=e;return(0,s.jsxs)("div",{className:(0,i.cn)("flex-grow",n),children:[a&&(0,s.jsxs)("p",{className:"text-sm mb-4",children:["Everything from ",a,", plus:"]}),(0,s.jsx)("div",{className:"space-y-3",children:t.map((e,t)=>{var a,n,l;return(0,s.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[r&&(0,s.jsx)(T.A,{className:"h-4 w-4 text-primary flex-shrink-0 mt-0.5"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{children:null==(a=e.display)?void 0:a.primary_text}),(null==(n=e.display)?void 0:n.secondary_text)&&(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:null==(l=e.display)?void 0:l.secondary_text})]})]},t)})})]})},O=a.forwardRef((e,t)=>{let{recommended:r,children:n,className:l,onClick:o,...d}=e,[c,x]=(0,a.useState)(!1),p=async e=>{x(!0);try{await (null==o?void 0:o(e))}catch(e){console.error(e)}finally{x(!1)}};return(0,s.jsx)("button",{className:(0,i.cn)("w-full py-3 px-4 group overflow-hidden relative transition-all duration-300 border rounded-[10px] inline-flex items-center justify-center whitespace-nowrap text-sm font-medium disabled:pointer-events-none disabled:opacity-50",r?"btn-firecrawl-orange":"btn-firecrawl-default",l),...d,"data-primary":r?"true":"false",ref:t,disabled:c||d.disabled,onClick:p,children:c?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)(k.A,{className:"h-4 w-4 animate-spin mr-2"}),(0,s.jsx)("span",{children:"Loading checkout..."})]}):(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsx)("span",{children:n}),(0,s.jsx)("span",{className:"text-sm transition-transform duration-300 group-hover:translate-x-1",children:"→"})]})})});O.displayName="PricingCardButton";let M=e=>{let{isAnnualToggle:t,setIsAnnualToggle:r}=e;return(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"Monthly"}),(0,s.jsx)(N,{id:"annual-billing",checked:t,onCheckedChange:r}),(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"Annual"})]})},Y=e=>{let{recommended:t}=e;return(0,s.jsx)("div",{className:"bg-black absolute border text-white text-sm font-medium lg:rounded-full px-3 lg:py-0.5 lg:top-4 lg:right-4 top-[-1px] right-[-1px] rounded-bl-lg",children:t})};var $=r(8449);function H(e){let{products:t}=e,[r,n]=(0,a.useState)(null),l=(0,$.useRouter)(),o=async e=>{n(e),l.push("/register?plan=".concat(e))},d=t.some(e=>e.recommendText);return(0,s.jsx)("div",{className:"flex items-center flex-col",children:(0,s.jsx)("div",{className:(0,i.cn)("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-[repeat(auto-fit,minmax(200px,1fr))] w-full gap-2",d&&"!py-10"),children:t.map(e=>(0,s.jsxs)("div",{className:(0,i.cn)("w-full h-full py-6 text-foreground border rounded-lg shadow-sm max-w-xl",e.recommendText&&"lg:-translate-y-6 lg:shadow-lg dark:shadow-zinc-800/80 lg:h-[calc(100%+48px)] bg-secondary/40"),children:[e.recommendText&&(0,s.jsx)("div",{className:"bg-black absolute border text-white text-sm font-medium lg:rounded-full px-3 lg:py-0.5 lg:top-4 lg:right-4 top-[-1px] right-[-1px] rounded-bl-lg",children:e.recommendText}),(0,s.jsxs)("div",{className:(0,i.cn)("flex flex-col h-full flex-grow",e.recommendText&&"lg:translate-y-6"),children:[(0,s.jsxs)("div",{className:"h-full",children:[(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"pb-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold px-6 truncate",children:e.name||e.id}),e.description&&(0,s.jsx)("div",{className:"text-sm text-muted-foreground px-6 h-8",children:(0,s.jsx)("p",{className:"line-clamp-2",children:e.description})})]}),(0,s.jsx)("div",{className:"mb-2",children:(0,s.jsx)("h3",{className:"font-semibold h-16 flex px-6 items-center border-y mb-4 bg-secondary/40",children:(0,s.jsxs)("div",{className:"line-clamp-2",children:[e.price.primaryText," ",e.price.secondaryText&&(0,s.jsx)("span",{className:"font-normal text-muted-foreground mt-1",children:e.price.secondaryText})]})})})]}),e.items.length>0&&(0,s.jsx)("div",{className:"flex-grow px-6 mb-6",children:(0,s.jsx)("div",{className:"space-y-3",children:e.items.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start gap-2 text-sm",children:[(0,s.jsx)(T.A,{className:"h-4 w-4 text-primary flex-shrink-0 mt-0.5"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{children:e.primaryText}),e.secondaryText&&(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:e.secondaryText})]})]},t))})})]}),(0,s.jsx)("div",{className:(0,i.cn)("px-6",e.recommendText&&"lg:-translate-y-12"),children:(0,s.jsx)("button",{onClick:()=>o(e.id),disabled:r===e.id,className:(0,i.cn)("w-full py-3 px-4 group overflow-hidden relative transition-all duration-300 border rounded-[10px] inline-flex items-center justify-center whitespace-nowrap text-sm font-medium disabled:pointer-events-none disabled:opacity-50",e.recommendText?"btn-firecrawl-orange":"btn-firecrawl-default"),children:r===e.id?(0,s.jsx)(k.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between w-full transition-transform duration-300 group-hover:-translate-y-[150%]",children:[(0,s.jsx)("span",{children:"Get Started"}),(0,s.jsx)("span",{className:"text-sm",children:"→"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between w-full absolute inset-x-0 px-4 translate-y-[150%] transition-transform duration-300 group-hover:translate-y-0",children:[(0,s.jsx)("span",{children:"Get Started"}),(0,s.jsx)("span",{className:"text-sm",children:"→"})]})]})})})]})]},e.id))})})}var V=r(62e3);let J=[{id:"free",name:"Free",description:"Perfect for trying out our service",price:{primaryText:"Free",secondaryText:"No credit card required"},items:[{primaryText:"10 messages per month",secondaryText:"AI-powered chat responses"},{primaryText:"Community support",secondaryText:"Get help from our community"},{primaryText:"Basic features",secondaryText:"Essential tools to get started"}]},{id:"pro",name:"Pro",description:"For all your messaging needs",recommendText:"Most Popular",price:{primaryText:"$10/month",secondaryText:"billed monthly"},items:[{primaryText:"100 messages per month",secondaryText:"AI-powered chat responses"},{primaryText:"Premium support",secondaryText:"Get help from our team"},{primaryText:"Priority access",secondaryText:"Be first to try new features"}]}];function W(){var e;let{data:t}=(0,V.wV)();return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-b from-white to-gray-50 py-12",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h1",{className:"text-[3rem] lg:text-[4.5rem] font-bold tracking-tight mb-6",children:(0,s.jsx)("span",{className:"bg-gradient-to-tr from-orange-600 to-orange-400 bg-clip-text text-transparent",children:"Simple, transparent pricing"})}),(0,s.jsx)("p",{className:"text-xl text-zinc-600 max-w-2xl mx-auto",children:"Choose the perfect plan for your needs. Always flexible to scale up or down."}),t&&(0,s.jsxs)("p",{className:"text-sm text-zinc-500 mt-4",children:["Logged in as: ",null==(e=t.user)?void 0:e.email]})]}),(0,s.jsx)("div",{className:"bg-white rounded-[20px] shadow-xl p-8 border border-zinc-200",children:t?(0,s.jsx)(R,{}):(0,s.jsx)(H,{products:J})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4350,3677,6887,73,6504,6703,588,7358],()=>t(55752)),_N_E=e.O()}]);