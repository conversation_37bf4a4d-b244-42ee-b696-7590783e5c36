(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[595],{58815:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>eG});var s=a(87183),r=a(12359),n=a(1591);let l=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",a),...r})});l.displayName="Card";let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...r})});o.displayName="CardHeader";let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",a),...r})});i.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",a),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",a),...r})});d.displayName="CardContent",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",a),...r})}).displayName="CardFooter";var p=a(39878),x=a(13419);let m={url:"",urlValid:null,error:null,loading:!1,analyzing:!1,preparingAnalysis:!1,scrapingCompetitors:!1,company:null,analysis:null,showInput:!0,showCompanyCard:!1,showPromptsList:!1,showCompetitors:!1,customPrompts:[],removedDefaultPrompts:[],analyzingPrompts:[],identifiedCompetitors:[],availableProviders:[],analysisProgress:{stage:"initializing",progress:0,message:"",competitors:[],prompts:[],partialResults:[]},promptCompletionStatus:{},analysisTiles:[],statusUpdateCount:0,activeResultsTab:"matrix",expandedPromptIndex:null,currentPeriod:!0,showAddPromptModal:!1,showAddCompetitorModal:!1,newPromptText:"",newCompetitorName:"",newCompetitorUrl:""};function u(e,t){switch(t.type){case"SET_URL":return{...e,url:t.payload};case"SET_URL_VALID":return{...e,urlValid:t.payload};case"SET_ERROR":return{...e,error:t.payload};case"SET_LOADING":return{...e,loading:t.payload};case"SET_ANALYZING":return{...e,analyzing:t.payload};case"SET_PREPARING_ANALYSIS":return{...e,preparingAnalysis:t.payload};case"SET_COMPANY":return{...e,company:t.payload};case"SET_SHOW_INPUT":return{...e,showInput:t.payload};case"SET_SHOW_COMPANY_CARD":return{...e,showCompanyCard:t.payload};case"SET_SHOW_PROMPTS_LIST":return{...e,showPromptsList:t.payload};case"SET_SHOW_COMPETITORS":return{...e,showCompetitors:t.payload};case"SET_CUSTOM_PROMPTS":return{...e,customPrompts:t.payload};case"ADD_CUSTOM_PROMPT":return{...e,customPrompts:[...e.customPrompts,t.payload]};case"REMOVE_DEFAULT_PROMPT":return{...e,removedDefaultPrompts:[...e.removedDefaultPrompts,t.payload]};case"SET_AVAILABLE_PROVIDERS":return{...e,availableProviders:t.payload};case"SET_IDENTIFIED_COMPETITORS":return{...e,identifiedCompetitors:t.payload};case"REMOVE_COMPETITOR":return{...e,identifiedCompetitors:e.identifiedCompetitors.filter((e,a)=>a!==t.payload)};case"ADD_COMPETITOR":return{...e,identifiedCompetitors:[...e.identifiedCompetitors,t.payload]};case"UPDATE_COMPETITOR_METADATA":return{...e,identifiedCompetitors:e.identifiedCompetitors.map((e,a)=>a===t.payload.index?{...e,metadata:t.payload.metadata}:e)};case"SET_ANALYSIS_PROGRESS":return{...e,analysisProgress:t.payload};case"UPDATE_ANALYSIS_PROGRESS":return{...e,analysisProgress:{...e.analysisProgress,...t.payload}};case"SET_PROMPT_COMPLETION_STATUS":return{...e,promptCompletionStatus:t.payload};case"UPDATE_PROMPT_STATUS":let{prompt:a,provider:s,status:r}=t.payload,n=a.trim();return{...e,promptCompletionStatus:{...e.promptCompletionStatus,[n]:{...e.promptCompletionStatus[n],[s]:r}},statusUpdateCount:e.statusUpdateCount+1};case"SET_ANALYZING_PROMPTS":return{...e,analyzingPrompts:t.payload};case"SET_ANALYSIS_TILES":return{...e,analysisTiles:t.payload};case"UPDATE_ANALYSIS_TILE":return{...e,analysisTiles:e.analysisTiles.map((e,a)=>a===t.payload.index?t.payload.tile:e)};case"SET_ANALYSIS":return{...e,analysis:t.payload};case"SET_ACTIVE_RESULTS_TAB":return{...e,activeResultsTab:t.payload};case"SET_EXPANDED_PROMPT_INDEX":return{...e,expandedPromptIndex:t.payload};case"TOGGLE_MODAL":if("addPrompt"===t.payload.modal)return{...e,showAddPromptModal:t.payload.show};return{...e,showAddCompetitorModal:t.payload.show};case"SET_NEW_PROMPT_TEXT":return{...e,newPromptText:t.payload};case"SET_NEW_COMPETITOR":return{...e,...void 0!==t.payload.name&&{newCompetitorName:t.payload.name},...void 0!==t.payload.url&&{newCompetitorUrl:t.payload.url}};case"RESET_STATE":return m;case"SCRAPE_SUCCESS":return{...e,company:t.payload,showInput:!1,loading:!1,error:null};case"ANALYSIS_COMPLETE":return{...e,analysis:t.payload,analyzing:!1};default:return e}}function h(e){try{let t=new URL(e.startsWith("http")?e:"https://".concat(e)).hostname.split(".");if(t.length<2)return!1;let a=t[t.length-1];if(a.length<2||!/^[a-zA-Z]+$/.test(a))return!1;for(let e of t)if(!/^[a-zA-Z0-9-]+$/.test(e)||e.startsWith("-")||e.endsWith("-"))return!1;return!0}catch(e){return console.error("URL validation error:",e),!1}}function g(e){if(!e)return;let t=e.trim().replace(/\/$/,"");t.startsWith("http://")||t.startsWith("https://")||(t="https://"+t);try{let e=new URL(t);return e.hostname+("/"!==e.pathname?e.pathname:"")}catch(e){return}}function f(e){let t=e.toLowerCase().trim();return({"amazon web services":"aws","amazon web services (aws)":"aws","amazon aws":"aws","microsoft azure":"azure","google cloud platform":"google cloud","google cloud platform (gcp)":"google cloud",gcp:"google cloud","digital ocean":"digitalocean","beautiful soup":"beautifulsoup","bright data":"brightdata"})[t]||t}function b(e){return({apify:"apify.com",scrapy:"scrapy.org",octoparse:"octoparse.com",parsehub:"parsehub.com",diffbot:"diffbot.com","import.io":"import.io","bright data":"brightdata.com",zyte:"zyte.com",puppeteer:"pptr.dev",playwright:"playwright.dev",selenium:"selenium.dev","beautiful soup":"pypi.org/project/beautifulsoup4",scrapfly:"scrapfly.io",crawlbase:"crawlbase.com",webharvy:"webharvy.com",openai:"openai.com",anthropic:"anthropic.com","google ai":"ai.google","microsoft azure":"azure.microsoft.com","ibm watson":"ibm.com/watson","amazon aws":"aws.amazon.com",perplexity:"perplexity.ai",claude:"anthropic.com",chatgpt:"openai.com",gemini:"gemini.google.com",salesforce:"salesforce.com",hubspot:"hubspot.com",zendesk:"zendesk.com",slack:"slack.com",atlassian:"atlassian.com","monday.com":"monday.com",notion:"notion.so",airtable:"airtable.com",shopify:"shopify.com",woocommerce:"woocommerce.com",magento:"magento.com",bigcommerce:"bigcommerce.com",squarespace:"squarespace.com",wix:"wix.com",vercel:"vercel.com",netlify:"netlify.com",aws:"aws.amazon.com","google cloud":"cloud.google.com",azure:"azure.microsoft.com",heroku:"heroku.com",digitalocean:"digitalocean.com",cloudflare:"cloudflare.com"})[e.toLowerCase().trim()]}function y(e){var t;let a=(e.description||"").toLowerCase(),s=((null==(t=e.scrapedData)?void 0:t.mainContent)||"").toLowerCase(),r=(e.name||"").toLowerCase();if(a.includes("beverage")||a.includes("drink")||a.includes("cola")||a.includes("soda")||s.includes("beverage")||s.includes("refreshment")||r.includes("coca")||r.includes("pepsi"))return"beverage brand";if(a.includes("restaurant")||a.includes("food")||a.includes("dining")||s.includes("menu")||s.includes("restaurant"))return"restaurant";if(a.includes("retail")||a.includes("store")||a.includes("shopping")||s.includes("retail")||s.includes("shopping"))return"retailer";if(a.includes("bank")||a.includes("financial")||a.includes("finance")||s.includes("banking")||s.includes("financial services"))return"financial service";if(a.includes("scraping")||a.includes("crawl")||a.includes("extract")||s.includes("web scraping")||s.includes("data extraction"))return"web scraper";else if(a.includes("ai")||a.includes("artificial intelligence")||a.includes("llm")||s.includes("machine learning")||s.includes("ai-powered"))return"AI tool";else if(a.includes("hosting")||a.includes("deploy")||a.includes("cloud")||s.includes("deployment")||s.includes("infrastructure"))return"hosting platform";else if(a.includes("e-commerce")||a.includes("online store")||a.includes("marketplace"))return"e-commerce platform";else if(a.includes("software")||a.includes("saas")||a.includes("platform"))return"software";return"brand"}var _=a(27589),v=a(72070),j=a(41509),w=a(65511),N=a(37811);let E={openai:!0,anthropic:!0,google:!1,perplexity:!0},S={openai:{id:"openai",name:"OpenAI",icon:"\uD83E\uDD16",envKey:"OPENAI_API_KEY",enabled:E.openai,models:[{id:"gpt-4o",name:"GPT-4 Optimized",maxTokens:128e3,supportsFunctionCalling:!0,supportsStructuredOutput:!0,supportsWebSearch:!1},{id:"gpt-4o-mini",name:"GPT-4 Mini",maxTokens:128e3,supportsFunctionCalling:!0,supportsStructuredOutput:!0,supportsWebSearch:!0},{id:"gpt-4-turbo",name:"GPT-4 Turbo",maxTokens:128e3,supportsFunctionCalling:!0,supportsStructuredOutput:!0,supportsWebSearch:!1}],defaultModel:"gpt-4o",capabilities:{webSearch:!0,functionCalling:!0,structuredOutput:!0,streamingResponse:!0,maxRequestsPerMinute:500},getModel:(e,t)=>{if(!N.env.OPENAI_API_KEY)return null;let a=e||S.openai.defaultModel;return(null==t?void 0:t.useWebSearch)&&"gpt-4o-mini"===a?_.N.responses(a):(0,_.N)(a)},isConfigured:()=>!!N.env.OPENAI_API_KEY},anthropic:{id:"anthropic",name:"Anthropic",icon:"\uD83E\uDDE0",envKey:"ANTHROPIC_API_KEY",enabled:E.anthropic,models:[{id:"claude-4-sonnet-20250514",name:"Claude 4 Sonnet",maxTokens:2e5,supportsFunctionCalling:!0,supportsStructuredOutput:!0,supportsWebSearch:!1},{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",maxTokens:2e5,supportsFunctionCalling:!0,supportsStructuredOutput:!0,supportsWebSearch:!1},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",maxTokens:2e5,supportsFunctionCalling:!0,supportsStructuredOutput:!0,supportsWebSearch:!1}],defaultModel:"claude-4-sonnet-20250514",capabilities:{webSearch:!1,functionCalling:!0,structuredOutput:!0,streamingResponse:!0,maxRequestsPerMinute:50},getModel:e=>N.env.ANTHROPIC_API_KEY?(0,v.P)(e||S.anthropic.defaultModel):null,isConfigured:()=>!!N.env.ANTHROPIC_API_KEY},google:{id:"google",name:"Google",icon:"\uD83C\uDF1F",envKey:"GOOGLE_GENERATIVE_AI_API_KEY",enabled:E.google,models:[{id:"gemini-2.5-pro",name:"Gemini 2.5 Pro",maxTokens:1e6,supportsFunctionCalling:!0,supportsStructuredOutput:!0,supportsWebSearch:!0},{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",maxTokens:1e6,supportsFunctionCalling:!0,supportsStructuredOutput:!0,supportsWebSearch:!0},{id:"gemini-2.0-flash-exp",name:"Gemini 2.0 Flash Experimental",maxTokens:1e6,supportsFunctionCalling:!0,supportsStructuredOutput:!0,supportsWebSearch:!0}],defaultModel:"gemini-2.5-pro",capabilities:{webSearch:!0,functionCalling:!0,structuredOutput:!0,streamingResponse:!0,maxRequestsPerMinute:60},getModel:(e,t)=>N.env.GOOGLE_GENERATIVE_AI_API_KEY?(0,j.q)(e||S.google.defaultModel,{useSearchGrounding:(null==t?void 0:t.useWebSearch)||!1}):null,isConfigured:()=>!!N.env.GOOGLE_GENERATIVE_AI_API_KEY},perplexity:{id:"perplexity",name:"Perplexity",icon:"\uD83D\uDD0D",envKey:"PERPLEXITY_API_KEY",enabled:E.perplexity,models:[{id:"sonar-pro",name:"Sonar Pro",maxTokens:127e3,supportsFunctionCalling:!1,supportsStructuredOutput:!1,supportsWebSearch:!0},{id:"sonar",name:"Sonar",maxTokens:127e3,supportsFunctionCalling:!1,supportsStructuredOutput:!1,supportsWebSearch:!0}],defaultModel:"sonar-pro",capabilities:{webSearch:!0,functionCalling:!1,structuredOutput:!1,streamingResponse:!0,maxRequestsPerMinute:20},getModel:e=>N.env.PERPLEXITY_API_KEY?(0,w.b)(e||S.perplexity.defaultModel):null,isConfigured:()=>!!N.env.PERPLEXITY_API_KEY}};function A(){return Object.values(S).filter(e=>e.enabled)}var C=a(18513),T=a(56949),P=a(74290),O=a(62e3),R=a(4579),k=a(23736);function I(e){let{url:t,urlValid:a,loading:r,analyzing:n,onUrlChange:l,onSubmit:o}=e;return(0,s.jsx)("div",{className:"flex items-center justify-center animate-panel-in pb-12",children:(0,s.jsx)("div",{className:"w-full max-w-5xl px-6",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(R.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-black"}),(0,s.jsx)("input",{type:"text",className:"w-full pl-12 pr-16 h-14 text-base border-2 rounded-xl focus:outline-none focus:ring-2 transition-all ".concat(!1===a?"border-red-300 focus:ring-red-500 focus:border-transparent":!0===a?"border-orange-300 focus:ring-orange-500 focus:border-transparent":"border-gray-300 focus:ring-orange-500 focus:border-transparent"),placeholder:"Enter your website URL (e.g., example.com)",value:t,onChange:e=>l(e.target.value),onKeyDown:e=>{"Enter"!==e.key||r||n||!t||o()},onFocus:e=>{t||(e.target.placeholder="example.com")},onBlur:e=>{e.target.placeholder="Enter your website URL (e.g., example.com)"},disabled:r||n}),(0,s.jsx)("button",{onClick:o,disabled:r||n||!t||!1===a,className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-10 w-10 rounded-lg flex items-center justify-center transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 bg-[#36322F] hover:bg-[#4a4542] disabled:bg-gray-300 disabled:hover:bg-gray-300","aria-label":"Analyze website",children:r?(0,s.jsx)(k.A,{className:"h-5 w-5 animate-spin text-white"}):(0,s.jsx)("svg",{className:"h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})})]})})})}let M=(0,a(48287).F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function L(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{className:(0,n.cn)(M({variant:a}),t),...r})}var D=a(94497),z=a(39604),U=a(26475),F=a(37418),G=a(57220);function W(e){var t;let{company:a,onAnalyze:n,analyzing:o,showCompetitors:i=!1,identifiedCompetitors:c=[],onRemoveCompetitor:d,onAddCompetitor:p,onContinueToAnalysis:x}=e,[m,u]=r.useState(!1),[h,g]=r.useState(!1),f=e=>{if(!e)return!1;try{return new URL(e),!0}catch(e){return!1}},b=f(a.logo)?a.logo:null,y=f(a.favicon)?a.favicon:null;return(0,s.jsxs)(l,{className:"p-2 bg-card text-card-foreground gap-6 rounded-xl border py-6 shadow-sm border-gray-200 overflow-hidden transition-all hover:shadow-lg",children:[(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsxs)("div",{className:"relative w-80 h-48 ml-4 overflow-hidden",children:[b&&!m?(0,s.jsx)("div",{className:"absolute inset-0 pr-4 py-4",children:(0,s.jsx)(G.default,{src:b,alt:"",fill:!0,className:"object-contain",sizes:"320px",onError:()=>u(!0)})}):(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)("div",{className:"relative h-16 w-16 rounded-xl bg-white/80 shadow-md border border-gray-200 flex items-center justify-center p-2",children:y&&!h?(0,s.jsx)(G.default,{src:y,alt:"".concat(a.name," logo"),width:32,height:32,className:"object-contain w-8 h-8",onError:()=>g(!0)}):(0,s.jsx)(D.A,{className:"h-8 w-8 text-gray-400"})})}),(0,s.jsx)("a",{href:a.url,target:"_blank",rel:"noopener noreferrer",className:"absolute top-4 right-4 p-2 rounded-lg bg-white/90 backdrop-blur-sm hover:bg-white transition-all shadow-md group",children:(0,s.jsx)(z.A,{className:"h-4 w-4 text-gray-600 group-hover:text-gray-900"})})]}),(0,s.jsxs)("div",{className:"flex-1 p-8",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:a.name}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[a.industry&&(0,s.jsx)(L,{variant:"secondary",children:a.industry}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 flex items-center gap-1",children:[(0,s.jsx)(R.A,{className:"h-3 w-3"}),new URL(a.url).hostname]})]})]}),(0,s.jsx)("button",{onClick:n,disabled:o,className:"h-9 rounded-[10px] text-sm font-medium flex items-center transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 bg-[#36322F] text-[#fff] hover:bg-[#4a4542] disabled:bg-[#8c8885] disabled:hover:bg-[#8c8885] [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#171310,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(58,_33,_8,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#171310,_0px_1px_3px_0px_rgba(58,_33,_8,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#171310,_0px_1px_2px_0px_rgba(58,_33,_8,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100 px-4 py-1",children:o?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"h-4 w-4 rounded-full border-2 border-white border-t-transparent animate-spin mr-2"}),"Analyzing..."]}):"Identify Competitors"})]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4 line-clamp-2",children:a.description}),(null==(t=a.scrapedData)?void 0:t.keywords)&&a.scrapedData.keywords.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[a.scrapedData.keywords.slice(0,6).map((e,t)=>(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:e},t)),a.scrapedData.keywords.length>6&&(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["+",a.scrapedData.keywords.length-6," more"]})]})]})]}),i&&c.length>0&&(0,s.jsx)("div",{className:"border-t border-gray-200",children:(0,s.jsxs)("div",{className:"px-8 py-6",children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Competitors"}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["We'll compare ",a.name," against these ",c.length," competitors"]})]})}),(0,s.jsx)("div",{className:"grid grid-cols-3 gap-4",children:c.map((e,t)=>(0,s.jsxs)("div",{className:"group relative bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all opacity-0 animate-fade-up",style:{animationDelay:"".concat(50*t,"ms"),animationFillMode:"forwards",animationDuration:"400ms"},children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 flex-shrink-0",children:e.url?(0,s.jsx)("img",{src:"https://www.google.com/s2/favicons?domain=".concat(e.url,"&sz=64"),alt:"",className:"w-10 h-10 rounded",onError:e=>{e.currentTarget.style.display="none";let t=document.createElement("div");t.className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center",t.innerHTML='<svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>',e.currentTarget.parentElement.appendChild(t)}}):(0,s.jsx)("div",{className:"w-10 h-10 bg-gray-100 rounded flex items-center justify-center",children:(0,s.jsx)(D.A,{className:"w-5 h-5 text-gray-400"})})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("span",{className:"font-medium text-gray-900 text-sm",children:e.name}),e.url&&(0,s.jsx)("a",{href:e.url.startsWith("http")?e.url:"https://".concat(e.url),target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(z.A,{className:"w-3 h-3"})})]}),e.url&&(0,s.jsx)("p",{className:"text-xs text-gray-500 truncate mt-0.5",children:e.url})]})]}),d&&(0,s.jsx)("button",{onClick:()=>d(t),className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-red-50",children:(0,s.jsx)(U.A,{className:"w-3.5 h-3.5 text-red-600"})})]},t))}),(0,s.jsxs)("div",{className:"flex items-center gap-4 mt-6 pt-6 border-t",children:[p&&(0,s.jsxs)("button",{onClick:p,className:"h-10 px-4 rounded-[10px] text-sm font-medium flex items-center gap-1 transition-all duration-200 bg-orange-500 text-white hover:bg-orange-600 [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#c2410c,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(234,_88,_12,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#c2410c,_0px_1px_3px_0px_rgba(234,_88,_12,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#c2410c,_0px_1px_2px_0px_rgba(234,_88,_12,_30%)]",children:[(0,s.jsx)(F.A,{className:"w-4 h-4"}),"Add Competitor"]}),(0,s.jsx)("div",{className:"flex-1"}),x&&(0,s.jsx)("button",{onClick:x,className:"h-10 px-6 rounded-[10px] text-sm font-medium flex items-center transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 bg-[#36322F] text-[#fff] hover:bg-[#4a4542] disabled:bg-[#8c8885] disabled:hover:bg-[#8c8885] [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#171310,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(58,_33,_8,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#171310,_0px_1px_3px_0px_rgba(58,_33,_8,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#171310,_0px_1px_2px_0px_rgba(58,_33,_8,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",children:"Continue to Analysis"})]})]})})]})}var Y=a(6642);let B=e=>{switch(e){case"OpenAI":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idR3duQxYl/theme/dark/symbol.svg?c=1dxbfHSJFAPEGdCLU4o5B",alt:"OpenAI",className:"w-7 h-7"});case"Anthropic":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idmJWF3N06/theme/dark/symbol.svg",alt:"Anthropic",className:"w-5 h-5"});case"Google":return(0,s.jsx)("div",{className:"w-5 h-5 flex items-center justify-center",children:(0,s.jsxs)("svg",{viewBox:"0 0 24 24",className:"w-5 h-5",children:[(0,s.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]})});case"Perplexity":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idNdawywEZ/w/800/h/800/theme/dark/icon.png?c=1dxbfHSJFAPEGdCLU4o5B",alt:"Perplexity",className:"w-5 h-5"});default:return(0,s.jsx)("div",{className:"w-5 h-5 bg-gray-400 rounded"})}};function V(e){let{company:t,analyzing:a,identifiedCompetitors:r,scrapingCompetitors:n,analysisProgress:p,prompts:x,customPrompts:m,removedDefaultPrompts:u,promptCompletionStatus:h,onRemoveDefaultPrompt:g,onRemoveCustomPrompt:f,onAddPromptClick:b,onStartAnalysis:y,detectServiceType:_}=e,v=_(t),j=new Date().getFullYear(),w=["Best ".concat(v,"s in ").concat(j,"?"),"Top ".concat(v,"s for startups?"),"Most popular ".concat(v,"s today?"),"Recommended ".concat(v,"s for developers?")].filter((e,t)=>!u.includes(t)),N=x.length>0?x:[...w,...m];return(0,s.jsx)("div",{className:"flex items-center justify-center animate-panel-in",children:(0,s.jsx)("div",{className:"max-w-4xl w-full",children:(0,s.jsx)("div",{className:"transition-all duration-400 opacity-100 translate-y-0",children:(0,s.jsxs)(l,{className:"p-2 bg-card text-card-foreground gap-6 rounded-xl border py-6 shadow-sm border-gray-200 h-full flex flex-col",children:[(0,s.jsxs)(o,{className:"pb-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(i,{className:"text-xl font-semibold",children:a?"Analysis Progress":"Prompts"}),!a&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"Competitors:"}),(0,s.jsxs)("div",{className:"flex -space-x-2",children:[r.slice(0,6).map((e,t)=>(0,s.jsxs)("div",{className:"w-8 h-8 rounded-full bg-white border-2 border-white shadow-sm overflow-hidden",title:e.name,children:[e.url?(0,s.jsx)("img",{src:"https://www.google.com/s2/favicons?domain=".concat(e.url,"&sz=64"),alt:e.name,className:"w-full h-full object-contain p-0.5",onError:e=>{e.currentTarget.style.display="none";let t=e.currentTarget.nextSibling;t&&(t.style.display="flex")}}):null,(0,s.jsx)("div",{className:"w-full h-full bg-gray-100 flex items-center justify-center text-xs font-medium text-gray-600",style:{display:e.url?"none":"flex"},children:e.name.charAt(0)})]},t)),r.length>6&&(0,s.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-100 border-2 border-white shadow-sm flex items-center justify-center",children:(0,s.jsxs)("span",{className:"text-xs text-gray-600 font-medium",children:["+",r.length-6]})})]})]})]}),n&&!a&&(0,s.jsxs)(c,{className:"mt-2 flex items-center justify-center gap-2 text-orange-600",children:[(0,s.jsx)(k.A,{className:"w-4 h-4 animate-spin"}),(0,s.jsx)("span",{children:"Validating competitor data in background..."})]}),a&&p&&(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)(c,{className:"flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"w-4 h-4 animate-spin text-orange-500"}),(0,s.jsx)("span",{children:p.message})]}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[p.progress,"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 overflow-hidden",children:(0,s.jsx)("div",{className:"bg-orange-500 h-2 rounded-full transition-all duration-500 ease-out",style:{width:"".concat(p.progress,"%")}})})]})]}),(0,s.jsxs)(d,{className:"space-y-6",children:[(0,s.jsx)("div",{children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:N.map((e,t)=>{let r=m.includes(e);return(0,s.jsxs)("div",{className:"group relative bg-white rounded-lg border border-gray-200 p-5 hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between gap-4",children:[(0,s.jsx)("p",{className:"text-base font-medium text-gray-900 flex-1",children:e}),!a&&!r&&(0,s.jsx)("button",{onClick:t=>{t.stopPropagation();let a=w.findIndex(t=>t===e);-1!==a&&g(a)},className:"opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-red-50",children:(0,s.jsx)(U.A,{className:"w-4 h-4 text-red-600"})}),!a&&r&&(0,s.jsx)("button",{onClick:t=>{t.stopPropagation(),f(e)},className:"opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded hover:bg-red-50",children:(0,s.jsx)(U.A,{className:"w-4 h-4 text-red-600"})})]}),(0,s.jsx)("div",{className:"mt-4 flex items-center gap-3 justify-end",children:A().map(t=>{var r;let n=t.name,l=e.trim(),o=a?(null==(r=h[l])?void 0:r[n])||"pending":null;return(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[B(n),a&&(0,s.jsxs)(s.Fragment,{children:["pending"===o&&(0,s.jsx)("div",{className:"w-4 h-4 rounded-full border border-gray-300"}),"running"===o&&(0,s.jsx)(k.A,{className:"w-4 h-4 animate-spin text-orange-500"}),"completed"===o&&(0,s.jsx)(Y.A,{className:"w-4 h-4 text-green-500"}),"failed"===o&&(0,s.jsx)("div",{className:"w-4 h-4 rounded-full bg-red-500"}),"skipped"===o&&(0,s.jsx)("div",{className:"w-4 h-4 rounded-full bg-gray-400"})]})]},"".concat(e,"-").concat(n))})}),r&&(0,s.jsx)(L,{variant:"outline",className:"text-xs mt-2",children:"Custom"})]},"".concat(e,"-").concat(t))})})}),(0,s.jsx)("div",{className:"flex justify-end mb-4",children:(0,s.jsxs)("button",{onClick:b,disabled:a,className:"h-9 rounded-[10px] text-sm font-medium flex items-center transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 bg-[#36322F] text-[#fff] hover:bg-[#4a4542] disabled:bg-[#8c8885] disabled:hover:bg-[#8c8885] [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#171310,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(58,_33,_8,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#171310,_0px_1px_3px_0px_rgba(58,_33,_8,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#171310,_0px_1px_2px_0px_rgba(58,_33,_8,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100 px-4 py-1 gap-1",children:[(0,s.jsx)(F.A,{className:"h-4 w-4"}),"Add Prompt"]})}),(0,s.jsx)("div",{className:"flex justify-center pt-4",children:(0,s.jsx)("button",{onClick:y,disabled:a,className:"h-10 px-6 rounded-[10px] text-sm font-medium flex items-center transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 bg-orange-500 text-white hover:bg-orange-600 [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#c2410c,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(234,_88,_12,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#c2410c,_0px_1px_3px_0px_rgba(234,_88,_12,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#c2410c,_0px_1px_2px_0px_rgba(234,_88,_12,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k.A,{className:"mr-2 h-5 w-5 animate-spin"}),"Analyzing..."]}):"Start Analysis"})})]})]})})})})}function H(e){let{activeTab:t,onTabChange:a,onRestart:r,brandData:n,brandName:l}=e,o=e=>{a(e)};return(0,s.jsx)("nav",{className:"w-80 flex-shrink-0 animate-fade-in flex flex-col h-[calc(100vh-8rem)] ml-[-2rem] sticky top-8",style:{animationDelay:"0.3s"},children:(0,s.jsxs)("div",{className:"w-full flex flex-col justify-between flex-1",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("button",{onClick:()=>o("matrix"),className:"w-full text-left px-4 py-3 rounded-[10px] text-sm font-medium transition-all duration-200 ".concat("matrix"===t?"bg-[#36322F] text-white [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#171310,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(58,_33,_8,_58%)]":"bg-orange-500 text-white hover:bg-orange-600 [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#c2410c,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(234,_88,_12,_58%)] hover:translate-y-[1px] hover:scale-[0.98]"),children:"Comparison Matrix"}),(0,s.jsx)("button",{onClick:()=>o("prompts"),className:"w-full text-left px-4 py-3 rounded-[10px] text-sm font-medium transition-all duration-200 ".concat("prompts"===t?"bg-[#36322F] text-white [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#171310,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(58,_33,_8,_58%)]":"bg-orange-500 text-white hover:bg-orange-600 [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#c2410c,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(234,_88,_12,_58%)] hover:translate-y-[1px] hover:scale-[0.98]"),children:"Prompts & Responses"}),(0,s.jsx)("button",{onClick:()=>o("rankings"),className:"w-full text-left px-4 py-3 rounded-[10px] text-sm font-medium transition-all duration-200 ".concat("rankings"===t?"bg-[#36322F] text-white [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#171310,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(58,_33,_8,_58%)]":"bg-orange-500 text-white hover:bg-orange-600 [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#c2410c,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(234,_88,_12,_58%)] hover:translate-y-[1px] hover:scale-[0.98]"),children:"Provider Rankings"}),(0,s.jsx)("button",{onClick:()=>o("visibility"),className:"w-full text-left px-4 py-3 rounded-[10px] text-sm font-medium transition-all duration-200 ".concat("visibility"===t?"bg-[#36322F] text-white [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#171310,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(58,_33,_8,_58%)]":"bg-orange-500 text-white hover:bg-orange-600 [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#c2410c,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(234,_88,_12,_58%)] hover:translate-y-[1px] hover:scale-[0.98]"),children:"Visibility Score"})]}),(0,s.jsx)("div",{className:"pt-4 pb-8 border-t border-gray-200",children:(0,s.jsxs)("button",{onClick:r,className:"w-full text-left px-4 py-3 rounded-[10px] text-sm font-medium transition-all duration-200 bg-[#36322F] text-[#fff] hover:bg-[#4a4542] [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#171310,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(58,_33,_8,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#171310,_0px_1px_3px_0px_rgba(58,_33,_8,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#171310,_0px_1px_2px_0px_rgba(58,_33,_8,_30%)] flex items-center gap-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Analyze another website"]})})]})})}var K=a(90536),q=a(99237),J=a(81416),Z=a(79607),X=a(57918);let $={...{defaultOptions:{caseSensitive:!1,wholeWordOnly:!0,includeVariations:!0,excludeNegativeContext:!1},brandAliases:new Map,ignoredSuffixes:["inc","incorporated","llc","limited liability company","ltd","limited","corp","corporation","co","company","plc","public limited company","gmbh","ag","sa","srl"],negativeContextPatterns:[/\bnot\s+(?:recommended|good|worth|reliable|suitable)\b/i,/\bavoid(?:ing)?\s+/i,/\bworse\s+than\b/i,/\binferior\s+to\b/i,/\bdon't\s+(?:use|recommend|like|trust)\b/i,/\bstay\s+away\s+from\b/i,/\bnever\s+use\b/i,/\bterrible\s+(?:service|product|quality)\b/i,/\bscam\b/i,/\bfraud(?:ulent)?\b/i],confidenceThresholds:{high:.8,medium:.5,low:.3}}};function Q(e){let{response:t,brandName:a,competitors:n,showHighlighting:l=!0,highlightClassName:o="bg-green-100 text-green-900 px-0.5 rounded font-medium",renderMarkdown:i=!0}=e,c=function(e,t){let a=e;if(a=a.replace(/^\d+\n/gm,""),t){let e=RegExp("^".concat(t,"\\s*\n?"),"i");a=a.replace(e,"")}return["OpenAI","Anthropic","Google","Perplexity"].forEach(e=>{let t=RegExp("^".concat(e,"\\s*\n?"),"i");a=a.replace(t,"")}),(a=(a=a.replace(/<[^>]*>/g,"")).replace(/\n\s*\n\s*\n/g,"\n\n")).trim()}(t.response,t.provider),d=r.useMemo(()=>{if(!l)return new Map;if(t.detectionDetails){let e=new Map;return t.detectionDetails.brandMatches&&t.detectionDetails.brandMatches.length>0&&e.set(a,{mentioned:!0,matches:t.detectionDetails.brandMatches,confidence:Math.max(...t.detectionDetails.brandMatches.map(e=>e.confidence))}),t.detectionDetails.competitorMatches&&(t.detectionDetails.competitorMatches instanceof Map?t.detectionDetails.competitorMatches.forEach((t,a)=>{t.length>0&&e.set(a,{mentioned:!0,matches:t,confidence:Math.max(...t.map(e=>e.confidence))})}):Object.entries(t.detectionDetails.competitorMatches).forEach(t=>{let[a,s]=t;s&&s.length>0&&e.set(a,{mentioned:!0,matches:s,confidence:Math.max(...s.map(e=>e.confidence))})})),e}{let e=[a,...n],t=new Map;return e.forEach(e=>{let a=function(e){let t={...$.defaultOptions},a=$.brandAliases.get(e);return a&&a.length>0&&(t.customVariations=a),t}(e),s=function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{caseSensitive:s=!1,wholeWordOnly:r=!0,includeVariations:n=!0,customVariations:l=[],excludeNegativeContext:o=!1}=a,i=s?e:e.toLowerCase(),c=[];(r?function(e,t){let a=new Set([...function(e){let t=function(e){let t={...$},a=RegExp("\\b(".concat(t.ignoredSuffixes.join("|"),")\\b\\.?$"),"gi");return e.toLowerCase().trim().replace(/\s+/g," ").replace(/'s\b/g,"").replace(a,"").trim()}(e),a=new Set;a.add(e.toLowerCase()),a.add(t),a.add(t.replace(/\s+/g,"")),a.add(t.replace(/\s+/g,"-")),a.add(t.replace(/\s+/g,"_")),a.add(t.replace(/\s+/g,"."));let s=t.split(" ");return s.length>1&&(a.add(s.map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join("")),a.add(s[0]+s.slice(1).map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join("")),a.add(s.join("").toLowerCase()),a.add(s.map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ").toLowerCase())),e.includes("&")&&(a.add(t.replace(/&/g,"and")),a.add(t.replace(/&/g,"n")),a.add(t.replace(/&/g,""))),e.includes("+")&&(a.add(t.replace(/\+/g,"plus")),a.add(t.replace(/\+/g,"and")),a.add(t.replace(/\+/g,""))),Object.entries({1:"one",2:"two",3:"three",4:"four",5:"five",6:"six",7:"seven",8:"eight",9:"nine",0:"zero"}).forEach(e=>{let[s,r]=e;t.includes(s)&&a.add(t.replace(RegExp(s,"g"),r))}),Object.entries({"artificial intelligence":["ai"],"machine learning":["ml"],"natural language":["nl","nlp"],technologies:["tech"],laboratories:["labs"],solutions:["sol"],systems:["sys"],software:["sw"],hardware:["hw"],incorporated:["inc"],corporation:["corp"],limited:["ltd"]}).forEach(e=>{let[s,r]=e;t.includes(s)&&r.forEach(e=>{a.add(t.replace(s,e))})}),!e.includes(".")&&e.length>2&&["com","io","ai","dev","co","net","org","app"].forEach(e=>{a.add("".concat(t.replace(/\s+/g,""),".").concat(e))}),Array.from(a)}(e),...t||[]]),s=[];return a.forEach(e=>{let t=e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");s.push(RegExp("\\b".concat(t,"\\b"),"i")),s.push(RegExp("\\b".concat(t,"(?:-\\w+)*\\b"),"i")),s.push(RegExp("\\b".concat(t,"'s?\\b"),"i")),s.push(RegExp("\\b".concat(t,"(?:\\s+(?:inc|llc|ltd|corp|corporation|company|co)\\.?)?\\b"),"i"))}),s}(t,l):[new RegExp(t,s?"g":"gi")]).forEach(e=>{let a,s=RegExp(e.source,e.flags+"g");for(;null!==(a=s.exec(i));){let s=a[0],r=a.index;if(o){let e=Math.max(0,r-50),t=Math.min(i.length,r+s.length+50),a=i.substring(e,t);if([/\bnot\s+(?:recommended|good|worth|reliable)/i,/\bavoid\b/i,/\bworse\s+than\b/i,/\binferior\s+to\b/i,/\bdon't\s+(?:use|recommend|like)\b/i].some(e=>e.test(a)))continue}let l=.5;s.toLowerCase()===t.toLowerCase()?l=1:s.toLowerCase().startsWith(t.toLowerCase()+" ")?l=.9:n&&(l=.7),c.push({text:s,index:r,pattern:e.source,confidence:l})}});let d=c.reduce((e,t)=>{let a=e.find(e=>e.index===t.index);return!a||t.confidence>a.confidence?[...e.filter(e=>e.index!==t.index),t]:e},[]),p=d.length>0?Math.max(...d.map(e=>e.confidence)):0;return{mentioned:d.length>0,matches:d.sort((e,t)=>t.confidence-e.confidence),confidence:p}}(c,e,a);s.mentioned&&t.set(e,s)}),t}},[t,a,n,c,l]),p=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"bg-yellow-200 px-0.5 rounded";return e.map((e,a)=>e.highlighted?(0,s.jsx)("span",{className:t,title:"".concat(e.brandName," (").concat(Math.round(100*(e.confidence||0)),"% confidence)"),children:e.text},a):(0,s.jsx)("span",{children:e.text},a))}(r.useMemo(()=>{if(!l)return[];let e=[];d.forEach((t,a)=>{t.matches.forEach(t=>{e.push({start:t.index,end:t.index+t.text.length,brandName:a,matchText:t.text,confidence:t.confidence})})}),e.sort((e,t)=>e.start-t.start);let t=e.reduce((e,t)=>{let a=e[e.length-1];return!a||t.start>=a.end?e.push(t):t.confidence>a.confidence&&(e[e.length-1]=t),e},[]),a=[],s=0;return t.forEach(e=>{e.start>s&&a.push({text:c.substring(s,e.start),highlighted:!1}),a.push({text:e.matchText,highlighted:!0,brandName:e.brandName,confidence:e.confidence}),s=e.end}),s<c.length&&a.push({text:c.substring(s),highlighted:!1}),a},[c,d,l]),o);return l?i?(0,s.jsx)("div",{className:"prose prose-sm max-w-none prose-slate",children:(0,s.jsx)(Z.oz,{remarkPlugins:[X.A],components:{p:e=>{let{children:t}=e;return(0,s.jsx)("p",{className:"mb-3 leading-relaxed",children:t})},ul:e=>{let{children:t}=e;return(0,s.jsx)("ul",{className:"list-disc pl-5 mb-3 space-y-1",children:t})},ol:e=>{let{children:t}=e;return(0,s.jsx)("ol",{className:"list-decimal pl-5 mb-3 space-y-1",children:t})},li:e=>{let{children:t}=e;return(0,s.jsx)("li",{className:"text-sm",children:t})},strong:e=>{let{children:t}=e;return(0,s.jsx)("strong",{className:"font-semibold text-gray-900",children:t})},em:e=>{let{children:t}=e;return(0,s.jsx)("em",{className:"italic",children:t})},h1:e=>{let{children:t}=e;return(0,s.jsx)("h1",{className:"text-lg font-bold mb-3 text-gray-900",children:t})},h2:e=>{let{children:t}=e;return(0,s.jsx)("h2",{className:"text-base font-semibold mb-2 text-gray-900",children:t})},h3:e=>{let{children:t}=e;return(0,s.jsx)("h3",{className:"text-sm font-semibold mb-2 text-gray-900",children:t})},table:e=>{let{children:t}=e;return(0,s.jsx)("div",{className:"overflow-x-auto my-4",children:(0,s.jsx)("table",{className:"min-w-full border-collapse border border-gray-300 text-xs",children:t})})},thead:e=>{let{children:t}=e;return(0,s.jsx)("thead",{className:"bg-gray-50",children:t})},tbody:e=>{let{children:t}=e;return(0,s.jsx)("tbody",{children:t})},tr:e=>{let{children:t}=e;return(0,s.jsx)("tr",{className:"border-b border-gray-200",children:t})},th:e=>{let{children:t}=e;return(0,s.jsx)("th",{className:"border border-gray-300 px-2 py-1 text-left font-semibold bg-gray-100",children:t})},td:e=>{let{children:t}=e;return(0,s.jsx)("td",{className:"border border-gray-300 px-2 py-1",children:t})},code:e=>{let{children:t,className:a}=e;return(null==a?void 0:a.includes("language-"))?(0,s.jsx)("pre",{className:"bg-gray-100 rounded p-2 text-xs overflow-x-auto mb-3",children:(0,s.jsx)("code",{children:t})}):(0,s.jsx)("code",{className:"bg-gray-100 px-1 py-0.5 rounded text-xs",children:t})},blockquote:e=>{let{children:t}=e;return(0,s.jsx)("blockquote",{className:"border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-3",children:t})}},children:c})}):(0,s.jsx)("div",{className:"whitespace-pre-wrap",children:p}):i?(0,s.jsx)(Z.oz,{remarkPlugins:[X.A],components:{p:e=>{let{children:t}=e;return(0,s.jsx)("p",{className:"mb-2",children:t})},ul:e=>{let{children:t}=e;return(0,s.jsx)("ul",{className:"list-disc pl-4 mb-2",children:t})},ol:e=>{let{children:t}=e;return(0,s.jsx)("ol",{className:"list-decimal pl-4 mb-2",children:t})},li:e=>{let{children:t}=e;return(0,s.jsx)("li",{className:"mb-1",children:t})},strong:e=>{let{children:t}=e;return(0,s.jsx)("strong",{className:"font-semibold",children:t})},em:e=>{let{children:t}=e;return(0,s.jsx)("em",{className:"italic",children:t})},table:e=>{let{children:t}=e;return(0,s.jsx)("div",{className:"overflow-x-auto my-4",children:(0,s.jsx)("table",{className:"min-w-full border-collapse border border-gray-300 text-xs",children:t})})},thead:e=>{let{children:t}=e;return(0,s.jsx)("thead",{className:"bg-gray-50",children:t})},tbody:e=>{let{children:t}=e;return(0,s.jsx)("tbody",{children:t})},tr:e=>{let{children:t}=e;return(0,s.jsx)("tr",{className:"border-b border-gray-200",children:t})},th:e=>{let{children:t}=e;return(0,s.jsx)("th",{className:"border border-gray-300 px-2 py-1 text-left font-semibold bg-gray-100",children:t})},td:e=>{let{children:t}=e;return(0,s.jsx)("td",{className:"border border-gray-300 px-2 py-1",children:t})}},children:c}):(0,s.jsx)(s.Fragment,{children:c})}let ee=e=>{switch(e){case"OpenAI":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idR3duQxYl/theme/dark/symbol.svg?c=1dxbfHSJFAPEGdCLU4o5B",alt:"OpenAI",className:"w-6 h-6"});case"Anthropic":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idmJWF3N06/theme/dark/symbol.svg",alt:"Anthropic",className:"w-6 h-6"});case"Google":return(0,s.jsx)("div",{className:"w-6 h-6 flex items-center justify-center",children:(0,s.jsxs)("svg",{viewBox:"0 0 24 24",className:"w-6 h-6",children:[(0,s.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]})});case"Perplexity":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idNdawywEZ/w/800/h/800/theme/dark/icon.png?c=1dxbfHSJFAPEGdCLU4o5B",alt:"Perplexity",className:"w-6 h-6"});default:return(0,s.jsx)("div",{className:"w-6 h-6 bg-gray-400 rounded"})}};function et(e){let{prompts:t,responses:a,expandedPromptIndex:n,onToggleExpand:l,brandName:o,competitors:i}=e,[c,d]=(0,r.useState)(!1),[p,x]=(0,r.useState)(""),m=t.map((e,t)=>{if(!p)return t;let s=e.prompt.toLowerCase().includes(p.toLowerCase()),r=((null==a?void 0:a.filter(t=>t.prompt===e.prompt))||[]).some(e=>e.response.toLowerCase().includes(p.toLowerCase())||e.provider.toLowerCase().includes(p.toLowerCase()));return s||r?t:null}).filter(e=>null!==e);return(0,s.jsxs)("div",{className:"space-y-2",children:[t.length>0&&(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[(0,s.jsx)("input",{type:"text",value:p,onChange:e=>x(e.target.value),placeholder:"Search prompts and responses...",className:"w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm"}),(0,s.jsx)("svg",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),p&&(0,s.jsx)("button",{onClick:()=>x(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsx)("button",{onClick:()=>{c?(d(!1),l(null)):(d(!0),l(-1))},className:"h-9 px-4 py-2 rounded-[10px] text-sm font-medium flex items-center gap-2 transition-all duration-200 bg-orange-500 text-white hover:bg-orange-600 [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#c2410c,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(234,_88,_12,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#c2410c,_0px_1px_3px_0px_rgba(234,_88,_12,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#c2410c,_0px_1px_2px_0px_rgba(234,_88,_12,_30%)]",children:c?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(K.A,{className:"h-4 w-4"}),"Collapse All"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(q.A,{className:"h-4 w-4"}),"Expand All"]})})]}),t.map((e,t)=>{if(!m.includes(t))return null;let r=(null==a?void 0:a.filter(t=>t.prompt===e.prompt))||[],c=r.some(e=>e.brandMentioned),x=!!p||-1===n||n===t;return(0,s.jsxs)("div",{className:"\n              relative border rounded-lg transition-all duration-300\n              ".concat(x?"border-orange-200 bg-white shadow-md":"border-gray-200 bg-white hover:border-orange-100 hover:shadow-sm","\n            "),children:[(0,s.jsx)("div",{className:"px-3 py-4 cursor-pointer select-none",onClick:()=>{-1===n?(d(!1),l(t)):l(x?null:t)},children:(0,s.jsxs)("div",{className:"flex items-center justify-between gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.prompt}),c&&(0,s.jsx)(L,{variant:"default",className:"text-xs bg-green-100 text-green-800 shrink-0",children:"Brand Mentioned"})]}),(0,s.jsx)("div",{className:"flex items-center gap-2 shrink-0",children:["OpenAI","Anthropic","Google","Perplexity"].map(e=>{let t=r.find(t=>t.provider===e);if(!t)return null;let a=!t.response||0===t.response.trim().length;return(0,s.jsxs)("div",{className:"relative flex items-center",children:[(0,s.jsx)("div",{className:"w-6 h-6 flex items-center justify-center",children:ee(e)}),a?(0,s.jsx)("div",{className:"absolute -top-0.5 -right-0.5 w-3 h-3 flex items-center justify-center bg-red-500 rounded-full border border-white",children:(0,s.jsx)("span",{className:"text-white text-xs font-bold leading-none",children:"\xd7"})}):t.brandMentioned?(0,s.jsx)("div",{className:"absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-green-500 rounded-full border border-white"}):null]},e)})}),(0,s.jsx)("div",{className:"transition-transform duration-300 shrink-0 ".concat(x?"rotate-180":""),children:(0,s.jsx)(J.A,{className:"h-4 w-4 text-gray-400"})})]})}),(0,s.jsx)("div",{className:"\n                overflow-hidden transition-all duration-300\n                ".concat(x?"max-h-[4000px] opacity-100":"max-h-0 opacity-0","\n              "),onClick:e=>e.stopPropagation(),children:(0,s.jsx)("div",{className:"border-t border-gray-100 px-3 py-3",children:r.length>0?(0,s.jsx)("div",{className:"space-y-4",children:["OpenAI","Anthropic","Google","Perplexity"].map(e=>{let t=r.find(t=>t.provider===e);if(!t)return null;let a=!t.response||0===t.response.trim().length;return(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[ee(t.provider),(0,s.jsx)("span",{className:"font-medium text-sm text-gray-900",children:t.provider})]}),a?(0,s.jsx)(L,{variant:"destructive",className:"text-xs bg-red-100 text-red-800",children:"Failed \xd7"}):t.brandMentioned?(0,s.jsx)(L,{variant:"default",className:"text-xs bg-green-100 text-green-800",children:"Brand Mentioned"}):null,t.brandPosition&&t.brandPosition>0&&(0,s.jsxs)(L,{variant:"outline",className:"text-xs",children:["Position #",t.brandPosition]})]}),(0,s.jsx)("div",{className:"bg-gray-50 rounded-md p-3 text-sm text-gray-700 select-text cursor-text",children:a?(0,s.jsx)("div",{className:"text-red-600 italic",children:"Response failed or returned empty content"}):(0,s.jsx)(Q,{response:t,brandName:o,competitors:i,showHighlighting:!0,highlightClassName:"bg-green-100 text-green-900 px-0.5 rounded font-medium",renderMarkdown:!0})})]},e)})}):(0,s.jsx)("div",{className:"text-gray-500 text-sm text-center py-4",children:"No responses available for this prompt"})})})]},t)}),p&&0===m.length&&(0,s.jsxs)("div",{className:"text-center py-8 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("p",{className:"text-gray-600 mb-2",children:['No results found for "',p,'"']}),(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"Try searching for different keywords"})]})]})}var ea=a(69896),es=a(98330),er=a(77588),en=a(87545),el=a(65755);function eo(e){let{competitors:t,brandData:a,identifiedCompetitors:r}=e,n=t.filter(e=>!e.isOwn)[0],p=t.findIndex(e=>e.isOwn)+1,x=n?a.visibilityScore-n.visibilityScore:0;return(0,s.jsx)("div",{className:"flex flex-col h-full",children:(0,s.jsxs)(l,{className:"p-2 bg-card text-card-foreground gap-6 rounded-xl border py-6 shadow-sm border-gray-200 h-full flex flex-col",children:[(0,s.jsx)(o,{className:"border-b",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(i,{className:"text-xl font-semibold",children:"Visibility Score"}),(0,s.jsx)(c,{className:"text-sm text-gray-600 mt-1",children:"Your brand visibility across AI providers"})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-3xl font-bold text-orange-600",children:[a.visibilityScore,"%"]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Overall Score"})]})]})}),(0,s.jsx)(d,{className:"pt-6 flex-1",children:(0,s.jsxs)("div",{className:"flex gap-8",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"h-80 relative",children:[(0,s.jsx)(ea.u,{width:"100%",height:"100%",children:(0,s.jsxs)(es.r,{children:[(0,s.jsxs)("defs",{children:[(0,s.jsxs)("linearGradient",{id:"orangeGradient",x1:"0",y1:"0",x2:"1",y2:"1",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:"#fb923c"}),(0,s.jsx)("stop",{offset:"100%",stopColor:"#ea580c"})]}),(0,s.jsxs)("filter",{id:"shadow",x:"-50%",y:"-50%",width:"200%",height:"200%",children:[(0,s.jsx)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:"3"}),(0,s.jsx)("feOffset",{dx:"0",dy:"2",result:"offsetblur"}),(0,s.jsx)("feFlood",{floodColor:"#000000",floodOpacity:"0.1"}),(0,s.jsx)("feComposite",{in2:"offsetblur",operator:"in"}),(0,s.jsxs)("feMerge",{children:[(0,s.jsx)("feMergeNode",{}),(0,s.jsx)("feMergeNode",{in:"SourceGraphic"})]})]})]}),(0,s.jsx)(er.F,{data:t.slice(0,8).map(e=>({name:e.name,value:e.visibilityScore,isOwn:e.isOwn})),cx:"50%",cy:"50%",innerRadius:70,outerRadius:110,paddingAngle:1,dataKey:"value",startAngle:90,endAngle:-270,animationBegin:0,animationDuration:800,filter:"url(#shadow)",children:t.slice(0,8).map((e,t)=>(0,s.jsx)(en.f,{fill:e.isOwn?"url(#orangeGradient)":["#3b82f6","#8b5cf6","#ec4899","#10b981","#f59e0b","#6366f1","#14b8a6","#f43f5e"][t%8],stroke:e.isOwn?"#ea580c":"none",strokeWidth:2*!!e.isOwn},"cell-".concat(t)))}),(0,s.jsx)(el.m,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.95)",border:"1px solid #e5e7eb",borderRadius:"8px",fontSize:"14px",padding:"8px 12px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"},formatter:(e,t)=>["".concat(e,"% visibility"),t],labelStyle:{fontWeight:600}})]})}),(0,s.jsxs)("div",{className:"absolute top-[50%] left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center",children:[(0,s.jsxs)("p",{className:"text-3xl font-bold text-gray-900",children:["#",p]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Rank"}),0!==x&&(0,s.jsxs)("p",{className:"text-xs mt-2 font-medium ".concat(x>0?"text-green-600":"text-red-600"),children:[x>0?"+":"",Math.abs(x).toFixed(1),"% vs #1"]})]})]})}),(0,s.jsx)("div",{className:"w-80 space-y-2",children:t.slice(0,8).map((e,t)=>{let a=r.find(t=>t.name===e.name||t.name.toLowerCase()===e.name.toLowerCase()),n=(null==a?void 0:a.url)?"https://www.google.com/s2/favicons?domain=".concat(a.url,"&sz=64"):null,l=e.isOwn?"#ea580c":["#3b82f6","#8b5cf6","#ec4899","#10b981","#f59e0b","#6366f1","#14b8a6","#f43f5e"][t%8];return(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full flex-shrink-0",style:{backgroundColor:l}}),(0,s.jsxs)("div",{className:"flex items-center gap-2 min-w-0",children:[(0,s.jsxs)("div",{className:"w-5 h-5 rounded flex items-center justify-center bg-gray-100 flex-shrink-0",children:[n?(0,s.jsx)("img",{src:n,alt:e.name,className:"w-4 h-4 object-contain",onError:e=>{e.currentTarget.style.display="none";let t=e.currentTarget.nextSibling;t&&(t.style.display="flex")}}):null,(0,s.jsx)("div",{className:"w-full h-full ".concat(e.isOwn?"bg-orange-500":"bg-gray-300"," flex items-center justify-center text-white text-[8px] font-bold rounded"),style:{display:n?"none":"flex"},children:e.name.charAt(0)})]}),(0,s.jsx)("span",{className:"text-sm truncate ".concat(e.isOwn?"font-semibold text-orange-600":"text-gray-700"),children:e.name}),(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-900 ml-auto",children:[e.visibilityScore,"%"]})]})]},t)})})]})})]})})}function ei(e){let{error:t,onDismiss:a}=e;return(0,s.jsxs)("div",{className:"fixed bottom-4 right-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg shadow-lg flex items-center gap-2 animate-fade-in",children:[(0,s.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}),(0,s.jsx)("span",{className:"text-sm",children:t}),(0,s.jsx)("button",{onClick:a,className:"ml-2 text-red-500 hover:text-red-700",children:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}function ec(e){let{isOpen:t,promptText:a,onPromptTextChange:r,onAdd:n,onClose:l}=e;return t?(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 animate-fade-in",children:(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-xl max-w-lg w-full mx-4 animate-fade-in",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Add Custom Prompt"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Enter a prompt that AI models will answer about your industry."}),(0,s.jsx)("textarea",{value:a,onChange:e=>r(e.target.value),onKeyDown:e=>{"Enter"===e.key&&!e.shiftKey&&a.trim()&&(e.preventDefault(),n())},placeholder:"Example: What is the best AI scraper?",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none",rows:3,autoFocus:!0}),(0,s.jsxs)("div",{className:"flex gap-3 mt-6",children:[(0,s.jsx)("button",{onClick:n,disabled:!a.trim(),className:"flex-1 h-10 px-4 rounded-[10px] text-sm font-medium transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 bg-orange-500 text-white hover:bg-orange-300 dark:bg-orange-500 dark:hover:bg-orange-300 dark:text-white [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#c2410c,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(234,_88,_12,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#c2410c,_0px_1px_3px_0px_rgba(234,_88,_12,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#c2410c,_0px_1px_2px_0px_rgba(234,_88,_12,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",children:"Add Prompt"}),(0,s.jsx)("button",{onClick:l,className:"px-4 h-10 rounded-[10px] text-sm font-medium transition-all duration-200 bg-[#36322F] text-[#fff] hover:bg-[#4a4542] [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#171310,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(58,_33,_8,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#171310,_0px_1px_3px_0px_rgba(58,_33,_8,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#171310,_0px_1px_2px_0px_rgba(58,_33,_8,_30%)]",children:"Cancel"})]})]})})}):null}function ed(e){let{isOpen:t,competitorName:a,competitorUrl:r,onNameChange:n,onUrlChange:l,onAdd:o,onClose:i}=e;if(!t)return null;let c=e=>{"Enter"===e.key&&a.trim()&&(e.preventDefault(),o())};return(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 animate-fade-in",children:(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-xl max-w-lg w-full mx-4 animate-fade-in",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Add Competitor"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Competitor Name"}),(0,s.jsx)("input",{type:"text",value:a,onChange:e=>n(e.target.value),onKeyDown:c,placeholder:"e.g., Anthropic",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",autoFocus:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Website URL (optional)"}),(0,s.jsx)("input",{type:"text",value:r,onChange:e=>l(e.target.value),onKeyDown:c,placeholder:"e.g., anthropic.com",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"})]})]}),(0,s.jsxs)("div",{className:"flex gap-3 mt-6",children:[(0,s.jsx)("button",{onClick:o,disabled:!a.trim(),className:"flex-1 h-10 px-4 rounded-[10px] text-sm font-medium transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 bg-orange-500 text-white hover:bg-orange-300 dark:bg-orange-500 dark:hover:bg-orange-300 dark:text-white [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#c2410c,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(234,_88,_12,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#c2410c,_0px_1px_3px_0px_rgba(234,_88,_12,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#c2410c,_0px_1px_2px_0px_rgba(234,_88,_12,_30%)] disabled:shadow-none disabled:hover:translate-y-0 disabled:hover:scale-100",children:"Add Competitor"}),(0,s.jsx)("button",{onClick:i,className:"px-4 h-10 rounded-[10px] text-sm font-medium transition-all duration-200 bg-[#36322F] text-[#fff] hover:bg-[#4a4542] [box-shadow:inset_0px_-2.108433723449707px_0px_0px_#171310,_0px_1.2048193216323853px_6.325301647186279px_0px_rgba(58,_33,_8,_58%)] hover:translate-y-[1px] hover:scale-[0.98] hover:[box-shadow:inset_0px_-1px_0px_0px_#171310,_0px_1px_3px_0px_rgba(58,_33,_8,_40%)] active:translate-y-[2px] active:scale-[0.97] active:[box-shadow:inset_0px_1px_1px_0px_#171310,_0px_1px_2px_0px_rgba(58,_33,_8,_30%)]",children:"Cancel"})]})]})})})}var ep=a(83210),ex=a(24183),em=a(1290);let eu=e=>{let{name:t,isOwn:a=!1,description:n,favicon:l,url:o}=e,[i,c]=r.useState(!1),d=l||(o?"https://www.google.com/s2/favicons?domain=".concat(o,"&sz=64"):null);return(0,s.jsxs)("div",{className:"flex items-center gap-2 p-3 hover:bg-gray-50",children:[(0,s.jsx)("div",{className:"w-6 h-6 flex items-center justify-center rounded overflow-hidden flex-shrink-0",children:d&&!i?(0,s.jsx)(G.default,{src:d,alt:"".concat(t," logo"),width:24,height:24,className:"object-contain",onError:()=>c(!0)}):(0,s.jsx)("div",{className:"w-6 h-6 bg-gray-200 rounded flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-gray-600 font-semibold text-xs",children:t.charAt(0).toUpperCase()})})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"flex items-center gap-1.5",children:o&&!a?(0,s.jsxs)("a",{href:o.startsWith("http")?o:"https://".concat(o),target:"_blank",rel:"noopener noreferrer",className:"text-sm font-medium hover:underline ".concat(a?"text-orange-600":"text-gray-900 hover:text-blue-600"," flex items-center gap-1"),onClick:e=>e.stopPropagation(),children:[t,(0,s.jsx)(z.A,{className:"w-3.5 h-3.5 text-gray-400"})]}):(0,s.jsx)("h3",{className:"text-sm font-medium ".concat(a?"text-orange-600":"text-gray-900"),children:t})}),n&&(0,s.jsx)("p",{className:"text-sm text-gray-500",children:n})]})]})},eh=e=>{switch(e){case"OpenAI":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idR3duQxYl/theme/dark/symbol.svg?c=1dxbfHSJFAPEGdCLU4o5B",alt:"OpenAI",className:"w-7 h-7"});case"Anthropic":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idmJWF3N06/theme/dark/symbol.svg",alt:"Anthropic",className:"w-5 h-5"});case"Google":return(0,s.jsx)("div",{className:"w-5 h-5 flex items-center justify-center",children:(0,s.jsxs)("svg",{viewBox:"0 0 24 24",className:"w-5 h-5",children:[(0,s.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]})});case"Perplexity":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idNdawywEZ/w/800/h/800/theme/dark/icon.png?c=1dxbfHSJFAPEGdCLU4o5B",alt:"Perplexity",className:"w-5 h-5"});default:return(0,s.jsx)("div",{className:"w-5 h-5 bg-gray-400 rounded"})}},eg=e=>{let t=e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"").trim();if(!(t.length<3||["inc","llc","corp","company","the"].includes(t)))return"".concat(t,".com")};function ef(e){let{data:t,brandName:a,competitors:n}=e,[l,o]=(0,r.useState)("competitor"),[i,c]=(0,r.useState)("asc");if(!t||0===t.length)return(0,s.jsxs)("div",{className:"text-center py-12 bg-gray-50 rounded-lg",children:[(0,s.jsx)("p",{className:"text-gray-600 text-lg mb-2",children:"No comparison data available"}),(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"The analysis may still be processing or no providers returned data."})]});let d=Object.values(S).filter(e=>e.enabled&&e.isConfigured()).map(e=>e.name);if(0===d.length&&t.length>0){let e=new Set;t.forEach(t=>{Object.keys(t.providers).forEach(t=>e.add(t))}),d=Array.from(e)}let p=e=>{let t=Math.pow(e/100,.5);return{backgroundColor:"rgba(251, 146, 60, ".concat(t,")"),border:e>0?"1px solid rgb(251, 146, 60)":void 0}},x=e=>{l===e?(c(e=>"asc"===e?"desc":"desc"===e?null:"asc"),null===i&&o("competitor")):(o(e),c("asc"))},m=e=>l!==e?(0,s.jsx)(ep.A,{className:"w-4 h-4 opacity-30"}):"asc"===i?(0,s.jsx)(ex.A,{className:"w-4 h-4"}):"desc"===i?(0,s.jsx)(em.A,{className:"w-4 h-4"}):(0,s.jsx)(ep.A,{className:"w-4 h-4"});return 0===d.length?null:(0,s.jsx)("div",{className:"overflow-x-auto rounded-lg border border-gray-200",children:(0,s.jsxs)("table",{className:"w-full border-collapse",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"bg-gray-50 border-b border-r border-gray-200 w-[180px]",children:(0,s.jsxs)("button",{onClick:()=>x("competitor"),className:"w-full p-3 font-medium text-gray-900 flex items-center justify-between hover:bg-gray-100 transition-colors text-left",children:["Competitors",m("competitor")]})}),d.map((e,t)=>(0,s.jsx)("th",{className:"bg-gray-50 border-b ".concat(t<d.length-1?"border-r":""," border-gray-200"),children:(0,s.jsx)("button",{onClick:()=>x(e),className:"w-full p-3 font-medium text-gray-900 flex items-center justify-center hover:bg-gray-100 transition-colors",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[eh(e),m(e)]})})},e))]})}),(0,s.jsx)("tbody",{children:[...t].sort((e,t)=>{var a,s;if(null===i)return 0;if("competitor"===l)return"asc"===i?e.competitor.localeCompare(t.competitor):t.competitor.localeCompare(e.competitor);let r=(null==(a=e.providers[l])?void 0:a.visibilityScore)||0,n=(null==(s=t.providers[l])?void 0:s.visibilityScore)||0;return"asc"===i?r-n:n-r}).map((e,t)=>{var a;let r=null==n?void 0:n.find(t=>t.name===e.competitor||t.name.toLowerCase()===e.competitor.toLowerCase()),l=(null==r?void 0:r.url)?void 0:eg(e.competitor);return(0,s.jsxs)("tr",{className:t>0?"border-t border-gray-200":"",children:[(0,s.jsx)("td",{className:"border-r border-gray-200 bg-white",children:(0,s.jsx)(eu,{name:e.competitor,isOwn:e.isOwn,favicon:null==r||null==(a=r.metadata)?void 0:a.favicon,url:(null==r?void 0:r.url)||l})}),d.map((t,a)=>{let r=e.providers[t],n=(null==r?void 0:r.visibilityScore)||0;return(0,s.jsx)("td",{className:"text-center p-3 ".concat(a<d.length-1?"border-r border-gray-200":""),style:p(n),children:(0,s.jsxs)("span",{className:"text-orange-900 font-medium text-xs",children:[n,"%"]})},t)})]},e.competitor)})})]})})}var eb=a(25265);let ey=eb.bL,e_=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(eb.B8,{ref:t,className:(0,n.cn)("inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground",a),...r})});e_.displayName=eb.B8.displayName;let ev=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(eb.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow",a),...r})});ev.displayName=eb.l9.displayName;let ej=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(eb.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...r})});ej.displayName=eb.UC.displayName;var ew=a(94610),eN=a(1863),eE=a(84954);let eS=e=>{switch(e){case"OpenAI":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idR3duQxYl/theme/dark/symbol.svg?c=1dxbfHSJFAPEGdCLU4o5B",alt:"OpenAI",className:"w-7 h-7"});case"Anthropic":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idmJWF3N06/theme/dark/symbol.svg",alt:"Anthropic",className:"w-6 h-6"});case"Google":return(0,s.jsx)("div",{className:"w-7 h-7 flex items-center justify-center",children:(0,s.jsxs)("svg",{viewBox:"0 0 24 24",className:"w-7 h-7",children:[(0,s.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]})});case"Perplexity":return(0,s.jsx)("img",{src:"https://cdn.brandfetch.io/idNdawywEZ/w/800/h/800/theme/dark/icon.png?c=1dxbfHSJFAPEGdCLU4o5B",alt:"Perplexity",className:"w-6 h-6"});default:return(0,s.jsx)("div",{className:"w-7 h-7 bg-gray-400 rounded"})}},eA=e=>{let{name:t,isOwn:a,url:n}=e,[l,o]=(0,r.useState)(!1),i=n?"https://www.google.com/s2/favicons?domain=".concat(n,"&sz=64"):null;return(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-5 h-5 flex items-center justify-center rounded overflow-hidden flex-shrink-0",children:i&&!l?(0,s.jsx)(G.default,{src:i,alt:"".concat(t," logo"),width:20,height:20,className:"object-contain",onError:()=>o(!0)}):(0,s.jsx)("div",{className:"w-5 h-5 bg-gray-200 rounded flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-gray-600 font-semibold text-[10px]",children:t.charAt(0).toUpperCase()})})}),(0,s.jsx)("span",{className:"text-sm ".concat(a?"font-semibold text-black":"text-black"),children:t})]})},eC=e=>{let t=e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"").trim();if(!(t.length<3||["inc","llc","corp","company","the"].includes(t)))return"".concat(t,".com")};function eT(e){var t,a;let{providerRankings:n,brandName:p,shareOfVoice:x,averagePosition:m,sentimentScore:u,weeklyChange:h}=e,[g,f]=(0,r.useState)((null==n||null==(t=n[0])?void 0:t.provider)||"OpenAI");if(!n||0===n.length)return null;let b=e=>{switch(e){case"positive":return(0,s.jsx)(L,{variant:"secondary",className:"bg-green-50 text-black text-xs",children:"Positive"});case"negative":return(0,s.jsx)(L,{variant:"secondary",className:"bg-red-50 text-black text-xs",children:"Negative"});default:return(0,s.jsx)(L,{variant:"secondary",className:"bg-gray-50 text-black text-xs",children:"Neutral"})}},y=e=>e?e>0?(0,s.jsx)(eN.A,{className:"h-3 w-3 text-black"}):(0,s.jsx)(eE.A,{className:"h-3 w-3 text-black"}):(0,s.jsx)(ew.A,{className:"h-3 w-3 text-gray-400"}),_=n.find(e=>e.provider===g),v=(null==_?void 0:_.competitors)||[],j=v.findIndex(e=>e.isOwn)+1,w=(null==(a=v.find(e=>e.isOwn))?void 0:a.visibilityScore)||0;return(0,s.jsxs)(l,{className:"p-2 bg-card text-card-foreground gap-6 rounded-xl border py-6 shadow-sm border-gray-200 h-full flex flex-col",children:[(0,s.jsx)(o,{className:"border-b",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(i,{className:"text-xl font-semibold",children:"Provider Rankings"}),(0,s.jsx)(c,{className:"text-sm text-gray-600 mt-1",children:"Your brand performance by AI provider"})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-2xl font-bold text-orange-600",children:["#",j]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Average Rank"})]})]})}),(0,s.jsxs)(d,{className:"pt-6 pb-2 flex-1 flex flex-col",children:[(0,s.jsxs)(ey,{value:g,onValueChange:f,className:"flex-1 flex flex-col",children:[(0,s.jsx)(e_,{className:"grid w-full mb-2 h-14 ".concat(2===n.length?"grid-cols-2":3===n.length?"grid-cols-3":"grid-cols-4"),children:n.map(e=>{let{provider:t}=e;return(0,s.jsx)(ev,{value:t,className:"text-sm flex items-center justify-center h-full",title:t,children:eS(t)},t)})}),n.map(e=>{let{provider:t,competitors:a}=e;return(0,s.jsx)(ej,{value:t,className:"mt-0",children:(0,s.jsx)("div",{className:"overflow-x-auto rounded-lg border border-gray-200",children:(0,s.jsxs)("table",{className:"w-full border-collapse",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"bg-gray-50 border-b border-r border-gray-200 text-left p-3 text-xs font-medium text-gray-900 w-8",children:"#"}),(0,s.jsx)("th",{className:"bg-gray-50 border-b border-r border-gray-200 text-left p-3 text-xs font-medium text-gray-900 w-[200px]",children:"Company"}),(0,s.jsx)("th",{className:"bg-gray-50 border-b border-r border-gray-200 text-right p-3 text-xs font-medium text-gray-900",children:"Visibility"}),(0,s.jsx)("th",{className:"bg-gray-50 border-b border-r border-gray-200 text-right p-3 text-xs font-medium text-gray-900",children:"Share of Voice"}),(0,s.jsx)("th",{className:"bg-gray-50 border-b border-gray-200 text-right p-3 text-xs font-medium text-gray-900",children:"Sentiment"})]})}),(0,s.jsx)("tbody",{children:a.map((e,t)=>{let a=eC(e.name);return(0,s.jsxs)("tr",{className:"\n                            ".concat(t>0?"border-t border-gray-200":"","\n                            ").concat(e.isOwn?"bg-orange-50":"hover:bg-gray-50 transition-colors","\n                          "),children:[(0,s.jsx)("td",{className:"border-r border-gray-200 p-3 text-xs text-black",children:t+1}),(0,s.jsx)("td",{className:"border-r border-gray-200 p-3",children:(0,s.jsx)(eA,{name:e.name,isOwn:e.isOwn,url:a})}),(0,s.jsx)("td",{className:"border-r border-gray-200 p-3 text-right",children:(0,s.jsxs)("div",{className:"flex items-center justify-end gap-1",children:[(0,s.jsxs)("span",{className:"text-sm font-medium text-black",children:[e.visibilityScore,"%"]}),void 0!==e.weeklyChange&&0!==e.weeklyChange&&y(e.weeklyChange)]})}),(0,s.jsx)("td",{className:"border-r border-gray-200 p-3 text-right",children:(0,s.jsxs)("span",{className:"text-sm text-black",children:[e.shareOfVoice,"%"]})}),(0,s.jsx)("td",{className:"p-3 text-right",children:b(e.sentiment)})]},t)})})]})})},t)})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mt-6 pt-6 border-t",children:[(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[(0,s.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:"Competitors"}),(0,s.jsx)("p",{className:"text-lg font-semibold text-black",children:v.length})]}),(0,s.jsxs)("div",{className:"bg-orange-50 rounded-lg p-4 text-center",children:[(0,s.jsxs)("p",{className:"text-xs text-gray-500 mb-1",children:[p," Rank"]}),(0,s.jsxs)("p",{className:"text-lg font-semibold text-black",children:["#",j]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[(0,s.jsxs)("p",{className:"text-xs text-gray-500 mb-1",children:[p," Visibility"]}),(0,s.jsxs)("p",{className:"text-lg font-semibold text-black",children:[w,"%"]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[(0,s.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:"Share of Voice"}),(0,s.jsxs)("p",{className:"text-lg font-semibold text-black",children:[x,"%"]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[(0,s.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:"Average Position"}),(0,s.jsxs)("p",{className:"text-lg font-semibold text-black",children:["#",m]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[(0,s.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:"Sentiment Score"}),(0,s.jsxs)("p",{className:"text-lg font-semibold text-black",children:[u,"%"]})]})]})]})]})}class eP{parse(e){this.buffer+=e;let t=this.buffer.split("\n"),a=[];this.buffer=t[t.length-1];for(let e=0;e<t.length-1;e++){let s=t[e].trim();if(""===s){this.currentEvent.data&&(a.push({...this.currentEvent}),this.currentEvent={});continue}s.startsWith("event:")?this.currentEvent.event=s.slice(6).trim():s.startsWith("data:")&&(this.currentEvent.data=s.slice(5).trim())}return a}reset(){this.buffer=""}constructor(){this.buffer="",this.currentEvent={}}}var eO=a(37811);function eR(){var e,t,a;let{creditsAvailable:n=0,onCreditsUpdate:_,selectedAnalysis:v,onSaveAnalysis:j}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[w,N]=(0,r.useReducer)(u,m),[E]=(0,r.useState)("example.com"),S=function(){let e=(0,T.jE)(),{data:t}=(0,O.wV)();return(0,P.n)({mutationFn:async e=>{let t=await fetch("/api/brand-monitor/analyses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to save brand analysis");return t.json()},onSuccess:()=>{var a;e.invalidateQueries({queryKey:["brandAnalyses",null==t||null==(a=t.user)?void 0:a.id]})}})}(),[C,R]=(0,r.useState)(!1),k=(0,r.useRef)(!1),{startSSEConnection:M}=function(e){let{state:t,dispatch:a,onCreditsUpdate:s,onAnalysisComplete:n}=e,l=(0,r.useRef)(t.promptCompletionStatus),o=(0,r.useRef)(t.analyzingPrompts);(0,r.useEffect)(()=>{l.current=t.promptCompletionStatus},[t.promptCompletionStatus]),(0,r.useEffect)(()=>{o.current=t.analyzingPrompts},[t.analyzingPrompts]);let i=e=>{switch(console.log("[SSE] Received event:",e.type,e.data),e.type){case"credits":s&&s();break;case"progress":let r=e.data;a({type:"UPDATE_ANALYSIS_PROGRESS",payload:{stage:r.stage,progress:r.progress,message:r.message}});break;case"competitor-found":let i=e.data;a({type:"UPDATE_ANALYSIS_PROGRESS",payload:{competitors:[...t.analysisProgress.competitors||[],i.competitor]}});break;case"prompt-generated":let c=e.data,d=o.current||[],p=t.analysisProgress.prompts||[];if(d.length>0){p.includes(c.prompt)||a({type:"UPDATE_ANALYSIS_PROGRESS",payload:{prompts:[...p,c.prompt]}});break}if(!d.includes(c.prompt)){a({type:"UPDATE_ANALYSIS_PROGRESS",payload:{prompts:[...p,c.prompt]}}),a({type:"SET_ANALYZING_PROMPTS",payload:[...d,c.prompt]});let e={...l.current},s=c.prompt.trim();e[s]={},t.availableProviders.forEach(t=>{e[s][t]="pending"}),a({type:"SET_PROMPT_COMPLETION_STATUS",payload:e})}break;case"analysis-start":let x=e.data,m=x.prompt.trim();a({type:"UPDATE_ANALYSIS_PROGRESS",payload:{currentProvider:x.provider,currentPrompt:m}}),a({type:"UPDATE_PROMPT_STATUS",payload:{prompt:m,provider:x.provider,status:"running"}});let u=t.analysisTiles.findIndex(e=>e.prompt===x.prompt);if(-1!==u){let e={...t.analysisTiles[u]},s=e.providers.findIndex(e=>e.name===x.provider);-1!==s&&(e.providers[s].status="running",a({type:"UPDATE_ANALYSIS_TILE",payload:{index:u,tile:e}}))}break;case"partial-result":let h=e.data,g=h.prompt.trim();a({type:"UPDATE_ANALYSIS_PROGRESS",payload:{partialResults:[...t.analysisProgress.partialResults||[],h]}}),a({type:"UPDATE_PROMPT_STATUS",payload:{prompt:g,provider:h.provider,status:"completed"}});let f=t.analysisTiles.findIndex(e=>e.prompt===h.prompt);if(-1!==f){let e={...t.analysisTiles[f]},s=e.providers.findIndex(e=>e.name===h.provider);-1!==s&&(e.providers[s]={...e.providers[s],status:"completed",result:{brandMentioned:h.response.brandMentioned||!1,brandPosition:h.response.brandPosition,sentiment:h.response.sentiment||"neutral"}},a({type:"UPDATE_ANALYSIS_TILE",payload:{index:f,tile:e}}))}break;case"analysis-complete":let b=e.data;if(!b.prompt||!b.provider){console.error("[ERROR] Missing prompt or provider in analysis-complete event");break}let y=b.prompt.trim();if("failed"===b.status){a({type:"UPDATE_PROMPT_STATUS",payload:{prompt:y,provider:b.provider,status:"failed"}});let e=t.analysisTiles.findIndex(e=>e.prompt===b.prompt);if(-1!==e){let s={...t.analysisTiles[e]},r=s.providers.findIndex(e=>e.name===b.provider);-1!==r&&(s.providers[r].status="failed",a({type:"UPDATE_ANALYSIS_TILE",payload:{index:e,tile:s}}))}}a({type:"UPDATE_PROMPT_STATUS",payload:{prompt:y,provider:b.provider,status:"completed"}});break;case"complete":let _=e.data;a({type:"ANALYSIS_COMPLETE",payload:_.analysis}),s&&s(),n&&n(_.analysis);break;case"error":a({type:"SET_ERROR",payload:e.data.message||"Analysis failed"}),console.error("Analysis error:",e.data)}};return{startSSEConnection:async(e,t)=>{try{var s;let a=await fetch(e,t);if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to analyze")}let r=null==(s=a.body)?void 0:s.getReader();if(!r)throw Error("No response body");let n=new eP,l=new TextDecoder;for(;;){let{done:e,value:t}=await r.read();if(e)break;let a=l.decode(t,{stream:!0});for(let e of n.parse(a))if(e.data)try{let t=JSON.parse(e.data);i(t)}catch(e){console.error("Failed to parse SSE event:",e)}}}catch(e){e instanceof TypeError&&e.message.includes("network")?a({type:"SET_ERROR",payload:"Connection lost. Please check your internet connection and try again."}):a({type:"SET_ERROR",payload:"Failed to analyze brand visibility"}),console.error(e),a({type:"SET_ANALYSIS_PROGRESS",payload:{stage:"initializing",progress:0,message:"",competitors:[],prompts:[],partialResults:[]}})}}}}({state:w,dispatch:N,onCreditsUpdate:_,onAnalysisComplete:e=>{if(!v&&!k.current){k.current=!0;let t={url:(null==Y?void 0:Y.url)||L,companyName:null==Y?void 0:Y.name,industry:null==Y?void 0:Y.industry,analysisData:e,competitors:$,prompts:es,creditsUsed:p.CT};S.mutate(t,{onSuccess:e=>{console.log("Analysis saved successfully:",e),j&&j(e)},onError:e=>{console.error("Failed to save analysis:",e),k.current=!1}})}}}),{url:L,urlValid:D,error:z,loading:U,analyzing:F,preparingAnalysis:G,company:Y,showInput:B,showCompanyCard:K,showPromptsList:q,showCompetitors:J,customPrompts:Z,removedDefaultPrompts:X,identifiedCompetitors:$,availableProviders:Q,analysisProgress:ee,promptCompletionStatus:ea,analyzingPrompts:es,analysis:er,activeResultsTab:en,expandedPromptIndex:el,showAddPromptModal:ep,showAddCompetitorModal:ex,newPromptText:em,newCompetitorName:eu,newCompetitorUrl:eh,scrapingCompetitors:eg}=w;(0,r.useEffect)(()=>{v&&v.analysisData?(R(!0),N({type:"SET_ANALYSIS",payload:v.analysisData}),v.companyName&&N({type:"SCRAPE_SUCCESS",payload:{name:v.companyName,url:v.url,industry:v.industry}}),setTimeout(()=>R(!1),100)):null===v&&(N({type:"RESET_STATE"}),k.current=!1,R(!1))},[v]);let eb=(0,r.useCallback)(e=>{N({type:"SET_URL",payload:e}),z&&N({type:"SET_ERROR",payload:null}),e.length>0?N({type:"SET_URL_VALID",payload:h(e)}):N({type:"SET_URL_VALID",payload:null})},[z]),ey=(0,r.useCallback)(async()=>{if(!L)return void N({type:"SET_ERROR",payload:"Please enter a URL"});if(!h(L)){N({type:"SET_ERROR",payload:"Please enter a valid URL (e.g., example.com or https://example.com)"}),N({type:"SET_URL_VALID",payload:!1});return}if(n<1)return void N({type:"SET_ERROR",payload:"Insufficient credits. You need at least 1 credit to analyze a URL."});console.log("Starting scrape for URL:",L),N({type:"SET_LOADING",payload:!0}),N({type:"SET_ERROR",payload:null}),N({type:"SET_URL_VALID",payload:!0});try{let t=await fetch("/api/brand-monitor/scrape",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:L,maxAge:6048e5})});if(console.log("Scrape response status:",t.status),!t.ok)try{var e;let a=await t.json();if(console.error("Scrape API error:",a),null==(e=a.error)?void 0:e.message)throw new x.Q$(a);throw Error(a.error||"Failed to scrape")}catch(e){if(e instanceof x.Q$)throw e;throw Error("Failed to scrape")}let a=await t.json();if(console.log("Scrape data received:",a),!a.company)throw Error("No company data received");_&&_(),N({type:"SET_SHOW_INPUT",payload:!1}),setTimeout(()=>{N({type:"SCRAPE_SUCCESS",payload:a.company}),setTimeout(()=>{N({type:"SET_SHOW_COMPANY_CARD",payload:!0}),console.log("Showing company card")},50)},500)}catch(t){let e="Failed to extract company information";t instanceof x.Q$?e=t.getUserMessage():t.message&&(e="Failed to extract company information: ".concat(t.message)),N({type:"SET_ERROR",payload:e}),console.error("HandleScrape error:",t)}finally{N({type:"SET_LOADING",payload:!1})}},[L,n,_]),e_=(0,r.useCallback)(async()=>{var e;if(!Y)return;N({type:"SET_PREPARING_ANALYSIS",payload:!0});try{let e=await fetch("/api/brand-monitor/check-providers",{method:"POST"});if(e.ok){let t=await e.json();N({type:"SET_AVAILABLE_PROVIDERS",payload:t.providers||["OpenAI","Anthropic","Google"]})}}catch(t){let e=[];eO.env.NEXT_PUBLIC_HAS_OPENAI_KEY&&e.push("OpenAI"),eO.env.NEXT_PUBLIC_HAS_ANTHROPIC_KEY&&e.push("Anthropic"),N({type:"SET_AVAILABLE_PROVIDERS",payload:e.length>0?e:["OpenAI","Anthropic"]})}let t=(null==(e=Y.scrapedData)?void 0:e.competitors)||[],a=function(e){let t=e.toLowerCase();for(let[e,a]of Object.entries({"web scraping":[{name:"Apify",url:"apify.com"},{name:"Scrapy",url:"scrapy.org"},{name:"Octoparse",url:"octoparse.com"},{name:"ParseHub",url:"parsehub.com"},{name:"Diffbot",url:"diffbot.com"},{name:"Import.io",url:"import.io"},{name:"Bright Data",url:"brightdata.com"},{name:"Zyte",url:"zyte.com"}],AI:[{name:"OpenAI",url:"openai.com"},{name:"Anthropic",url:"anthropic.com"},{name:"Google AI",url:"ai.google"},{name:"Microsoft Azure",url:"azure.microsoft.com"},{name:"IBM Watson",url:"ibm.com/watson"},{name:"Amazon AWS",url:"aws.amazon.com"}],SaaS:[{name:"Salesforce",url:"salesforce.com"},{name:"HubSpot",url:"hubspot.com"},{name:"Zendesk",url:"zendesk.com"},{name:"Slack",url:"slack.com"},{name:"Monday.com",url:"monday.com"},{name:"Asana",url:"asana.com"}],"E-commerce":[{name:"Shopify",url:"shopify.com"},{name:"WooCommerce",url:"woocommerce.com"},{name:"BigCommerce",url:"bigcommerce.com"},{name:"Magento",url:"magento.com"},{name:"Squarespace",url:"squarespace.com"},{name:"Wix",url:"wix.com"}],Cloud:[{name:"AWS",url:"aws.amazon.com"},{name:"Google Cloud",url:"cloud.google.com"},{name:"Microsoft Azure",url:"azure.microsoft.com"},{name:"DigitalOcean",url:"digitalocean.com"},{name:"Linode",url:"linode.com"},{name:"Vultr",url:"vultr.com"}]}))if(t.includes(e.toLowerCase())||e.toLowerCase().includes(t))return a;return[{name:"Competitor 1"},{name:"Competitor 2"},{name:"Competitor 3"},{name:"Competitor 4"},{name:"Competitor 5"}]}(Y.industry||""),s=new Map;a.forEach(e=>{let t=f(e.name);s.set(t,e)}),t.forEach(e=>{let t=f(e),a=s.get(t);if(a){if(!a.url){let a=b(e);s.set(t,{name:e,url:a})}return}let r=b(e);s.set(t,{name:e,url:r})});let r=Array.from(s.values()).filter(e=>"Competitor 1"!==e.name&&"Competitor 2"!==e.name&&"Competitor 3"!==e.name&&"Competitor 4"!==e.name&&"Competitor 5"!==e.name).slice(0,10);console.log("Identified competitors:",r=r.slice(0,6)),N({type:"SET_IDENTIFIED_COMPETITORS",payload:r}),N({type:"SET_SHOW_COMPETITORS",payload:!0}),N({type:"SET_PREPARING_ANALYSIS",payload:!1})},[Y]),ev=(0,r.useCallback)(()=>{let e=document.querySelector(".animate-panel-in");e&&e.classList.add("opacity-0"),setTimeout(()=>{N({type:"SET_SHOW_COMPETITORS",payload:!1}),N({type:"SET_SHOW_PROMPTS_LIST",payload:!0})},300)},[]),ej=(0,r.useCallback)(async()=>{if(!Y)return;if(k.current=!1,n<p.CT)return void N({type:"SET_ERROR",payload:"Insufficient credits. You need at least ".concat(p.CT," credits to run an analysis.")});_&&_();let e=y(Y),t=new Date().getFullYear(),a=[...["Best ".concat(e,"s in ").concat(t,"?"),"Top ".concat(e,"s for startups?"),"Most popular ".concat(e,"s today?"),"Recommended ".concat(e,"s for developers?")].filter((e,t)=>!X.includes(t)),...Z].map(e=>e.trim());N({type:"SET_ANALYZING_PROMPTS",payload:a}),console.log("Starting analysis..."),N({type:"SET_ANALYZING",payload:!0}),N({type:"SET_ANALYSIS_PROGRESS",payload:{stage:"initializing",progress:0,message:"Starting analysis...",competitors:[],prompts:[],partialResults:[]}}),N({type:"SET_ANALYSIS_TILES",payload:[]});let s={},r=A().map(e=>e.name);a.forEach(e=>{s[e]={},r.forEach(t=>{s[e][t]="pending"})}),N({type:"SET_PROMPT_COMPLETION_STATUS",payload:s});try{await M("/api/brand-monitor/analyze",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({company:Y,prompts:a,competitors:$})})}finally{N({type:"SET_ANALYZING",payload:!1})}},[Y,X,Z,$,M,n]),ew=(0,r.useCallback)(()=>{N({type:"RESET_STATE"}),k.current=!1,R(!1)},[]),eN=(0,r.useCallback)(async e=>{let t=e.map(e=>({...e,url:e.url?g(e.url):void 0})).filter(e=>e.url);0!==t.length&&console.log("Batch scraping validated competitors:",t)},[]),eE=null==er||null==(e=er.competitors)?void 0:e.find(e=>e.isOwn);return(0,s.jsxs)("div",{className:"flex flex-col",children:[B&&(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[50vh]",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-8",children:(0,s.jsx)(I,{url:L,urlValid:D,loading:U,analyzing:F,onUrlChange:eb,onSubmit:ey})})}),!B&&Y&&!q&&!F&&!er&&(0,s.jsx)("div",{className:"flex items-center justify-center animate-panel-in",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"w-full space-y-6",children:(0,s.jsx)("div",{className:"transition-all duration-500 ".concat(K?"opacity-100 translate-y-0":"opacity-0 translate-y-4"),children:(0,s.jsx)(W,{company:Y,onAnalyze:e_,analyzing:G,showCompetitors:J,identifiedCompetitors:$,onRemoveCompetitor:e=>N({type:"REMOVE_COMPETITOR",payload:e}),onAddCompetitor:()=>{N({type:"TOGGLE_MODAL",payload:{modal:"addCompetitor",show:!0}}),N({type:"SET_NEW_COMPETITOR",payload:{name:"",url:""}})},onContinueToAnalysis:ev})})})})}),q&&Y&&!er&&(0,s.jsx)("div",{className:"max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-8",children:(0,s.jsx)(V,{company:Y,analyzing:F,identifiedCompetitors:$,scrapingCompetitors:eg,analysisProgress:ee,prompts:es,customPrompts:Z,removedDefaultPrompts:X,promptCompletionStatus:ea,onRemoveDefaultPrompt:e=>N({type:"REMOVE_DEFAULT_PROMPT",payload:e}),onRemoveCustomPrompt:e=>{N({type:"SET_CUSTOM_PROMPTS",payload:Z.filter(t=>t!==e)})},onAddPromptClick:()=>{N({type:"TOGGLE_MODAL",payload:{modal:"addPrompt",show:!0}}),N({type:"SET_NEW_PROMPT_TEXT",payload:""})},onStartAnalysis:ej,detectServiceType:y})}),er&&eE&&(0,s.jsx)("div",{className:"flex-1 flex justify-center animate-panel-in pt-8",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex gap-6 relative",children:[(0,s.jsx)(H,{activeTab:en,onTabChange:e=>{N({type:"SET_ACTIVE_RESULTS_TAB",payload:e})},onRestart:ew}),(0,s.jsx)("div",{className:"flex-1 flex flex-col",children:(0,s.jsxs)("div",{className:"w-full flex-1 flex flex-col",children:["visibility"===en&&(0,s.jsx)(eo,{competitors:er.competitors,brandData:eE,identifiedCompetitors:$}),"matrix"===en&&(0,s.jsxs)(l,{className:"p-2 bg-card text-card-foreground gap-6 rounded-xl border py-6 shadow-sm border-gray-200 h-full flex flex-col",children:[(0,s.jsx)(o,{className:"border-b",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(i,{className:"text-xl font-semibold",children:"Comparison Matrix"}),(0,s.jsx)(c,{className:"text-sm text-gray-600 mt-1",children:"Compare visibility scores across different AI providers"})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-2xl font-bold text-orange-600",children:[eE.visibilityScore,"%"]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Average Score"})]})]})}),(0,s.jsx)(d,{className:"pt-6 flex-1 overflow-auto",children:er.providerComparison?(0,s.jsx)(ef,{data:er.providerComparison,brandName:(null==Y?void 0:Y.name)||"",competitors:$}):(0,s.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,s.jsx)("p",{children:"No comparison data available"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Please ensure AI providers are configured and the analysis has completed."})]})})]}),"rankings"===en&&er.providerRankings&&(0,s.jsx)("div",{id:"provider-rankings",className:"h-full",children:(0,s.jsx)(eT,{providerRankings:er.providerRankings,brandName:(null==Y?void 0:Y.name)||"Your Brand",shareOfVoice:eE.shareOfVoice,averagePosition:Math.round(eE.averagePosition),sentimentScore:eE.sentimentScore,weeklyChange:eE.weeklyChange})}),"prompts"===en&&er.prompts&&(0,s.jsxs)(l,{className:"p-2 bg-card text-card-foreground gap-6 rounded-xl border py-6 shadow-sm border-gray-200 h-full flex flex-col",children:[(0,s.jsx)(o,{className:"border-b",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(i,{className:"text-xl font-semibold",children:"Prompts & Responses"}),(0,s.jsx)(c,{className:"text-sm text-gray-600 mt-1",children:"AI responses to your brand queries"})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"text-2xl font-bold text-orange-600",children:er.prompts.length}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Total Prompts"})]})]})}),(0,s.jsx)(d,{className:"pt-6 flex-1 overflow-auto",children:(0,s.jsx)(et,{prompts:er.prompts,responses:er.responses,expandedPromptIndex:el,onToggleExpand:e=>N({type:"SET_EXPANDED_PROMPT_INDEX",payload:e}),brandName:(null==(t=er.company)?void 0:t.name)||"",competitors:(null==(a=er.competitors)?void 0:a.map(e=>e.name))||[]})})]})]})})]})})}),z&&(0,s.jsx)(ei,{error:z,onDismiss:()=>N({type:"SET_ERROR",payload:null})}),(0,s.jsx)(ec,{isOpen:ep,promptText:em,onPromptTextChange:e=>N({type:"SET_NEW_PROMPT_TEXT",payload:e}),onAdd:()=>{em.trim()&&(N({type:"ADD_CUSTOM_PROMPT",payload:em.trim()}),N({type:"TOGGLE_MODAL",payload:{modal:"addPrompt",show:!1}}),N({type:"SET_NEW_PROMPT_TEXT",payload:""}))},onClose:()=>{N({type:"TOGGLE_MODAL",payload:{modal:"addPrompt",show:!1}}),N({type:"SET_NEW_PROMPT_TEXT",payload:""})}}),(0,s.jsx)(ed,{isOpen:ex,competitorName:eu,competitorUrl:eh,onNameChange:e=>N({type:"SET_NEW_COMPETITOR",payload:{name:e}}),onUrlChange:e=>N({type:"SET_NEW_COMPETITOR",payload:{url:e}}),onAdd:async()=>{if(eu.trim()){let e=eh.trim(),t=e?g(e):void 0,a={name:eu.trim(),url:t};N({type:"ADD_COMPETITOR",payload:a}),N({type:"TOGGLE_MODAL",payload:{modal:"addCompetitor",show:!1}}),N({type:"SET_NEW_COMPETITOR",payload:{name:"",url:""}}),a.url&&await eN([a])}},onClose:()=>{N({type:"TOGGLE_MODAL",payload:{modal:"addCompetitor",show:!1}}),N({type:"SET_NEW_COMPETITOR",payload:{name:"",url:""}})}})]})}var ek=a(8449),eI=a(72346),eM=a(15361),eL=a(91574),eD=a(9944),ez=a(46629),eU=a(8256);function eF(e){var t;let{session:a}=e,n=(0,ek.useRouter)(),{customer:l,isLoading:o,error:i}=(0,eL.cN)(),c=(0,eL.DE)(),[d,p]=(0,r.useState)(!1),[x,m]=(0,r.useState)(null),[u,h]=(0,r.useState)(!1),[g,f]=(0,r.useState)(null),{data:b,isLoading:y}=function(){var e,t;let{data:a}=(0,O.wV)();return(0,C.I)({queryKey:["brandAnalyses",null==a||null==(e=a.user)?void 0:e.id],queryFn:async()=>{let e=await fetch("/api/brand-monitor/analyses");if(!e.ok)throw Error("Failed to fetch brand analyses");return e.json()},enabled:!!(null==a||null==(t=a.user)?void 0:t.id)})}(),{data:_}=function(e){var t;let{data:a}=(0,O.wV)();return(0,C.I)({queryKey:["brandAnalysis",e],queryFn:async()=>{let t=await fetch("/api/brand-monitor/analyses/".concat(e));if(!t.ok)throw Error("Failed to fetch brand analysis");return t.json()},enabled:!!(null==a||null==(t=a.user)?void 0:t.id)&&!!e})}(x),v=function(){let e=(0,T.jE)(),{data:t}=(0,O.wV)();return(0,P.n)({mutationFn:async e=>{let t=await fetch("/api/brand-monitor/analyses/".concat(e),{method:"DELETE"});if(!t.ok)throw Error("Failed to delete brand analysis");return t.json()},onSuccess:()=>{var a;e.invalidateQueries({queryKey:["brandAnalyses",null==t||null==(a=t.user)?void 0:a.id]})}})}(),j=null==l||null==(t=l.features)?void 0:t.messages,w=j&&j.balance||0;(0,r.useEffect)(()=>{((null==i?void 0:i.code)==="UNAUTHORIZED"||(null==i?void 0:i.code)==="AUTH_ERROR")&&n.push("/login")},[i,n]);let N=async()=>{await c()},E=async e=>{f(e),h(!0)},S=async()=>{g&&(await v.mutateAsync(g),x===g&&m(null),f(null))};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("div",{className:"relative overflow-hidden bg-white border-b",children:(0,s.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 py-12",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"text-center flex-1",children:[(0,s.jsxs)("h1",{className:"text-4xl lg:text-5xl font-bold tracking-tight mb-2 animate-fade-in-up",children:[(0,s.jsx)("span",{className:"block text-zinc-900",children:"FireGEO Monitor"}),(0,s.jsx)("span",{className:"block bg-gradient-to-r from-red-600 to-yellow-500 bg-clip-text text-transparent",children:"AI Brand Visibility Platform"})]}),(0,s.jsx)("p",{className:"text-lg text-zinc-600 animate-fade-in-up animation-delay-200",children:"Track how AI models rank your brand against competitors"})]})})})})}),(0,s.jsxs)("div",{className:"flex h-[calc(100vh-12rem)] relative",children:[(0,s.jsx)("button",{onClick:()=>p(!d),className:"absolute top-2 z-10 p-2 bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 border border-gray-200 ".concat(d?"left-[324px]":"left-4"),"aria-label":"Toggle sidebar",children:d?(0,s.jsx)(eI.A,{className:"h-5 w-5 text-gray-600"}):(0,s.jsx)(eM.A,{className:"h-5 w-5 text-gray-600"})}),(0,s.jsxs)("div",{className:"".concat(d?"w-80":"w-0"," bg-white border-r overflow-hidden flex flex-col transition-all duration-200"),children:[(0,s.jsx)("div",{className:"p-4 border-b",children:(0,s.jsxs)(eD.$,{onClick:()=>{m(null)},className:"w-full btn-firecrawl-orange",children:[(0,s.jsx)(F.A,{className:"w-4 h-4 mr-2"}),"New Analysis"]})}),(0,s.jsx)("div",{className:"overflow-y-auto flex-1",children:y?(0,s.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Loading analyses..."}):(null==b?void 0:b.length)===0?(0,s.jsx)("div",{className:"p-4 text-center text-gray-500",children:"No analyses yet"}):(0,s.jsx)("div",{className:"space-y-1 p-2",children:null==b?void 0:b.map(e=>(0,s.jsx)("div",{className:"p-3 rounded-lg cursor-pointer hover:bg-gray-100 ".concat(x===e.id?"bg-gray-100":""),onClick:()=>m(e.id),children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"font-medium truncate",children:e.companyName||"Untitled Analysis"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 truncate",children:e.url}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:e.createdAt&&(0,ez.GP)(new Date(e.createdAt),"MMM d, yyyy")})]}),(0,s.jsx)(eD.$,{size:"sm",variant:"ghost",onClick:t=>{t.stopPropagation(),E(e.id)},children:(0,s.jsx)(U.A,{className:"w-4 h-4"})})]})},e.id))})})]}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,s.jsx)("div",{className:"px-6 sm:px-8 lg:px-12 py-8",children:(0,s.jsx)(eR,{creditsAvailable:w,onCreditsUpdate:N,selectedAnalysis:x?_:null,onSaveAnalysis:e=>{}})})})]}),(0,s.jsx)(eU.K,{open:u,onOpenChange:h,title:"Delete Analysis",description:"Are you sure you want to delete this analysis? This action cannot be undone.",confirmText:"Delete",cancelText:"Cancel",onConfirm:S,isLoading:v.isPending})]})}function eG(){let{data:e,isPending:t}=(0,O.wV)();return t?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsx)(k.A,{className:"h-8 w-8 animate-spin text-gray-500"})}):e?(0,s.jsx)(eF,{session:e}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-gray-600",children:"Please log in to access the brand monitor"})})})}},76882:(e,t,a)=>{Promise.resolve().then(a.bind(a,58815))}},e=>{var t=t=>e(e.s=t);e.O(0,[4350,3677,6887,73,7220,6504,4834,7086,1750,6605,7497,6703,588,7358],()=>t(76882)),_N_E=e.O()}]);