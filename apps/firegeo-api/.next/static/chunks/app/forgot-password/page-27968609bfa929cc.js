(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2162],{8449:(e,t,r)=>{"use strict";var a=r(42221);r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},19856:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var a=r(12359);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:c="",children:m,iconNode:x,...u}=e;return(0,a.createElement)("svg",{ref:t,...d,width:s,height:s,stroke:r,strokeWidth:i?24*Number(l)/Number(s):l,className:n("lucide",c),...!m&&!o(u)&&{"aria-hidden":"true"},...u},[...x.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let r=(0,a.forwardRef)((r,l)=>{let{className:o,...d}=r;return(0,a.createElement)(c,{ref:l,iconNode:t,className:n("lucide-".concat(s(i(e))),"lucide-".concat(e),o),...d})});return r.displayName=i(e),r}},25171:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19856).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},62e3:(e,t,r)=>{"use strict";r.d(t,{CI:()=>i,Hh:()=>l,Jv:()=>s,wV:()=>n,yK:()=>a});let a=(0,r(33677).MB)({baseURL:"http://localhost:3000",fetchOptions:{credentials:"include"}}),{signIn:s,signUp:l,signOut:i,useSession:n}=a},74985:(e,t,r)=>{Promise.resolve().then(r.bind(r,95669))},95669:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var a=r(87183),s=r(12359),l=r(8449),i=r(34540),n=r.n(i),o=r(57220),d=r(62e3),c=r(25171);function m(){let[e,t]=(0,s.useState)(""),[r,i]=(0,s.useState)(!1),[m,x]=(0,s.useState)(!1),[u,h]=(0,s.useState)("");(0,l.useRouter)();let g=async t=>{t.preventDefault(),h(""),i(!0);try{await d.yK.forgetPassword({email:e,redirectTo:"/reset-password"}),x(!0)}catch(e){h(e.message||"Failed to send reset email")}finally{i(!1)}};return m?(0,a.jsxs)("div",{className:"min-h-screen flex",children:[(0,a.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 p-12 items-center justify-center relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-400/90 via-orange-500/90 to-orange-600/90"}),(0,a.jsxs)("div",{className:"relative z-10 max-w-md text-white",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Check your inbox!"}),(0,a.jsx)("p",{className:"text-lg opacity-90",children:"We've sent you instructions to reset your password. Check your email to continue."})]}),(0,a.jsx)("div",{className:"absolute top-20 right-20 w-64 h-64 bg-orange-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"}),(0,a.jsx)("div",{className:"absolute bottom-20 left-20 w-64 h-64 bg-orange-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"})]}),(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-white",children:(0,a.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4",children:(0,a.jsx)("svg",{className:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),(0,a.jsx)("h2",{className:"text-3xl font-extrabold text-gray-900",children:"Check your email"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"We've sent a password reset link to"}),(0,a.jsx)("p",{className:"mt-1 text-lg font-medium text-gray-900",children:e}),(0,a.jsx)("p",{className:"mt-4 text-sm text-gray-500",children:"Didn't receive the email? Check your spam folder or try again."}),(0,a.jsxs)(n(),{href:"/login",className:"mt-6 inline-flex items-center text-sm text-orange-600 hover:text-orange-500",children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Back to login"]})]})})})]}):(0,a.jsxs)("div",{className:"min-h-screen flex",children:[(0,a.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 p-12 items-center justify-center relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-orange-400/90 via-orange-500/90 to-orange-600/90"}),(0,a.jsxs)("div",{className:"relative z-10 max-w-md text-white",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Forgot your password?"}),(0,a.jsx)("p",{className:"text-lg opacity-90",children:"No worries! We'll help you reset it and get back to building amazing things."})]}),(0,a.jsx)("div",{className:"absolute top-20 right-20 w-64 h-64 bg-orange-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"}),(0,a.jsx)("div",{className:"absolute bottom-20 left-20 w-64 h-64 bg-orange-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"})]}),(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-white",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"lg:hidden mb-8 flex justify-center",children:(0,a.jsx)(o.default,{src:"/firecrawl-logo-with-fire.webp",alt:"Firecrawl",width:180,height:37,priority:!0})}),(0,a.jsx)("h2",{className:"text-center text-3xl font-extrabold text-gray-900",children:"Reset your password"}),(0,a.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Enter your email and we'll send you a reset link"})]}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:g,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm",placeholder:"Enter your email"})]}),u&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:u}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:r,className:"btn-firecrawl-default w-full inline-flex items-center justify-center whitespace-nowrap rounded-[10px] text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 h-10 px-4",children:r?"Sending...":"Send reset link"})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(n(),{href:"/login",className:"text-sm text-orange-600 hover:text-orange-500 inline-flex items-center",children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Back to login"]})})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3677,7220,4540,6703,588,7358],()=>t(74985)),_N_E=e.O()}]);