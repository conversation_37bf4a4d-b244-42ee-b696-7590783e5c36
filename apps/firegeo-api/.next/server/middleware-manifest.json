{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\..*).))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\..*).)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "KUdw3UMc98wkbSZokqE3h", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ZxtUFUflcKxit+0w7y53sFeJX/+ejIrVriQ7zSCNpU0=", "__NEXT_PREVIEW_MODE_ID": "2a808d00f401b352f99ca14a861afd67", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "66c2e0105a1cf58f2eb08c9d535102dcd404c6290f90ddeb9a5436905c3c1a29", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a3639486369b67ecfbd52674b5b7a049b30e695ebd5d94cfbedf78c6fe21ba5e"}}}, "functions": {}, "sortedMiddleware": ["/"]}