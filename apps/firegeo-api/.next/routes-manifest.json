{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/auth/[...all]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPall": "nxtPall"}, "namedRegex": "^/api/auth/(?<nxtPall>.+?)(?:/)?$"}, {"page": "/api/autumn/[...all]", "regex": "^/api/autumn/(.+?)(?:/)?$", "routeKeys": {"nxtPall": "nxtPall"}, "namedRegex": "^/api/autumn/(?<nxtPall>.+?)(?:/)?$"}, {"page": "/api/brand-monitor/analyses/[analysisId]", "regex": "^/api/brand\\-monitor/analyses/([^/]+?)(?:/)?$", "routeKeys": {"nxtPanalysisId": "nxtPanalysisId"}, "namedRegex": "^/api/brand\\-monitor/analyses/(?<nxtPanalysisId>[^/]+?)(?:/)?$"}, {"page": "/api/chat/[conversationId]", "regex": "^/api/chat/([^/]+?)(?:/)?$", "routeKeys": {"nxtPconversationId": "nxtPconversationId"}, "namedRegex": "^/api/chat/(?<nxtPconversationId>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/autumn-verify", "regex": "^/autumn\\-verify(?:/)?$", "routeKeys": {}, "namedRegex": "^/autumn\\-verify(?:/)?$"}, {"page": "/brand-monitor", "regex": "^/brand\\-monitor(?:/)?$", "routeKeys": {}, "namedRegex": "^/brand\\-monitor(?:/)?$"}, {"page": "/chat", "regex": "^/chat(?:/)?$", "routeKeys": {}, "namedRegex": "^/chat(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/plans", "regex": "^/plans(?:/)?$", "routeKeys": {}, "namedRegex": "^/plans(?:/)?$"}, {"page": "/pricing-dynamic", "regex": "^/pricing\\-dynamic(?:/)?$", "routeKeys": {}, "namedRegex": "^/pricing\\-dynamic(?:/)?$"}, {"page": "/pricing-public", "regex": "^/pricing\\-public(?:/)?$", "routeKeys": {}, "namedRegex": "^/pricing\\-public(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/reset-password", "regex": "^/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/reset\\-password(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}