import type * as Kit from '@sveltejs/kit';

type Expand<T> = T extends infer O ? { [K in keyof O]: O[K] } : never;
// @ts-ignore
type MatcherParam<M> = M extends (param : string) => param is infer U ? U extends string ? U : string : string;
type RouteParams = {  };
type RouteId = '/(marketing)';
type MaybeWithVoid<T> = {} extends T ? T | void : T;
export type RequiredKeys<T> = { [K in keyof T]-?: {} extends { [P in K]: T[K] } ? never : K; }[keyof T];
type OutputDataShape<T> = MaybeWithVoid<Omit<App.PageData, RequiredKeys<T>> & Partial<Pick<App.PageData, keyof T & keyof App.PageData>> & Record<string, any>>
type EnsureDefined<T> = T extends null | undefined ? {} : T;
type OptionalUnion<U extends Record<string, any>, A extends keyof U = U extends U ? keyof U : never> = U extends unknown ? { [P in Exclude<A, keyof U>]?: never } & U : never;
export type Snapshot<T = any> = Kit.Snapshot<T>;
type LayoutRouteId = RouteId | "/(marketing)/account" | "/(marketing)/account/api" | "/(marketing)/account/billing" | "/(marketing)/account/billing/manage" | "/(marketing)/account/select_plan" | "/(marketing)/account/sign_out" | "/(marketing)/account/subscribe/[slug]" | "/(marketing)/blog" | "/(marketing)/blog/(posts)/awesome_post" | "/(marketing)/blog/(posts)/example_blog_post" | "/(marketing)/blog/(posts)/how_we_built_our_41kb_saas_website" | "/(marketing)/contact_us" | "/(marketing)/find-env" | "/(marketing)/login" | "/(marketing)/login/check_email" | "/(marketing)/login/confirm" | "/(marketing)/login/current_password_error" | "/(marketing)/login/forgot_password" | "/(marketing)/login/sign_in" | "/(marketing)/login/sign_up" | "/(marketing)/onboarding" | "/(marketing)/pricing" | "/(marketing)/search"
type LayoutParams = RouteParams & { slug?: string }
type LayoutParentData = EnsureDefined<import('../$types.js').LayoutData>;

export type LayoutServerData = null;
export type LayoutData = Expand<LayoutParentData>;
export type LayoutProps = { data: LayoutData; children: import("svelte").Snippet }