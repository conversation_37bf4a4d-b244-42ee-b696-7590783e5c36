import type * as Kit from '@sveltejs/kit';

type Expand<T> = T extends infer O ? { [K in keyof O]: O[K] } : never;
// @ts-ignore
type MatcherParam<M> = M extends (param : string) => param is infer U ? U extends string ? U : string : string;
type RouteParams = { envSlug: string };
type RouteId = '/(admin)/dashboard/[envSlug]';
type MaybeWithVoid<T> = {} extends T ? T | void : T;
export type RequiredKeys<T> = { [K in keyof T]-?: {} extends { [P in K]: T[K] } ? never : K; }[keyof T];
type OutputDataShape<T> = MaybeWithVoid<Omit<App.PageData, RequiredKeys<T>> & Partial<Pick<App.PageData, keyof T & keyof App.PageData>> & Record<string, any>>
type EnsureDefined<T> = T extends null | undefined ? {} : T;
type OptionalUnion<U extends Record<string, any>, A extends keyof U = U extends U ? keyof U : never> = U extends unknown ? { [P in Exclude<A, keyof U>]?: never } & U : never;
export type Snapshot<T = any> = Kit.Snapshot<T>;
type LayoutRouteId = RouteId | "/(admin)/dashboard/[envSlug]/(menu)" | "/(admin)/dashboard/[envSlug]/(menu)/billing" | "/(admin)/dashboard/[envSlug]/(menu)/billing/manage" | "/(admin)/dashboard/[envSlug]/(menu)/settings" | "/(admin)/dashboard/[envSlug]/(menu)/settings/change_email" | "/(admin)/dashboard/[envSlug]/(menu)/settings/change_email_subscription" | "/(admin)/dashboard/[envSlug]/(menu)/settings/change_password" | "/(admin)/dashboard/[envSlug]/(menu)/settings/delete_account" | "/(admin)/dashboard/[envSlug]/(menu)/settings/edit_profile" | "/(admin)/dashboard/[envSlug]/(menu)/settings/reset_password" | "/(admin)/dashboard/[envSlug]/agent-seo" | "/(admin)/dashboard/[envSlug]/campaign-orchestrator" | "/(admin)/dashboard/[envSlug]/content-agent" | "/(admin)/dashboard/[envSlug]/create_profile" | "/(admin)/dashboard/[envSlug]/researcher" | "/(admin)/dashboard/[envSlug]/select_plan" | "/(admin)/dashboard/[envSlug]/subscribe/[slug]"
type LayoutParams = RouteParams & { envSlug?: string; slug?: string }
type LayoutParentData = Omit<EnsureDefined<import('../../../$types.js').LayoutData>, keyof import('../../$types.js').LayoutData> & EnsureDefined<import('../../$types.js').LayoutData>;

export type EntryGenerator = () => Promise<Array<RouteParams>> | Array<RouteParams>;
export type LayoutServerData = null;
export type LayoutData = Expand<LayoutParentData>;
export type LayoutProps = { data: LayoutData; children: import("svelte").Snippet }