var lr=Object.defineProperty;var cr=(e,t,r)=>t in e?lr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var an=(e,t,r)=>cr(e,typeof t!="symbol"?t+"":t,r);import{b as Se,d as Ee,a as se,W as Ft,T as vn,S as An,U as _n,p as Be,l as G,j as ye,m as ge,aT as B,i as v,g as Bt,h as Ue,e as qe,f as ht,c as tt,r as nt,t as qt,ak as _e,o as wn,s as fr}from"./DDiqt3uM.js";import{d as Ht,g as Ye,w as L,r as Et}from"./rjRVMZXi.js";import{p as Xe,n as sn}from"./ClgAkN2B.js";import{a as dt}from"./RnwjOPnl.js";import{U as Sn,N as dr,P as mr,a as pr,b as En,e as on,c as yr,p as gr,d as hr,f as On,h as mt,j as br,g as vr}from"./L1Y_HHz6.js";import{Z as b}from"./BjLTCHJw.js";import{a as Ar}from"./C4iS2aBk.js";import"./CWj6FrbW.js";import"./DhRTwODG.js";import{s as je}from"./iCEqKm8o.js";import{i as He}from"./B_FgA42l.js";import{l as De,s as Kt,p as ce}from"./C-ZVHnwW.js";import{d as ut,s as Y,a as bt}from"./B82PTGnX.js";import{c as Me}from"./Bf9nHHn7.js";import{L as _r}from"./Cn9N4mio.js";import{s as Tn}from"./DWulv87v.js";import{e as Pn,i as Mn}from"./OiKQa7Wx.js";import{a as It}from"./C36Ip9GY.js";import{t as wr}from"./A4ulxp7Q.js";import{i as Sr}from"./2C89X9tI.js";import{b as Er}from"./Dqu9JXqq.js";import{f as Or}from"./DSm1r-pw.js";import{I as Tr}from"./CkoRhfQ8.js";import{c as Pr,s as Mr}from"./DE2v8SHj.js";import{n as jr}from"./DKwX7yNB.js";import"./BGh_Dfnt.js";import"./ChutyBgo.js";class Ot extends Error{constructor(t,r){super(t),this.name="DevalueError",this.path=r.join("")}}function un(e){return Object(e)!==e}const xr=Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function kr(e){const t=Object.getPrototypeOf(e);return t===Object.prototype||t===null||Object.getOwnPropertyNames(t).sort().join("\0")===xr}function Fr(e){return Object.prototype.toString.call(e).slice(8,-1)}function Ir(e){switch(e){case'"':return'\\"';case"<":return"\\u003C";case"\\":return"\\\\";case`
`:return"\\n";case"\r":return"\\r";case"	":return"\\t";case"\b":return"\\b";case"\f":return"\\f";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";default:return e<" "?`\\u${e.charCodeAt(0).toString(16).padStart(4,"0")}`:""}}function Ve(e){let t="",r=0;const n=e.length;for(let i=0;i<n;i+=1){const s=e[i],o=Ir(s);o&&(t+=e.slice(r,i)+o,r=i+1)}return`"${r===0?e:t+e.slice(r)}"`}function $r(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.getOwnPropertyDescriptor(e,t).enumerable)}const Nr=/^[a-zA-Z_$][a-zA-Z_$0-9]*$/;function ln(e){return Nr.test(e)?"."+e:"["+JSON.stringify(e)+"]"}function Dr(e,t){const r=[],n=new Map,i=[];if(t)for(const c of Object.getOwnPropertyNames(t))i.push({key:c,fn:t[c]});const s=[];let o=0;function u(c){if(typeof c=="function")throw new Ot("Cannot stringify a function",s);if(n.has(c))return n.get(c);if(c===void 0)return Sn;if(Number.isNaN(c))return dr;if(c===1/0)return mr;if(c===-1/0)return pr;if(c===0&&1/c<0)return En;const m=o++;n.set(c,m);for(const{key:S,fn:T}of i){const P=T(c);if(P)return r[m]=`["${S}",${u(P)}]`,m}let y="";if(un(c))y=Tt(c);else{const S=Fr(c);switch(S){case"Number":case"String":case"Boolean":y=`["Object",${Tt(c)}]`;break;case"BigInt":y=`["BigInt",${c}]`;break;case"Date":y=`["Date","${!isNaN(c.getDate())?c.toISOString():""}"]`;break;case"RegExp":const{source:P,flags:O}=c;y=O?`["RegExp",${Ve(P)},"${O}"]`:`["RegExp",${Ve(P)}]`;break;case"Array":y="[";for(let h=0;h<c.length;h+=1)h>0&&(y+=","),h in c?(s.push(`[${h}]`),y+=u(c[h]),s.pop()):y+=yr;y+="]";break;case"Set":y='["Set"';for(const h of c)y+=`,${u(h)}`;y+="]";break;case"Map":y='["Map"';for(const[h,F]of c)s.push(`.get(${un(h)?Tt(h):"..."})`),y+=`,${u(h)},${u(F)}`,s.pop();y+="]";break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const F=on(c.buffer);y='["'+S+'","'+F+'"]';break}case"ArrayBuffer":{y=`["ArrayBuffer","${on(c)}"]`;break}default:if(!kr(c))throw new Ot("Cannot stringify arbitrary non-POJOs",s);if($r(c).length>0)throw new Ot("Cannot stringify POJOs with symbolic keys",s);if(Object.getPrototypeOf(c)===null){y='["null"';for(const h in c)s.push(ln(h)),y+=`,${Ve(h)},${u(c[h])}`,s.pop();y+="]"}else{y="{";let h=!1;for(const F in c)h&&(y+=","),h=!0,s.push(ln(F)),y+=`${Ve(F)}:${u(c[F])}`,s.pop();y+="}"}}}return r[m]=y,m}const f=u(e);return f<0?`${f}`:`[${r.join(",")}]`}function Tt(e){const t=typeof e;return t==="string"?Ve(e):e instanceof String?Ve(e.toString()):e===void 0?Sn.toString():e===0&&1/e<0?En.toString():t==="bigint"?`["BigInt","${e}"]`:String(e)}function Lr(e,t){const r=De(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n=[["circle",{cx:"12",cy:"12",r:"10"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16"}]];Tr(e,Kt({name:"circle-alert"},()=>r,{get iconNode(){return n},children:(i,s)=>{var o=Se(),u=Ee(o);je(u,t,"default",{},null),se(i,o)},$$slots:{default:!0}}))}function $t(e,t,r){return e[t]=r,"skip"}function Cr(e,t){return t.value!==void 0&&typeof t.value!="object"&&t.path.length<e.length}function Pe(e,t,r={}){r.modifier||(r.modifier=i=>Cr(t,i)?void 0:i.value);const n=be(e,t,r.modifier);if(n)return r.value===void 0||r.value(n.value)?n:void 0}function be(e,t,r){if(!t.length)return;const n=[t[0]];let i=e;for(;i&&n.length<t.length;){const o=n[n.length-1],u=r?r({parent:i,key:String(o),value:i[o],path:n.map(f=>String(f)),isLeaf:!1,set:f=>$t(i,o,f)}):i[o];if(u===void 0)return;i=u,n.push(t[n.length])}if(!i)return;const s=t[t.length-1];return{parent:i,key:String(s),value:i[s],path:t.map(o=>String(o)),isLeaf:!0,set:o=>$t(i,s,o)}}function we(e,t,r=[]){for(const n in e){const i=e[n],s=i===null||typeof i!="object",o={parent:e,key:n,value:i,path:r.concat([n]),isLeaf:s,set:f=>$t(e,n,f)},u=t(o);if(u==="abort")return u;if(u==="skip")continue;if(!s){const f=we(i,t,o.path);if(f==="abort")return f}}}function Zr(e,t){return e===t||e.size===t.size&&[...e].every(r=>t.has(r))}function cn(e,t){const r=new Map;function n(u,f){return u instanceof Date&&f instanceof Date&&u.getTime()!==f.getTime()||u instanceof Set&&f instanceof Set&&!Zr(u,f)||u instanceof File&&f instanceof File&&u!==f}function i(u){return u instanceof Date||u instanceof Set||u instanceof File}function s(u,f){const c=f?be(f,u.path):void 0;function m(){return r.set(u.path.join(" "),u.path),"skip"}if(i(u.value)&&(!i(c==null?void 0:c.value)||n(u.value,c.value)))return m();u.isLeaf&&(!c||u.value!==c.value)&&m()}we(e,u=>s(u,t)),we(t,u=>s(u,e));const o=Array.from(r.values());return o.sort((u,f)=>u.length-f.length),o}function me(e,t,r){const n=typeof r=="function";for(const i of t){const s=be(e,i,({parent:o,key:u,value:f})=>((f===void 0||typeof f!="object")&&(o[u]={}),o[u]));s&&(s.parent[s.key]=n?r(i,s):r)}}function ze(e){return e.toString().split(/[[\].]+/).filter(t=>t)}function Qe(e){return e.reduce((t,r)=>{const n=String(r);return typeof r=="number"||/^\d+$/.test(n)?t+=`[${n}]`:t?t+=`.${n}`:t+=n,t},"")}function et(e){const t={}.toString.call(e).slice(8,-1);if(t=="Set")return new Set([...e].map(r=>et(r)));if(t=="Map")return new Map([...e].map(r=>[et(r[0]),et(r[1])]));if(t=="Date")return new Date(e.getTime());if(t=="RegExp")return RegExp(e.source,e.flags);if(t=="Array"||t=="Object"){const r=t=="Object"?Object.create(Object.getPrototypeOf(e)):[];for(const n in e)r[n]=et(e[n]);return r}return e}function ne(e){return e&&typeof e=="object"?et(e):e}function vt(e,t){if(typeof e=="boolean")throw new pe("Schema property cannot be defined as boolean.",t)}const lt=e=>{if(typeof e=="object"&&e!==null){if(typeof Object.getPrototypeOf=="function"){const t=Object.getPrototypeOf(e);return t===Object.prototype||t===null}return Object.prototype.toString.call(e)==="[object Object]"}return!1},oe=(...e)=>e.reduce((t,r)=>{if(r===void 0)return t;if(Array.isArray(r))throw new TypeError("Arguments provided to ts-deepmerge must be objects, not arrays.");return Object.keys(r).forEach(n=>{["__proto__","constructor","prototype"].includes(n)||(Array.isArray(t[n])&&Array.isArray(r[n])?t[n]=oe.options.mergeArrays?oe.options.uniqueArrayItems?Array.from(new Set(t[n].concat(r[n]))):[...t[n],...r[n]]:r[n]:lt(t[n])&&lt(r[n])?t[n]=oe(t[n],r[n]):!lt(t[n])&&lt(r[n])?t[n]=oe(r[n],void 0):t[n]=r[n]===void 0?oe.options.allowUndefinedOverrides?r[n]:t[n]:r[n])}),t},{}),Nt={allowUndefinedOverrides:!0,mergeArrays:!0,uniqueArrayItems:!0};oe.options=Nt;oe.withOptions=(e,...t)=>{oe.options=Object.assign(Object.assign({},Nt),e);const r=oe(...t);return oe.options=Nt,r};const Rr=["unix-time","bigint","any","symbol","set","int64"];function he(e,t,r){var c;vt(e,r);const n=jn(e,r),i=e.items&&n.includes("array")?(Array.isArray(e.items)?e.items:[e.items]).filter(m=>typeof m!="boolean"):void 0,s=e.additionalProperties&&typeof e.additionalProperties=="object"&&n.includes("object")?Object.fromEntries(Object.entries(e.additionalProperties).filter(([,m])=>typeof m!="boolean")):void 0,o=e.properties&&n.includes("object")?Object.fromEntries(Object.entries(e.properties).filter(([,m])=>typeof m!="boolean")):void 0,u=(c=Vr(e))==null?void 0:c.filter(m=>m.type!=="null"&&m.const!==null),f={types:n.filter(m=>m!=="null"),isOptional:t,isNullable:n.includes("null"),schema:e,union:u!=null&&u.length?u:void 0,array:i,properties:o,additionalProperties:s,required:e.required};return!e.allOf||!e.allOf.length?f:{...oe.withOptions({allowUndefinedOverrides:!1},f,...e.allOf.map(m=>he(m,!1,[]))),schema:e}}function jn(e,t){vt(e,t);let r=e.const===null?["null"]:[];if(e.type&&(r=Array.isArray(e.type)?e.type:[e.type]),e.anyOf&&(r=e.anyOf.flatMap(n=>jn(n,t))),r.includes("array")&&e.uniqueItems){const n=r.findIndex(i=>i!="array");r[n]="set"}else if(e.format&&Rr.includes(e.format)){if(r.unshift(e.format),e.format=="unix-time"||e.format=="int64"){const n=r.findIndex(i=>i=="integer");r.splice(n,1)}if(e.format=="bigint"){const n=r.findIndex(i=>i=="string");r.splice(n,1)}}return e.const&&e.const!==null&&typeof e.const!="function"&&r.push(typeof e.const),Array.from(new Set(r))}function Vr(e){if(!(!e.anyOf||!e.anyOf.length))return e.anyOf.filter(t=>typeof t!="boolean")}function zr(e,t=!1,r=[]){return Ze(e,t,r)}function Ze(e,t,r){var c;if(!e)throw new pe("Schema was undefined",r);const n=he(e,t,r);if(!n)return;let i;if("default"in e)if(n.types.includes("object")&&e.default&&typeof e.default=="object"&&!Array.isArray(e.default))i=e.default;else{if(n.types.length>1&&n.types.includes("unix-time")&&(n.types.includes("integer")||n.types.includes("number")))throw new pe("Cannot resolve a default value with a union that includes a date and a number/integer.",r);const[m]=n.types;return Ur(m,e.default)}let s;const o=()=>!n.union||n.union.length<2?!1:n.union.some(m=>m.enum)?!0:(s||(s=new Set(n.types.map(m=>["integer","unix-time"].includes(m)?"number":m))),s.size>1);let u;if(!i&&n.union){const m=n.union.filter(y=>typeof y!="boolean"&&y.default!==void 0);if(m.length==1)return Ze(m[0],t,r);if(m.length>1)throw new pe("Only one default value can exist in a union, or set a default value for the whole union.",r);if(n.isNullable)return null;if(n.isOptional)return;if(o())throw new pe("Multi-type unions must have a default value, or exactly one of the union types must have.",r);if(n.union.length)if(n.types[0]=="object")u===void 0&&(u={}),u=n.union.length>1?oe.withOptions({allowUndefinedOverrides:!0},...n.union.map(y=>Ze(y,t,r))):Ze(n.union[0],t,r);else return Ze(n.union[0],t,r)}if(!i){if(n.isNullable)return null;if(n.isOptional)return}if(n.properties)for(const[m,y]of Object.entries(n.properties)){vt(y,[...r,m]);const S=i&&i[m]!==void 0?i[m]:Ze(y,!((c=n.required)!=null&&c.includes(m)),[...r,m]);u===void 0&&(u={}),u[m]=S}else if(i)return i;if(e.enum)return e.enum[0];if("const"in e)return e.const;if(o())throw new pe("Default values cannot have more than one type.",r);if(n.types.length==0)return;const[f]=n.types;return u??Br(f,e.enum)}function Ur(e,t){switch(e){case"set":return Array.isArray(t)?new Set(t):t;case"Date":case"date":case"unix-time":if(typeof t=="string"||typeof t=="number")return new Date(t);break;case"bigint":if(typeof t=="string"||typeof t=="number")return BigInt(t);break;case"symbol":if(typeof t=="string"||typeof t=="number")return Symbol(t);break}return t}function Br(e,t){switch(e){case"string":return t&&t.length>0?t[0]:"";case"number":case"integer":return t&&t.length>0?t[0]:0;case"boolean":return!1;case"array":return[];case"object":return{};case"null":return null;case"Date":case"date":case"unix-time":return;case"int64":case"bigint":return BigInt(0);case"set":return new Set;case"symbol":return Symbol();case"undefined":case"any":return;default:throw new pe("Schema type or format not supported, requires explicit default value: "+e)}}class Z extends Error{constructor(t){super(t),Object.setPrototypeOf(this,Z.prototype)}}class pe extends Z{constructor(r,n){super((n&&n.length?`[${Array.isArray(n)?n.join("."):n}] `:"")+r);an(this,"path");this.path=Array.isArray(n)?n.join("."):n,Object.setPrototypeOf(this,pe.prototype)}}function qr(e,t){var i;const r={};function n(s){if("_errors"in r||(r._errors=[]),!Array.isArray(r._errors))if(typeof r._errors=="string")r._errors=[r._errors];else throw new Z("Form-level error was not an array.");r._errors.push(s.message)}for(const s of e){if(!s.path||s.path.length==1&&!s.path[0]){n(s);continue}const u=!/^\d$/.test(String(s.path[s.path.length-1]))&&((i=Pe(t,s.path.filter(y=>/\D/.test(String(y)))))==null?void 0:i.value),f=be(r,s.path,({value:y,parent:S,key:T})=>(y===void 0&&(S[T]={}),S[T]));if(!f){n(s);continue}const{parent:c,key:m}=f;u?(m in c||(c[m]={}),"_errors"in c[m]?c[m]._errors.push(s.message):c[m]._errors=[s.message]):m in c?c[m].push(s.message):c[m]=[s.message]}return r}function fn(e,t,r){return r?e:(we(t,n=>{Array.isArray(n.value)&&n.set(void 0)}),we(e,n=>{!Array.isArray(n.value)&&n.value!==void 0||me(t,[n.path],n.value)}),t)}function Hr(e){return xn(e,[])}function xn(e,t){return Object.entries(e).filter(([,n])=>n!==void 0).flatMap(([n,i])=>{if(Array.isArray(i)&&i.length>0){const s=t.concat([n]);return{path:Qe(s),messages:i}}else return xn(e[n],t.concat([n]))})}function dn(e){e.flashMessage&&Dt(e)&&(document.cookie=`flash=; Max-Age=0; Path=${e.flashMessage.cookiePath??"/"};`)}function Dt(e){return e.flashMessage?e.syncFlashMessage:!1}function Lt(e){const t=JSON.parse(e);return t.data&&(t.data=gr(t.data,hr.decoders)),t}function Pt(e){return HTMLElement.prototype.cloneNode.call(e)}function Kr(e,t=()=>{}){const r=async({action:i,result:s,reset:o=!0,invalidateAll:u=!0})=>{s.type==="success"&&(o&&HTMLFormElement.prototype.reset.call(e),u&&await On()),(location.origin+location.pathname===i.origin+i.pathname||s.type==="redirect"||s.type==="error")&&await mt(s)};async function n(i){var O,h,F,ue,$;if(((O=i.submitter)!=null&&O.hasAttribute("formmethod")?i.submitter.formMethod:Pt(e).method)!=="post")return;i.preventDefault();const o=new URL((h=i.submitter)!=null&&h.hasAttribute("formaction")?i.submitter.formAction:Pt(e).action),u=(F=i.submitter)!=null&&F.hasAttribute("formenctype")?i.submitter.formEnctype:Pt(e).enctype,f=new FormData(e),c=(ue=i.submitter)==null?void 0:ue.getAttribute("name");c&&f.append(c,(($=i.submitter)==null?void 0:$.getAttribute("value"))??"");const m=new AbortController;let y=!1;const T=await t({action:o,cancel:()=>y=!0,controller:m,formData:f,formElement:e,submitter:i.submitter})??r;if(y)return;let P;try{const E=new Headers({accept:"application/json","x-sveltekit-action":"true"});u!=="multipart/form-data"&&E.set("Content-Type",/^(:?application\/x-www-form-urlencoded|text\/plain)$/.test(u)?u:"application/x-www-form-urlencoded");const I=u==="multipart/form-data"?f:new URLSearchParams(f),ae=await fetch(o,{method:"POST",headers:E,cache:"no-store",body:I,signal:m.signal});P=Lt(await ae.text()),P.type==="error"&&(P.status=ae.status)}catch(E){if((E==null?void 0:E.name)==="AbortError")return;P={type:"error",error:E}}await T({action:o,formData:f,formElement:e,update:E=>r({action:o,result:P,reset:E==null?void 0:E.reset,invalidateAll:E==null?void 0:E.invalidateAll}),result:P})}return HTMLFormElement.prototype.addEventListener.call(e,"submit",n),{destroy(){HTMLFormElement.prototype.removeEventListener.call(e,"submit",n)}}}const kn="noCustomValidity";async function mn(e,t){"setCustomValidity"in e&&e.setCustomValidity(""),!(kn in e.dataset)&&Fn(e,t)}function Wr(e,t){for(const r of e.querySelectorAll("input,select,textarea,button")){if("dataset"in r&&kn in r.dataset||!r.name)continue;const n=be(t,ze(r.name)),i=n&&typeof n.value=="object"&&"_errors"in n.value?n.value._errors:n==null?void 0:n.value;if(Fn(r,i),i)return}}function Fn(e,t){if(!("setCustomValidity"in e))return;const r=t&&t.length?t.join(`
`):"";e.setCustomValidity(r),r&&e.reportValidity()}const Jr=(e,t=0)=>{const r=e.getBoundingClientRect();return r.top>=t&&r.left>=0&&r.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&r.right<=(window.innerWidth||document.documentElement.clientWidth)},Gr=(e,t=1.125,r="smooth")=>{const s=e.getBoundingClientRect().top+window.pageYOffset-window.innerHeight/(2*t);window.scrollTo({left:0,top:s,behavior:r})},Yr=["checkbox","radio","range","file"];function pn(e){const t=!!e&&(e instanceof HTMLSelectElement||e instanceof HTMLInputElement&&Yr.includes(e.type)),r=!!e&&e instanceof HTMLSelectElement&&e.multiple,n=!!e&&e instanceof HTMLInputElement&&e.type=="file";return{immediate:t,multiple:r,file:n}}var K;(function(e){e[e.Idle=0]="Idle",e[e.Submitting=1]="Submitting",e[e.Delayed=2]="Delayed",e[e.Timeout=3]="Timeout"})(K||(K={}));const Xr=new Set;function Qr(e,t,r){let n=K.Idle,i,s;const o=Xr;function u(){f(),m(n!=K.Delayed?K.Submitting:K.Delayed),i=window.setTimeout(()=>{i&&n==K.Submitting&&m(K.Delayed)},r.delayMs),s=window.setTimeout(()=>{s&&n==K.Delayed&&m(K.Timeout)},r.timeoutMs),o.add(f)}function f(){clearTimeout(i),clearTimeout(s),i=s=0,o.delete(f),m(K.Idle)}function c(){o.forEach(h=>h()),o.clear()}function m(h){n=h,t.submitting.set(n>=K.Submitting),t.delayed.set(n>=K.Delayed),t.timeout.set(n>=K.Timeout)}const y=e;function S(h){const F=h.target;r.selectErrorText&&F.select()}function T(){r.selectErrorText&&y.querySelectorAll("input").forEach(h=>{h.addEventListener("invalid",S)})}function P(){r.selectErrorText&&y.querySelectorAll("input").forEach(h=>h.removeEventListener("invalid",S))}const O=e;{T();const h=F=>{F.clearAll?c():f(),F.cancelled||setTimeout(()=>Ct(O,r),1)};return dt(()=>{P(),h({cancelled:!0})}),{submitting(){u()},completed:h,scrollToFirstError(){setTimeout(()=>Ct(O,r),1)},isSubmitting:()=>n===K.Submitting||n===K.Delayed}}}const Ct=async(e,t)=>{if(t.scrollToError=="off")return;const r=t.errorSelector;if(!r)return;await Ft();let n;if(n=e.querySelector(r),!n)return;n=n.querySelector(r)??n;const i=t.stickyNavbar?document.querySelector(t.stickyNavbar):null;typeof t.scrollToError!="string"?n.scrollIntoView(t.scrollToError):Jr(n,(i==null?void 0:i.offsetHeight)??0)||Gr(n,void 0,t.scrollToError);function s(u){return typeof t.autoFocusOnError=="boolean"?t.autoFocusOnError:!/iPhone|iPad|iPod|Android/i.test(u)}if(!s(navigator.userAgent))return;let o;if(o=n,["INPUT","SELECT","BUTTON","TEXTAREA"].includes(o.tagName)||(o=o.querySelector('input:not([type="hidden"]):not(.flatpickr-input), select, textarea')),o)try{o.focus({preventScroll:!0}),t.selectErrorText&&o.tagName=="INPUT"&&o.select()}catch{}};function yt(e,t,r){const n=be(e,t,({parent:i,key:s,value:o})=>(o===void 0&&(i[s]=/\D/.test(s)?{}:[]),i[s]));if(n){const i=r(n.value);n.parent[n.key]=i}return e}function ea(e,t,r){const n=e.form,i=ze(t),s=Ht(n,o=>{const u=be(o,i);return u==null?void 0:u.value});return{subscribe(...o){const u=s.subscribe(...o);return()=>u()},update(o,u){n.update(f=>yt(f,i,o),u??r)},set(o,u){n.update(f=>yt(f,i,()=>o),u??r)}}}function ta(e,t){const r="form"in e;if(!r&&(t==null?void 0:t.taint)!==void 0)throw new Z("If options.taint is set, the whole superForm object must be used as a proxy.");return r}function ct(e,t,r){const n=ze(t);if(ta(e,r))return ea(e,t,r);const i=Ht(e,s=>{const o=be(s,n);return o==null?void 0:o.value});return{subscribe(...s){const o=i.subscribe(...s);return()=>o()},update(s){e.update(o=>yt(o,n,s))},set(s){e.update(o=>yt(o,n,()=>s))}}}function na(e,t=[]){const r=Zt(e,t);if(!r)throw new pe("No shape could be created for schema.",t);return r}function Zt(e,t){vt(e,t);const r=he(e,!1,t);if(r.array||r.union){const n=r.array||[],i=r.union||[];return n.concat(i).reduce((s,o)=>{const u=Zt(o,t);return u&&(s={...s??{},...u}),s},n.length?{}:void 0)}if(r.properties){const n={};for(const[i,s]of Object.entries(r.properties)){const o=Zt(s,[...t,i]);o&&(n[i]=o)}return n}return r.types.includes("array")||r.types.includes("object")?{}:void 0}function Rt(e){let t={};const r=Array.isArray(e);for(const[n,i]of Object.entries(e))!i||typeof i!="object"||(r?t={...t,...Rt(i)}:t[n]=Rt(i));return t}const ft=new WeakMap,Ce=new WeakMap,In=e=>{throw e.result.error},ra={applyAction:!0,invalidateAll:!0,resetForm:!0,autoFocusOnError:"detect",scrollToError:"smooth",errorSelector:'[aria-invalid="true"],[data-invalid]',selectErrorText:!1,stickyNavbar:void 0,taintedMessage:!1,onSubmit:void 0,onResult:void 0,onUpdate:void 0,onUpdated:void 0,onError:In,dataType:"form",validators:void 0,customValidity:!1,clearOnSubmit:"message",delayMs:500,timeoutMs:8e3,multipleSubmits:"prevent",SPA:void 0,validationMethod:"auto"};function aa(e){return`Duplicate form id's found: "${e}". Multiple forms will receive the same data. Use the id option to differentiate between them, or if this is intended, set the warnings.duplicateId option to false in superForm to disable this warning. More information: https://superforms.rocks/concepts/multiple-forms`}let $n=!1;try{SUPERFORMS_LEGACY&&($n=!0)}catch{}let Re=!1;try{globalThis.STORIES&&(Re=!0)}catch{}function Ui(e,t){var rn;let r,n=t??{},i;{if((n.legacy??$n)&&(n.resetForm===void 0&&(n.resetForm=!1),n.taintedMessage===void 0&&(n.taintedMessage=!0)),Re&&n.applyAction===void 0&&(n.applyAction=!1),typeof n.SPA=="string"&&(n.invalidateAll===void 0&&(n.invalidateAll=!1),n.applyAction===void 0&&(n.applyAction=!1)),i=n.validators,n={...ra,...n},(n.SPA===!0||typeof n.SPA=="object")&&n.validators===void 0&&console.warn("No validators set for superForm in SPA mode. Add a validation adapter to the validators option, or set it to false to disable this warning."),!e)throw new Z("No form data sent to superForm. Make sure the output from superValidate is used (usually data.form) and that it's not null or undefined. Alternatively, an object with default values for the form can also be used, but then constraints won't be available.");c(e)===!1&&(e={id:n.id??Math.random().toString(36).slice(2,10),valid:!1,posted:!1,errors:{},data:e,shape:Rt(e)}),e=e;const a=e.id=n.id??e.id,l=Ye(Xe)??(Re?{}:void 0);if(((rn=n.warnings)==null?void 0:rn.duplicateId)!==!1)if(!ft.has(l))ft.set(l,new Set([a]));else{const d=ft.get(l);d!=null&&d.has(a)?console.warn(aa(a)):d==null||d.add(a)}if(Ce.has(e)||Ce.set(e,e),r=Ce.get(e),e=ne(r),dt(()=>{var d;nr(),Jn(),ir();for(const p of Object.values(Q))p.length=0;(d=ft.get(l))==null||d.delete(a)}),n.dataType!=="json"){const d=(p,g)=>{if(!(!g||typeof g!="object")){if(Array.isArray(g))g.length>0&&d(p,g[0]);else if(!(g instanceof Date)&&!(g instanceof File)&&!(g instanceof FileList))throw new Z(`Object found in form field "${p}". Set the dataType option to "json" and add use:enhance to use nested data structures. More information: https://superforms.rocks/concepts/nested-data`)}};for(const[p,g]of Object.entries(e.data))d(p,g)}}const s={formId:e.id,form:ne(e.data),constraints:e.constraints??{},posted:e.posted,errors:ne(e.errors),message:ne(e.message),tainted:void 0,valid:e.valid,submitting:!1,shape:e.shape},o=s,u=L(n.id??e.id);function f(a){return Object.values(a).filter(d=>c(d)!==!1)}function c(a){return!a||typeof a!="object"||!("valid"in a&&"errors"in a&&typeof a.valid=="boolean")?!1:"id"in a&&typeof a.id=="string"?a.id:!1}const m=L(e.data),y={subscribe:m.subscribe,set:(a,l={})=>{const d=ne(a);return Xt(d,l.taint??!0),m.set(d)},update:(a,l={})=>m.update(d=>{const p=a(d);return Xt(p,l.taint??!0),p})};function S(){return n.SPA===!0||typeof n.SPA=="object"}function T(a){var l;return a>400?a:(typeof n.SPA=="boolean"||typeof n.SPA=="string"||(l=n.SPA)==null?void 0:l.failStatus)||a}async function P(a={}){const l=a.formData??o.form;let d={},p;const g=a.adapter??n.validators;if(typeof g=="object"){if(g!=i&&!("jsonSchema"in g))throw new Z('Client validation adapter found in options.validators. A full adapter must be used when changing validators dynamically, for example "zod" instead of "zodClient".');if(p=await g.validate(l),!p.success)d=qr(p.issues,g.shape??o.shape??{});else if(a.recheckValidData!==!1)return P({...a,recheckValidData:!1})}else p={success:!0,data:{}};const _={...o.form,...l,...p.success?p.data:{}};return{valid:p.success,posted:!1,errors:d,data:_,constraints:o.constraints,message:void 0,id:o.formId,shape:o.shape}}function O(a){if(!n.onChange||!a.paths.length||a.type=="blur")return;let l;const d=a.paths.map(Qe);a.type&&a.paths.length==1&&a.formElement&&a.target instanceof Element?l={path:d[0],paths:d,formElement:a.formElement,target:a.target,set(p,g,_){ct({form:y},p,_).set(g)},get(p){return Ye(ct(y,p))}}:l={paths:d,target:void 0,set(p,g,_){ct({form:y},p,_).set(g)},get(p){return Ye(ct(y,p))}},n.onChange(l)}async function h(a,l=!1,d){a&&(n.validators=="clear"&&le.update(_=>(me(_,a.paths,void 0),_)),setTimeout(()=>O(a)));let p=!1;if(l||(n.validationMethod=="onsubmit"||n.validationMethod=="submit-only"||n.validationMethod=="onblur"&&(a==null?void 0:a.type)=="input"||n.validationMethod=="oninput"&&(a==null?void 0:a.type)=="blur")&&(p=!0),p||!a||!n.validators||n.validators=="clear"){if(a!=null&&a.paths){const _=(a==null?void 0:a.formElement)??Ge();_&&F(_)}return}const g=await P({adapter:d});return g.valid&&(a.immediate||a.type!="input")&&y.set(g.data,{taint:"ignore"}),await Ft(),ue(g.errors,a,l),g}function F(a){const l=new Map;if(n.customValidity&&a)for(const d of a.querySelectorAll("[name]")){if(typeof d.name!="string"||!d.name.length)continue;const p="validationMessage"in d?String(d.validationMessage):"";l.set(d.name,{el:d,message:p}),mn(d,void 0)}return l}async function ue(a,l,d){const{type:p,immediate:g,multiple:_,paths:ee}=l,Te=o.errors,Fe={};let q=new Map;const V=l.formElement??Ge();V&&(q=F(V)),we(a,N=>{if(!Array.isArray(N.value))return;const z=[...N.path];z[z.length-1]=="_errors"&&z.pop();const Le=z.join(".");function te(){if(me(Fe,[N.path],N.value),n.customValidity&&Ie&&q.has(Le)){const{el:W,message:$e}=q.get(Le);$e!=N.value&&(setTimeout(()=>mn(W,N.value)),q.clear())}}if(d)return te();const st=N.path[N.path.length-1]=="_errors",Ie=N.value&&ee.some(W=>st?z&&W&&z.length>0&&z[0]==W[0]:Le==W.join("."));if(Ie&&n.validationMethod=="oninput"||g&&!_&&Ie)return te();if(_){const W=Pe(Ye(le),N.path.slice(0,-1));if(W!=null&&W.value&&typeof(W==null?void 0:W.value)=="object"){for(const $e of Object.values(W.value))if(Array.isArray($e))return te()}}const Ae=Pe(Te,N.path);if(Ae&&Ae.key in Ae.parent)return te();if(st){if(n.validationMethod=="oninput"||p=="blur"&&Qn(Qe(N.path.slice(0,-1))))return te()}else if(p=="blur"&&Ie)return te()}),le.set(Fe)}function $(a,l={}){return l.keepFiles&&we(o.form,d=>{if(!(d.parent instanceof FileList)&&(d.value instanceof File||d.value instanceof FileList)){const p=Pe(a,d.path);(!p||!(p.key in p.parent))&&me(a,[d.path],d.value)}}),y.set(a,l)}function E(a,l){return a&&l&&n.resetForm&&(n.resetForm===!0||n.resetForm())}function I(a=!0){let l=o.form,d=o.tainted;if(a){const p=or(o.form);l=p.data;const g=p.paths;g.length&&(d=ne(d)??{},me(d,g,!1))}return{valid:o.valid,posted:o.posted,errors:o.errors,data:l,constraints:o.constraints,message:o.message,id:o.formId,tainted:d,shape:o.shape}}async function ae(a,l){a.valid&&l&&E(a.valid,l)?ve({message:a.message,posted:!0}):it({form:a,untaint:l,keepFiles:!0,pessimisticUpdate:n.invalidateAll=="force"||n.invalidateAll=="pessimistic"}),Q.onUpdated.length&&await Ft();for(const d of Q.onUpdated)d({form:a})}function ve(a={}){a.newState&&(r.data={...r.data,...a.newState});const l=ne(r);l.data={...l.data,...a.data},a.id!==void 0&&(l.id=a.id),it({form:l,untaint:!0,message:a.message,keepFiles:!1,posted:a.posted,resetted:!0})}async function rt(a){if(a.type=="error")throw new Z(`ActionResult of type "${a.type}" cannot be passed to update function.`);if(a.type=="redirect"){E(!0,!0)&&ve({posted:!0});return}if(typeof a.data!="object")throw new Z("Non-object validation data returned from ActionResult.");const l=f(a.data);if(!l.length)throw new Z("No form data returned from ActionResult. Make sure you return { form } in the form actions.");for(const d of l)d.id===o.formId&&await ae(d,a.status>=200&&a.status<300)}const Oe=L(s.message),Ke=L(s.constraints),We=L(s.posted),ke=L(s.shape),Je=L(e.errors),le={subscribe:Je.subscribe,set(a,l){return Je.set(fn(a,o.errors,l==null?void 0:l.force))},update(a,l){return Je.update(d=>fn(a(d),o.errors,l==null?void 0:l.force))},clear:()=>le.set({})};let R=null;function Kn(a){var l;R&&a&&Object.keys(a).length==1&&((l=a.paths)!=null&&l.length)&&R.target&&R.target instanceof HTMLInputElement&&R.target.type.toLowerCase()=="file"?R.paths=a.paths:R=a,setTimeout(()=>{h(R)},0)}function Wn(a,l,d,p,g){R===null&&(R={paths:[]}),R.type=a,R.immediate=l,R.multiple=d,R.formElement=p,R.target=g}function Wt(){return(R==null?void 0:R.paths)??[]}function Jn(){R=null}const H={defaultMessage:"Leave page? Changes that you made may not be saved.",state:L(),message:n.taintedMessage,clean:ne(e.data),forceRedirection:!1};function Jt(){return n.taintedMessage&&!o.submitting&&!H.forceRedirection&&Yt()}function Gt(a){if(!Jt())return;a.preventDefault(),a.returnValue="";const{taintedMessage:l}=n,p=typeof l=="function"||l===!0?H.defaultMessage:l;return(a||window.event).returnValue=p||H.defaultMessage,p}async function Gn(a){if(!Jt())return;const{taintedMessage:l}=n,d=typeof l=="function";if(d&&a.cancel(),a.type==="leave")return;const p=d||l===!0?H.defaultMessage:l;let g;try{g=d?await l(a):window.confirm(p||H.defaultMessage)}catch{g=!1}if(g&&a.to)try{H.forceRedirection=!0,await vr(a.to.url,{...a.to.params});return}finally{H.forceRedirection=!1}else!g&&!d&&a.cancel()}function Yn(){n.taintedMessage=H.message}function Xn(){return H.state}function Qn(a){if(!o.tainted)return!1;if(!a)return!!o.tainted;const l=Pe(o.tainted,ze(a));return!!l&&l.key in l.parent}function Yt(a){if(!arguments.length)return at(o.tainted);if(typeof a=="boolean")return a;if(typeof a=="object")return at(a);if(!o.tainted||a===void 0)return!1;const l=Pe(o.tainted,ze(a));return at(l==null?void 0:l.value)}function at(a){if(!a)return!1;if(typeof a=="object"){for(const l of Object.values(a))if(at(l))return!0}return a===!0}function Xt(a,l){if(l=="ignore")return;const d=cn(a,o.form),p=cn(a,H.clean).map(g=>g.join());d.length&&(H.state.update(g=>(g||(g={}),me(g,d,(_,ee)=>{if(!p.includes(_.join()))return;const Te=be(a,_),Fe=be(H.clean,_);return Te&&Fe&&Te.value===Fe.value?void 0:l===!0?!0:l==="untaint"?void 0:ee.value}),g)),Kn({paths:d})),(l=="untaint-all"||l=="untaint-form")&&H.state.set(void 0)}function er(a,l){H.state.set(a),l&&(H.clean=l)}const _t=L(!1),Qt=L(!1),en=L(!1),tn=[H.state.subscribe(a=>s.tainted=ne(a)),y.subscribe(a=>s.form=ne(a)),le.subscribe(a=>s.errors=ne(a)),u.subscribe(a=>s.formId=a),Ke.subscribe(a=>s.constraints=a),We.subscribe(a=>s.posted=a),Oe.subscribe(a=>s.message=a),_t.subscribe(a=>s.submitting=a),ke.subscribe(a=>s.shape=a)];function tr(a){tn.push(a)}function nr(){tn.forEach(a=>a())}let X;function Ge(){return X}function rr(a){X=document.createElement("form"),X.method="POST",X.action=a,nn(X),document.body.appendChild(X)}function ar(a){X&&(X.action=a)}function ir(){X!=null&&X.parentElement&&X.remove(),X=void 0}const sr=Ht(le,a=>a?Hr(a):[]);n.taintedMessage=void 0;function it(a){const l=a.form,d=a.message??l.message;if((a.untaint||a.resetted)&&er(typeof a.untaint=="boolean"?void 0:a.untaint,l.data),a.pessimisticUpdate||$(l.data,{taint:"ignore",keepFiles:a.keepFiles}),Oe.set(d),a.resetted?le.update(()=>({}),{force:!0}):le.set(l.errors),u.set(l.id),We.set(a.posted??l.posted),l.constraints&&Ke.set(l.constraints),l.shape&&ke.set(l.shape),s.valid=l.valid,n.flashMessage&&Dt(n)){const p=n.flashMessage.module.getFlash(Xe);d&&Ye(p)===void 0&&p.set(d)}}const Q={onSubmit:n.onSubmit?[n.onSubmit]:[],onResult:n.onResult?[n.onResult]:[],onUpdate:n.onUpdate?[n.onUpdate]:[],onUpdated:n.onUpdated?[n.onUpdated]:[],onError:n.onError?[n.onError]:[]};window.addEventListener("beforeunload",Gt),dt(()=>{window.removeEventListener("beforeunload",Gt)}),br(Gn),tr(Xe.subscribe(async a=>{Re&&a===void 0&&(a={status:200});const l=a.status>=200&&a.status<300;if(n.applyAction&&a.form&&typeof a.form=="object"){const d=a.form;if(d.type==="error")return;for(const p of f(d)){const g=Ce.has(p);p.id!==o.formId||g||(Ce.set(p,p),await ae(p,l))}}else if(n.applyAction!=="never"&&a.data&&typeof a.data=="object")for(const d of f(a.data)){const p=Ce.has(d);if(d.id!==o.formId||p)continue;(n.invalidateAll==="force"||n.invalidateAll==="pessimistic")&&(r.data=d.data);const g=E(d.valid,!0);it({form:d,untaint:l,keepFiles:!g,resetted:g})}})),typeof n.SPA=="string"&&rr(n.SPA);function nn(a,l){if(n.SPA!==void 0&&a.method=="get"&&(a.method="post"),typeof n.SPA=="string"?n.SPA.length&&a.action==document.location.href&&(a.action=n.SPA):X=a,l){if(l.onError){if(n.onError==="apply")throw new Z('options.onError is set to "apply", cannot add any onError events.');if(l.onError==="apply")throw new Z('Cannot add "apply" as onError event in use:enhance.');Q.onError.push(l.onError)}l.onResult&&Q.onResult.push(l.onResult),l.onSubmit&&Q.onSubmit.push(l.onSubmit),l.onUpdate&&Q.onUpdate.push(l.onUpdate),l.onUpdated&&Q.onUpdated.push(l.onUpdated)}Yn();let d;async function p(q){const V=pn(q.target);V.immediate&&!V.file&&await new Promise(N=>setTimeout(N,0)),d=Wt(),Wn("input",V.immediate,V.multiple,a,q.target??void 0)}async function g(q){if(o.submitting||!d||Wt()!=d)return;const V=pn(q.target);V.immediate&&!V.file&&await new Promise(N=>setTimeout(N,0)),d!==void 0&&(h({paths:d,immediate:V.multiple,multiple:V.multiple,type:"blur",formElement:a,target:q.target??void 0}),d=void 0)}a.addEventListener("focusout",g),a.addEventListener("input",p),dt(()=>{a.removeEventListener("focusout",g),a.removeEventListener("input",p)});const _=Qr(a,{submitting:_t,delayed:Qt,timeout:en},n);let ee,Te;const Fe=Kr(a,async q=>{let V,N=n.validators;const z={...q,jsonData(w){if(n.dataType!=="json")throw new Z("options.dataType must be set to 'json' to use jsonData.");V=w},validators(w){N=w},customRequest(w){Te=w}},Le=z.cancel;let te=!1;function wt(w){const A={...w,posted:!0},M=A.valid?200:T(400),U={form:A},D=A.valid?{type:"success",status:M,data:U}:{type:"failure",status:M,data:U};setTimeout(()=>$e({result:D}),0)}function st(){switch(n.clearOnSubmit){case"errors-and-message":le.clear(),Oe.set(void 0);break;case"errors":le.clear();break;case"message":Oe.set(void 0);break}}async function Ie(w,A){var M;if(w.status=A,n.onError!=="apply"){const U={result:w,message:Oe,form:e};for(const D of Q.onError)D!=="apply"&&(D!=In||!((M=n.flashMessage)!=null&&M.onError))&&await D(U)}n.flashMessage&&n.flashMessage.onError&&await n.flashMessage.onError({result:w,flashMessage:n.flashMessage.module.getFlash(Xe)}),n.applyAction&&(n.onError=="apply"?await mt(w):await mt({type:"failure",status:T(w.status),data:w}))}function Ae(w={resetTimers:!0}){return te=!0,w.resetTimers&&_.isSubmitting()&&_.completed({cancelled:te}),Le()}if(z.cancel=Ae,_.isSubmitting()&&n.multipleSubmits=="prevent")Ae({resetTimers:!1});else{_.isSubmitting()&&n.multipleSubmits=="abort"&&ee&&ee.abort(),_.submitting(),ee=z.controller;for(const w of Q.onSubmit)try{await w(z)}catch(A){Ae(),Ie({type:"error",error:A},500)}}if(te&&n.flashMessage&&dn(n),!te){const w=!S()&&(a.noValidate||(z.submitter instanceof HTMLButtonElement||z.submitter instanceof HTMLInputElement)&&z.submitter.formNoValidate);let A;const M=async()=>await P({adapter:N});if(st(),w||(A=await M(),A.valid||(Ae({resetTimers:!1}),wt(A))),!te){n.flashMessage&&(n.clearOnSubmit=="errors-and-message"||n.clearOnSubmit=="message")&&Dt(n)&&n.flashMessage.module.getFlash(Xe).set(void 0);const U="formData"in z?z.formData:z.data;if(d=void 0,S())A||(A=await M()),Ae({resetTimers:!1}),wt(A);else if(n.dataType==="json"){A||(A=await M());const D=ne(V??A.data);we(D,j=>{if(j.value instanceof File){const C="__superform_file_"+Qe(j.path);return U.append(C,j.value),j.set(void 0)}else if(Array.isArray(j.value)&&j.value.length&&j.value.every(C=>C instanceof File)){const C="__superform_files_"+Qe(j.path);for(const ie of j.value)U.append(C,ie);return j.set(void 0)}}),Object.keys(D).forEach(j=>{typeof U.get(j)=="string"&&U.delete(j)});const Ne=n.transport?Object.fromEntries(Object.entries(n.transport).map(([j,C])=>[j,C.encode])):void 0,ot=W(Dr(D,Ne),n.jsonChunkSize??5e5);for(const j of ot)U.append("__superform_json",j)}if(!U.has("__superform_id")){const D=o.formId;D!==void 0&&U.set("__superform_id",D)}typeof n.SPA=="string"&&ar(n.SPA)}}function W(w,A){const M=Math.ceil(w.length/A),U=new Array(M);for(let D=0,Ne=0;D<M;++D,Ne+=A)U[D]=w.substring(Ne,Ne+A);return U}async function $e(w){let A=!1;ee=null;let M="type"in w.result&&"status"in w.result?w.result:{type:"error",status:T(parseInt(String(w.result.status))||500),error:w.result.error instanceof Error?w.result.error:w.result};const U=()=>A=!0,D={result:M,formEl:a,formElement:a,cancel:U},Ne=Re||!S()?()=>{}:sn.subscribe(j=>{var C,ie;!j||((C=j.from)==null?void 0:C.route.id)===((ie=j.to)==null?void 0:ie.route.id)||U()});function ot(j,C,ie){C.result={type:"error",error:j,status:T(ie)}}for(const j of Q.onResult)try{await j(D)}catch(C){ot(C,D,Math.max(M.status??500,400))}if(M=D.result,!A){if((M.type==="success"||M.type==="failure")&&M.data){const j=f(M.data);if(!j.length)throw new Z("No form data returned from ActionResult. Make sure you return { form } in the form actions.");for(const C of j){if(C.id!==o.formId)continue;const ie={form:C,formEl:a,formElement:a,cancel:()=>A=!0,result:M};for(const St of Q.onUpdate)try{await St(ie)}catch(ur){ot(ur,ie,Math.max(M.status??500,400))}M=ie.result,A||(n.customValidity&&Wr(a,ie.form.errors),E(ie.form.valid,M.type=="success")&&ie.formElement.querySelectorAll('input[type="file"]').forEach(St=>St.value=""))}}A||(M.type!=="error"?(M.type==="success"&&n.invalidateAll&&await On(),n.applyAction?await mt(M):await rt(M)):await Ie(M,Math.max(M.status??500,400)))}if(A&&n.flashMessage&&dn(n),A||M.type!="redirect")_.completed({cancelled:A});else if(Re)_.completed({cancelled:A,clearAll:!0});else{const j=sn.subscribe(C=>{C||(setTimeout(()=>{try{j&&j()}catch{}}),_.isSubmitting()&&_.completed({cancelled:A,clearAll:!0}))})}Ne()}if(!te&&Te){Le();const w=await Te(q);let A;w instanceof Response?A=Lt(await w.text()):w instanceof XMLHttpRequest?A=Lt(w.responseText):A=w,A.type==="error"&&(A.status=w.status),$e({result:A})}return $e});return{destroy:()=>{for(const[q,V]of Object.entries(Q))Q[q]=V.filter(N=>N===n[q]);Fe.destroy()}}}function or(a){const l=[];if(we(a,p=>{if(p.value instanceof File)return l.push(p.path),"skip";if(Array.isArray(p.value)&&p.value.length&&p.value.every(g=>g instanceof File))return l.push(p.path),"skip"}),!l.length)return{data:a,paths:l};const d=ne(a);return me(d,l,p=>{var g;return(g=Pe(r.data,p))==null?void 0:g.value}),{data:d,paths:l}}return{form:y,formId:u,errors:le,message:Oe,constraints:Ke,tainted:Xn(),submitting:Et(_t),delayed:Et(Qt),timeout:Et(en),options:n,capture:I,restore:a=>{it({form:a,untaint:a.tainted??!0})},async validate(a,l={}){if(!n.validators)throw new Z("options.validators must be set to use the validate method.");l.update===void 0&&(l.update=!0),l.taint===void 0&&(l.taint=!1),typeof l.errors=="string"&&(l.errors=[l.errors]);let d;const p=ze(a);"value"in l?l.update===!0||l.update==="value"?(y.update(ee=>(me(ee,[p],l.value),ee),{taint:l.taint}),d=o.form):(d=ne(o.form),me(d,[p],l.value)):d=o.form;const g=await P({formData:d}),_=Pe(g.errors,p);return _&&_.value&&l.errors&&(_.value=l.errors),(l.update===!0||l.update=="errors")&&le.update(ee=>(me(ee,[p],_==null?void 0:_.value),ee)),_==null?void 0:_.value},async validateForm(a={}){if(!n.validators&&!a.schema)throw new Z("options.validators or the schema option must be set to use the validateForm method.");const l=a.update?await h({paths:[]},!0,a.schema):P({adapter:a.schema}),d=Ge();return a.update&&d&&setTimeout(()=>{d&&Ct(d,{...n,scrollToError:a.focusOnError===!1?"off":n.scrollToError})},1),l||P({adapter:a.schema})},allErrors:sr,posted:We,reset(a){return ve({message:a!=null&&a.keepMessage?o.message:void 0,data:a==null?void 0:a.data,id:a==null?void 0:a.id,newState:a==null?void 0:a.newState})},submit(a){const l=Ge()?Ge():a&&a instanceof HTMLElement?a.closest("form"):void 0;if(!l)throw new Z("use:enhance must be added to the form to use submit, or pass a HTMLElement inside the form (or the form itself) as an argument.");if(!l.requestSubmit)return l.submit();const d=a&&(a instanceof HTMLButtonElement&&a.type=="submit"||a instanceof HTMLInputElement&&["submit","image"].includes(a.type));l.requestSubmit(d?a:void 0)},isTainted:Yt,enhance:nn}}function ia(e){return pt(he(e,!1,[]),[])}function Mt(...e){const t=e.filter(r=>!!r);if(t.length)return t.length==1?t[0]:oe(...t)}function pt(e,t){var n;if(!e)return;let r;if(e.union&&e.union.length){const i=e.union.map(o=>he(o,e.isOptional,t)),s=i.map(o=>pt(o,t));r=Mt(r,...s),r&&(e.isNullable||e.isOptional||i.some(o=>(o==null?void 0:o.isNullable)||(o==null?void 0:o.isOptional)))&&delete r.required}if(e.array&&(r=Mt(r,...e.array.map(i=>pt(he(i,e.isOptional,t),t)))),e.properties){const i={};for(const[s,o]of Object.entries(e.properties)){const u=he(o,!((n=e.required)!=null&&n.includes(s))||o.default!==void 0,[s]),f=pt(u,[...t,s]);typeof f=="object"&&Object.values(f).length>0&&(i[s]=f)}r=Mt(r,i)}return r??sa(e)}function sa(e){const t={},r=e.schema,n=r.type,i=r.format;if(n=="integer"&&i=="unix-time"){const s=r;s.minimum!==void 0&&(t.min=new Date(s.minimum).toISOString()),s.maximum!==void 0&&(t.max=new Date(s.maximum).toISOString())}else if(n=="string"){const s=r,o=[s.pattern,...s.allOf?s.allOf.map(u=>typeof u=="boolean"?void 0:u.pattern):[]].filter(u=>u!==void 0);o.length>0&&(t.pattern=o[0]),s.minLength!==void 0&&(t.minlength=s.minLength),s.maxLength!==void 0&&(t.maxlength=s.maxLength)}else if(n=="number"||n=="integer"){const s=r;s.minimum!==void 0?t.min=s.minimum:s.exclusiveMinimum!==void 0&&(t.min=s.exclusiveMinimum+(n=="integer"?1:Number.MIN_VALUE)),s.maximum!==void 0?t.max=s.maximum:s.exclusiveMaximum!==void 0&&(t.max=s.exclusiveMaximum-(n=="integer"?1:Number.MIN_VALUE)),s.multipleOf!==void 0&&(t.step=s.multipleOf)}else if(n=="array"){const s=r;s.minItems!==void 0&&(t.min=s.minItems),s.maxItems!==void 0&&(t.max=s.maxItems)}return!e.isNullable&&!e.isOptional&&(t.required=!0),Object.keys(t).length>0?t:void 0}function oa(e){return ua(Vt(he(e,!1,[]),0,[]))}function Vt(e,t,r){var o;if(!e)return"";function n(){return"  ".repeat(t)}function i(u){return u.map(f=>Vt(he(f,(e==null?void 0:e.isOptional)??!1,r),t+1,r)).filter(f=>f).join("|")}function s(){const u=[];return e!=null&&e.isNullable&&u.push("null"),e!=null&&e.isOptional&&u.push("undefined"),u.length?"|"+u.join("|"):""}if(e.union)return`Union {
  `+n()+i(e.union)+`
`+n()+"}"+s();if(e.properties){const u=[];for(const[f,c]of Object.entries(e.properties)){const m=he(c,!((o=e.required)!=null&&o.includes(f))||c.default!==void 0,[f]);u.push(f+": "+Vt(m,t+1,r))}return`Object {
  `+n()+u.join(`,
  `)+`
`+n()+"}"+s()}return e.array?"Array["+i(e.array)+"]"+s():e.types.join("|")+s()}function ua(e){let t=0;for(let r=0,n=e.length;r<n;r++){const i=e.charCodeAt(r);t=(t<<5)-t+i,t|=0}return t<0&&(t=t>>>0),t.toString(36)}function la(e,t){if(!e||!("superFormValidationLibrary"in e))throw new Z('Superforms v2 requires a validation adapter for the schema. Import one of your choice from "sveltekit-superforms/adapters" and wrap the schema with it.');return t||(t=e.jsonSchema),{...e,constraints:e.constraints??ia(t),defaults:e.defaults??zr(t),shape:na(t),id:oa(t)}}let ca=!1;try{SUPERFORMS_LEGACY&&(ca=!0)}catch{}function At(e){return typeof e!="object"&&typeof e!="function"||e===null}function xe(){this.childBranches=new WeakMap,this.primitiveKeys=new Map,this.hasValue=!1,this.value=void 0}xe.prototype.has=function(t){var r=At(t)?this.primitiveKeys.get(t):t;return r?this.childBranches.has(r):!1};xe.prototype.get=function(t){var r=At(t)?this.primitiveKeys.get(t):t;return r?this.childBranches.get(r):void 0};xe.prototype.resolveBranch=function(t){if(this.has(t))return this.get(t);var r=new xe,n=this.createKey(t);return this.childBranches.set(n,r),r};xe.prototype.setValue=function(t){return this.hasValue=!0,this.value=t};xe.prototype.createKey=function(t){if(At(t)){var r={};return this.primitiveKeys.set(t,r),r}return t};xe.prototype.clear=function(){if(arguments.length===0)this.childBranches=new WeakMap,this.primitiveKeys.clear(),this.hasValue=!1,this.value=void 0;else if(arguments.length===1){var t=arguments[0];if(At(t)){var r=this.primitiveKeys.get(t);r&&(this.childBranches.delete(r),this.primitiveKeys.delete(t))}else this.childBranches.delete(t)}else{var n=arguments[0];if(this.has(n)){var i=this.get(n);i.clear.apply(i,Array.prototype.slice.call(arguments,1))}}};var fa=function(t){var r=new xe;function n(){var i=Array.prototype.slice.call(arguments),s=i.reduce(function(f,c){return f.resolveBranch(c)},r);if(s.hasValue)return s.value;var o=t.apply(null,i);return s.setValue(o)}return n.clear=r.clear.bind(r),n},da=fa;const ma=Ar(da),Nn=ma,pa=Symbol("Let zodToJsonSchema decide on which parser to use"),yn={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref",openAiAnyTypeName:"OpenAiAnyType"},ya=e=>typeof e=="string"?{...yn,name:e}:{...yn,...e},ga=e=>{const t=ya(e),r=t.name!==void 0?[...t.basePath,t.definitionPath,t.name]:t.basePath;return{...t,flags:{hasReferencedOpenAiAnyType:!1},currentPath:r,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map(([n,i])=>[i._def,{def:i._def,path:[...t.basePath,t.definitionPath,n],jsonSchema:void 0}]))}};function Dn(e,t,r,n){n!=null&&n.errorMessages&&r&&(e.errorMessage={...e.errorMessage,[t]:r})}function k(e,t,r,n,i){e[t]=r,Dn(e,t,n,i)}const Ln=(e,t)=>{let r=0;for(;r<e.length&&r<t.length&&e[r]===t[r];r++);return[(e.length-r).toString(),...t.slice(r)].join("/")};function re(e){if(e.target!=="openAi")return{};const t=[...e.basePath,e.definitionPath,e.openAiAnyTypeName];return e.flags.hasReferencedOpenAiAnyType=!0,{$ref:e.$refStrategy==="relative"?Ln(t,e.currentPath):t.join("/")}}function ha(e,t){var n,i,s;const r={type:"array"};return(n=e.type)!=null&&n._def&&((s=(i=e.type)==null?void 0:i._def)==null?void 0:s.typeName)!==b.ZodAny&&(r.items=x(e.type._def,{...t,currentPath:[...t.currentPath,"items"]})),e.minLength&&k(r,"minItems",e.minLength.value,e.minLength.message,t),e.maxLength&&k(r,"maxItems",e.maxLength.value,e.maxLength.message,t),e.exactLength&&(k(r,"minItems",e.exactLength.value,e.exactLength.message,t),k(r,"maxItems",e.exactLength.value,e.exactLength.message,t)),r}function ba(e,t){const r={type:"integer",format:"int64"};if(!e.checks)return r;for(const n of e.checks)switch(n.kind){case"min":t.target==="jsonSchema7"?n.inclusive?k(r,"minimum",n.value,n.message,t):k(r,"exclusiveMinimum",n.value,n.message,t):(n.inclusive||(r.exclusiveMinimum=!0),k(r,"minimum",n.value,n.message,t));break;case"max":t.target==="jsonSchema7"?n.inclusive?k(r,"maximum",n.value,n.message,t):k(r,"exclusiveMaximum",n.value,n.message,t):(n.inclusive||(r.exclusiveMaximum=!0),k(r,"maximum",n.value,n.message,t));break;case"multipleOf":k(r,"multipleOf",n.value,n.message,t);break}return r}function va(){return{type:"boolean"}}function Cn(e,t){return x(e.type._def,t)}const Aa=(e,t)=>x(e.innerType._def,t);function Zn(e,t,r){const n=r??t.dateStrategy;if(Array.isArray(n))return{anyOf:n.map((i,s)=>Zn(e,t,i))};switch(n){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return _a(e,t)}}const _a=(e,t)=>{const r={type:"integer",format:"unix-time"};if(t.target==="openApi3")return r;for(const n of e.checks)switch(n.kind){case"min":k(r,"minimum",n.value,n.message,t);break;case"max":k(r,"maximum",n.value,n.message,t);break}return r};function wa(e,t){return{...x(e.innerType._def,t),default:e.defaultValue()}}function Sa(e,t){return t.effectStrategy==="input"?x(e.schema._def,t):re(t)}function Ea(e){return{type:"string",enum:Array.from(e.values)}}const Oa=e=>"type"in e&&e.type==="string"?!1:"allOf"in e;function Ta(e,t){const r=[x(e.left._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),x(e.right._def,{...t,currentPath:[...t.currentPath,"allOf","1"]})].filter(s=>!!s);let n=t.target==="jsonSchema2019-09"?{unevaluatedProperties:!1}:void 0;const i=[];return r.forEach(s=>{if(Oa(s))i.push(...s.allOf),s.unevaluatedProperties===void 0&&(n=void 0);else{let o=s;if("additionalProperties"in s&&s.additionalProperties===!1){const{additionalProperties:u,...f}=s;o=f}else n=void 0;i.push(o)}}),i.length?{allOf:i,...n}:void 0}function Pa(e,t){const r=typeof e.value;return r!=="bigint"&&r!=="number"&&r!=="boolean"&&r!=="string"?{type:Array.isArray(e.value)?"array":"object"}:t.target==="openApi3"?{type:r==="bigint"?"integer":r,enum:[e.value]}:{type:r==="bigint"?"integer":r,const:e.value}}let jt;const fe={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(jt===void 0&&(jt=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),jt),uuid:/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/,ipv4:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6:/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function Rn(e,t){const r={type:"string"};if(e.checks)for(const n of e.checks)switch(n.kind){case"min":k(r,"minLength",typeof r.minLength=="number"?Math.max(r.minLength,n.value):n.value,n.message,t);break;case"max":k(r,"maxLength",typeof r.maxLength=="number"?Math.min(r.maxLength,n.value):n.value,n.message,t);break;case"email":switch(t.emailStrategy){case"format:email":de(r,"email",n.message,t);break;case"format:idn-email":de(r,"idn-email",n.message,t);break;case"pattern:zod":J(r,fe.email,n.message,t);break}break;case"url":de(r,"uri",n.message,t);break;case"uuid":de(r,"uuid",n.message,t);break;case"regex":J(r,n.regex,n.message,t);break;case"cuid":J(r,fe.cuid,n.message,t);break;case"cuid2":J(r,fe.cuid2,n.message,t);break;case"startsWith":J(r,RegExp(`^${xt(n.value,t)}`),n.message,t);break;case"endsWith":J(r,RegExp(`${xt(n.value,t)}$`),n.message,t);break;case"datetime":de(r,"date-time",n.message,t);break;case"date":de(r,"date",n.message,t);break;case"time":de(r,"time",n.message,t);break;case"duration":de(r,"duration",n.message,t);break;case"length":k(r,"minLength",typeof r.minLength=="number"?Math.max(r.minLength,n.value):n.value,n.message,t),k(r,"maxLength",typeof r.maxLength=="number"?Math.min(r.maxLength,n.value):n.value,n.message,t);break;case"includes":{J(r,RegExp(xt(n.value,t)),n.message,t);break}case"ip":{n.version!=="v6"&&de(r,"ipv4",n.message,t),n.version!=="v4"&&de(r,"ipv6",n.message,t);break}case"base64url":J(r,fe.base64url,n.message,t);break;case"jwt":J(r,fe.jwt,n.message,t);break;case"cidr":{n.version!=="v6"&&J(r,fe.ipv4Cidr,n.message,t),n.version!=="v4"&&J(r,fe.ipv6Cidr,n.message,t);break}case"emoji":J(r,fe.emoji(),n.message,t);break;case"ulid":{J(r,fe.ulid,n.message,t);break}case"base64":{switch(t.base64Strategy){case"format:binary":{de(r,"binary",n.message,t);break}case"contentEncoding:base64":{k(r,"contentEncoding","base64",n.message,t);break}case"pattern:zod":{J(r,fe.base64,n.message,t);break}}break}case"nanoid":J(r,fe.nanoid,n.message,t)}return r}function xt(e,t){return t.patternStrategy==="escape"?ja(e):e}const Ma=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function ja(e){let t="";for(let r=0;r<e.length;r++)Ma.has(e[r])||(t+="\\"),t+=e[r];return t}function de(e,t,r,n){var i;e.format||(i=e.anyOf)!=null&&i.some(s=>s.format)?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&n.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,Object.keys(e.errorMessage).length===0&&delete e.errorMessage)),e.anyOf.push({format:t,...r&&n.errorMessages&&{errorMessage:{format:r}}})):k(e,"format",t,r,n)}function J(e,t,r,n){var i;e.pattern||(i=e.allOf)!=null&&i.some(s=>s.pattern)?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&n.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,Object.keys(e.errorMessage).length===0&&delete e.errorMessage)),e.allOf.push({pattern:gn(t,n),...r&&n.errorMessages&&{errorMessage:{pattern:r}}})):k(e,"pattern",gn(t,n),r,n)}function gn(e,t){var f;if(!t.applyRegexFlags||!e.flags)return e.source;const r={i:e.flags.includes("i"),m:e.flags.includes("m"),s:e.flags.includes("s")},n=r.i?e.source.toLowerCase():e.source;let i="",s=!1,o=!1,u=!1;for(let c=0;c<n.length;c++){if(s){i+=n[c],s=!1;continue}if(r.i){if(o){if(n[c].match(/[a-z]/)){u?(i+=n[c],i+=`${n[c-2]}-${n[c]}`.toUpperCase(),u=!1):n[c+1]==="-"&&((f=n[c+2])!=null&&f.match(/[a-z]/))?(i+=n[c],u=!0):i+=`${n[c]}${n[c].toUpperCase()}`;continue}}else if(n[c].match(/[a-z]/)){i+=`[${n[c]}${n[c].toUpperCase()}]`;continue}}if(r.m){if(n[c]==="^"){i+=`(^|(?<=[\r
]))`;continue}else if(n[c]==="$"){i+=`($|(?=[\r
]))`;continue}}if(r.s&&n[c]==="."){i+=o?`${n[c]}\r
`:`[${n[c]}\r
]`;continue}i+=n[c],n[c]==="\\"?s=!0:o&&n[c]==="]"?o=!1:!o&&n[c]==="["&&(o=!0)}try{new RegExp(i)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return i}function Vn(e,t){var n,i,s,o,u,f;if(t.target==="openAi"&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),t.target==="openApi3"&&((n=e.keyType)==null?void 0:n._def.typeName)===b.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce((c,m)=>({...c,[m]:x(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",m]})??re(t)}),{}),additionalProperties:t.rejectedAdditionalProperties};const r={type:"object",additionalProperties:x(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??t.allowedAdditionalProperties};if(t.target==="openApi3")return r;if(((i=e.keyType)==null?void 0:i._def.typeName)===b.ZodString&&((s=e.keyType._def.checks)!=null&&s.length)){const{type:c,...m}=Rn(e.keyType._def,t);return{...r,propertyNames:m}}else{if(((o=e.keyType)==null?void 0:o._def.typeName)===b.ZodEnum)return{...r,propertyNames:{enum:e.keyType._def.values}};if(((u=e.keyType)==null?void 0:u._def.typeName)===b.ZodBranded&&e.keyType._def.type._def.typeName===b.ZodString&&((f=e.keyType._def.type._def.checks)!=null&&f.length)){const{type:c,...m}=Cn(e.keyType._def,t);return{...r,propertyNames:m}}}return r}function xa(e,t){if(t.mapStrategy==="record")return Vn(e,t);const r=x(e.keyType._def,{...t,currentPath:[...t.currentPath,"items","items","0"]})||re(t),n=x(e.valueType._def,{...t,currentPath:[...t.currentPath,"items","items","1"]})||re(t);return{type:"array",maxItems:125,items:{type:"array",items:[r,n],minItems:2,maxItems:2}}}function ka(e){const t=e.values,n=Object.keys(e.values).filter(s=>typeof t[t[s]]!="number").map(s=>t[s]),i=Array.from(new Set(n.map(s=>typeof s)));return{type:i.length===1?i[0]==="string"?"string":"number":["string","number"],enum:n}}function Fa(e){return e.target==="openAi"?void 0:{not:re({...e,currentPath:[...e.currentPath,"not"]})}}function Ia(e){return e.target==="openApi3"?{enum:["null"],nullable:!0}:{type:"null"}}const gt={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"};function $a(e,t){if(t.target==="openApi3")return hn(e,t);const r=e.options instanceof Map?Array.from(e.options.values()):e.options;if(r.every(n=>n._def.typeName in gt&&(!n._def.checks||!n._def.checks.length))){const n=r.reduce((i,s)=>{const o=gt[s._def.typeName];return o&&!i.includes(o)?[...i,o]:i},[]);return{type:n.length>1?n:n[0]}}else if(r.every(n=>n._def.typeName==="ZodLiteral"&&!n.description)){const n=r.reduce((i,s)=>{const o=typeof s._def.value;switch(o){case"string":case"number":case"boolean":return[...i,o];case"bigint":return[...i,"integer"];case"object":if(s._def.value===null)return[...i,"null"];case"symbol":case"undefined":case"function":default:return i}},[]);if(n.length===r.length){const i=n.filter((s,o,u)=>u.indexOf(s)===o);return{type:i.length>1?i:i[0],enum:r.reduce((s,o)=>s.includes(o._def.value)?s:[...s,o._def.value],[])}}}else if(r.every(n=>n._def.typeName==="ZodEnum"))return{type:"string",enum:r.reduce((n,i)=>[...n,...i._def.values.filter(s=>!n.includes(s))],[])};return hn(e,t)}const hn=(e,t)=>{const r=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((n,i)=>x(n._def,{...t,currentPath:[...t.currentPath,"anyOf",`${i}`]})).filter(n=>!!n&&(!t.strictUnions||typeof n=="object"&&Object.keys(n).length>0));return r.length?{anyOf:r}:void 0};function Na(e,t){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return t.target==="openApi3"?{type:gt[e.innerType._def.typeName],nullable:!0}:{type:[gt[e.innerType._def.typeName],"null"]};if(t.target==="openApi3"){const n=x(e.innerType._def,{...t,currentPath:[...t.currentPath]});return n&&"$ref"in n?{allOf:[n],nullable:!0}:n&&{...n,nullable:!0}}const r=x(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","0"]});return r&&{anyOf:[r,{type:"null"}]}}function Da(e,t){const r={type:"number"};if(!e.checks)return r;for(const n of e.checks)switch(n.kind){case"int":r.type="integer",Dn(r,"type",n.message,t);break;case"min":t.target==="jsonSchema7"?n.inclusive?k(r,"minimum",n.value,n.message,t):k(r,"exclusiveMinimum",n.value,n.message,t):(n.inclusive||(r.exclusiveMinimum=!0),k(r,"minimum",n.value,n.message,t));break;case"max":t.target==="jsonSchema7"?n.inclusive?k(r,"maximum",n.value,n.message,t):k(r,"exclusiveMaximum",n.value,n.message,t):(n.inclusive||(r.exclusiveMaximum=!0),k(r,"maximum",n.value,n.message,t));break;case"multipleOf":k(r,"multipleOf",n.value,n.message,t);break}return r}function La(e,t){const r=t.target==="openAi",n={type:"object",properties:{}},i=[],s=e.shape();for(const u in s){let f=s[u];if(f===void 0||f._def===void 0)continue;let c=Za(f);c&&r&&(f._def.typeName==="ZodOptional"&&(f=f._def.innerType),f.isNullable()||(f=f.nullable()),c=!1);const m=x(f._def,{...t,currentPath:[...t.currentPath,"properties",u],propertyPath:[...t.currentPath,"properties",u]});m!==void 0&&(n.properties[u]=m,c||i.push(u))}i.length&&(n.required=i);const o=Ca(e,t);return o!==void 0&&(n.additionalProperties=o),n}function Ca(e,t){if(e.catchall._def.typeName!=="ZodNever")return x(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]});switch(e.unknownKeys){case"passthrough":return t.allowedAdditionalProperties;case"strict":return t.rejectedAdditionalProperties;case"strip":return t.removeAdditionalStrategy==="strict"?t.allowedAdditionalProperties:t.rejectedAdditionalProperties}}function Za(e){try{return e.isOptional()}catch{return!0}}const Ra=(e,t)=>{var n;if(t.currentPath.toString()===((n=t.propertyPath)==null?void 0:n.toString()))return x(e.innerType._def,t);const r=x(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","1"]});return r?{anyOf:[{not:re(t)},r]}:re(t)},Va=(e,t)=>{if(t.pipeStrategy==="input")return x(e.in._def,t);if(t.pipeStrategy==="output")return x(e.out._def,t);const r=x(e.in._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),n=x(e.out._def,{...t,currentPath:[...t.currentPath,"allOf",r?"1":"0"]});return{allOf:[r,n].filter(i=>i!==void 0)}};function za(e,t){return x(e.type._def,t)}function Ua(e,t){const n={type:"array",uniqueItems:!0,items:x(e.valueType._def,{...t,currentPath:[...t.currentPath,"items"]})};return e.minSize&&k(n,"minItems",e.minSize.value,e.minSize.message,t),e.maxSize&&k(n,"maxItems",e.maxSize.value,e.maxSize.message,t),n}function Ba(e,t){return e.rest?{type:"array",minItems:e.items.length,items:e.items.map((r,n)=>x(r._def,{...t,currentPath:[...t.currentPath,"items",`${n}`]})).reduce((r,n)=>n===void 0?r:[...r,n],[]),additionalItems:x(e.rest._def,{...t,currentPath:[...t.currentPath,"additionalItems"]})}:{type:"array",minItems:e.items.length,maxItems:e.items.length,items:e.items.map((r,n)=>x(r._def,{...t,currentPath:[...t.currentPath,"items",`${n}`]})).reduce((r,n)=>n===void 0?r:[...r,n],[])}}function qa(e){return{not:re(e)}}function Ha(e){return re(e)}const Ka=(e,t)=>x(e.innerType._def,t),Wa=(e,t,r)=>{switch(t){case b.ZodString:return Rn(e,r);case b.ZodNumber:return Da(e,r);case b.ZodObject:return La(e,r);case b.ZodBigInt:return ba(e,r);case b.ZodBoolean:return va();case b.ZodDate:return Zn(e,r);case b.ZodUndefined:return qa(r);case b.ZodNull:return Ia(r);case b.ZodArray:return ha(e,r);case b.ZodUnion:case b.ZodDiscriminatedUnion:return $a(e,r);case b.ZodIntersection:return Ta(e,r);case b.ZodTuple:return Ba(e,r);case b.ZodRecord:return Vn(e,r);case b.ZodLiteral:return Pa(e,r);case b.ZodEnum:return Ea(e);case b.ZodNativeEnum:return ka(e);case b.ZodNullable:return Na(e,r);case b.ZodOptional:return Ra(e,r);case b.ZodMap:return xa(e,r);case b.ZodSet:return Ua(e,r);case b.ZodLazy:return()=>e.getter()._def;case b.ZodPromise:return za(e,r);case b.ZodNaN:case b.ZodNever:return Fa(r);case b.ZodEffects:return Sa(e,r);case b.ZodAny:return re(r);case b.ZodUnknown:return Ha(r);case b.ZodDefault:return wa(e,r);case b.ZodBranded:return Cn(e,r);case b.ZodReadonly:return Ka(e,r);case b.ZodCatch:return Aa(e,r);case b.ZodPipeline:return Va(e,r);case b.ZodFunction:case b.ZodVoid:case b.ZodSymbol:return;default:return(n=>{})()}};function x(e,t,r=!1){var u;const n=t.seen.get(e);if(t.override){const f=(u=t.override)==null?void 0:u.call(t,e,t,n,r);if(f!==pa)return f}if(n&&!r){const f=Ja(n,t);if(f!==void 0)return f}const i={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,i);const s=Wa(e,e.typeName,t),o=typeof s=="function"?x(s(),t):s;if(o&&Ga(e,t,o),t.postProcess){const f=t.postProcess(o,e,t);return i.jsonSchema=o,f}return i.jsonSchema=o,o}const Ja=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:Ln(t.currentPath,e.path)};case"none":case"seen":return e.path.length<t.currentPath.length&&e.path.every((r,n)=>t.currentPath[n]===r)?(console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),re(t)):t.$refStrategy==="seen"?re(t):void 0}},Ga=(e,t,r)=>(e.description&&(r.description=e.description,t.markdownDescription&&(r.markdownDescription=e.description)),r),Ya=(e,t)=>{const r=ga(t);let n=typeof t=="object"&&t.definitions?Object.entries(t.definitions).reduce((f,[c,m])=>({...f,[c]:x(m._def,{...r,currentPath:[...r.basePath,r.definitionPath,c]},!0)??re(r)}),{}):void 0;const i=typeof t=="string"?t:(t==null?void 0:t.nameStrategy)==="title"||t==null?void 0:t.name,s=x(e._def,i===void 0?r:{...r,currentPath:[...r.basePath,r.definitionPath,i]},!1)??re(r),o=typeof t=="object"&&t.name!==void 0&&t.nameStrategy==="title"?t.name:void 0;o!==void 0&&(s.title=o),r.flags.hasReferencedOpenAiAnyType&&(n||(n={}),n[r.openAiAnyTypeName]||(n[r.openAiAnyTypeName]={type:["string","number","integer","boolean","array","null"],items:{$ref:r.$refStrategy==="relative"?"1":[...r.basePath,r.definitionPath,r.openAiAnyTypeName].join("/")}}));const u=i===void 0?n?{...s,[r.definitionPath]:n}:s:{$ref:[...r.$refStrategy==="relative"?[]:r.basePath,r.definitionPath,i].join("/"),[r.definitionPath]:{...n,[i]:s}};return r.target==="jsonSchema7"?u.$schema="http://json-schema.org/draft-07/schema#":(r.target==="jsonSchema2019-09"||r.target==="openAi")&&(u.$schema="https://json-schema.org/draft/2019-09/schema#"),r.target==="openAi"&&("anyOf"in u||"oneOf"in u||"allOf"in u||"type"in u&&Array.isArray(u.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),u},bn={dateStrategy:"integer",pipeStrategy:"output",$refStrategy:"none"},Xa=(...e)=>(e[1]=typeof e[1]=="object"?{...bn,...e[1]}:bn,Ya(...e));async function zn(e,t,r){const n=await e.safeParseAsync(t,{errorMap:r});return n.success?{data:n.data,success:!0}:{issues:n.error.issues.map(({message:i,path:s})=>({message:i,path:s})),success:!1}}function Qa(e,t){return la({superFormValidationLibrary:"zod",validate:async r=>zn(e,r,t==null?void 0:t.errorMap),jsonSchema:(t==null?void 0:t.jsonSchema)??Xa(e,t==null?void 0:t.config),defaults:t==null?void 0:t.defaults})}function ei(e,t){return{superFormValidationLibrary:"zod",validate:async r=>zn(e,r,t==null?void 0:t.errorMap)}}const Bi=Nn(Qa),qi=Nn(ei),zt=Symbol("FORM_FIELD_CTX");function ti(e){return _n(zt,e),e}function Un(){return vn(zt)||Bn("Form.Field"),An(zt)}const Ut=Symbol("FORM_CONTROL_CTX");function ni(e){return _n(Ut,e),e}function ri(){return vn(Ut)||Bn("<Control />"),An(Ut)}function Bn(e){throw new Error(`Unable to find \`${e}\` context. Did you forget to wrap the component in a \`${e}\`?`)}function ai({fieldErrorsId:e=void 0,descriptionId:t=void 0,errors:r}){let n="";return t&&(n+=t+" "),r.length&&e&&(n+=e),n?n.trim():void 0}function ii(e){if("required"in e)return e.required?"true":void 0}function si(e){return e&&e.length?"true":void 0}function qn(e){return e&&e.length?"":void 0}function Hn(){return jr(5)}function oi(e){return Array.isArray(e)?e:typeof e=="object"&&"_errors"in e&&e._errors!==void 0?e._errors:[]}function kt(e,t){const r=e.split(/[[\].]/).filter(Boolean);let n=t;for(const i of r){if(typeof n!="object"||n===null)return;n=n[i]}return n}function ui(e,t){Be(t,!1);const[r,n]=bt(),i=()=>Y(v(m),"$formErrors",r),s=()=>Y(v(y),"$formConstraints",r),o=()=>Y(v(S),"$formTainted",r),u=()=>Y(v(T),"$formData",r),f=()=>Y(ue,"$errors",r),c=()=>Y(F,"$tainted",r),m=ge(),y=ge(),S=ge(),T=ge();let P=ce(t,"form",8),O=ce(t,"name",8);const h={name:L(O()),errors:L([]),constraints:L({}),tainted:L(!1),fieldErrorsId:L(),descriptionId:L(),form:P()},{tainted:F,errors:ue}=h;ti(h),G(()=>(v(m),v(y),v(S),v(T),B(P())),()=>{(I=>{ut(ye(m,I.errors),"$formErrors",r),ut(ye(y,I.constraints),"$formConstraints",r),ut(ye(S,I.tainted),"$formTainted",r),ut(ye(T,I.form),"$formData",r)})(P())}),G(()=>B(O()),()=>{h.name.set(O())}),G(()=>(B(O()),i()),()=>{h.errors.set(oi(kt(O(),i())))}),G(()=>(B(O()),s()),()=>{h.constraints.set(kt(O(),s())??{})}),G(()=>(o(),B(O())),()=>{h.tainted.set(o()?kt(O(),o())===!0:!1)}),Bt(),He();var $=Se(),E=Ee($);je(E,t,"default",{get value(){return u(),B(O()),Ue(()=>u()[O()])},get errors(){return f()},get tainted(){return c()},get constraints(){return s(),B(O()),Ue(()=>s()[O()])}},null),se(e,$),qe(),n()}function li(e,t){Be(t,!1);const[r,n]=bt(),i=()=>Y(F,"$errors",r),s=()=>Y(P,"$name",r),o=()=>Y(E,"$idStore",r),u=()=>Y(O,"$fieldErrorsId",r),f=()=>Y(h,"$descriptionId",r),c=()=>Y(ue,"$constraints",r),m=ge(),y=ge(),S=ge();let T=ce(t,"id",24,Hn);const{name:P,fieldErrorsId:O,descriptionId:h,errors:F,constraints:ue}=Un(),$={id:L(T()),attrs:L(),labelAttrs:L()},{id:E}=$;ni($),G(()=>B(T()),()=>{$.id.set(T())}),G(()=>i(),()=>{ye(m,qn(i()))}),G(()=>(s(),o(),v(m),u(),f(),i(),c()),()=>{ye(y,{name:s(),id:o(),"data-fs-error":v(m),"aria-describedby":ai({fieldErrorsId:u(),descriptionId:f(),errors:i()}),"aria-invalid":si(i()),"aria-required":ii(c()),"data-fs-control":""})}),G(()=>(o(),v(m)),()=>{ye(S,{for:o(),"data-fs-label":"","data-fs-error":v(m)})}),G(()=>v(y),()=>{$.attrs.set(v(y))}),G(()=>v(S),()=>{$.labelAttrs.set(v(S))}),Bt(),He();var I=Se(),ae=Ee(I);je(ae,t,"default",{get attrs(){return v(y)}},null),se(e,I),qe(),n()}var ci=ht("<div> </div>"),fi=ht("<div><!></div>");function di(e,t){const r=De(t,["children","$$slots","$$events","$$legacy"]),n=De(r,["id","asChild","el"]);Be(t,!1);const[i,s]=bt(),o=()=>Y(S,"$errors",i),u=()=>Y(y,"$fieldErrorsId",i),f=ge(),c=ge(),m=ge(),{fieldErrorsId:y,errors:S}=Un();let T=ce(t,"id",24,Hn),P=ce(t,"asChild",8,!1),O=ce(t,"el",28,()=>{});G(()=>o(),()=>{ye(f,qn(o()))}),G(()=>B(T()),()=>{y.set(T())}),G(()=>(u(),v(f),B(n)),()=>{ye(c,{id:u(),"data-fs-error":v(f),"data-fs-field-errors":"","aria-live":"assertive",...n})}),G(()=>v(f),()=>{ye(m,{"data-fs-field-error":"","data-fs-error":v(f)})}),Bt(),He();var h=Se(),F=Ee(h);{var ue=E=>{var I=Se(),ae=Ee(I);je(ae,t,"default",{get errors(){return o()},get fieldErrorsAttrs(){return v(c)},get errorAttrs(){return v(m)}},null),se(E,I)},$=E=>{var I=fi();It(I,()=>({...v(c)}));var ae=tt(I);je(ae,t,"default",{get errors(){return o()},get fieldErrorsAttrs(){return v(c)},get errorAttrs(){return v(m)}},ve=>{var rt=Se(),Oe=Ee(rt);Pn(Oe,1,o,Mn,(Ke,We)=>{var ke=ci();It(ke,()=>({...v(m)}));var Je=tt(ke,!0);nt(ke),qt(()=>Tn(Je,v(We))),se(Ke,ke)}),se(ve,rt)}),nt(I),Er(I,ve=>O(ve),()=>O()),se(E,I)};Sr(F,E=>{P()?E(ue):E($,!1)})}se(e,h),qe(),s()}function Hi(e,t){const r=De(t,["children","$$slots","$$events","$$legacy"]),n=De(r,["class"]);Be(t,!1);const[i,s]=bt(),o=()=>Y(f,"$labelAttrs",i);let u=ce(t,"class",8,void 0);const{labelAttrs:f}=ri();He();{let c=_e(()=>(B(Me),B(u()),Ue(()=>Me("data-[fs-error]:text-destructive",u()))));_r(e,Kt(o,{get class(){return v(c)}},()=>n,{children:(m,y)=>{var S=Se(),T=Ee(S);je(T,t,"default",{get labelAttrs(){return f}},null),se(m,S)},$$slots:{default:!0}}))}qe(),s()}var mi=ht("<div><!> <span> </span></div>");function Ki(e,t){const r=De(t,["children","$$slots","$$events","$$legacy"]),n=De(r,["class","errorClasses"]);Be(t,!1);let i=ce(t,"class",8,void 0),s=ce(t,"errorClasses",8,void 0);He();{let o=_e(()=>(B(Me),B(i()),Ue(()=>Me("text-destructive text-sm font-medium",i()))));di(e,Kt({get class(){return v(o)}},()=>n,{children:wn,$$slots:{default:(u,f)=>{const c=_e(()=>f.errors),m=_e(()=>f.fieldErrorsAttrs),y=_e(()=>f.errorAttrs);var S=Se(),T=Ee(S);je(T,t,"default",{get errors(){return v(c)},get fieldErrorsAttrs(){return v(m)},get errorAttrs(){return v(y)}},P=>{var O=Se(),h=Ee(O);Pn(h,1,()=>v(c),Mn,(F,ue)=>{var $=mi();It($,ve=>({...v(y),class:ve}),[()=>(B(Me),B(s()),Ue(()=>Me("flex items-center gap-2 p-3 mt-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium","dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400",s())))]);var E=tt($);Lr(E,{size:16,class:"flex-shrink-0"});var I=fr(E,2),ae=tt(I,!0);nt(I),nt($),qt(()=>Tn(ae,v(ue))),wr(3,$,()=>Or,()=>({y:-10,duration:300})),se(F,$)}),se(P,O)}),se(u,S)}}}))}qe()}var pi=ht("<div><!></div>");function Wi(e,t){Be(t,!1);let r=ce(t,"form",8),n=ce(t,"name",8),i=ce(t,"class",8,void 0);He(),ui(e,{get form(){return r()},get name(){return n()},children:wn,$$slots:{default:(s,o)=>{const u=_e(()=>o.constraints),f=_e(()=>o.errors),c=_e(()=>o.tainted),m=_e(()=>o.value);var y=pi(),S=tt(y);je(S,t,"default",{get constraints(){return v(u)},get errors(){return v(f)},get tainted(){return v(c)},get value(){return v(m)}},null),nt(y),qt(T=>Mr(y,1,T),[()=>Pr((B(Me),B(i()),Ue(()=>Me("space-y-2",i()))))]),se(s,y)}}}),qe()}const Ji=li;export{Ji as C,Hi as F,Ki as a,Wi as b,Bi as c,Lr as d,Kr as e,Ui as s,qi as z};
