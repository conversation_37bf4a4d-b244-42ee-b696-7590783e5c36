import"../chunks/CWj6FrbW.js";import"../chunks/DhRTwODG.js";import{c as ge,o as Te,a as Je}from"../chunks/RnwjOPnl.js";import{p as ne,l as ye,j as H,i as s,m as ae,g as _e,f as w,c as t,s as n,r as e,h as i,t as F,bf as ht,a as v,n as ce,e as ie,aY as xt,aT as Q,q as De,b as be,d as fe,$ as bt}from"../chunks/DDiqt3uM.js";import{d as ve,s as B,h as yt}from"../chunks/DWulv87v.js";import{i as E}from"../chunks/2C89X9tI.js";import{r as Me,s as Pe,b as Ie}from"../chunks/C36Ip9GY.js";import{i as de}from"../chunks/B_FgA42l.js";import{p as W}from"../chunks/C-ZVHnwW.js";import{s as pe,a as Le}from"../chunks/B82PTGnX.js";import{p as _t}from"../chunks/ClgAkN2B.js";import{w as Ke,d as Be}from"../chunks/rjRVMZXi.js";import{e as Fe,i as Xe}from"../chunks/OiKQa7Wx.js";import{b as Ee}from"../chunks/DUGxtfU6.js";import{s as ee}from"../chunks/DE2v8SHj.js";import{h as wt}from"../chunks/CYhDwGx0.js";class kt{constructor(r,a){this.baseUrl=r.replace(/\/$/,""),this.apiSecret=a}async makeRequest(r,a={}){const l=`${this.baseUrl}${r}`,o={method:a.method||"GET",headers:{"Content-Type":"application/json","X-API-Secret":this.apiSecret,...a.headers}};a.body&&(o.method==="POST"||o.method==="PUT"||o.method==="PATCH")&&(o.body=JSON.stringify(a.body));try{const p=await fetch(l,o),b=await p.json();if(!p.ok)throw new Error(b.error||`HTTP ${p.status}: ${p.statusText}`);return b}catch(p){throw console.error("FireGeo API request failed:",p),p}}async makeAuthenticatedRequest(r,a,l={}){return this.makeRequest(r,{...l,headers:{Authorization:`Bearer ${a}`,...l.headers}})}async authenticateUser(r){var o;const a={supabaseToken:r,user:{}},l=await this.makeRequest("/api/auth/bridge",{method:"POST",body:a});return(o=l.data)!=null&&o.success&&l.data.session&&(this.session=l.data.session),l.data}async startBrandAnalysis(r,a){return(await this.makeAuthenticatedRequest("/api/brand-monitor/analyze",a,{method:"POST",body:r})).data}async getBrandAnalyses(r){return(await this.makeAuthenticatedRequest("/api/brand-monitor/analyses",r)).data||[]}async getBrandAnalysis(r,a){try{return(await this.makeAuthenticatedRequest(`/api/brand-monitor/analyses/${r}`,a)).data||null}catch(l){return console.error("Failed to get brand analysis:",l),null}}async checkProviders(r){return this.makeAuthenticatedRequest("/api/brand-monitor/check-providers",r)}async scrapeWebsite(r,a){return this.makeAuthenticatedRequest("/api/brand-monitor/scrape",a,{method:"POST",body:{url:r}})}async healthCheck(){return this.makeRequest("/api/auth/bridge")}createEventSource(r,a,l,o){const p=new URL(`${this.baseUrl}${r}`);p.searchParams.set("token",a);const b=new EventSource(p.toString());return l&&(b.onmessage=A=>{try{const g=JSON.parse(A.data);l(g)}catch(g){console.error("Failed to parse SSE data:",g)}}),o&&(b.onerror=o),b}}const At="https://your-firegeo-api.vercel.app";let je=null;function Qe(){if(!je){const u=At,r="dev_secret_key_123";je=new kt(u,r)}return je}const xe=Qe(),Re=Ke({connected:!1,authenticated:!1,error:null,latency:void 0});async function Ze(){try{const u=Qe(),r=Date.now();await u.healthCheck();const a=Date.now()-r;return Re.update(l=>({...l,connected:!0,error:null,latency:a})),!0}catch(u){return Re.update(r=>({...r,connected:!1,error:u instanceof Error?u.message:"Connection failed",latency:void 0})),!1}}function Ct(u,r){s(r).length<5&&H(r,[...s(r),""])}function ze(u,r){u.key==="Enter"&&!u.shiftKey&&(u.preventDefault(),r())}var St=w('<p class="text-sm text-destructive mt-1">Please enter a valid URL</p>'),$t=w('<button type="button" class="px-3 py-3 border-2 border-border bg-destructive/10 text-destructive hover:bg-destructive/20 transition-colors" title="Remove competitor"><svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button>'),Mt=w('<div class="flex gap-2"><input placeholder="competitor.com" class="flex-1 px-4 py-3 border-2 border-border bg-background text-foreground placeholder-muted-foreground focus:border-primary focus:outline-none transition-colors"/> <!></div>'),Bt=w('<button type="button" class="w-full px-4 py-3 border-2 border-dashed border-border text-muted-foreground hover:border-primary hover:text-foreground transition-colors">+ Add Competitor</button>'),Et=w('<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div> Analyzing...',1),jt=xt('<svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> Start Analysis',1),zt=w(`<div class="card-brutal p-8"><div class="space-y-6"><div><h2 class="text-2xl font-semibold text-foreground mb-2">Start Brand Analysis</h2> <p class="text-muted-foreground">Enter your company's website URL to analyze how AI models represent your
        brand in their responses.</p></div> <div class="space-y-4"><div><label for="url" class="block text-sm font-medium text-foreground mb-2">Company Website URL *</label> <input id="url" placeholder="example.com or https://example.com" class="w-full px-4 py-3 border-2 border-border bg-background text-foreground placeholder-muted-foreground focus:border-primary focus:outline-none transition-colors"/> <!></div> <div><label for="company-name" class="block text-sm font-medium text-foreground mb-2">Company Name (Optional)</label> <input id="company-name" placeholder="Your Company Name" class="w-full px-4 py-3 border-2 border-border bg-background text-foreground placeholder-muted-foreground focus:border-primary focus:outline-none transition-colors"/></div> <div><label class="block text-sm font-medium text-foreground mb-2">Competitors (Optional)</label> <div class="space-y-2"><!> <!></div></div> <button class="btn-primary px-6 py-3 font-bold w-full flex items-center justify-center gap-2"><!></button></div> <div class="bg-muted/50 p-4 rounded-lg border border-border"><h3 class="text-sm font-medium text-foreground mb-2">What happens next?</h3> <ul class="text-sm text-muted-foreground space-y-1"><li>• We'll analyze your website content and brand positioning</li> <li>• Test how AI models like ChatGPT, Claude, and Perplexity represent
          your brand</li> <li>• Compare your brand visibility against competitors</li> <li>• Provide actionable insights to improve your AI presence</li></ul></div></div></div>`);function Dt(u,r){ne(r,!1);let a=W(r,"isAnalyzing",8,!1);const l=ge();let o=ae(""),p=ae(""),b=ae([""]),A=ae(!1);function g(x){if(!x.trim())return!1;try{return new URL(x.startsWith("http")?x:`https://${x}`),!0}catch{return!1}}function L(x){s(b).length>1&&H(b,s(b).filter((P,M)=>M!==x))}function R(){if(s(A)&&!a()){const x={url:s(o).startsWith("http")?s(o):`https://${s(o)}`,companyName:s(p).trim()||void 0,competitors:s(b).filter(P=>P.trim()).map(P=>P.trim())};l("startAnalysis",x)}}ye(()=>s(o),()=>{H(A,g(s(o)))}),_e(),de();var U=zt(),q=t(U),V=n(t(q),2),c=t(V),f=n(t(c),2);Me(f),f.__keydown=[ze,R];var z=n(f,2);{var S=x=>{var P=St();v(x,P)};E(z,x=>{s(o)&&!s(A)&&x(S)})}e(c);var I=n(c,2),_=n(t(I),2);Me(_),_.__keydown=[ze,R],e(I);var m=n(I,2),$=n(t(m),2),h=t($);Fe(h,1,()=>s(b),Xe,(x,P,M)=>{var C=Mt(),D=t(C);Me(D),D.__keydown=[ze,R];var K=n(D,2);{var Z=k=>{var N=$t();N.__click=()=>L(M),F(()=>N.disabled=a()),v(k,N)};E(K,k=>{s(b),i(()=>s(b).length>1)&&k(Z)})}e(C),F(()=>D.disabled=a()),Ee(D,()=>s(b)[M],k=>(s(b)[M]=k,ht(()=>s(b)))),v(x,C)});var G=n(h,2);{var J=x=>{var P=Bt();P.__click=[Ct,b],F(()=>P.disabled=a()),v(x,P)};E(G,x=>{s(b),i(()=>s(b).length<5)&&x(J)})}e($),e(m);var y=n(m,2);y.__click=R;var d=t(y);{var T=x=>{var P=Et();ce(),v(x,P)},j=x=>{var P=jt();ce(),v(x,P)};E(d,x=>{a()?x(T):x(j,!1)})}e(y),e(V),ce(2),e(q),e(U),F(()=>{f.disabled=a(),_.disabled=a(),y.disabled=!s(A)||a()}),Ee(f,()=>s(o),x=>H(o,x)),Ee(_,()=>s(p),x=>H(p,x)),v(u,U),ie()}ve(["keydown","click"]);function It(u,r){r("back")}var Rt=w('<span class="px-2 py-1 bg-muted text-muted-foreground text-xs rounded border"> </span>'),Tt=w('<div><h3 class="text-sm font-medium text-foreground mb-2">Competitors Analyzed</h3> <div class="flex flex-wrap gap-2"></div></div>'),Pt=w(`<div class="card-brutal p-6"><h2 class="text-xl font-semibold text-foreground mb-4">Analysis Results</h2> <div class="space-y-4"><div class="bg-muted/50 p-4 rounded-lg border border-border"><h3 class="font-medium text-foreground mb-2">AI Model Responses</h3> <p class="text-sm text-muted-foreground">Analysis of how different AI models represent your brand in their responses.</p></div> <div class="bg-muted/50 p-4 rounded-lg border border-border"><h3 class="font-medium text-foreground mb-2">Competitive Positioning</h3> <p class="text-sm text-muted-foreground">Comparison of your brand visibility against competitors in AI responses.</p></div> <div class="bg-muted/50 p-4 rounded-lg border border-border"><h3 class="font-medium text-foreground mb-2">Recommendations</h3> <p class="text-sm text-muted-foreground">Actionable insights to improve your brand's representation in AI models.</p></div></div></div>`),Lt=w(`<div class="card-brutal p-6"><div class="text-center space-y-4"><div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div></div> <div><h2 class="text-xl font-semibold text-foreground mb-2">Analysis in Progress</h2> <p class="text-muted-foreground">We're analyzing your brand representation across multiple AI models. This typically takes 2-5 minutes.</p></div> <div class="max-w-md mx-auto"><div class="bg-muted rounded-full h-2"><div class="bg-primary h-2 rounded-full animate-pulse" style="width: 60%"></div></div> <p class="text-xs text-muted-foreground mt-2">Analyzing AI model responses...</p></div></div></div>`),Ft=w('<button class="btn-secondary px-4 py-2 text-sm">Export Results</button> <button class="btn-secondary px-4 py-2 text-sm">Share Analysis</button>',1),Ut=w('<button class="btn-secondary px-4 py-2 text-sm">Refresh Status</button>'),Gt=w('<div class="space-y-6"><div class="card-brutal p-6"><div class="flex items-start justify-between mb-4"><button class="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"><svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg> Back to analyses</button> <div class="flex items-center gap-2"><div> </div></div></div> <div class="space-y-3"><h1 class="text-2xl font-semibold text-foreground"> </h1> <div class="flex items-center gap-4 text-sm text-muted-foreground"><div class="flex items-center gap-1"><svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path></svg> <a target="_blank" rel="noopener noreferrer" class="hover:text-foreground transition-colors"> </a></div> <div class="flex items-center gap-1"><svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> </div></div> <!></div></div> <!> <div class="card-brutal p-4"><div class="flex items-center justify-between"><div class="text-sm text-muted-foreground"> </div> <div class="flex gap-2"><!></div></div></div></div>');function Nt(u,r){ne(r,!1);const a=ae();let l=W(r,"analysis",8);const o=ge();function p(k){return new Date(k).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}function b(){return l().analysisData?{status:"completed",color:"text-green-600",bgColor:"bg-green-100"}:{status:"in_progress",color:"text-yellow-600",bgColor:"bg-yellow-100"}}ye(()=>{},()=>{H(a,b())}),_e(),de();var A=Gt(),g=t(A),L=t(g),R=t(L);R.__click=[It,o];var U=n(R,2),q=t(U),V=t(q,!0);e(q),e(U),e(L);var c=n(L,2),f=t(c),z=t(f,!0);e(f);var S=n(f,2),I=t(S),_=n(t(I),2),m=t(_,!0);e(_),e(I);var $=n(I,2),h=n(t($));e($),e(S);var G=n(S,2);{var J=k=>{var N=Tt(),se=n(t(N),2);Fe(se,5,()=>(Q(l()),i(()=>l().competitors)),Xe,(le,re)=>{var X=Rt(),te=t(X,!0);e(X),F(()=>B(te,(s(re),i(()=>s(re).name)))),v(le,X)}),e(se),e(N),v(k,N)};E(G,k=>{Q(l()),i(()=>l().competitors&&l().competitors.length>0)&&k(J)})}e(c),e(g);var y=n(g,2);{var d=k=>{var N=Pt();v(k,N)},T=k=>{var N=Lt();v(k,N)};E(y,k=>{s(a),Q(l()),i(()=>s(a).status==="completed"&&l().analysisData)?k(d):k(T,!1)})}var j=n(y,2),x=t(j),P=t(x),M=t(P);e(P);var C=n(P,2),D=t(C);{var K=k=>{var N=Ft();ce(2),v(k,N)},Z=k=>{var N=Ut();v(k,N)};E(D,k=>{s(a),i(()=>s(a).status==="completed")?k(K):k(Z,!1)})}e(C),e(x),e(j),e(A),F(k=>{ee(q,1,`px-3 py-1 rounded-full text-xs font-medium ${s(a),i(()=>s(a).bgColor)??""} ${s(a),i(()=>s(a).color)??""}`),B(V,(s(a),i(()=>s(a).status==="completed"?"Completed":"In Progress"))),B(z,(Q(l()),i(()=>l().companyName||"Brand Analysis"))),Pe(_,"href",(Q(l()),i(()=>l().url))),B(m,(Q(l()),i(()=>l().url))),B(h,` ${k??""}`),B(M,`Analysis ID: ${Q(l()),i(()=>l().id)??""}`)},[()=>(Q(l()),i(()=>p(l().createdAt)))]),v(u,A),ie()}ve(["click"]);function Ve(u,r){r("clearSelection")}var qt=w('<button class="text-sm text-muted-foreground hover:text-foreground transition-colors">← Back to list</button>'),Ot=w('<div class="text-center py-6"><div class="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-3"><svg class="w-6 h-6 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg></div> <p class="text-sm text-muted-foreground">No analyses yet</p> <p class="text-xs text-muted-foreground mt-1">Start your first analysis</p></div>'),Wt=(u,r,a)=>r(s(a)),Ht=w('<div class="text-xs text-muted-foreground"> </div>'),Vt=w('<button><div class="space-y-2"><div class="flex items-start justify-between"><div class="flex-1 min-w-0"><h4 class="font-medium text-foreground truncate"> </h4> <p class="text-xs text-muted-foreground truncate"> </p></div> <div class="flex items-center gap-1 ml-2"><div></div></div></div> <div class="flex items-center justify-between text-xs"><span class="text-muted-foreground"> </span> <span class="text-muted-foreground"> </span></div> <!></div></button>'),Yt=w('<div class="space-y-2"></div>'),Jt=w('<div class="card-brutal p-4"><div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold text-foreground">Analyses</h3> <!></div> <!> <div class="mt-4 pt-4 border-t border-border"><button class="w-full px-4 py-2 text-sm border-2 border-dashed border-border text-muted-foreground hover:border-primary hover:text-foreground transition-colors">+ New Analysis</button></div></div>');function Kt(u,r){ne(r,!1);let a=W(r,"analyses",24,()=>[]),l=W(r,"selectedAnalysis",8,null);const o=ge();function p(_){o("select",_)}function b(_){return _.analysisData?"bg-green-500":"bg-yellow-500"}function A(_){return _.analysisData?"Completed":"In Progress"}function g(_){return new Date(_).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}function L(_,m=30){return _.length<=m?_:_.substring(0,m)+"..."}de();var R=Jt(),U=t(R),q=n(t(U),2);{var V=_=>{var m=qt();m.__click=[Ve,o],v(_,m)};E(q,_=>{l()&&_(V)})}e(U);var c=n(U,2);{var f=_=>{var m=Ot();v(_,m)},z=_=>{var m=Yt();Fe(m,5,a,$=>$.id,($,h)=>{var G=Vt();G.__click=[Wt,p,h];var J=t(G),y=t(J),d=t(y),T=t(d),j=t(T,!0);e(T);var x=n(T,2),P=t(x,!0);e(x),e(d);var M=n(d,2),C=t(M);e(M),e(y);var D=n(y,2),K=t(D),Z=t(K,!0);e(K);var k=n(K,2),N=t(k,!0);e(k),e(D);var se=n(D,2);{var le=re=>{var X=Ht(),te=t(X);e(X),F(()=>B(te,`${s(h),i(()=>s(h).competitors.length)??""} competitor${s(h),i(()=>s(h).competitors.length!==1?"s":"")??""}`)),v(re,X)};E(se,re=>{s(h),i(()=>s(h).competitors&&s(h).competitors.length>0)&&re(le)})}e(J),e(G),F((re,X,te,me)=>{ee(G,1,`w-full p-3 text-left border-2 border-border bg-background hover:bg-accent transition-colors ${Q(l()),s(h),i(()=>{var he;return((he=l())==null?void 0:he.id)===s(h).id?"border-primary bg-primary/5":""})??""}`),B(j,(s(h),i(()=>s(h).companyName||"Unknown Company"))),B(P,re),ee(C,1,`w-2 h-2 rounded-full ${X??""}`),B(Z,te),B(N,me)},[()=>(s(h),i(()=>L(s(h).url))),()=>(s(h),i(()=>b(s(h)))),()=>(s(h),i(()=>A(s(h)))),()=>(s(h),i(()=>g(s(h).createdAt)))]),v($,G)}),e(m),v(_,m)};E(c,_=>{Q(a()),i(()=>a().length===0)?_(f):_(z,!1)})}var S=n(c,2),I=t(S);I.__click=[Ve,o],e(S),e(R),v(u,R),ie()}ve(["click"]);function Xt(u,r){r("action")}var Qt=w('<button class="btn-primary px-6 py-3 font-bold"> </button>'),Zt=w(`<div class="card-brutal p-8 text-center"><div class="space-y-6"><div class="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto"><svg class="w-8 h-8 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg></div> <div class="space-y-2"><h3 class="text-lg font-medium text-foreground"> </h3> <p class="text-muted-foreground max-w-md mx-auto"> </p></div> <!> <div class="bg-muted/50 p-4 rounded-lg border border-border max-w-md mx-auto"><h4 class="text-sm font-medium text-foreground mb-3">What you'll get:</h4> <ul class="text-sm text-muted-foreground space-y-2 text-left"><li class="flex items-center gap-2"><svg class="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg> AI model brand visibility analysis</li> <li class="flex items-center gap-2"><svg class="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg> Competitor positioning insights</li> <li class="flex items-center gap-2"><svg class="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg> Actionable improvement recommendations</li> <li class="flex items-center gap-2"><svg class="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg> Multi-provider comparison matrix</li></ul></div></div></div>`);function er(u,r){ne(r,!1);let a=W(r,"title",8,"No analyses yet"),l=W(r,"description",8,"Start your first brand analysis to see how AI models represent your company."),o=W(r,"actionText",8,"Start Analysis"),p=W(r,"showAction",8,!0);const b=ge();de();var A=Zt(),g=t(A),L=n(t(g),2),R=t(L),U=t(R,!0);e(R);var q=n(R,2),V=t(q,!0);e(q),e(L);var c=n(L,2);{var f=z=>{var S=Qt();S.__click=[Xt,b];var I=t(S,!0);e(S),F(()=>B(I,o())),v(z,S)};E(c,z=>{p()&&z(f)})}ce(2),e(g),e(A),F(()=>{B(U,a()),B(V,l())}),v(u,A),ie()}ve(["click"]);function tr(u,r){r("retry")}function Ye(u,r){r("dismiss")}var rr=w('<p class="text-sm text-muted-foreground mt-2"> </p>'),ar=w('<button class="btn-secondary px-4 py-2 text-sm">Try Again</button>'),sr=w('<div class="card-brutal p-6 bg-destructive/5 border-destructive/20"><div class="space-y-4"><div class="flex items-start gap-3"><div class="w-6 h-6 text-destructive mt-0.5"><svg fill="none" viewBox="0 0 24 24" stroke="currentColor"><!></svg></div> <div class="flex-1"><h3 class="font-semibold text-destructive"> </h3> <p class="text-destructive/80 mt-1"> </p> <!></div> <button class="text-muted-foreground hover:text-foreground transition-colors" title="Dismiss"><svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div> <div class="flex items-center gap-3"><!> <button class="text-sm text-muted-foreground hover:text-foreground transition-colors">Dismiss</button></div></div></div>');function et(u,r){ne(r,!1);let a=W(r,"error",8),l=W(r,"title",8,"Error"),o=W(r,"retryable",8,!0),p=W(r,"details",8,"");const b=ge();function A(){return'<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />'}de();var g=sr(),L=t(g),R=t(L),U=t(R),q=t(U),V=t(q);wt(V,()=>i(A),!0),e(q),e(U);var c=n(U,2),f=t(c),z=t(f,!0);e(f);var S=n(f,2),I=t(S,!0);e(S);var _=n(S,2);{var m=d=>{var T=rr(),j=t(T,!0);e(T),F(()=>B(j,p())),v(d,T)};E(_,d=>{p()&&d(m)})}e(c);var $=n(c,2);$.__click=[Ye,b],e(R);var h=n(R,2),G=t(h);{var J=d=>{var T=ar();T.__click=[tr,b],v(d,T)};E(G,d=>{o()&&d(J)})}var y=n(G,2);y.__click=[Ye,b],e(h),e(L),e(g),F(()=>{B(z,l()),B(I,a())}),v(u,g),ie()}ve(["click"]);function or(){const u=Ke({id:null,status:"idle",progress:null,result:null,error:null,retryCount:0,lastError:null,connectionLost:!1});let r=null;const a=Be(u,c=>c.status==="starting"||c.status==="in_progress"||c.status==="retrying"),l=Be(u,c=>{var f;return((f=c.progress)==null?void 0:f.progress)||0}),o=Be(u,c=>{var f;return((f=c.progress)==null?void 0:f.step)||""});async function p(c,f){try{u.update(S=>({...S,status:"starting",progress:null,result:null,error:null}));const z=await xe.startBrandAnalysis(c,f);u.update(S=>({...S,id:z.id,status:"in_progress"})),b(z.id,f)}catch(z){console.error("Failed to start analysis:",z),u.update(S=>({...S,status:"failed",error:z instanceof Error?z.message:"Failed to start analysis"}))}}function b(c,f){r&&r.close();try{r=xe.createEventSource(`/api/brand-monitor/analyze?id=${c}`,f,A,g),r.onopen=()=>{console.log("SSE connection opened for analysis:",c)}}catch(z){console.error("Failed to setup SSE connection:",z),u.update(S=>({...S,status:"failed",error:"Failed to establish real-time connection"}))}}function A(c){console.log("SSE message received:",c),c.type==="progress"?u.update(f=>({...f,progress:c.progress})):c.type==="completed"?(u.update(f=>({...f,status:"completed",result:c.result,progress:{...f.progress,completed:!0,progress:100}})),L()):c.type==="error"&&(u.update(f=>({...f,status:"failed",error:c.error||"Analysis failed"})),L())}function g(c){console.error("SSE connection error:",c),u.update(f=>({...f,status:"failed",error:"Real-time connection lost"})),L()}function L(){r&&(r.close(),r=null)}function R(){L(),u.update(c=>({...c,status:"idle",id:null,progress:null,result:null,error:null}))}async function U(c,f){const z=u.get();if(z.retryCount>=3){u.update(I=>({...I,status:"failed",error:"Maximum retry attempts reached. Please try again later."}));return}u.update(I=>({...I,status:"retrying",retryCount:I.retryCount+1,lastError:I.error,error:null}));const S=Math.min(1e3*Math.pow(2,z.retryCount),1e4);await new Promise(I=>setTimeout(I,S)),await p(c,f)}function q(){L(),u.set({id:null,status:"idle",progress:null,result:null,error:null,retryCount:0,lastError:null,connectionLost:!1})}function V(){L()}return{analysisState:u,isAnalyzing:a,progressPercentage:l,currentStep:o,startAnalysis:p,retryAnalysis:U,cancelAnalysis:R,reset:q,destroy:V}}var nr=w('<span class="text-sm text-muted-foreground"> </span>'),ir=w('<div class="bg-muted/50 p-4 rounded-lg border border-border"><div class="flex items-start gap-3"><div class="w-5 h-5 text-primary mt-0.5"><svg fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path></svg></div> <div class="flex-1"><h4 class="font-medium text-foreground mb-1"> </h4> <p class="text-sm text-muted-foreground"> </p></div></div></div>'),lr=w("<p> </p>"),dr=w("<p> </p>"),cr=w('<button class="btn-secondary px-4 py-2 text-sm">Cancel Analysis</button>'),vr=w('<div class="card-brutal p-6"><div class="space-y-6"><div class="flex items-center justify-between"><h3 class="text-lg font-semibold text-foreground"> </h3> <!></div> <div class="space-y-2"><div class="flex items-center justify-between text-sm"><span class="text-foreground font-medium"> </span> <span class="text-muted-foreground"> </span></div> <div class="w-full bg-muted rounded-full h-3 border border-border"><div></div></div> <p class="text-sm text-muted-foreground"> </p></div> <!> <div class="text-center"><p class="text-xs text-muted-foreground"> </p></div> <div class="bg-muted/30 p-4 rounded-lg border border-border"><h4 class="font-medium text-foreground mb-2">Analyzing:</h4> <div class="space-y-1 text-sm text-muted-foreground"><p> </p> <!> <!></div></div> <div class="flex items-center justify-between"><div class="text-sm text-muted-foreground"><!></div> <!></div></div></div>'),ur=w('<div class="space-y-6"><!> <!></div>');function mr(u,r){ne(r,!1);const[a,l]=Le(),o=()=>pe(q,"$analysisState",a),p=()=>pe(V,"$isAnalyzing",a),b=()=>pe(f,"$currentStep",a),A=()=>pe(c,"$progressPercentage",a);let g=W(r,"analysisData",8),L=W(r,"supabaseToken",8),R=W(r,"onComplete",8,()=>{}),U=W(r,"onCancel",8,()=>{});const{analysisState:q,isAnalyzing:V,progressPercentage:c,currentStep:f,startAnalysis:z,cancelAnalysis:S,destroy:I}=or();Te(async()=>{await z(g(),L())}),Je(()=>{I()});function _(){S(),U()()}function m(){z(g(),L())}function $(j){return j>=100?"Completed":j>=80?"Almost done!":j>=50?"1-2 minutes remaining":j>=20?"2-4 minutes remaining":"3-5 minutes remaining"}function h(j,x){return j?"M5 13l4 4L19 7":"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"}ye(()=>(o(),Q(R())),()=>{o().status==="completed"&&o().result&&R()(o().result)}),_e(),de();var G=ur(),J=t(G);{var y=j=>{et(j,{get error(){return o(),i(()=>o().error)},title:"Analysis Failed",$$events:{retry:m,dismiss:_}})};E(J,j=>{o(),i(()=>o().status==="failed"&&o().error)&&j(y)})}var d=n(J,2);{var T=j=>{var x=vr(),P=t(x),M=t(P),C=t(M),D=t(C,!0);e(C);var K=n(C,2);{var Z=O=>{var Y=nr(),oe=t(Y);e(Y),F(ue=>B(oe,`ID: ${ue??""}...`),[()=>(o(),i(()=>o().id.slice(0,8)))]),v(O,Y)};E(K,O=>{o(),i(()=>o().id)&&O(Z)})}e(M);var k=n(M,2),N=t(k),se=t(N),le=t(se,!0);e(se);var re=n(se,2),X=t(re);e(re),e(N);var te=n(N,2),me=t(te);e(te);var he=n(te,2),tt=t(he,!0);e(he),e(k);var Ue=n(k,2);{var rt=O=>{var Y=ir(),oe=t(Y),ue=t(oe),Se=t(ue),mt=t(Se);e(Se),e(ue);var We=n(ue,2),$e=t(We),pt=t($e);e($e);var He=n($e,2),ft=t(He,!0);e(He),e(We),e(oe),e(Y),F(gt=>{ee(Se,0,p()?"animate-spin":""),Pe(mt,"d",gt),B(pt,`Current Step: ${o(),i(()=>o().progress.step)??""}`),B(ft,(o(),i(()=>o().progress.message)))},[()=>(o(),p(),i(()=>h(o().progress.completed,p())))]),v(O,Y)};E(Ue,O=>{o(),i(()=>o().progress)&&O(rt)})}var we=n(Ue,2),Ge=t(we),at=t(Ge,!0);e(Ge),e(we);var ke=n(we,2),Ne=n(t(ke),2),Ae=t(Ne),st=t(Ae);e(Ae);var qe=n(Ae,2);{var ot=O=>{var Y=lr(),oe=t(Y);e(Y),F(()=>B(oe,`• Company: ${Q(g()),i(()=>g().companyName)??""}`)),v(O,Y)};E(qe,O=>{Q(g()),i(()=>g().companyName)&&O(ot)})}var nt=n(qe,2);{var it=O=>{var Y=dr(),oe=t(Y);e(Y),F(ue=>B(oe,`• Competitors: ${ue??""}`),[()=>(Q(g()),i(()=>g().competitors.join(", ")))]),v(O,Y)};E(nt,O=>{Q(g()),i(()=>g().competitors&&g().competitors.length>0)&&O(it)})}e(Ne),e(ke);var Oe=n(ke,2),Ce=t(Oe),lt=t(Ce);{var dt=O=>{var Y=De("Analysis completed successfully");v(O,Y)},ct=O=>{var Y=De("Real-time updates active");v(O,Y)};E(lt,O=>{o(),i(()=>o().status==="completed")?O(dt):O(ct,!1)})}e(Ce);var vt=n(Ce,2);{var ut=O=>{var Y=cr();Y.__click=_,v(O,Y)};E(vt,O=>{p()&&O(ut)})}e(Oe),e(P),e(x),F((O,Y)=>{B(D,(o(),i(()=>o().status==="completed"?"Analysis Complete":"Analyzing Your Brand"))),B(le,b()||"Initializing..."),B(X,`${O??""}%`),ee(me,1,`bg-primary h-full rounded-full transition-all duration-500 ease-out ${p()?"animate-pulse":""}`),Ie(me,`width: ${A()??""}%`),B(tt,(o(),i(()=>{var oe;return((oe=o().progress)==null?void 0:oe.message)||"Starting analysis..."}))),B(at,Y),B(st,`• Website: ${Q(g()),i(()=>g().url)??""}`)},[()=>(A(),i(()=>Math.round(A()))),()=>(A(),i(()=>$(A())))]),v(j,x)};E(d,j=>{p(),o(),i(()=>p()||o().status==="completed")&&j(T)})}e(G),v(u,G),ie(),l()}ve(["click"]);var pr=w('<div class="grid grid-cols-1 lg:grid-cols-4 gap-6"><div class="lg:col-span-1"><!></div> <div class="lg:col-span-3 space-y-6"><!> <!></div></div>');function fr(u,r){ne(r,!1);let a=W(r,"analyses",28,()=>[]),l=W(r,"session",8),o=W(r,"error",8,null);const p=ge();let b=ae(null),A=ae(!1),g=ae(null);function L(d){H(g,d.detail),H(A,!0)}function R(d){H(A,!1),H(g,null),d&&(a([d,...a()]),p("newAnalysis",d))}function U(){H(A,!1),H(g,null)}function q(d){H(b,d)}function V(){H(b,null)}function c(){p("retry")}function f(){p("dismissError")}function z(){V()}de();var S=pr(),I=t(S),_=t(I);Kt(_,{get analyses(){return a()},get selectedAnalysis(){return s(b)},$$events:{select:d=>q(d.detail),clearSelection:V}}),e(I);var m=n(I,2),$=t(m);{var h=d=>{et(d,{get error(){return o()},title:"Connection Error",$$events:{retry:c,dismiss:f}})};E($,d=>{o()&&d(h)})}var G=n($,2);{var J=d=>{Nt(d,{get analysis(){return s(b)},$$events:{back:V}})},y=d=>{var T=be(),j=fe(T);{var x=M=>{mr(M,{get analysisData(){return s(g)},get supabaseToken(){return Q(l()),i(()=>l().access_token)},onComplete:R,onCancel:U})},P=M=>{var C=be(),D=fe(C);{var K=k=>{er(k,{title:"Start Your First Brand Analysis",description:"Enter your company's website URL to analyze how AI models like ChatGPT, Claude, and Perplexity represent your brand in their responses.",actionText:"Start Analysis",$$events:{action:z}})},Z=k=>{Dt(k,{get isAnalyzing(){return s(A)},$$events:{startAnalysis:L,complete:R}})};E(D,k=>{Q(a()),i(()=>a().length===0)?k(K):k(Z,!1)},!0)}v(M,C)};E(j,M=>{s(A)&&s(g)&&l()?M(x):M(P,!1)},!0)}v(d,T)};E(G,d=>{s(b)?d(J):d(y,!1)})}e(m),e(S),v(u,S),ie()}var gr=(u,r)=>r(!r()),hr=w('<button class="text-xs text-muted-foreground hover:text-foreground transition-colors">Hide Details</button>'),xr=(u,r)=>r(!r()),br=w('<button class="text-xs text-muted-foreground hover:text-foreground transition-colors">Show Details</button>'),yr=w(`<div class="mt-2 p-2 bg-destructive/10 border border-destructive/20 rounded text-destructive"><div class="flex items-center gap-2"><svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> <span class="font-medium">Connection Issues</span></div> <p class="mt-1 text-xs">Unable to connect to FireGeo service. Please check your internet
          connection or try again later.</p></div>`),_r=w('<div class="mt-3 p-3 bg-muted/50 rounded-lg border border-border text-xs"><div class="grid grid-cols-2 gap-2"><div><span class="font-medium text-foreground">Status:</span> <span class="text-muted-foreground"> </span></div> <div><span class="font-medium text-foreground">Latency:</span> <span class="text-muted-foreground"> <!></span></div> <div><span class="font-medium text-foreground">Last Checked:</span> <span class="text-muted-foreground"> </span></div> <div><span class="font-medium text-foreground">Service:</span> <span class="text-muted-foreground">FireGeo API</span></div></div> <!></div>'),wr=w('<div class="flex items-center gap-2 text-sm"><div class="flex items-center gap-2"><div></div> <span class="text-muted-foreground"> </span></div> <!> <button class="text-xs text-muted-foreground hover:text-foreground transition-colors" title="Check connection" aria-label="Check connection"><svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg></button></div> <!>',1);function kr(u,r){ne(r,!1);const[a,l]=Le(),o=()=>pe(Re,"$fireGeoConnectionState",a);let p=W(r,"showDetails",12,!1),b=null,A=null;Te(()=>{g(),b=setInterval(g,3e4)}),Je(()=>{b&&clearInterval(b)});async function g(){try{await Ze(),A=new Date}catch(y){console.error("Connection check failed:",y),A=new Date}}function L(){return o().connected?o().latency&&o().latency>1e3?"bg-yellow-500":"bg-green-500":"bg-red-500"}function R(){return o().connected?o().latency&&o().latency>1e3?"Slow Connection":"Connected":"Disconnected"}function U(y){return y?y<100?"Excellent":y<300?"Good":y<1e3?"Fair":"Poor":"Unknown"}function q(){if(!A)return"Never";const d=new Date().getTime()-A.getTime(),T=Math.floor(d/1e3);if(T<60)return`${T}s ago`;const j=Math.floor(T/60);return j<60?`${j}m ago`:`${Math.floor(j/60)}h ago`}de();var V=wr(),c=fe(V),f=t(c),z=t(f),S=n(z,2),I=t(S,!0);e(S),e(f);var _=n(f,2);{var m=y=>{var d=hr();d.__click=[gr,p],v(y,d)},$=y=>{var d=br();d.__click=[xr,p],v(y,d)};E(_,y=>{p()?y(m):y($,!1)})}var h=n(_,2);h.__click=g,e(c);var G=n(c,2);{var J=y=>{var d=_r(),T=t(d),j=t(T),x=n(t(j),2),P=t(x,!0);e(x),e(j);var M=n(j,2),C=n(t(M),2),D=t(C),K=n(D);{var Z=X=>{var te=De();F(()=>B(te,`(${o(),i(()=>o().latency)??""}ms)`)),v(X,te)};E(K,X=>{o(),i(()=>o().latency)&&X(Z)})}e(C),e(M);var k=n(M,2),N=n(t(k),2),se=t(N,!0);e(N),e(k),ce(2),e(T);var le=n(T,2);{var re=X=>{var te=yr();v(X,te)};E(le,X=>{o(),i(()=>!o().connected)&&X(re)})}e(d),F((X,te,me)=>{B(P,X),B(D,`${te??""} `),B(se,me)},[()=>i(R),()=>(o(),i(()=>U(o().latency))),()=>i(q)]),v(y,d)};E(G,y=>{p()&&y(J)})}F((y,d)=>{ee(z,1,`w-2 h-2 rounded-full ${y??""} ${o(),i(()=>o().connected?"animate-pulse":"")??""}`),B(I,d)},[()=>i(L),()=>i(R)]),v(u,V),ie(),l()}ve(["click"]);var Ar=w("<div></div>"),Cr=w("<div><!> <span> </span></div>"),Sr=w("<div></div>"),$r=w("<p> </p>"),Mr=w('<div class="space-y-2"><div class="flex items-center justify-between text-xs"><span class="text-muted-foreground">Progress</span> <span class="text-muted-foreground"> </span></div> <div class="w-full bg-muted rounded-full h-2 border border-border"><div class="bg-primary h-full rounded-full transition-all duration-300 ease-out"></div></div></div>'),Br=w('<div><div class="space-y-4"><div class="flex items-center gap-3"><!> <div><h3> </h3> <!></div></div> <!> <div class="flex items-center justify-center py-4"><div class="flex space-x-1"><div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 0ms"></div> <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 150ms"></div> <div class="w-2 h-2 bg-primary rounded-full animate-bounce" style="animation-delay: 300ms"></div></div></div></div></div>'),Er=w('<div class="flex justify-center"><div></div></div>'),jr=w("<p> </p>"),zr=w('<div class="max-w-xs mx-auto space-y-2"><div class="w-full bg-muted rounded-full h-2 border border-border"><div class="bg-primary h-full rounded-full transition-all duration-300 ease-out"></div></div> <p class="text-xs text-muted-foreground"> </p></div>'),Dr=w('<div><div class="space-y-4"><!> <div><h3> </h3> <!></div> <!></div></div>');function Ir(u,r){ne(r,!1);const a=ae();let l=W(r,"title",8,"Loading..."),o=W(r,"description",8,""),p=W(r,"progress",8,null),b=W(r,"showSpinner",8,!0),A=W(r,"size",8,"md"),g=W(r,"variant",8,"default");function L(){switch(A()){case"sm":return{container:"p-4",spinner:"w-6 h-6",title:"text-sm",description:"text-xs"};case"lg":return{container:"p-8",spinner:"w-12 h-12",title:"text-xl",description:"text-base"};default:return{container:"p-6",spinner:"w-8 h-8",title:"text-lg",description:"text-sm"}}}ye(()=>{},()=>{H(a,L())}),_e();var R=be(),U=fe(R);{var q=c=>{var f=Cr(),z=t(f);{var S=m=>{var $=Ar();F(()=>ee($,1,`animate-spin rounded-full ${s(a),i(()=>s(a).spinner)??""} border-b-2 border-primary`)),v(m,$)};E(z,m=>{b()&&m(S)})}var I=n(z,2),_=t(I,!0);e(I),e(f),F(()=>{ee(f,1,`flex items-center gap-3 ${s(a),i(()=>s(a).container)??""}`),ee(I,1,`text-muted-foreground ${s(a),i(()=>s(a).title)??""}`),B(_,l())}),v(c,f)},V=c=>{var f=be(),z=fe(f);{var S=_=>{var m=Br(),$=t(m),h=t($),G=t(h);{var J=C=>{var D=Sr();F(()=>ee(D,1,`animate-spin rounded-full ${s(a),i(()=>s(a).spinner)??""} border-b-2 border-primary`)),v(C,D)};E(G,C=>{b()&&C(J)})}var y=n(G,2),d=t(y),T=t(d,!0);e(d);var j=n(d,2);{var x=C=>{var D=$r(),K=t(D,!0);e(D),F(()=>{ee(D,1,`text-muted-foreground ${s(a),i(()=>s(a).description)??""} mt-1`),B(K,o())}),v(C,D)};E(j,C=>{o()&&C(x)})}e(y),e(h);var P=n(h,2);{var M=C=>{var D=Mr(),K=t(D),Z=n(t(K),2),k=t(Z);e(Z),e(K);var N=n(K,2),se=t(N);e(N),e(D),F(le=>{B(k,`${le??""}%`),Ie(se,`width: ${p()??""}%`)},[()=>(Q(p()),i(()=>Math.round(p())))]),v(C,D)};E(P,C=>{p()!==null&&C(M)})}ce(2),e($),e(m),F(()=>{ee(m,1,`card-brutal ${s(a),i(()=>s(a).container)??""}`),ee(d,1,`font-semibold text-foreground ${s(a),i(()=>s(a).title)??""}`),B(T,l())}),v(_,m)},I=_=>{var m=Dr(),$=t(m),h=t($);{var G=M=>{var C=Er(),D=t(C);e(C),F(()=>ee(D,1,`animate-spin rounded-full ${s(a),i(()=>s(a).spinner)??""} border-b-2 border-primary`)),v(M,C)};E(h,M=>{b()&&M(G)})}var J=n(h,2),y=t(J),d=t(y,!0);e(y);var T=n(y,2);{var j=M=>{var C=jr(),D=t(C,!0);e(C),F(()=>{ee(C,1,`text-muted-foreground ${s(a),i(()=>s(a).description)??""} mt-2`),B(D,o())}),v(M,C)};E(T,M=>{o()&&M(j)})}e(J);var x=n(J,2);{var P=M=>{var C=zr(),D=t(C),K=t(D);e(D);var Z=n(D,2),k=t(Z);e(Z),e(C),F(N=>{Ie(K,`width: ${p()??""}%`),B(k,`${N??""}% complete`)},[()=>(Q(p()),i(()=>Math.round(p())))]),v(M,C)};E(x,M=>{p()!==null&&M(P)})}e($),e(m),F(()=>{ee(m,1,`card-brutal ${s(a),i(()=>s(a).container)??""} text-center`),ee(y,1,`font-semibold text-foreground ${s(a),i(()=>s(a).title)??""}`),B(d,l())}),v(_,m)};E(z,_=>{g()==="detailed"?_(S):_(I,!1)},!0)}v(c,f)};E(U,c=>{g()==="minimal"?c(q):c(V,!1)})}v(u,R),ie()}var Rr=w('<meta name="description" content="Track how AI models rank your brand against competitors in search results and recommendations."/>'),Tr=()=>window.location.reload(),Pr=w('<div class="card-brutal p-6 bg-destructive/10 border-destructive"><div class="flex items-center gap-3"><svg class="w-5 h-5 text-destructive" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> <div><h3 class="font-medium text-destructive">Error</h3> <p class="text-destructive/80"> </p></div></div> <button class="mt-4 btn-secondary px-4 py-2 text-sm">Retry</button></div>'),Lr=w(`<div class="min-h-screen bg-background"><nav class="flex items-center space-x-2 text-sm text-muted-foreground mb-6"><a class="hover:text-foreground transition-colors">Dashboard</a> <span>/</span> <span class="text-foreground font-medium">Brand Monitor</span></nav> <div class="mb-8"><div class="flex items-center gap-4 mb-4"><div class="w-12 h-12 bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div> <div><h1 class="text-4xl font-semibold text-foreground">FireGeo Brand Monitor</h1> <p class="text-lg text-muted-foreground mt-1">Track how AI models rank your brand against competitors in search
          results and recommendations.</p></div></div> <!></div> <!></div>`);function ta(u,r){ne(r,!1);const[a,l]=Le(),o=()=>pe(_t,"$page",a);let p=W(r,"data",8),b=ae([]),A=ae(!0),g=ae(null),L=ae(!1);Te(async()=>{var m;try{const $=await Ze();if(H(L,!0),!$){H(g,"Unable to connect to FireGeo service"),H(A,!1);return}const h=(m=p().session)==null?void 0:m.access_token;h&&(await xe.authenticateUser(h),H(b,await xe.getBrandAnalyses(h)))}catch($){console.error("Brand monitor initialization error:",$),H(g,$ instanceof Error?$.message:"Failed to load brand monitor")}finally{H(A,!1)}});async function R(m){var $;try{H(A,!0);const h=($=p().session)==null?void 0:$.access_token;if(h){const G=await xe.startBrandAnalysis(m.detail,h);H(b,[G,...s(b)])}}catch(h){console.error("Failed to start analysis:",h),H(g,h instanceof Error?h.message:"Failed to start analysis")}finally{H(A,!1)}}de();var U=Lr();yt(m=>{var $=Rr();bt.title="Brand Monitor - FireGeo",v(m,$)});var q=t(U),V=t(q);ce(4),e(q);var c=n(q,2),f=n(t(c),2);{var z=m=>{kr(m,{showDetails:!1})};E(f,m=>{s(L)&&m(z)})}e(c);var S=n(c,2);{var I=m=>{Ir(m,{title:"Loading Brand Monitor",description:"Connecting to FireGeo service and loading your analyses...",variant:"detailed"})},_=m=>{var $=be(),h=fe($);{var G=y=>{var d=Pr(),T=t(d),j=n(t(T),2),x=n(t(j),2),P=t(x,!0);e(x),e(j),e(T);var M=n(T,2);M.__click=[Tr],e(d),F(()=>B(P,s(g))),v(y,d)},J=y=>{fr(y,{get analyses(){return s(b)},get error(){return s(g)},get session(){return Q(p()),i(()=>p().session)},$$events:{newAnalysis:R,retry:()=>window.location.reload(),dismissError:()=>H(g,null)}})};E(h,y=>{s(g)?y(G):y(J,!1)},!0)}v(m,$)};E(S,m=>{s(A)?m(I):m(_,!1)})}e(U),F(()=>Pe(V,"href",`/dashboard/${o(),i(()=>o().params.envSlug)??""}`)),v(u,U),ie(),l()}ve(["click"]);export{ta as component};
