import"../chunks/CWj6FrbW.js";import{b as w,d as m,a as s,p as G,f,h as ha,aT as O,c as k,r as S,e as M,s as v,$ as _a,I as Y,i as p,j as D,t as ga,n as j,q as wa}from"../chunks/DDiqt3uM.js";import{d as $a,h as ba,s as Pa}from"../chunks/DWulv87v.js";import{i as C}from"../chunks/2C89X9tI.js";import{c as g}from"../chunks/BCmD-YNt.js";import{s as ya}from"../chunks/DE2v8SHj.js";import{b as xa}from"../chunks/Dqu9JXqq.js";import{a as Ca,s as ka}from"../chunks/B82PTGnX.js";import{p as Sa}from"../chunks/ClgAkN2B.js";import{S as Ta,A as Aa}from"../chunks/XAW1B1R8.js";import{a as Ea,C as Fa}from"../chunks/CfRLwaeF.js";import"../chunks/DhRTwODG.js";import"../chunks/18cO7kEJ.js";import{b as Na}from"../chunks/ChutyBgo.js";import{c as Ba}from"../chunks/CCJOWbOV.js";import{g as Va}from"../chunks/CaxpRkM3.js";import{s as H}from"../chunks/iCEqKm8o.js";import{l as T,s as Ia,p as za}from"../chunks/C-ZVHnwW.js";import{I as Oa}from"../chunks/CkoRhfQ8.js";import{a as Ya}from"../chunks/C36Ip9GY.js";import{i as ja}from"../chunks/B_FgA42l.js";import{c as q}from"../chunks/Bf9nHHn7.js";function qa(c,t){const r=T(t,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d=[["path",{d:"M20 6 9 17l-5-5"}]];Oa(c,Ia({name:"check"},()=>r,{get iconNode(){return d},children:(i,a)=>{var n=w(),l=m(n);H(l,t,"default",{},null),s(i,n)},$$slots:{default:!0}}))}var Ga=f("<div><!></div>");function Ma(c,t){const r=T(t,["children","$$slots","$$events","$$legacy"]),d=T(r,["class"]);G(t,!1);let i=za(t,"class",8,void 0);ja();var a=Ga();Ya(a,l=>({class:l,...d}),[()=>(O(q),O(i()),ha(()=>q("text-sm [&_p]:leading-relaxed",i())))]);var n=k(a);H(n,t,"default",{},null),S(a),s(c,a),M()}const Da=(c,t,r,d,i,a,n)=>{var h;p(t)&&(p(t).disabled=!0,p(t).textContent="Sending...");let l=r==null?void 0:r.user.email;l&&d.auth.resetPasswordForEmail(l,{redirectTo:`${i().url.origin}/auth/callback?next=%2Fdashboard%2F${(h=a.value)==null?void 0:h.name}%2Fsettings%2Freset_password`}).then($=>{D(n,!$.error,!0)})};var Ha=f(`<div class="font-bold">Set Password By Email</div> <div>You use oAuth to sign in ("Sign in with Github" or similar). You can
            continue to access your account using only oAuth if you like!</div>`,1),Ja=f('<div class="font-bold">Change Password By Email</div>'),Ka=f("<!> <!>",1),La=f('<div class="flex flex-col gap-y-4"><!> <div> </div> <button>Send Set Password Email</button> <!></div>'),Qa=f('<h1 class="text-2xl font-bold mb-6">Change Password</h1> <!>',1);function bt(c,t){var E,F;G(t,!0);const[r,d]=Ca(),i=()=>ka(Sa,"$page",r);let{session:a,supabase:n}=t.data;const l=Va();let h=!!(a!=null&&a.user.user_metadata.hasPassword),$=!!["google","github"].includes(((F=(E=a==null?void 0:a.user)==null?void 0:E.app_metadata)==null?void 0:F.provider)??""),b=Y(void 0),P=Y(!1);var A=Qa();ba(u=>{_a.title="Change Password"});var J=v(m(A),2);{var K=u=>{Ta(u,{get data(){return t.data.form},get schema(){return Ba},title:"Change Password",editable:!0,saveButtonTitle:"Change Password",successTitle:"Password Changed",successBody:"On next sign in, use your new password.",formTarget:"/api?/updatePassword",fields:[{id:"newPassword1",label:"New Password",initialValue:"",inputType:"password"},{id:"newPassword2",label:"Confirm New Password",initialValue:"",inputType:"password"},{id:"currentPassword",label:"Current Password",initialValue:"",inputType:"password"}]})},L=u=>{var N=w(),Q=m(N);g(Q,()=>Fa,(R,U)=>{U(R,{children:(W,Ra)=>{var B=w(),X=m(B);g(X,()=>Ea,(Z,aa)=>{aa(Z,{children:(ta,Ua)=>{var y=La(),V=k(y);{var ea=e=>{var o=Ha();j(2),s(e,o)},sa=e=>{var o=Ja();s(e,o)};C(V,e=>{$?e(ea):e(sa,!1)})}var x=v(V,2),ra=k(x);S(x);var _=v(x,2);_.__click=[Da,b,a,n,i,l,P],xa(_,e=>D(b,e),()=>p(b));var oa=v(_,2);{var ia=e=>{var o=w(),na=m(o);g(na,()=>Aa,(la,da)=>{da(la,{children:(ua,Wa)=>{var I=Ka(),z=m(I);qa(z,{class:"h-4 w-4 "});var ca=v(z,2);g(ca,()=>Ma,(ma,pa)=>{pa(ma,{children:(fa,Xa)=>{j();var va=wa(`Sent email! Please check your inbox and use the link to set your
              password.`);s(fa,va)},$$slots:{default:!0}})}),s(ua,I)},$$slots:{default:!0}})}),s(e,o)};C(oa,e=>{p(P)&&e(ia)})}S(y),ga(e=>{var o;Pa(ra,`The button below will send you an email at ${((o=a==null?void 0:a.user)==null?void 0:o.email)??""} which
          will allow you to set your password.`),ya(_,1,`${e??""} ${p(P)?"hidden":""}`)},[()=>Na({variant:"outline"})]),s(ta,y)},$$slots:{default:!0}})}),s(W,B)},$$slots:{default:!0}})}),s(u,N)};C(J,u=>{h?u(K):u(L,!1)})}s(c,A),M(),d()}$a(["click"]);export{bt as component};
