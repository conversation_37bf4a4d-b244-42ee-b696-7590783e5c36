import"../chunks/CWj6FrbW.js";import{p as $t,f as k,t as lt,a as r,e as gt,$ as ht,s as o,c as p,b as I,d as $,o as j,n as N,q as F,h,i as A,k as E,r as g}from"../chunks/DDiqt3uM.js";import{h as xt,s as mt}from"../chunks/DWulv87v.js";import{i as dt}from"../chunks/2C89X9tI.js";import{c as s}from"../chunks/BCmD-YNt.js";import{a as bt}from"../chunks/D-ywOz1J.js";import{s as G}from"../chunks/C-ZVHnwW.js";import{a as Pt,b as H,s as J}from"../chunks/B82PTGnX.js";import{s as yt,z as Ct,C as K,F as L,a as M,b as Q}from"../chunks/BomMa-eL.js";import"../chunks/DhRTwODG.js";import"../chunks/L1Y_HHz6.js";import"../chunks/I1elDgVI.js";import{I as R}from"../chunks/Cn9N4mio.js";import"../chunks/ChutyBgo.js";import{p as wt}from"../chunks/CCJOWbOV.js";import{B as Ft}from"../chunks/BFbKPyUZ.js";var kt=k("<!> <!> <!>",1),Dt=k("<!> <!> <!>",1),St=k("<!> <!> <!>",1),zt=k('<p class="text-destructive text-sm font-bold text-center mt-3"> </p>'),Bt=k('<div class="text-center content-center max-w-lg mx-auto min-h-[100vh] pb-12 flex items-center place-content-center"><div class="flex flex-col w-64 lg:w-80"><div><h1 class="text-2xl font-bold mb-6">Create Profile</h1> <form class="grid gap-4" method="POST" action="/account/api?/updateProfile"><!> <!> <!> <!> <div class="mt-4"><!></div></form> <div class="text-sm mt-14"> <br/> <a class="underline" href="/account/sign_out">Sign out</a></div></div></div></div>');function Qt(it,Y){$t(Y,!0);const[q,vt]=Pt(),i=()=>J(S,"$formData",q),U=()=>J(_t,"$errors",q),V=()=>J(ft,"$delayed",q);let{session:O}=Y.data;const D=yt(Y.data.form,{validators:Ct(wt)}),{enhance:T,form:S,delayed:ft,errors:_t}=D;var W=Bt();xt(t=>{ht.title="Create Profile"});var X=p(W),Z=p(X),z=o(p(Z),2),tt=p(z);s(tt,()=>Q,(t,l)=>{l(t,{get form(){return D},name:"full_name",children:(m,B)=>{var d=I(),u=$(d);s(u,()=>K,(a,v)=>{v(a,{children:j,$$slots:{default:(x,b)=>{const P=E(()=>b.attrs);var f=kt(),_=$(f);s(_,()=>L,(e,n)=>{n(e,{children:(C,nt)=>{N();var w=F("Your Name");r(C,w)},$$slots:{default:!0}})});var c=o(_,2);R(c,G(()=>A(P),{get value(){return i().full_name},set value(e){H(S,h(i).full_name=e,h(i))}}));var y=o(c,2);s(y,()=>M,(e,n)=>{n(e,{})}),r(x,f)}}})}),r(m,d)},$$slots:{default:!0}})});var et=o(tt,2);s(et,()=>Q,(t,l)=>{l(t,{get form(){return D},name:"company_name",children:(m,B)=>{var d=I(),u=$(d);s(u,()=>K,(a,v)=>{v(a,{children:j,$$slots:{default:(x,b)=>{const P=E(()=>b.attrs);var f=Dt(),_=$(f);s(_,()=>L,(e,n)=>{n(e,{children:(C,nt)=>{N();var w=F("Company Name");r(C,w)},$$slots:{default:!0}})});var c=o(_,2);R(c,G(()=>A(P),{get value(){return i().company_name},set value(e){H(S,h(i).company_name=e,h(i))}}));var y=o(c,2);s(y,()=>M,(e,n)=>{n(e,{})}),r(x,f)}}})}),r(m,d)},$$slots:{default:!0}})});var rt=o(et,2);s(rt,()=>Q,(t,l)=>{l(t,{get form(){return D},name:"website",children:(m,B)=>{var d=I(),u=$(d);s(u,()=>K,(a,v)=>{v(a,{children:j,$$slots:{default:(x,b)=>{const P=E(()=>b.attrs);var f=St(),_=$(f);s(_,()=>L,(e,n)=>{n(e,{children:(C,nt)=>{N();var w=F("Company Website");r(C,w)},$$slots:{default:!0}})});var c=o(_,2);R(c,G(()=>A(P),{get value(){return i().website},set value(e){H(S,h(i).website=e,h(i))}}));var y=o(c,2);s(y,()=>M,(e,n)=>{n(e,{})}),r(x,f)}}})}),r(m,d)},$$slots:{default:!0}})});var at=o(rt,2);{var ct=t=>{var l=zt(),m=p(l,!0);g(l),lt(()=>mt(m,U()._errors[0])),r(t,l)};dt(at,t=>{U()._errors&&t(ct)})}var ot=o(at,2),ut=p(ot);Ft(ut,{type:"submit",class:"mt-3",get disabled(){return V()},children:(t,l)=>{var m=I(),B=$(m);{var d=a=>{var v=F("...");r(a,v)},u=a=>{var v=F("Create Profile");r(a,v)};dt(B,a=>{V()?a(d):a(u,!1)})}r(t,m)},$$slots:{default:!0}}),g(ot),g(z),bt(z,t=>T==null?void 0:T(t));var st=o(z,2),pt=p(st);N(3),g(st),g(Z),g(X),g(W),lt(()=>{var t;return mt(pt,`You are logged in as ${((t=O==null?void 0:O.user)==null?void 0:t.email)??"an anonymous user"??""}. `)}),r(it,W),gt(),vt()}export{Qt as component};
