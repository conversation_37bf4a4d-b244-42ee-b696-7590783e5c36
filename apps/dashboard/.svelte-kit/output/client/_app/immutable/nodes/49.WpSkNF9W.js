import"../chunks/CWj6FrbW.js";import{b as ye,d as V,a,p as Ee,f as m,h as A,aT as i,c as s,r as o,e as Ze,l as z,j as x,m as j,i as e,g as et,s as n,n as K,t as be,ak as Fe,q as _e,$ as ct,o as He,k as Ae,I as Ne}from"../chunks/DDiqt3uM.js";import{s as ze,e as ft,d as mt,h as ut}from"../chunks/DWulv87v.js";import{i as N}from"../chunks/2C89X9tI.js";import{c as q}from"../chunks/BCmD-YNt.js";import{a as pt}from"../chunks/D-ywOz1J.js";import{t as oe}from"../chunks/A4ulxp7Q.js";import{l as Se,s as qe,p as xe}from"../chunks/C-ZVHnwW.js";import{a as gt,b as Le,s as Ve}from"../chunks/B82PTGnX.js";import{d as tt,s as _t,z as xt,b as We,C as Ke,F as Ye,a as Ge}from"../chunks/BomMa-eL.js";import{a as ht,C as $t}from"../chunks/CfRLwaeF.js";import{C as bt}from"../chunks/Ckrcpd9Z.js";import"../chunks/DhRTwODG.js";import{s as Be}from"../chunks/iCEqKm8o.js";import{a as wt,c as yt,b as nt}from"../chunks/C36Ip9GY.js";import{i as Oe}from"../chunks/B_FgA42l.js";import{c as st}from"../chunks/Bf9nHHn7.js";import{C as kt,a as Pt}from"../chunks/Bls5ffmn.js";import"../chunks/L1Y_HHz6.js";import"../chunks/I1elDgVI.js";import{s as qt}from"../chunks/CCJOWbOV.js";import{I as Je}from"../chunks/Cn9N4mio.js";import"../chunks/ChutyBgo.js";import{a as we,f as it,b as rt}from"../chunks/DSm1r-pw.js";import{e as zt,i as jt}from"../chunks/OiKQa7Wx.js";import{s as ce}from"../chunks/DE2v8SHj.js";import{c as ot}from"../chunks/CD_pvLCz.js";import{I as at}from"../chunks/CkoRhfQ8.js";import{C as Re}from"../chunks/DR0c5pEj.js";import{M as Ct}from"../chunks/bEcYyObW.js";import{E as Qe}from"../chunks/Dxemv0ia.js";import{B as Mt}from"../chunks/BFbKPyUZ.js";function lt(R,d){const u=Se(d,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p=[["circle",{cx:"12",cy:"12",r:"10"}],["path",{d:"m15 9-6 6"}],["path",{d:"m9 9 6 6"}]];at(R,qe({name:"circle-x"},()=>u,{get iconNode(){return p},children:(l,f)=>{var y=ye(),r=V(y);Be(r,d,"default",{},null),a(l,y)},$$slots:{default:!0}}))}function Xe(R,d){const u=Se(d,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143"}],["path",{d:"m2 2 20 20"}]];at(R,qe({name:"eye-off"},()=>u,{get iconNode(){return p},children:(l,f)=>{var y=ye(),r=V(y);Be(r,d,"default",{},null),a(l,y)},$$slots:{default:!0}}))}function dt(R,d){const u=Se(d,["children","$$slots","$$events","$$legacy"]);/**
 * @license lucide-svelte v0.436.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p=[["circle",{cx:"12",cy:"12",r:"10"}],["path",{d:"M12 16v-4"}],["path",{d:"M12 8h.01"}]];at(R,qe({name:"info"},()=>u,{get iconNode(){return p},children:(l,f)=>{var y=ye(),r=V(y);Be(r,d,"default",{},null),a(l,y)},$$slots:{default:!0}}))}var It=m("<div><!></div>");function At(R,d){const u=Se(d,["children","$$slots","$$events","$$legacy"]),p=Se(u,["class"]);Ee(d,!1);let l=xe(d,"class",8,void 0);Oe();var f=It();wt(f,r=>({class:r,...p}),[()=>(i(st),i(l()),A(()=>st("flex items-center p-6 pt-0",l())))]);var y=s(f);Be(y,d,"default",{},null),o(f),a(R,f),Ze()}var Nt=m("<div></div>"),St=m('<div class="flex items-center justify-between svelte-1dxq2jv"><span>Password strength</span> <div class="flex items-center gap-1 svelte-1dxq2jv"><!> <span> </span></div></div>'),Et=m('<div class="space-y-1.5 p-3 bg-base-200/50 rounded-lg svelte-1dxq2jv"><div class="flex items-center gap-2 text-sm font-medium text-base-content/70 mb-2 svelte-1dxq2jv"><!> <span class="svelte-1dxq2jv">Password requirements</span></div> <div class="flex items-center gap-2 text-sm transition-all duration-200 svelte-1dxq2jv"><!> <span>8-16 characters</span></div> <div class="flex items-center gap-2 text-sm transition-all duration-200 svelte-1dxq2jv"><!> <span>One lowercase letter</span></div> <div class="flex items-center gap-2 text-sm transition-all duration-200 svelte-1dxq2jv"><!> <span>One uppercase letter</span></div> <div class="flex items-center gap-2 text-sm transition-all duration-200 svelte-1dxq2jv"><!> <span>One number</span></div></div>'),Zt=m('<div class="flex items-center gap-2 text-sm text-success svelte-1dxq2jv"><!> <span class="font-medium svelte-1dxq2jv">Strong password!</span></div>'),Dt=m('<div class="space-y-3 svelte-1dxq2jv"><div class="space-y-1 svelte-1dxq2jv"><div class="flex items-center justify-between text-sm svelte-1dxq2jv"><span>Character length</span> <span> </span></div> <div class="relative svelte-1dxq2jv"><progress max="100"></progress> <div class="absolute top-0 left-[50%] w-px h-2 bg-base-content/20 svelte-1dxq2jv"></div> <div class="absolute -bottom-4 left-[50%] -translate-x-1/2 text-xs text-base-content/50 svelte-1dxq2jv">8</div></div></div> <!> <!> <!></div>');function Ft(R,d){Ee(d,!1);const u=j(),p=j(),l=j(),f=j(),y=j(),r=j(),g=j(),_=j(),ne=j(),ve=j(),fe=j(),Y=j();let t=xe(d,"password",8,""),J=xe(d,"showRequirements",8,!0);function Q(v,c=!1){return c?tt:v?Re:lt}function ie(v,c=!1){return c?"text-base-content/30":v?"text-success":"text-base-content/30"}z(()=>i(t()),()=>{x(u,t().length>=8&&t().length<=16)}),z(()=>i(t()),()=>{x(p,/[a-z]/.test(t()))}),z(()=>i(t()),()=>{x(l,/[A-Z]/.test(t()))}),z(()=>i(t()),()=>{x(f,/\d/.test(t()))}),z(()=>(e(u),e(p),e(l),e(f)),()=>{x(y,[e(u),e(p),e(l),e(f)])}),z(()=>e(y),()=>{x(r,e(y).filter(Boolean).length)}),z(()=>e(r),()=>{x(g,e(r)===4)}),z(()=>e(r),()=>{x(_,e(r)===0?"empty":e(r)===1?"weak":e(r)===2?"fair":e(r)===3?"good":"strong")}),z(()=>e(_),()=>{x(ne,e(_)==="empty"?"text-base-content/50":e(_)==="weak"?"text-error":e(_)==="fair"?"text-warning":e(_)==="good"?"text-info":"text-success")}),z(()=>e(_),()=>{x(ve,e(_)==="empty"?"progress-base-200":e(_)==="weak"?"progress-error":e(_)==="fair"?"progress-warning":e(_)==="good"?"progress-info":"progress-success")}),z(()=>i(t()),()=>{x(fe,Math.min(t().length/16*100,100))}),z(()=>i(t()),()=>{x(Y,t().length===0?"text-base-content/50":t().length<8?"text-error":t().length<=16?"text-success":"text-error")}),et(),Oe();var S=Dt(),P=s(S),X=s(P),le=s(X),G=n(le,2),T=s(G);o(G),o(X);var U=n(X,2),ee=s(U);K(4),o(U),o(P);var b=n(P,2);{var h=v=>{var c=St(),H=s(c),L=n(H,2),B=s(L);zt(B,0,()=>Array(4),jt,(ue,C,O)=>{var E=Nt();let re;be(W=>{re=ce(E,1,`h-1 w-8 rounded-full transition-all duration-300 ${O<e(r)?"bg-current":"bg-base-300"}`,"svelte-1dxq2jv",re,W),nt(E,`color: var(--${e(_)==="weak"?"er":e(_)==="fair"?"wa":e(_)==="good"?"in":"su"})`)},[()=>({"opacity-100":O<e(r),"opacity-30":O>=e(r)})]),a(ue,E)});var te=n(B,2),me=s(te,!0);o(te),o(L),o(c),be(()=>{ce(H,1,`text-sm font-medium ${e(ne)??""} transition-colors duration-200`,"svelte-1dxq2jv"),ce(te,1,`ml-2 text-sm ${e(ne)??""} font-medium capitalize transition-colors duration-200`,"svelte-1dxq2jv"),ze(me,e(_))}),oe(3,c,()=>we,()=>({duration:200})),a(v,c)};N(b,v=>{i(t()),A(()=>t().length>0)&&v(h)})}var k=n(b,2);{var w=v=>{var c=Et(),H=s(c),L=s(H);dt(L,{size:14}),K(2),o(H);var B=n(H,2),te=s(B);{let M=Fe(()=>(e(u),i(t()),A(()=>ie(e(u),t().length===0))));q(te,()=>Q(e(u),t().length===0),(Z,se)=>{se(Z,{size:16,get class(){return e(M)}})})}var me=n(te,2);o(B);var ue=n(B,2),C=s(ue);{let M=Fe(()=>(e(p),i(t()),A(()=>ie(e(p),t().length===0))));q(C,()=>Q(e(p),t().length===0),(Z,se)=>{se(Z,{size:16,get class(){return e(M)}})})}var O=n(C,2);o(ue);var E=n(ue,2),re=s(E);{let M=Fe(()=>(e(l),i(t()),A(()=>ie(e(l),t().length===0))));q(re,()=>Q(e(l),t().length===0),(Z,se)=>{se(Z,{size:16,get class(){return e(M)}})})}var W=n(re,2);o(E);var ae=n(E,2),F=s(ae);{let M=Fe(()=>(e(f),i(t()),A(()=>ie(e(f),t().length===0))));q(F,()=>Q(e(f),t().length===0),(Z,se)=>{se(Z,{size:16,get class(){return e(M)}})})}var de=n(F,2);o(ae),o(c),be(()=>{ce(me,1,`${e(u)?"text-success":"text-base-content/70"} transition-colors duration-200`,"svelte-1dxq2jv"),ce(O,1,`${e(p)?"text-success":"text-base-content/70"} transition-colors duration-200`,"svelte-1dxq2jv"),ce(W,1,`${e(l)?"text-success":"text-base-content/70"} transition-colors duration-200`,"svelte-1dxq2jv"),ce(de,1,`${e(f)?"text-success":"text-base-content/70"} transition-colors duration-200`,"svelte-1dxq2jv")}),oe(3,c,()=>it,()=>({y:-10,duration:300,easing:ot})),a(v,c)};N(k,v=>{i(J()),i(t()),A(()=>J()&&(t().length>0||J()==="always"))&&v(w)})}var D=n(k,2);{var $=v=>{var c=Zt(),H=s(c);Re(H,{size:16}),K(2),o(c),oe(3,c,()=>rt,()=>({duration:300,easing:ot})),a(v,c)};N(D,v=>{e(g)&&v($)})}o(S),be(()=>{ce(le,1,`font-medium ${e(Y)??""} transition-colors duration-200`,"svelte-1dxq2jv"),ce(G,1,`${e(Y)??""} tabular-nums transition-colors duration-200`,"svelte-1dxq2jv"),ze(T,`${i(t()),A(()=>t().length)??""}/16`),ce(ee,1,`progress ${e(ve)??""} h-2 transition-all duration-300`,"svelte-1dxq2jv"),yt(ee,e(fe))}),a(R,S),Ze()}var Vt=m('<div class="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm font-medium dark:bg-green-950/20 dark:border-green-800/30 dark:text-green-400"><!> <span>Valid email format</span></div>'),Rt=m('<div class="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-700 text-sm font-medium dark:bg-yellow-950/20 dark:border-yellow-800/30 dark:text-yellow-400"><!> <span><!></span></div>'),Bt=m('<div class="flex items-center gap-2 p-2 bg-gray-50 border border-gray-200 rounded-lg text-gray-600 text-sm dark:bg-gray-950/20 dark:border-gray-800/30 dark:text-gray-400"><!> <span>Enter your email address</span></div>'),Ot=m('<div class="flex items-center gap-2"><!></div>'),Tt=m(`<div class="flex items-start gap-2 p-3 bg-info/10 border border-info/20 rounded-lg text-sm"><!> <div class="space-y-1"><p class="font-medium text-info">Email confirmation required</p> <p class="text-base-content/70">We'll send you a confirmation email to verify your account before you
          can sign in.</p></div></div>`),Ut=m('<span>Did you mean <span class="font-medium"> </span>?</span>'),Ht=m('<div class="text-xs text-base-content/60 ml-6"><!></div>'),Lt=m('<div class="space-y-2"><!> <!> <!></div>');function Wt(R,d){Ee(d,!1);const u=j(),p=j(),l=j(),f=j(),y=j();let r=xe(d,"email",8,""),g=xe(d,"showConfirmationNotice",8,!0),_=xe(d,"touched",8,!1);const ne=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;z(()=>i(r()),()=>{x(u,ne.test(r()))}),z(()=>i(r()),()=>{x(p,r().includes("@"))}),z(()=>(i(_()),i(r())),()=>{x(l,_()&&r().length>0)}),z(()=>i(r()),()=>{x(f,r().includes("@")?r().split("@")[1]:"")}),z(()=>e(f),()=>{x(y,e(f)&&e(f).includes(".")&&e(f).length>3)}),et(),Oe();var ve=Lt(),fe=s(ve);{var Y=S=>{var P=Ot(),X=s(P);{var le=T=>{var U=Vt(),ee=s(U);Re(ee,{size:16,class:"flex-shrink-0"}),K(2),o(U),oe(3,U,()=>rt,()=>({duration:400,start:.8})),a(T,U)},G=T=>{var U=ye(),ee=V(U);{var b=k=>{var w=Rt(),D=s(w);tt(D,{size:16,class:"flex-shrink-0"});var $=n(D,2),v=s($);{var c=L=>{var B=_e("Please complete the email address");a(L,B)},H=L=>{var B=_e("Please check the email format");a(L,B)};N(v,L=>{e(y)?L(H,!1):L(c)})}o($),o(w),oe(3,w,()=>we,()=>({duration:300})),a(k,w)},h=k=>{var w=Bt(),D=s(w);Ct(D,{size:16,class:"flex-shrink-0"}),K(2),o(w),oe(3,w,()=>we,()=>({duration:300})),a(k,w)};N(ee,k=>{e(p)?k(b):k(h,!1)},!0)}a(T,U)};N(X,T=>{e(u)?T(le):T(G,!1)})}o(P),oe(3,P,()=>we,()=>({duration:300})),a(S,P)};N(fe,S=>{e(l)&&S(Y)})}var t=n(fe,2);{var J=S=>{var P=Tt(),X=s(P);dt(X,{size:16,class:"text-info mt-0.5 flex-shrink-0"}),K(2),o(P),oe(3,P,()=>we,()=>({duration:200})),a(S,P)};N(t,S=>{i(g()),e(u),i(_()),i(r()),A(()=>g()&&(e(u)||!_()&&r().length===0))&&S(J)})}var Q=n(t,2);{var ie=S=>{var P=Ht(),X=s(P);{var le=G=>{var T=Ut(),U=n(s(T)),ee=s(U);o(U),K(),o(T),be(()=>ze(ee,`${e(f)??""}.com`)),a(G,T)};N(X,G=>{(e(f)==="gmail"||e(f)==="yahoo"||e(f)==="hotmail")&&G(le)})}o(P),a(S,P)};N(Q,S=>{e(l)&&e(p)&&!e(u)&&S(ie)})}o(ve),a(R,ve),Ze()}var Kt=m('<div class="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-lg text-green-700 font-medium dark:bg-green-950/20 dark:border-green-800/30 dark:text-green-400"><!> <span>Passwords match!</span></div>'),Yt=m('<div class="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-700 font-medium dark:bg-yellow-950/20 dark:border-yellow-800/30 dark:text-yellow-400"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" class="flex-shrink-0"><circle cx="12" cy="12" r="10"></circle><path d="M12 6v6l4 2"></path></svg> <span>Keep typing...</span></div>'),Gt=m(`<div class="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400"><!> <span>Passwords don't match</span></div>`),Jt=m("<!> <span>Hide</span>",1),Qt=m("<!> <span>Show</span>",1),Xt=m('<button type="button" class="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors p-1 rounded"><!></button>'),er=m('<div class="space-y-1"><div class="flex justify-between text-xs text-base-content/60"><span>Match progress</span> <span> </span></div> <div class="relative h-1.5 bg-base-300 rounded-full overflow-hidden"><div class="absolute inset-y-0 left-0 bg-warning rounded-full transition-all duration-300"></div></div></div>'),tr=m('<p class="text-xs text-base-content/60"> </p>'),rr=m('<div class="space-y-3 p-3 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-950/20 dark:border-gray-800/30"><div class="flex items-center justify-between"><div class="flex items-center gap-2 text-sm"><!></div> <!></div> <!> <!></div>');function ar(R,d){Ee(d,!1);const u=j(),p=j(),l=j(),f=j(),y=j();let r=xe(d,"password",8,""),g=xe(d,"confirmPassword",8,""),_=xe(d,"showPassword",12,!1);z(()=>i(g()),()=>{x(u,g().length>0)}),z(()=>(i(r()),i(g()),e(u)),()=>{x(p,r()===g()&&e(u))}),z(()=>(i(r()),i(g()),e(u),e(p)),()=>{x(l,r().startsWith(g())&&e(u)&&!e(p))}),z(()=>(i(r()),i(g())),()=>{x(f,(()=>{let Y=0;for(let t=0;t<Math.min(r().length,g().length)&&r()[t]===g()[t];t++)Y++;return Y})())}),z(()=>(i(r()),e(f)),()=>{x(y,r().length>0?e(f)/r().length*100:0)}),et(),Oe();var ne=ye(),ve=V(ne);{var fe=Y=>{var t=rr(),J=s(t),Q=s(J),ie=s(Q);{var S=b=>{var h=Kt(),k=s(h);Re(k,{size:16,class:"flex-shrink-0"}),K(2),o(h),oe(3,h,()=>rt,()=>({duration:400,start:.8})),a(b,h)},P=b=>{var h=ye(),k=V(h);{var w=$=>{var v=Yt();oe(3,v,()=>we,()=>({duration:300})),a($,v)},D=$=>{var v=Gt(),c=s(v);lt(c,{size:16,class:"flex-shrink-0"}),K(2),o(v),oe(3,v,()=>we,()=>({duration:300})),a($,v)};N(k,$=>{e(l)?$(w):$(D,!1)},!0)}a(b,h)};N(ie,b=>{e(p)?b(S):b(P,!1)})}o(Q);var X=n(Q,2);{var le=b=>{var h=Xt(),k=s(h);{var w=$=>{var v=Jt(),c=V(v);Xe(c,{size:14}),K(2),a($,v)},D=$=>{var v=Qt(),c=V(v);Qe(c,{size:14}),K(2),a($,v)};N(k,$=>{_()?$(w):$(D,!1)})}o(h),ft("click",h,()=>_(!_())),a(b,h)};N(X,b=>{e(p),i(g()),A(()=>!e(p)&&g().length>3)&&b(le)})}o(J);var G=n(J,2);{var T=b=>{var h=er(),k=s(h),w=n(s(k),2),D=s(w);o(w),o(k);var $=n(k,2),v=s($);o($),o(h),be(()=>{ze(D,`${e(f)??""}/${i(r()),A(()=>r().length)??""} characters`),nt(v,`width: ${e(y)??""}%`)}),a(b,h)};N(G,b=>{e(p),i(g()),A(()=>!e(p)&&g().length>0)&&b(T)})}var U=n(G,2);{var ee=b=>{var h=tr(),k=s(h);o(h),be(()=>ze(k,`Confirmation is ${i(g()),i(r()),A(()=>g().length-r().length)??""} character${i(g()),i(r()),A(()=>g().length-r().length!==1?"s":"")??""} longer`)),a(b,h)};N(U,b=>{e(p),i(g()),i(r()),A(()=>!e(p)&&g().length>r().length)&&b(ee)})}o(t),oe(3,t,()=>we,()=>({duration:300})),a(Y,t)};N(ve,Y=>{e(u)&&Y(fe)})}a(R,ne),Ze()}var sr=m("<!> <!>",1),or=m("<!> <!>",1),nr=m("<!> <!> <!>",1),ir=(R,d)=>x(d,!e(d)),lr=m('<!> <div class="relative"><!> <button type="button" class="absolute right-3 top-1/2 -translate-y-1/2 text-base-content/60 hover:text-base-content transition-colors"><!></button></div>',1),dr=m("<!> <!> <!>",1),vr=(R,d)=>x(d,!e(d)),cr=m('<!> <div class="relative"><!> <button type="button" class="absolute right-3 top-1/2 -translate-y-1/2 text-base-content/60 hover:text-base-content transition-colors"><!></button></div>',1),fr=m("<!> <!> <!>",1),mr=m('<div class="flex items-center gap-2 p-3 mt-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400"><!> <span> </span></div>'),ur=m('<form method="post" class="grid gap-4"><!> <!> <!> <!> <!></form>'),pr=m('<div class="mt-4 mb-2">Have an account? <a class="underline" href="/login/sign_in">Sign in</a>.</div>'),gr=m("<!> <!> <!>",1);function Yr(R,d){Ee(d,!0);const[u,p]=gt(),l=()=>Ve(_,"$formData",u),f=()=>Ve(Y,"$constraints",u),y=()=>Ve(fe,"$errors",u),r=()=>Ve(ve,"$delayed",u),g=_t(d.data.form,{validators:xt(qt)}),{form:_,enhance:ne,delayed:ve,errors:fe,constraints:Y}=g;let t=Ne(!1),J=Ne(!1),Q=Ne(!1),ie=Ne(!1),S=Ne(!1);var P=ye();ut(le=>{ct.title="Sign up"});var X=V(P);q(X,()=>$t,(le,G)=>{G(le,{class:"mt-6",children:(T,U)=>{var ee=gr(),b=V(ee);q(b,()=>kt,(w,D)=>{D(w,{children:($,v)=>{var c=sr(),H=V(c);q(H,()=>Pt,(B,te)=>{te(B,{class:"text-2xl font-bold text-center",children:(me,ue)=>{K();var C=_e("Sign Up");a(me,C)},$$slots:{default:!0}})});var L=n(H,2);q(L,()=>bt,(B,te)=>{te(B,{children:(me,ue)=>{K();var C=_e(`Create your account to get started. You'll receive a confirmation link
      via email.`);a(me,C)},$$slots:{default:!0}})}),a($,c)},$$slots:{default:!0}})});var h=n(b,2);q(h,()=>ht,(w,D)=>{D(w,{children:($,v)=>{var c=ur(),H=s(c);q(H,()=>We,(C,O)=>{O(C,{get form(){return g},name:"email",children:(E,re)=>{var W=nr(),ae=V(W);q(ae,()=>Ke,(M,Z)=>{Z(M,{children:He,$$slots:{default:(se,je)=>{const Ce=Ae(()=>je.attrs);var he=or(),$e=V(he);q($e,()=>Ye,(pe,ge)=>{ge(pe,{children:(Me,De)=>{K();var Ie=_e("Email");a(Me,Ie)},$$slots:{default:!0}})});var ke=n($e,2);Je(ke,qe({onblur:()=>x(Q,!0)},()=>e(Ce),()=>f().email,{get value(){return l().email},set value(pe){Le(_,A(l).email=pe,A(l))}})),a(se,he)}}})});var F=n(ae,2);Wt(F,{get email(){return l().email},get touched(){return e(Q)},showConfirmationNotice:!0});var de=n(F,2);q(de,()=>Ge,(M,Z)=>{Z(M,{})}),a(E,W)},$$slots:{default:!0}})});var L=n(H,2);q(L,()=>We,(C,O)=>{O(C,{get form(){return g},name:"password",children:(E,re)=>{var W=dr(),ae=V(W);q(ae,()=>Ke,(M,Z)=>{Z(M,{children:He,$$slots:{default:(se,je)=>{const Ce=Ae(()=>je.attrs);var he=lr(),$e=V(he);q($e,()=>Ye,(I,Pe)=>{Pe(I,{children:(Te,vt)=>{K();var Ue=_e("Password");a(Te,Ue)},$$slots:{default:!0}})});var ke=n($e,2),pe=s(ke);{let I=Ae(()=>e(t)?"text":"password");Je(pe,qe({get type(){return e(I)},onfocus:()=>x(ie,!0)},()=>e(Ce),()=>f().password,{get value(){return l().password},set value(Pe){Le(_,A(l).password=Pe,A(l))}}))}var ge=n(pe,2);ge.__click=[ir,t];var Me=s(ge);{var De=I=>{Xe(I,{size:18})},Ie=I=>{Qe(I,{size:18})};N(Me,I=>{e(t)?I(De):I(Ie,!1)})}o(ge),o(ke),a(se,he)}}})});var F=n(ae,2);Ft(F,{get password(){return l().password},get showRequirements(){return e(ie)}});var de=n(F,2);q(de,()=>Ge,(M,Z)=>{Z(M,{})}),a(E,W)},$$slots:{default:!0}})});var B=n(L,2);q(B,()=>We,(C,O)=>{O(C,{get form(){return g},name:"confirmPassword",children:(E,re)=>{var W=fr(),ae=V(W);q(ae,()=>Ke,(M,Z)=>{Z(M,{children:He,$$slots:{default:(se,je)=>{const Ce=Ae(()=>je.attrs);var he=cr(),$e=V(he);q($e,()=>Ye,(I,Pe)=>{Pe(I,{children:(Te,vt)=>{K();var Ue=_e("Confirm Password");a(Te,Ue)},$$slots:{default:!0}})});var ke=n($e,2),pe=s(ke);{let I=Ae(()=>e(J)||e(t)?"text":"password");Je(pe,qe({get type(){return e(I)},onfocus:()=>x(S,!0)},()=>e(Ce),()=>f().confirmPassword,{get value(){return l().confirmPassword},set value(Pe){Le(_,A(l).confirmPassword=Pe,A(l))}}))}var ge=n(pe,2);ge.__click=[vr,J];var Me=s(ge);{var De=I=>{Xe(I,{size:18})},Ie=I=>{Qe(I,{size:18})};N(Me,I=>{e(J)||e(t)?I(De):I(Ie,!1)})}o(ge),o(ke),a(se,he)}}})});var F=n(ae,2);ar(F,{get password(){return l().password},get confirmPassword(){return l().confirmPassword},get showPassword(){return e(t)},set showPassword(M){x(t,M,!0)}});var de=n(F,2);q(de,()=>Ge,(M,Z)=>{Z(M,{})}),a(E,W)},$$slots:{default:!0}})});var te=n(B,2);{var me=C=>{var O=mr(),E=s(O);tt(E,{size:16,class:"flex-shrink-0"});var re=n(E,2),W=s(re,!0);o(re),o(O),be(()=>ze(W,y()._errors[0])),oe(3,O,()=>it,()=>({y:-10,duration:300})),a(C,O)};N(te,C=>{y()._errors&&C(me)})}var ue=n(te,2);Mt(ue,{type:"submit",get disabled(){return r()},class:"w-full",children:(C,O)=>{var E=ye(),re=V(E);{var W=F=>{var de=_e("...");a(F,de)},ae=F=>{var de=_e("Sign Up");a(F,de)};N(re,F=>{r()?F(W):F(ae,!1)})}a(C,E)},$$slots:{default:!0}}),o(c),pt(c,C=>ne==null?void 0:ne(C)),a($,c)},$$slots:{default:!0}})});var k=n(h,2);q(k,()=>At,(w,D)=>{D(w,{children:($,v)=>{var c=pr();a($,c)},$$slots:{default:!0}})}),a(T,ee)},$$slots:{default:!0}})}),a(R,P),Ze(),p()}mt(["click"]);export{Yr as component};
