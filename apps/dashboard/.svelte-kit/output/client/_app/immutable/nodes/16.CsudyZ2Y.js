import"../chunks/CWj6FrbW.js";import{f as m,s as c,d,a as g,$ as p,i as s,k as r}from"../chunks/DDiqt3uM.js";import{h as f}from"../chunks/DWulv87v.js";import{S as h}from"../chunks/XAW1B1R8.js";var v=m('<h1 class="text-2xl font-bold mb-6">Email Subscription</h1> <!>',1);function x(o,u){let{profile:e}=u.data,t=e==null?void 0:e.unsubscribed;var i=v();f(a=>{p.title="Change Email Subscription"});var n=c(d(i),2);{let a=r(()=>t?"You are currently unsubscribed from emails":"You are currently subscribed to emails"),b=r(()=>t?"Re-subscribe":"Unsubscribe"),l=r(()=>t?"You have been re-subscribed to emails":"You have been unsubscribed from emails");h(n,{editable:!0,title:"Subscription",get message(){return s(a)},get saveButtonTitle(){return s(b)},get successBody(){return s(l)},formTarget:"/api?/toggleEmailSubscription",fields:[]})}g(o,i)}export{x as component};
