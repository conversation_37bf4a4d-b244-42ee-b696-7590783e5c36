// src/hooks.server.ts
import { env } from "$env/dynamic/private"
import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY } from "$env/static/public"
import { SUPABASE_SERVICE_ROLE_KEY } from "$env/static/private"
import { type Handle } from "@sveltejs/kit"
import { sequence } from "@sveltejs/kit/hooks"
import { createServerClient } from "@supabase/ssr"
import { createClient } from "@supabase/supabase-js"

const supabase: Handle = async ({ event, resolve }) => {
  // Use static environment variables (loaded at build time) or dynamic ones
  const supabaseUrl = PUBLIC_SUPABASE_URL || env.PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = PUBLIC_SUPABASE_ANON_KEY || env.PUBLIC_SUPABASE_ANON_KEY;
  const serviceRoleKey = SUPABASE_SERVICE_ROLE_KEY || env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Missing Supabase environment variables:', {
      hasUrl: !!supabaseUrl,
      hasAnonKey: !!supabaseAnonKey,
      staticUrl: !!PUBLIC_SUPABASE_URL,
      dynamicUrl: !!env.PUBLIC_SUPABASE_URL
    });
    event.locals.supabase = null as any;
    event.locals.supabaseServiceRole = null as any;
    return resolve(event);
  }

  event.locals.supabase = createServerClient(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        getAll() {
          return event.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) =>
            event.cookies.set(name, value, { ...options, path: "/" }),
          )
        },
      },
    },
  )

  event.locals.supabaseServiceRole = createClient(
    supabaseUrl,
    serviceRoleKey,
    {
      auth: { persistSession: false },
    },
  )

  return resolve(event, {
    filterSerializedResponseHeaders(name) {
      return name === "content-range"
    },
  })
}

const auth: Handle = async ({ event, resolve }) => {
  /**
   * Unlike `supabase.auth.getSession()`, which returns the session _without_
   * validating the JWT, this function also calls `getUser()` to validate the
   * JWT before returning the session.
   */
  event.locals.safeGetSession = async () => {
    // If Supabase is not initialized, return null session
    if (!event.locals.supabase) {
      return { session: null, user: null }
    }

    const {
      data: { session },
    } = await event.locals.supabase.auth.getSession()
    if (!session) {
      const {
        data: { session, user },
      } = await event.locals.supabase.auth.signInAnonymously()

      return { session, user }
    } else {
      return { session, user: session.user }
    }
  }

  event.locals.auth = await event.locals.safeGetSession()

  return resolve(event)
}

const environment: Handle = async ({ event, resolve }) => {
  // Only query if user ID exists, user is not anonymous, and Supabase is initialized
  if (event.locals.supabase && event.locals.auth.user?.id && !event.locals.auth.user?.is_anonymous) {
    const { data } = await event.locals.supabase
      .from("environments_profiles")
      .select("env:environments (*)")
      .eq("profile_id", event.locals.auth.user.id)

    event.locals.environment = data?.[0] ? data[0].env : null
  } else {
    event.locals.environment = null
  }

  return resolve(event)
}

export const handle = sequence(supabase, auth, environment)
