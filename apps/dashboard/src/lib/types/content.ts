// Base content interface
export interface BaseContent {
  frontmatter: Record<string, any>;
  content: string;
  slug?: string;
}

// Hero section content
export interface HeroContent extends BaseContent {
  frontmatter: {
    badge: string;
    title: string;
    titleHighlight: string;
    ctaPrimary: string;
    ctaSecondary?: string;
    trustBadge: string;
    trustCount: number;
  };
}

// Section content (problem, approach, results, stories, team, cta)
export interface SectionContent extends BaseContent {
  frontmatter: {
    title: string;
    titleHighlight?: string;
    titleSuffix?: string;
    subtitle: string;
    ctaPrimary?: string;
    ctaSecondary?: string;
    footer?: string;
  };
}

// Team member content
export interface TeamMemberContent extends BaseContent {
  frontmatter: {
    name: string;
    role: string;
    order: number;
    expertise: string[];
    avatar?: string;
  };
}

// Case study content
export interface CaseStudyContent extends BaseContent {
  frontmatter: {
    company: string;
    industry: string;
    logo: string;
    featured: boolean;
    order: number;
    results: Array<{
      metric: string;
      label: string;
    }>;
    author: string;
    role: string;
  };
}

// Features content
export interface FeatureContent extends BaseContent {
  frontmatter: {
    problems?: Array<{
      icon: string;
      title: string;
      description: string;
      color: string;
    }>;
    features?: Array<{
      icon: string;
      title: string;
      description: string;
    }>;
    services?: Array<{
      icon: string;
      title: string;
      description: string;
      color: string;
    }>;
    metrics?: Array<{
      metric: string;
      label: string;
      icon: string;
      color: string;
    }>;
    strategicBox?: {
      title: string;
      description: string;
      checklistItems: string[];
    };
  };
}

// Content loader return types
export interface ContentLoaderResult<T extends BaseContent> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface ContentCollectionResult<T extends BaseContent> {
  success: boolean;
  data?: T[];
  error?: string;
}

// Agent content
export interface AgentContent extends BaseContent {
  frontmatter: {
    name: string;
    order: number;
    videoPlaceholder: string;
  };
}

// Content paths for organization
export const CONTENT_PATHS = {
  HOME: 'home',
  TEAM: 'team',
  CASE_STUDIES: 'case-studies',
  FEATURES: 'features',
  AGENTS: 'agents',
  BLOG: 'blog',
  PAGES: 'pages'
} as const;

export type ContentPath = typeof CONTENT_PATHS[keyof typeof CONTENT_PATHS];