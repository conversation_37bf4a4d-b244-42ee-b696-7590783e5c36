import { z } from 'zod'
import { createTool } from '@mastra/core'
import { env } from '$env/dynamic/private'

const domainIntersectionSchema = z.object({
  target1: z.string().describe('First domain to compare (e.g., example.com)'),
  target2: z.string().describe('Second domain to compare (e.g., competitor.com)'),
  location_code: z.number().optional().default(2840).describe('Location code for search data (default: 2840 for United States)'),
  language_code: z.string().optional().default('en').describe('Language code for search data (default: en for English)'),
  include_serp_info: z.boolean().optional().default(true).describe('Include detailed SERP information'),
  limit: z.number().optional().default(1000).describe('Maximum number of results to return')
})

export const domainIntersectionTool = createTool({
  id: 'get_domain_intersection',
  description: 'Find keywords where two domains both rank in Google SERPs using DataForSEO Domain Intersection API',
  inputSchema: domainIntersectionSchema,
  execute: async (context) => {
    const { target1, target2, location_code, language_code, include_serp_info, limit } = context.context
    
    const apiLogin = env.DATAFORSEO_LOGIN
    const apiPassword = env.DATAFORSEO_PASSWORD
    
    // Check for API credentials
    
    if (!apiLogin || !apiPassword) {
      console.log('DataForSEO credentials not configured - using mock data');
      // Return mock data for testing
      const mockResults = []
      const mockKeywords = [
        'project management software', 'task tracking tools', 'team collaboration',
        'agile project management', 'kanban software', 'scrum tools',
        'time tracking software', 'resource planning', 'gantt chart software'
      ]
      
      for (const keyword of mockKeywords) {
        mockResults.push({
          keyword,
          search_volume: Math.floor(Math.random() * 50000) + 1000,
          keyword_difficulty: Math.floor(Math.random() * 80) + 10,
          cpc: Math.random() * 10 + 0.5,
          competition: ['LOW', 'MEDIUM', 'HIGH'][Math.floor(Math.random() * 3)],
          target1_position: Math.floor(Math.random() * 50) + 1,
          target2_position: Math.floor(Math.random() * 50) + 1,
          target1_url: `https://${target1}/page-${Math.random().toString(36).substr(2, 9)}`,
          target2_url: `https://${target2}/page-${Math.random().toString(36).substr(2, 9)}`,
          etv: Math.random() * 1000 + 50
        })
      }
      
      // Sort by search volume
      mockResults.sort((a, b) => b.search_volume - a.search_volume)
      
      return {
        success: true,
        target1,
        target2,
        total_keywords: mockResults.length,
        results: mockResults.slice(0, limit),
        note: 'Mock data returned - DataForSEO credentials not configured',
        debug: {
          reason: 'missing_credentials',
          apiLogin: !apiLogin ? 'MISSING' : 'OK',
          apiPassword: !apiPassword ? 'MISSING' : 'OK',
          timestamp: new Date().toISOString()
        }
      }
    }

    // Create base64 encoded credentials
    const credentials = Buffer.from(`${apiLogin}:${apiPassword}`).toString('base64')
    
    try {
      const requestData = [{
        target1,
        target2,
        location_code,
        language_code,
        include_serp_info,
        limit: Math.min(limit, 25) // Limit to max 25 keywords to prevent context overflow
      }]

      console.log('Making DataForSEO domain intersection request for:', target1, 'vs', target2);
      
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout
      
      const response = await fetch('https://api.dataforseo.com/v3/dataforseo_labs/google/domain_intersection/live', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`DataForSEO API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('DataForSEO domain intersection response status:', data.status_code, data.status_message)
      
      if (data.status_code !== 20000) {
        console.error('DataForSEO API error response:', data)
        throw new Error(`DataForSEO API error: ${data.status_message}`)
      }

      // Extract and format the results
      const results = []
      if (data.tasks && data.tasks[0] && data.tasks[0].result && data.tasks[0].result[0] && data.tasks[0].result[0].items) {
        console.log('Processing domain intersection results')
        for (const item of data.tasks[0].result[0].items) {
          // Get SERP data for both targets
          const target1Data = item.intersection_result?.['1']?.organic || {}
          const target2Data = item.intersection_result?.['2']?.organic || {}
          
          results.push({
            keyword: item.keyword_data?.keyword || item.keyword,
            search_volume: item.keyword_data?.keyword_info?.search_volume || 0,
            keyword_difficulty: item.keyword_data?.keyword_info?.competition || 0,
            cpc: item.keyword_data?.keyword_info?.cpc || 0,
            competition: item.keyword_data?.keyword_info?.competition_level || 'UNKNOWN',
            target1_position: target1Data.position || null,
            target2_position: target2Data.position || null,
            target1_url: target1Data.url || null,
            target2_url: target2Data.url || null,
            etv: target1Data.etv || 0,
            monthly_searches: item.keyword_data?.keyword_info?.monthly_searches || []
          })
        }
      } else {
        console.warn('No domain intersection results found in response')
      }

      // Sort by search volume descending
      results.sort((a, b) => b.search_volume - a.search_volume)
      
      // Limit to top 10 results to prevent context overflow
      const limitedResults = results.slice(0, 10);

      console.log('Domain intersection completed:', limitedResults.length, 'results');
      return {
        success: true,
        target1,
        target2,
        total_keywords: limitedResults.length,
        results: limitedResults,
        cost: data.cost || 0,
        debug: {
          reason: 'real_api_data',
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('Domain intersection API error:', error);
      throw new Error(`Failed to get domain intersection data: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})