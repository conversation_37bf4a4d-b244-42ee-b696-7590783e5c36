import { z } from 'zod'
import { createTool } from '@mastra/core'
import { env } from '$env/dynamic/private'

const relatedKeywordsSchema = z.object({
  keywords: z.array(z.string()).min(1).max(20).describe('Array of seed keywords to find related keywords for (1-20 keywords)'),
  location_code: z.number().optional().default(2840).describe('Location code for search data (default: 2840 for United States)'),
  language_code: z.string().optional().default('en').describe('Language code for search data (default: en for English)'),
  include_seed_keywords: z.boolean().optional().default(false).describe('Include seed keywords in results'),
  limit: z.number().optional().default(700).describe('Maximum number of keyword suggestions to return')
})

export const relatedKeywordsTool = createTool({
  id: 'get_related_keywords',
  description: 'Discover related and long-tail keywords based on seed keywords using DataForSEO Keywords For Keywords API',
  inputSchema: relatedKeywordsSchema,
  execute: async (context) => {
    const { keywords, location_code, language_code, include_seed_keywords, limit } = context.context
    
    const apiLogin = env.DATAFORSEO_LOGIN
    const apiPassword = env.DATAFORSEO_PASSWORD
    
    console.log('Related Keywords Tool - Checking credentials:', { 
      hasLogin: !!apiLogin, 
      hasPassword: !!apiPassword,
      seedKeywords: keywords.length 
    })
    
    if (!apiLogin || !apiPassword) {
      console.error('DataForSEO credentials missing - returning mock data for testing')
      // Return mock data for testing
      const mockResults = []
      const prefixes = ['best', 'how to', 'top', 'cheap', 'buy', 'review']
      const suffixes = ['guide', 'tips', 'ideas', 'near me', 'online', 'for beginners']
      
      // Generate related keywords based on seed keywords
      for (const seed of keywords) {
        // Add the seed itself if requested
        if (include_seed_keywords) {
          mockResults.push({
            keyword: seed,
            search_volume: Math.floor(Math.random() * 50000) + 1000,
            competition: ['LOW', 'MEDIUM', 'HIGH'][Math.floor(Math.random() * 3)],
            competition_index: Math.floor(Math.random() * 100),
            cpc: Math.random() * 5 + 0.5,
            keyword_difficulty: Math.floor(Math.random() * 80) + 10
          })
        }
        
        // Generate variations
        for (const prefix of prefixes.slice(0, 3)) {
          mockResults.push({
            keyword: `${prefix} ${seed}`,
            search_volume: Math.floor(Math.random() * 10000) + 100,
            competition: ['LOW', 'MEDIUM', 'HIGH'][Math.floor(Math.random() * 3)],
            competition_index: Math.floor(Math.random() * 100),
            cpc: Math.random() * 3 + 0.3,
            keyword_difficulty: Math.floor(Math.random() * 70) + 5
          })
        }
        
        for (const suffix of suffixes.slice(0, 3)) {
          mockResults.push({
            keyword: `${seed} ${suffix}`,
            search_volume: Math.floor(Math.random() * 5000) + 50,
            competition: ['LOW', 'MEDIUM'][Math.floor(Math.random() * 2)],
            competition_index: Math.floor(Math.random() * 70),
            cpc: Math.random() * 2 + 0.2,
            keyword_difficulty: Math.floor(Math.random() * 60) + 5
          })
        }
      }
      
      // Sort by search volume
      mockResults.sort((a, b) => b.search_volume - a.search_volume)
      
      return {
        success: true,
        seed_keywords: keywords,
        total_keywords: mockResults.length,
        results: mockResults.slice(0, limit),
        note: 'Mock data returned - DataForSEO credentials not configured'
      }
    }

    // Create base64 encoded credentials
    const credentials = Buffer.from(`${apiLogin}:${apiPassword}`).toString('base64')
    
    try {
      const requestData = [{
        keywords,
        location_code,
        language_code,
        include_seed_keywords,
        limit
      }]

      console.log('Making DataForSEO related keywords request for seed keywords:', keywords)
      
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout
      
      const response = await fetch('https://api.dataforseo.com/v3/keywords_data/google_ads/keywords_for_keywords/live', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`DataForSEO API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('DataForSEO related keywords response status:', data.status_code, data.status_message)
      
      if (data.status_code !== 20000) {
        console.error('DataForSEO API error response:', data)
        throw new Error(`DataForSEO API error: ${data.status_message}`)
      }

      // Extract and format the results
      const results = []
      if (data.tasks && data.tasks[0] && data.tasks[0].result) {
        console.log('Processing related keywords results')
        for (const item of data.tasks[0].result) {
          // Add the main keyword data
          if (item.keyword_data) {
            results.push({
              keyword: item.keyword,
              search_volume: item.keyword_data.keyword_info?.search_volume || 0,
              competition: item.keyword_data.keyword_info?.competition || 'UNKNOWN',
              competition_index: item.keyword_data.keyword_info?.competition_index || 0,
              cpc: item.keyword_data.keyword_info?.cpc || 0,
              monthly_searches: item.keyword_data.keyword_info?.monthly_searches || []
            })
          }
        }
      } else {
        console.warn('No related keywords results found in response')
      }

      // Sort by search volume descending
      results.sort((a, b) => b.search_volume - a.search_volume)

      console.log('Related keywords tool completed successfully, returning', results.length, 'results')
      return {
        success: true,
        seed_keywords: keywords,
        total_keywords: results.length,
        results: results.slice(0, limit),
        cost: data.cost || 0
      }
    } catch (error) {
      console.error('Related keywords API error:', error)
      throw new Error(`Failed to get related keywords: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})