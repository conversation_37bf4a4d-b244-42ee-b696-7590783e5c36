import { z } from "zod"
import { createTool } from "@mastra/core"
import { webSearchTool } from "./web-search-tool"

const citationSchema = z.object({
  sources: z
    .array(
      z.object({
        url: z.string().url().describe("URL of the source"),
        title: z.string().optional().describe("Title of the source (will be fetched if not provided)"),
        author: z.string().optional().describe("Author name(s)"),
        publishDate: z.string().optional().describe("Publication date"),
        accessDate: z.string().optional().describe("Date accessed (defaults to today)"),
        sourceType: z
          .enum([
            "website",
            "journal-article", 
            "book",
            "news-article",
            "blog-post",
            "report",
            "whitepaper",
            "academic-paper",
            "government-document",
            "social-media"
          ])
          .default("website")
          .describe("Type of source being cited")
      })
    )
    .min(1)
    .describe("Array of sources to generate citations for"),
  citationStyle: z
    .enum([
      "APA",
      "MLA", 
      "Chicago",
      "Harvard",
      "IEEE",
      "Vancouver",
      "AMA"
    ])
    .default("APA")
    .describe("Citation style format to use"),
  includeInText: z
    .boolean()
    .default(true)
    .describe("Whether to include in-text citation examples"),
  includeBibliography: z
    .boolean()
    .default(true)
    .describe("Whether to include full bibliography entries"),
  sortAlphabetically: z
    .boolean()
    .default(true)
    .describe("Whether to sort bibliography entries alphabetically"),
  validateUrls: z
    .boolean()
    .default(true)
    .describe("Whether to validate and fetch metadata from URLs"),
  generateDoi: z
    .boolean()
    .default(false)
    .describe("Whether to attempt to find DOI for academic sources")
})

export const citationTool = createTool({
  id: "citation-management",
  description: "Generate properly formatted citations in various academic and professional styles",
  inputSchema: citationSchema,
  execute: async (context) => {
    const {
      sources,
      citationStyle,
      includeInText,
      includeBibliography,
      sortAlphabetically,
      validateUrls,
      generateDoi
    } = context.context

    // Define citation style formats
    const citationFormats = {
      APA: {
        name: "American Psychological Association (7th Edition)",
        inTextFormat: "(Author, Year)",
        websiteFormat: "Author, A. A. (Year, Month Date). Title of webpage. Website Name. URL",
        journalFormat: "Author, A. A. (Year). Title of article. Journal Name, Volume(Issue), pages. DOI or URL"
      },
      MLA: {
        name: "Modern Language Association (9th Edition)", 
        inTextFormat: "(Author Page)",
        websiteFormat: "Author Last, First. \"Title of Webpage.\" Website Name, Date, URL.",
        journalFormat: "Author Last, First. \"Title of Article.\" Journal Name, vol. #, no. #, Year, pp. ##-##."
      },
      Chicago: {
        name: "Chicago Manual of Style (17th Edition)",
        inTextFormat: "(Author Year, Page)",
        websiteFormat: "Author Last, First. \"Title of Webpage.\" Website Name. Date. URL.",
        journalFormat: "Author Last, First. \"Title of Article.\" Journal Name Volume, no. Issue (Year): pages."
      },
      Harvard: {
        name: "Harvard Referencing Style",
        inTextFormat: "(Author Year)",
        websiteFormat: "Author, A. (Year) 'Title of webpage', Website Name, Date, Available at: URL",
        journalFormat: "Author, A. (Year) 'Title of article', Journal Name, Volume(Issue), pp. pages."
      },
      IEEE: {
        name: "Institute of Electrical and Electronics Engineers",
        inTextFormat: "[Number]",
        websiteFormat: "[#] Author, \"Title of webpage,\" Website Name, Date. [Online]. Available: URL",
        journalFormat: "[#] Author, \"Title of article,\" Journal Name, vol. #, no. #, pp. ##-##, Year."
      },
      Vancouver: {
        name: "Vancouver Referencing Style",
        inTextFormat: "(Number)",
        websiteFormat: "Author A. Title of webpage [Internet]. Website Name; Year [cited Date]. Available from: URL",
        journalFormat: "Author A. Title of article. Journal Name. Year;Volume(Issue):pages."
      },
      AMA: {
        name: "American Medical Association",
        inTextFormat: "(Number)",
        websiteFormat: "Author A. Title of webpage. Website Name. Published Date. Accessed Date. URL",
        journalFormat: "Author A. Title of article. Journal Name. Year;Volume(Issue):pages."
      }
    }

    const selectedFormat = citationFormats[citationStyle]

    // Process each source
    const processedSources = []
    let citationNumber = 1

    for (const source of sources) {
      let processedSource = {
        ...source,
        citationNumber,
        accessDate: source.accessDate || new Date().toISOString().split('T')[0]
      }

      // Validate and fetch metadata if requested
      if (validateUrls && source.url) {
        try {
          // Use existing web search tool to get metadata
          const searchResult = await webSearchTool.execute({
            context: {
              query: source.url,
              includeDetails: true,
              numResults: 1
            }
          })

          if (searchResult.generalInformation?.results?.[0]) {
            const metadata = searchResult.generalInformation.results[0]
            processedSource = {
              ...processedSource,
              title: processedSource.title || metadata.title,
              author: processedSource.author || metadata.author,
              publishDate: processedSource.publishDate || metadata.publishedDate
            }
          }
        } catch (error) {
          console.warn(`Failed to fetch metadata for ${source.url}:`, error)
        }
      }

      // Generate DOI if requested and source is academic
      if (generateDoi && (source.sourceType === 'journal-article' || source.sourceType === 'academic-paper')) {
        // This would typically integrate with CrossRef API or similar
        // For now, we'll note that DOI lookup would be performed
        processedSource.needsDoiLookup = true
      }

      processedSources.push(processedSource)
      citationNumber++
    }

    // Sort sources if requested
    if (sortAlphabetically) {
      processedSources.sort((a, b) => {
        const authorA = a.author || a.title || a.url
        const authorB = b.author || b.title || b.url
        return authorA.localeCompare(authorB)
      })
    }

    // Build citation generation prompt
    let citationPrompt = `Generate ${citationStyle} style citations for the following sources:

CITATION STYLE: ${selectedFormat.name}
IN-TEXT FORMAT: ${selectedFormat.inTextFormat}
WEBSITE FORMAT: ${selectedFormat.websiteFormat}
JOURNAL FORMAT: ${selectedFormat.journalFormat}

SOURCES TO CITE:
${processedSources.map((source, index) => `
${index + 1}. URL: ${source.url}
   Title: ${source.title || 'To be determined from URL'}
   Author: ${source.author || 'Unknown'}
   Source Type: ${source.sourceType}
   Publish Date: ${source.publishDate || 'Unknown'}
   Access Date: ${source.accessDate}
`).join('')}

REQUIREMENTS:
- Use ${citationStyle} citation style format exactly
- Include proper punctuation and formatting
- Handle missing information appropriately for ${citationStyle} style`

    if (includeInText) {
      citationPrompt += `\n- Provide in-text citation examples for each source`
    }

    if (includeBibliography) {
      citationPrompt += `\n- Generate complete bibliography/reference list entries`
    }

    if (sortAlphabetically) {
      citationPrompt += `\n- Sort bibliography entries alphabetically`
    }

    citationPrompt += `\n\nGenerate the properly formatted ${citationStyle} citations now:`

    // Calculate citation statistics
    const sourceTypeCount = processedSources.reduce((acc, source) => {
      acc[source.sourceType] = (acc[source.sourceType] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const missingInfo = processedSources.reduce((acc, source) => {
      if (!source.title) acc.missingTitles++
      if (!source.author) acc.missingAuthors++
      if (!source.publishDate) acc.missingDates++
      return acc
    }, { missingTitles: 0, missingAuthors: <AUTHORS>

    return {
      success: true,
      citationConfig: {
        style: citationStyle,
        styleName: selectedFormat.name,
        sourceCount: processedSources.length,
        includeInText,
        includeBibliography,
        sortAlphabetically
      },
      sourceAnalysis: {
        sourceTypeBreakdown: sourceTypeCount,
        missingInformation: missingInfo,
        validationPerformed: validateUrls,
        doiLookupRequested: generateDoi
      },
      processedSources: processedSources.map(source => ({
        citationNumber: source.citationNumber,
        url: source.url,
        title: source.title,
        author: source.author,
        sourceType: source.sourceType,
        publishDate: source.publishDate,
        accessDate: source.accessDate,
        needsDoiLookup: source.needsDoiLookup || false
      })),
      citationPrompt,
      metadata: {
        styleFormat: selectedFormat,
        totalSources: processedSources.length,
        completenessScore: Math.round(((processedSources.length * 3 - missingInfo.missingTitles - missingInfo.missingAuthors - missingInfo.missingDates) / (processedSources.length * 3)) * 100)
      }
    }
  }
})
