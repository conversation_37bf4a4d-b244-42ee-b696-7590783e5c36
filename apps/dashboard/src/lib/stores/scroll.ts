import { readable, derived } from "svelte/store"
import { browser } from "$app/environment"

// Throttle function for performance optimization
function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number,
): T {
  let inThrottle: boolean
  return ((...args: any[]) => {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }) as T
}

// Enhanced scroll store with direction tracking
export const scrollState = readable(
  { y: 0, direction: "up", prevY: 0 },
  (set) => {
    if (!browser) return

    let prevY = 0

    const updateScroll = throttle(() => {
      const currentY = window.scrollY
      const direction = currentY > prevY ? "down" : "up"

      set({
        y: currentY,
        direction,
        prevY,
      })

      prevY = currentY
    }, 16) // ~60fps throttling

    window.addEventListener("scroll", updateScroll, { passive: true })
    updateScroll()

    return () => window.removeEventListener("scroll", updateScroll)
  },
)

// Backward compatibility - existing scrollY store
export const scrollY = derived(scrollState, ($scrollState) => $scrollState.y)

// New derived stores for enhanced functionality
export const scrollDirection = derived(
  scrollState,
  ($scrollState) => $scrollState.direction,
)
export const isScrolled = derived(
  scrollState,
  ($scrollState) => $scrollState.y > 50,
)
export const shouldHideNav = derived(
  scrollState,
  ($scrollState) => $scrollState.direction === "down" && $scrollState.y > 100,
)
