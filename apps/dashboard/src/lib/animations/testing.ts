import { browser } from "$app/environment"
import { prefersReducedMotion } from "./index"
import { deviceCapabilities, AnimationPerformanceMonitor } from "./performance"

// Test result interface
interface TestResult {
  name: string
  passed: boolean
  duration?: number
  error?: string
  timestamp: number
}

// Animation testing utilities
export class AnimationTester {
  private testResults: TestResult[] = []

  // Test if animations respect reduced motion preference
  async testReducedMotionSupport(): Promise<TestResult> {
    const testName = "Reduced Motion Support"
    const startTime = performance.now()

    try {
      // Mock reduced motion preference
      const originalMatchMedia = window.matchMedia
      window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: query === "(prefers-reduced-motion: reduce)",
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }))

      const reducedMotion = prefersReducedMotion()

      // Restore original matchMedia
      window.matchMedia = originalMatchMedia

      const duration = performance.now() - startTime
      const result: TestResult = {
        name: testName,
        passed: reducedMotion === true,
        duration,
        timestamp: Date.now(),
      }

      this.testResults.push(result)
      return result
    } catch (error) {
      const result: TestResult = {
        name: testName,
        passed: false,
        error: error.message,
        timestamp: Date.now(),
      }

      this.testResults.push(result)
      return result
    }
  }

  // Test animation performance under load
  async testAnimationPerformance(): Promise<TestResult> {
    const testName = "Animation Performance"
    const startTime = performance.now()

    try {
      if (!browser) {
        throw new Error("Browser environment required")
      }

      const monitor = AnimationPerformanceMonitor.getInstance()
      monitor.startMonitoring()

      // Create multiple test elements
      const testElements: HTMLElement[] = []
      for (let i = 0; i < 10; i++) {
        const element = document.createElement("div")
        element.style.cssText = `
          position: absolute;
          width: 50px;
          height: 50px;
          background: red;
          top: ${i * 60}px;
          left: 0;
        `
        document.body.appendChild(element)
        testElements.push(element)
      }

      // Animate all elements simultaneously
      const animations = testElements.map((element) => {
        return element.animate(
          [
            { transform: "translateX(0px)" },
            { transform: "translateX(200px)" },
          ],
          {
            duration: 1000,
            easing: "ease-in-out",
          },
        )
      })

      // Wait for animations to complete
      await Promise.all(animations.map((anim) => anim.finished))

      // Clean up
      testElements.forEach((element) => element.remove())
      monitor.stopMonitoring()

      const duration = performance.now() - startTime
      const animationCount = monitor.getAnimationCount()

      const result: TestResult = {
        name: testName,
        passed: duration < 2000 && animationCount === 0, // Should complete in under 2s and clean up
        duration,
        timestamp: Date.now(),
      }

      this.testResults.push(result)
      return result
    } catch (error) {
      const result: TestResult = {
        name: testName,
        passed: false,
        error: error.message,
        timestamp: Date.now(),
      }

      this.testResults.push(result)
      return result
    }
  }

  // Test device capability detection
  async testDeviceCapabilities(): Promise<TestResult> {
    const testName = "Device Capabilities"
    const startTime = performance.now()

    try {
      const capabilities = {
        hardwareAcceleration: deviceCapabilities.supportsHardwareAcceleration(),
        isMobile: deviceCapabilities.isMobile(),
        hasTouch: deviceCapabilities.hasTouch(),
        performanceTier: deviceCapabilities.getPerformanceTier(),
        prefersReducedMotion: deviceCapabilities.prefersReducedMotion(),
      }

      // Validate that all capabilities return expected types
      const validations = [
        typeof capabilities.hardwareAcceleration === "boolean",
        typeof capabilities.isMobile === "boolean",
        typeof capabilities.hasTouch === "boolean",
        ["low", "medium", "high"].includes(capabilities.performanceTier),
        typeof capabilities.prefersReducedMotion === "boolean",
      ]

      const allValid = validations.every((v) => v === true)
      const duration = performance.now() - startTime

      const result: TestResult = {
        name: testName,
        passed: allValid,
        duration,
        timestamp: Date.now(),
      }

      this.testResults.push(result)
      return result
    } catch (error) {
      const result: TestResult = {
        name: testName,
        passed: false,
        error: error.message,
        timestamp: Date.now(),
      }

      this.testResults.push(result)
      return result
    }
  }

  // Test intersection observer functionality
  async testIntersectionObserver(): Promise<TestResult> {
    const testName = "Intersection Observer"
    const startTime = performance.now()

    try {
      if (!browser) {
        throw new Error("Browser environment required")
      }

      return new Promise<TestResult>((resolve) => {
        const testElement = document.createElement("div")
        testElement.style.cssText = `
          width: 100px;
          height: 100px;
          background: blue;
          position: absolute;
          top: -200px;
          left: 0;
        `
        document.body.appendChild(testElement)

        let observerTriggered = false

        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                observerTriggered = true
              }
            })
          },
          { threshold: 0.1 },
        )

        observer.observe(testElement)

        // Move element into view
        setTimeout(() => {
          testElement.style.top = "0px"
        }, 100)

        // Check result after animation
        setTimeout(() => {
          observer.disconnect()
          testElement.remove()

          const duration = performance.now() - startTime
          const result: TestResult = {
            name: testName,
            passed: observerTriggered,
            duration,
            timestamp: Date.now(),
          }

          this.testResults.push(result)
          resolve(result)
        }, 500)
      })
    } catch (error) {
      const result: TestResult = {
        name: testName,
        passed: false,
        error: error.message,
        timestamp: Date.now(),
      }

      this.testResults.push(result)
      return result
    }
  }

  // Test CSS animation support
  async testCSSAnimationSupport(): Promise<TestResult> {
    const testName = "CSS Animation Support"
    const startTime = performance.now()

    try {
      if (!browser) {
        throw new Error("Browser environment required")
      }

      const testElement = document.createElement("div")
      document.body.appendChild(testElement)

      // Test CSS animation properties
      const supportedProperties = [
        "animation",
        "transform",
        "transition",
        "opacity",
      ]

      const support = supportedProperties.map((prop) => {
        testElement.style[prop] = "initial"
        return testElement.style[prop] !== undefined
      })

      testElement.remove()

      const allSupported = support.every((s) => s === true)
      const duration = performance.now() - startTime

      const result: TestResult = {
        name: testName,
        passed: allSupported,
        duration,
        timestamp: Date.now(),
      }

      this.testResults.push(result)
      return result
    } catch (error) {
      const result: TestResult = {
        name: testName,
        passed: false,
        error: error.message,
        timestamp: Date.now(),
      }

      this.testResults.push(result)
      return result
    }
  }

  // Run all tests
  async runAllTests(): Promise<TestResult[]> {
    console.log("🧪 Running animation tests...")

    const tests = [
      this.testReducedMotionSupport(),
      this.testDeviceCapabilities(),
      this.testCSSAnimationSupport(),
      this.testIntersectionObserver(),
      this.testAnimationPerformance(),
    ]

    const results = await Promise.all(tests)

    // Log results
    console.log("📊 Animation Test Results:")
    results.forEach((result) => {
      const status = result.passed ? "✅" : "❌"
      const duration = result.duration
        ? ` (${result.duration.toFixed(2)}ms)`
        : ""
      const error = result.error ? ` - ${result.error}` : ""
      console.log(`${status} ${result.name}${duration}${error}`)
    })

    const passedCount = results.filter((r) => r.passed).length
    console.log(`\n📈 Overall: ${passedCount}/${results.length} tests passed`)

    return results
  }

  // Get test results
  getTestResults(): TestResult[] {
    return this.testResults
  }

  // Clear test results
  clearResults(): void {
    this.testResults = []
  }
}

// Cross-browser compatibility tests
export const browserCompatibilityTests = {
  // Test Web Animations API support
  testWebAnimationsAPI(): boolean {
    if (!browser) return false
    return "animate" in Element.prototype
  },

  // Test CSS Custom Properties support
  testCSSCustomProperties(): boolean {
    if (!browser) return false
    return window.CSS && CSS.supports && CSS.supports("color", "var(--test)")
  },

  // Test Intersection Observer support
  testIntersectionObserver(): boolean {
    if (!browser) return false
    return "IntersectionObserver" in window
  },

  // Test requestAnimationFrame support
  testRequestAnimationFrame(): boolean {
    if (!browser) return false
    return "requestAnimationFrame" in window
  },

  // Test CSS Grid support
  testCSSGrid(): boolean {
    if (!browser) return false
    return CSS.supports("display", "grid")
  },

  // Test CSS Flexbox support
  testCSSFlexbox(): boolean {
    if (!browser) return false
    return CSS.supports("display", "flex")
  },

  // Run all compatibility tests
  runCompatibilityTests(): Record<string, boolean> {
    return {
      webAnimationsAPI: this.testWebAnimationsAPI(),
      cssCustomProperties: this.testCSSCustomProperties(),
      intersectionObserver: this.testIntersectionObserver(),
      requestAnimationFrame: this.testRequestAnimationFrame(),
      cssGrid: this.testCSSGrid(),
      cssFlexbox: this.testCSSFlexbox(),
    }
  },
}

// Initialize testing in development
export const initializeAnimationTesting = () => {
  if (!browser || !import.meta.env.DEV) return

  console.log("🔧 Animation system initialized")

  // Run compatibility tests
  const compatibility = browserCompatibilityTests.runCompatibilityTests()
  console.log("🌐 Browser Compatibility:", compatibility)

  // Add global test function for manual testing
  ;(window as any).testAnimations = async () => {
    const tester = new AnimationTester()
    return await tester.runAllTests()
  }

  console.log("💡 Run testAnimations() in console to test animation system")
}
