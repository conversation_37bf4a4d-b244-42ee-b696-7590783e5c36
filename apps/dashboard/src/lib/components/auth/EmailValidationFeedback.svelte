<script lang="ts">
  import { fade, scale } from "svelte/transition"
  import { CheckCircle2, AlertCircle, Mail, Info } from "lucide-svelte"

  export let email: string = ""
  export let showConfirmationNotice: boolean = true
  export let touched: boolean = false

  // Email validation regex (same as <PERSON><PERSON>'s email validation)
  const emailRegex =
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/

  $: isValidEmail = emailRegex.test(email)
  $: hasAtSymbol = email.includes("@")
  $: showValidation = touched && email.length > 0

  // Extract domain for better feedback
  $: emailDomain = email.includes("@") ? email.split("@")[1] : ""
  $: hasValidDomain =
    emailDomain && emailDomain.includes(".") && emailDomain.length > 3
</script>

<div class="space-y-2">
  <!-- Email validation status -->
  {#if showValidation}
    <div class="flex items-center gap-2" transition:fade={{ duration: 300 }}>
      {#if isValidEmail}
        <div
          transition:scale={{ duration: 400, start: 0.8 }}
          class="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm font-medium dark:bg-green-950/20 dark:border-green-800/30 dark:text-green-400"
        >
          <CheckCircle2 size={16} class="flex-shrink-0" />
          <span>Valid email format</span>
        </div>
      {:else if hasAtSymbol}
        <div
          class="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-700 text-sm font-medium dark:bg-yellow-950/20 dark:border-yellow-800/30 dark:text-yellow-400"
          transition:fade={{ duration: 300 }}
        >
          <AlertCircle size={16} class="flex-shrink-0" />
          <span>
            {#if !hasValidDomain}
              Please complete the email address
            {:else}
              Please check the email format
            {/if}
          </span>
        </div>
      {:else}
        <div
          class="flex items-center gap-2 p-2 bg-gray-50 border border-gray-200 rounded-lg text-gray-600 text-sm dark:bg-gray-950/20 dark:border-gray-800/30 dark:text-gray-400"
          transition:fade={{ duration: 300 }}
        >
          <Mail size={16} class="flex-shrink-0" />
          <span>Enter your email address</span>
        </div>
      {/if}
    </div>
  {/if}

  <!-- Email confirmation notice -->
  {#if showConfirmationNotice && (isValidEmail || (!touched && email.length === 0))}
    <div
      class="flex items-start gap-2 p-3 bg-info/10 border border-info/20 rounded-lg text-sm"
      transition:fade={{ duration: 200 }}
    >
      <Info size={16} class="text-info mt-0.5 flex-shrink-0" />
      <div class="space-y-1">
        <p class="font-medium text-info">Email confirmation required</p>
        <p class="text-base-content/70">
          We'll send you a confirmation email to verify your account before you
          can sign in.
        </p>
      </div>
    </div>
  {/if}

  <!-- Domain-specific hints -->
  {#if showValidation && hasAtSymbol && !isValidEmail}
    <div class="text-xs text-base-content/60 ml-6">
      {#if emailDomain === "gmail" || emailDomain === "yahoo" || emailDomain === "hotmail"}
        <span
          >Did you mean <span class="font-medium">{emailDomain}.com</span
          >?</span
        >
      {/if}
    </div>
  {/if}
</div>
