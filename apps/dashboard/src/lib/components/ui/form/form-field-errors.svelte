<script lang="ts">
  import * as FormPrimitive from "formsnap"
  import { cn } from "$lib/utils.js"
  import { fade, fly } from "svelte/transition"
  import { AlertCircle } from "lucide-svelte"

  type $$Props = FormPrimitive.FieldErrorsProps & {
    errorClasses?: string | undefined | null
  }

  let className: $$Props["class"] = undefined
  export { className as class }
  export let errorClasses: $$Props["class"] = undefined
</script>

<FormPrimitive.FieldErrors
  class={cn("text-destructive text-sm font-medium", className)}
  {...$$restProps}
  let:errors
  let:fieldErrorsAttrs
  let:errorAttrs
>
  <slot {errors} {fieldErrorsAttrs} {errorAttrs}>
    {#each errors as error}
      <div
        {...errorAttrs}
        class={cn(
          "flex items-center gap-2 p-3 mt-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium",
          "dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400",
          errorClasses,
        )}
        transition:fly={{ y: -10, duration: 300 }}
      >
        <AlertCircle size={16} class="flex-shrink-0" />
        <span>{error}</span>
      </div>
    {/each}
  </slot>
</FormPrimitive.FieldErrors>
