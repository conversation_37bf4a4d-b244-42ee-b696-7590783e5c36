<script lang="ts">
  import { onMount } from 'svelte';
  
  interface Props {
    themeCSS: string;
  }
  
  let { themeCSS }: Props = $props();
  
  onMount(() => {
    // Remove any existing theme styles
    const existingThemeStyle = document.getElementById('dynamic-theme-styles');
    if (existingThemeStyle) {
      existingThemeStyle.remove();
    }
    
    // Create and inject new theme styles
    const styleElement = document.createElement('style');
    styleElement.id = 'dynamic-theme-styles';
    styleElement.textContent = themeCSS;
    document.head.appendChild(styleElement);
    
    return () => {
      // Cleanup on unmount
      const styleEl = document.getElementById('dynamic-theme-styles');
      if (styleEl) {
        styleEl.remove();
      }
    };
  });
</script>