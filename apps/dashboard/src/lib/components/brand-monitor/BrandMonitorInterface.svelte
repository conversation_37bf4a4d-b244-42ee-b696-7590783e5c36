<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import UrlInputSection from "./UrlInputSection.svelte"
  import AnalysisResults from "./AnalysisResults.svelte"
  import AnalysisList from "./AnalysisList.svelte"
  import EmptyState from "./EmptyState.svelte"
  import ErrorDisplay from "./ErrorDisplay.svelte"
  import type { BrandAnalysis } from "shared"

  export let analyses: BrandAnalysis[] = []
  export let session: any
  export let error: string | null = null

  const dispatch = createEventDispatcher()

  let selectedAnalysis: BrandAnalysis | null = null
  let isAnalyzing = false

  function handleStartAnalysis(event: CustomEvent) {
    isAnalyzing = true
    dispatch("newAnalysis", event.detail)
  }

  function handleAnalysisComplete() {
    isAnalyzing = false
  }

  function selectAnalysis(analysis: BrandAnalysis) {
    selectedAnalysis = analysis
  }

  function clearSelection() {
    selectedAnalysis = null
  }

  function handleErrorRetry() {
    dispatch("retry")
  }

  function handleErrorDismiss() {
    dispatch("dismissError")
  }

  function handleNewAnalysisFromEmpty() {
    clearSelection()
  }
</script>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
  <!-- Sidebar with analysis list -->
  <div class="lg:col-span-1">
    <AnalysisList
      {analyses}
      {selectedAnalysis}
      on:select={(e) => selectAnalysis(e.detail)}
      on:clearSelection={clearSelection}
    />
  </div>

  <!-- Main content area -->
  <div class="lg:col-span-3 space-y-6">
    <!-- Error Display -->
    {#if error}
      <ErrorDisplay
        {error}
        title="Connection Error"
        on:retry={handleErrorRetry}
        on:dismiss={handleErrorDismiss}
      />
    {/if}

    <!-- Main Content -->
    {#if selectedAnalysis}
      <AnalysisResults analysis={selectedAnalysis} on:back={clearSelection} />
    {:else if analyses.length === 0 && !isAnalyzing}
      <EmptyState
        title="Start Your First Brand Analysis"
        description="Enter your company's website URL to analyze how AI models like ChatGPT, Claude, and Perplexity represent your brand in their responses."
        actionText="Start Analysis"
        on:action={handleNewAnalysisFromEmpty}
      />
    {:else}
      <UrlInputSection
        {isAnalyzing}
        on:startAnalysis={handleStartAnalysis}
        on:complete={handleAnalysisComplete}
      />
    {/if}
  </div>
</div>
