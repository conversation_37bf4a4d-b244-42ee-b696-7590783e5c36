<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import UrlInputSection from './UrlInputSection.svelte'
  import AnalysisResults from './AnalysisResults.svelte'
  import AnalysisList from './AnalysisList.svelte'
  import type { BrandAnalysis } from 'shared'

  export let analyses: BrandAnalysis[] = []
  export let session: any

  const dispatch = createEventDispatcher()

  let selectedAnalysis: BrandAnalysis | null = null
  let isAnalyzing = false

  function handleStartAnalysis(event: CustomEvent) {
    isAnalyzing = true
    dispatch('newAnalysis', event.detail)
  }

  function handleAnalysisComplete() {
    isAnalyzing = false
  }

  function selectAnalysis(analysis: BrandAnalysis) {
    selectedAnalysis = analysis
  }

  function clearSelection() {
    selectedAnalysis = null
  }
</script>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
  <!-- Sidebar with analysis list -->
  <div class="lg:col-span-1">
    <AnalysisList 
      {analyses}
      {selectedAnalysis}
      on:select={(e) => selectAnalysis(e.detail)}
      on:clearSelection={clearSelection}
    />
  </div>

  <!-- Main content area -->
  <div class="lg:col-span-3">
    {#if selectedAnalysis}
      <AnalysisResults 
        analysis={selectedAnalysis} 
        on:back={clearSelection}
      />
    {:else}
      <UrlInputSection 
        {isAnalyzing}
        on:startAnalysis={handleStartAnalysis}
        on:complete={handleAnalysisComplete}
      />
    {/if}
  </div>
</div>
