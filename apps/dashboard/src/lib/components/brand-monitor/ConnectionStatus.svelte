<script lang="ts">
  import { onMount, onDestroy } from 'svelte'
  import { testFireGeoConnection, fireGeoConnectionState } from '$lib/firegeo-client'

  export let showDetails = false

  let connectionInterval: NodeJS.Timeout | null = null
  let lastChecked: Date | null = null

  onMount(() => {
    // Initial connection test
    checkConnection()
    
    // Set up periodic connection checks (every 30 seconds)
    connectionInterval = setInterval(checkConnection, 30000)
  })

  onDestroy(() => {
    if (connectionInterval) {
      clearInterval(connectionInterval)
    }
  })

  async function checkConnection() {
    try {
      await testFireGeoConnection()
      lastChecked = new Date()
    } catch (error) {
      console.error('Connection check failed:', error)
      lastChecked = new Date()
    }
  }

  function getStatusColor() {
    if (!$fireGeoConnectionState.connected) return 'bg-red-500'
    if ($fireGeoConnectionState.latency && $fireGeoConnectionState.latency > 1000) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  function getStatusText() {
    if (!$fireGeoConnectionState.connected) return 'Disconnected'
    if ($fireGeoConnectionState.latency && $fireGeoConnectionState.latency > 1000) return 'Slow Connection'
    return 'Connected'
  }

  function formatLatency(latency: number | undefined): string {
    if (!latency) return 'Unknown'
    if (latency < 100) return 'Excellent'
    if (latency < 300) return 'Good'
    if (latency < 1000) return 'Fair'
    return 'Poor'
  }

  function formatLastChecked(): string {
    if (!lastChecked) return 'Never'
    const now = new Date()
    const diff = now.getTime() - lastChecked.getTime()
    const seconds = Math.floor(diff / 1000)
    
    if (seconds < 60) return `${seconds}s ago`
    const minutes = Math.floor(seconds / 60)
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    return `${hours}h ago`
  }
</script>

<div class="flex items-center gap-2 text-sm">
  <!-- Status Indicator -->
  <div class="flex items-center gap-2">
    <div class="w-2 h-2 rounded-full {getStatusColor()} {$fireGeoConnectionState.connected ? 'animate-pulse' : ''}"></div>
    <span class="text-muted-foreground">
      {getStatusText()}
    </span>
  </div>

  <!-- Details Toggle -->
  {#if showDetails}
    <button
      onclick={() => showDetails = !showDetails}
      class="text-xs text-muted-foreground hover:text-foreground transition-colors"
    >
      Hide Details
    </button>
  {:else}
    <button
      onclick={() => showDetails = !showDetails}
      class="text-xs text-muted-foreground hover:text-foreground transition-colors"
    >
      Show Details
    </button>
  {/if}

  <!-- Manual Refresh -->
  <button
    onclick={checkConnection}
    class="text-xs text-muted-foreground hover:text-foreground transition-colors"
    title="Check connection"
  >
    <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
    </svg>
  </button>
</div>

<!-- Detailed Status -->
{#if showDetails}
  <div class="mt-3 p-3 bg-muted/50 rounded-lg border border-border text-xs">
    <div class="grid grid-cols-2 gap-2">
      <div>
        <span class="font-medium text-foreground">Status:</span>
        <span class="text-muted-foreground">{getStatusText()}</span>
      </div>
      <div>
        <span class="font-medium text-foreground">Latency:</span>
        <span class="text-muted-foreground">
          {formatLatency($fireGeoConnectionState.latency)}
          {#if $fireGeoConnectionState.latency}
            ({$fireGeoConnectionState.latency}ms)
          {/if}
        </span>
      </div>
      <div>
        <span class="font-medium text-foreground">Last Checked:</span>
        <span class="text-muted-foreground">{formatLastChecked()}</span>
      </div>
      <div>
        <span class="font-medium text-foreground">Service:</span>
        <span class="text-muted-foreground">FireGeo API</span>
      </div>
    </div>
    
    {#if !$fireGeoConnectionState.connected}
      <div class="mt-2 p-2 bg-destructive/10 border border-destructive/20 rounded text-destructive">
        <div class="flex items-center gap-2">
          <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span class="font-medium">Connection Issues</span>
        </div>
        <p class="mt-1 text-xs">
          Unable to connect to FireGeo service. Please check your internet connection or try again later.
        </p>
      </div>
    {/if}
  </div>
{/if}
