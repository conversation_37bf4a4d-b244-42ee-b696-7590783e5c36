<script lang="ts">
  import { 
    Sparkles, 
    Target,
    Filter,
    Download,
    Copy,
    ChevronRight,
    Loader2
  } from 'lucide-svelte'
  
  interface NicheKeyword {
    keyword: string
    search_volume: number
    difficulty: number
    competition: string
    cpc: number
    opportunity_score?: number
  }
  
  interface DiscoveryFilters {
    industry: string
    location: string
    volumeRange: { min: number; max: number }
    maxDifficulty: number
  }
  
  interface Props {
    onAnalyze: (seedKeywords: string[], filters: DiscoveryFilters) => void
    isLoading?: boolean
    discoveredKeywords?: NicheKeyword[]
  }
  
  let { 
    onAnalyze, 
    isLoading = false,
    discoveredKeywords = []
  }: Props = $props()
  
  let seedKeywords = $state('')
  let industry = $state('')
  let targetLocation = $state('United States')
  let volumeMin = $state(100)
  let volumeMax = $state(10000)
  let difficultyMax = $state(50)
  let showFilters = $state(false)
  
  const industryExamples = [
    { name: 'E-commerce', seeds: 'organic skincare, natural beauty products, vegan cosmetics' },
    { name: 'Saa<PERSON>', seeds: 'project management, team collaboration, task tracking' },
    { name: 'Local Business', seeds: 'coffee shop, specialty coffee, local cafe' },
    { name: 'Health & Wellness', seeds: 'yoga classes, meditation app, wellness coaching' }
  ]
  
  function handleAnalyze() {
    const seeds = seedKeywords.split(',').map(k => k.trim()).filter(k => k.length > 0)
    if (seeds.length === 0) return
    
    const filters = {
      industry,
      location: targetLocation,
      volumeRange: { min: volumeMin, max: volumeMax },
      maxDifficulty: difficultyMax
    }
    
    onAnalyze(seeds, filters)
  }
  
  function setSampleSeeds(seeds: string) {
    seedKeywords = seeds
  }
  
  function copyKeywords() {
    const keywords = filteredKeywords.map(k => k.keyword).join('\n')
    navigator.clipboard.writeText(keywords)
  }
  
  function exportCSV() {
    const headers = ['Keyword', 'Search Volume', 'Difficulty', 'Competition', 'CPC', 'Opportunity Score']
    const rows = filteredKeywords.map(k => [
      k.keyword,
      k.search_volume,
      k.difficulty,
      k.competition,
      k.cpc.toFixed(2),
      k.opportunity_score?.toFixed(2) || ''
    ])
    
    const csv = [headers, ...rows].map(row => row.join(',')).join('\n')
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `niche-keywords-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }
  
  // Calculate opportunity scores
  const scoredKeywords = $derived(discoveredKeywords.map(k => ({
    ...k,
    opportunity_score: (k.search_volume / (k.difficulty + 1)) * (k.competition === 'LOW' ? 2 : k.competition === 'MEDIUM' ? 1 : 0.5)
  })))
  
  // Apply filters
  const filteredKeywords = $derived(scoredKeywords
    .filter(k => k.search_volume >= volumeMin && k.search_volume <= volumeMax)
    .filter(k => k.difficulty <= difficultyMax)
    .sort((a, b) => (b.opportunity_score || 0) - (a.opportunity_score || 0)))
</script>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-3">
      <div class="w-10 h-10 flex items-center justify-center border-2" 
           style="background: var(--primary); border-color: var(--border);">
        <Sparkles class="w-5 h-5" style="color: var(--primary-foreground);" />
      </div>
      <div>
        <h3 class="text-lg font-bold" style="color: var(--foreground);">
          Niche Keyword Discovery
        </h3>
        <p class="text-sm" style="color: var(--muted-foreground);">
          Find untapped long-tail keywords in your specific niche
        </p>
      </div>
    </div>
  </div>
  
  <!-- Industry Examples -->
  <div class="grid md:grid-cols-2 gap-3">
    {#each industryExamples as example}
      <button
        on:click={() => setSampleSeeds(example.seeds)}
        class="p-3 border-2 text-left hover:border-primary transition-all"
        style="background: var(--card); border-color: var(--border);"
        disabled={isLoading}
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="font-medium text-sm" style="color: var(--foreground);">
              {example.name}
            </p>
            <p class="text-xs mt-1" style="color: var(--muted-foreground);">
              {example.seeds}
            </p>
          </div>
          <ChevronRight class="w-4 h-4" style="color: var(--muted-foreground);" />
        </div>
      </button>
    {/each}
  </div>
  
  <!-- Seed Keywords Input -->
  <div>
    <label class="block text-sm font-medium mb-2" style="color: var(--foreground);">
      Seed Keywords (comma-separated, max 20)
    </label>
    <textarea
      bind:value={seedKeywords}
      placeholder="e.g., organic coffee, fair trade coffee beans, specialty coffee roasters"
      class="w-full p-3 border-2 min-h-[80px]"
      style="background: var(--background); border-color: var(--border); color: var(--foreground);"
      disabled={isLoading}
    ></textarea>
  </div>
  
  <!-- Filters -->
  <div>
    <button
      on:click={() => showFilters = !showFilters}
      class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"
      disabled={isLoading}
    >
      <Filter class="w-4 h-4" />
      Advanced Filters
      <span class="text-xs {showFilters ? 'rotate-180' : ''} transition-transform">▼</span>
    </button>
    
    {#if showFilters}
      <div class="mt-4 p-4 border-2 space-y-4" 
           style="background: var(--muted); border-color: var(--border);">
        <div class="grid md:grid-cols-2 gap-4">
          <div>
            <label class="block text-xs font-bold mb-2" style="color: var(--foreground);">
              Industry/Niche
            </label>
            <input
              bind:value={industry}
              placeholder="e.g., E-commerce, SaaS, Healthcare"
              class="w-full p-2 border-2 text-sm"
              style="background: var(--background); border-color: var(--border); color: var(--foreground);"
              disabled={isLoading}
            />
          </div>
          <div>
            <label class="block text-xs font-bold mb-2" style="color: var(--foreground);">
              Target Location
            </label>
            <select
              bind:value={targetLocation}
              class="w-full p-2 border-2 text-sm"
              style="background: var(--background); border-color: var(--border); color: var(--foreground);"
              disabled={isLoading}
            >
              <option value="United States">United States</option>
              <option value="United Kingdom">United Kingdom</option>
              <option value="Canada">Canada</option>
              <option value="Australia">Australia</option>
              <option value="Global">Global</option>
            </select>
          </div>
        </div>
        
        <div class="grid md:grid-cols-3 gap-4">
          <div>
            <label class="block text-xs font-bold mb-2" style="color: var(--foreground);">
              Min Volume
            </label>
            <input
              type="number"
              bind:value={volumeMin}
              class="w-full p-2 border-2 text-sm"
              style="background: var(--background); border-color: var(--border); color: var(--foreground);"
              disabled={isLoading}
            />
          </div>
          <div>
            <label class="block text-xs font-bold mb-2" style="color: var(--foreground);">
              Max Volume
            </label>
            <input
              type="number"
              bind:value={volumeMax}
              class="w-full p-2 border-2 text-sm"
              style="background: var(--background); border-color: var(--border); color: var(--foreground);"
              disabled={isLoading}
            />
          </div>
          <div>
            <label class="block text-xs font-bold mb-2" style="color: var(--foreground);">
              Max Difficulty
            </label>
            <input
              type="number"
              bind:value={difficultyMax}
              max="100"
              class="w-full p-2 border-2 text-sm"
              style="background: var(--background); border-color: var(--border); color: var(--foreground);"
              disabled={isLoading}
            />
          </div>
        </div>
      </div>
    {/if}
  </div>
  
  <!-- Analyze Button -->
  <button
    on:click={handleAnalyze}
    disabled={!seedKeywords.trim() || isLoading}
    class="btn-primary px-6 py-3 font-bold flex items-center gap-2 w-full md:w-auto"
  >
    {#if isLoading}
      <Loader2 class="w-4 h-4 animate-spin" />
      Discovering Keywords...
    {:else}
      <Target class="w-4 h-4" />
      Discover Niche Keywords
    {/if}
  </button>
  
  <!-- Results -->
  {#if filteredKeywords.length > 0}
    <div class="border-2 p-6" style="background: var(--card); border-color: var(--border);">
      <div class="flex items-center justify-between mb-4">
        <h4 class="font-bold" style="color: var(--foreground);">
          Discovered Keywords ({filteredKeywords.length})
        </h4>
        <div class="flex gap-2">
          <button
            on:click={copyKeywords}
            class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
          >
            <Copy class="w-3 h-3" />
            Copy
          </button>
          <button
            on:click={exportCSV}
            class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
          >
            <Download class="w-3 h-3" />
            Export CSV
          </button>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b" style="border-color: var(--border);">
              <th class="text-left py-2 px-3 text-sm font-medium" style="color: var(--foreground);">
                Keyword
              </th>
              <th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">
                Volume
              </th>
              <th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">
                Difficulty
              </th>
              <th class="text-center py-2 px-3 text-sm font-medium" style="color: var(--foreground);">
                Competition
              </th>
              <th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">
                CPC
              </th>
              <th class="text-right py-2 px-3 text-sm font-medium" style="color: var(--foreground);">
                Opportunity
              </th>
            </tr>
          </thead>
          <tbody>
            {#each filteredKeywords as keyword}
              <tr class="border-b hover:bg-muted/50" style="border-color: var(--border);">
                <td class="py-2 px-3 text-sm" style="color: var(--foreground);">
                  {keyword.keyword}
                </td>
                <td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);">
                  {keyword.search_volume.toLocaleString()}
                </td>
                <td class="py-2 px-3 text-sm text-right">
                  <span class="px-2 py-1 text-xs rounded" 
                        style="background: {keyword.difficulty < 30 ? 'var(--primary)' : keyword.difficulty < 60 ? '#fbbf24' : '#ef4444'}; color: white;">
                    {keyword.difficulty}
                  </span>
                </td>
                <td class="py-2 px-3 text-sm text-center">
                  <span class="text-xs" style="color: {keyword.competition === 'LOW' ? 'var(--primary)' : keyword.competition === 'MEDIUM' ? '#fbbf24' : '#ef4444'};">
                    {keyword.competition}
                  </span>
                </td>
                <td class="py-2 px-3 text-sm text-right" style="color: var(--muted-foreground);">
                  ${keyword.cpc.toFixed(2)}
                </td>
                <td class="py-2 px-3 text-sm text-right">
                  <span class="font-medium" style="color: var(--primary);">
                    {keyword.opportunity_score?.toFixed(1) || '-'}
                  </span>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </div>
  {/if}
</div>