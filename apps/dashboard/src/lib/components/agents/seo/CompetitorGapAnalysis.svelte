<script lang="ts">
  import {
    Target,
    Filter,
    Download,
    Copy,
    T<PERSON>dingUp,
    AlertCircle,
    Loader2,
    Plus,
    X,
    CheckCircle2,
    Circle,
  } from "lucide-svelte"
  import { slide, fade } from "svelte/transition"

  interface GapKeyword {
    keyword: string
    search_volume: number
    difficulty: number
    competition: string
    cpc: number
    competitor_position: number
    your_position: number | null
    gap_type: "missing" | "lower_rank" | "opportunity"
    opportunity_score?: number
  }

  interface GapFilters {
    yourDomain: string
    competitors: string[]
    location: string
    minVolume: number
    maxDifficulty: number
    gapType: "missing" | "lower_rank" | "all"
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: "pending" | "active" | "completed"
    progress?: number
  }

  interface Props {
    onAnalyze: (filters: GapFilters) => void
    isLoading?: boolean
    gapKeywords?: GapKeyword[]
    progressSteps?: ProgressStep[]
    currentProgress?: number
    isMockData?: boolean
    aiResponse?: string
  }

  let {
    onAnalyze,
    isLoading = false,
    gapKeywords = [],
    progressSteps = [],
    currentProgress = 0,
    isMockData = false,
    aiResponse = "",
  }: Props = $props()

  let yourDomain = $state("")
  let competitors = $state<string[]>([""])
  let targetLocation = $state("United States")
  let volumeMin = $state(500)
  let difficultyMax = $state(70)
  let gapType = $state<"missing" | "lower_rank" | "all">("all")
  let showFilters = $state(false)

  function addCompetitor() {
    if (competitors.length < 3) {
      competitors = [...competitors, ""]
    }
  }

  function removeCompetitor(index: number) {
    competitors = competitors.filter((_, i) => i !== index)
  }

  function handleAnalyze() {
    const validCompetitors = competitors.filter((c) => c.trim().length > 0)
    if (!yourDomain.trim() || validCompetitors.length === 0) return

    const filters: GapFilters = {
      yourDomain: yourDomain.trim(),
      competitors: validCompetitors,
      location: targetLocation,
      minVolume: volumeMin,
      maxDifficulty: difficultyMax,
      gapType,
    }

    onAnalyze(filters)
  }

  function copyKeywords() {
    const keywords = filteredKeywords.map((k) => k.keyword).join("\n")
    navigator.clipboard.writeText(keywords)
  }

  function formatMarkdownContent(content: string): string {
    // Convert markdown-like formatting to HTML for better display
    let formatted = content
      // Headers
      .replace(
        /^### (.*$)/gim,
        '<h3 class="text-lg font-bold mb-2 mt-4" style="color: var(--foreground);">$1</h3>',
      )
      .replace(
        /^## (.*$)/gim,
        '<h2 class="text-xl font-bold mb-3 mt-6" style="color: var(--foreground);">$1</h2>',
      )
      .replace(
        /^# (.*$)/gim,
        '<h1 class="text-2xl font-bold mb-4" style="color: var(--foreground);">$1</h1>',
      )
      // Bold text
      .replace(
        /\*\*(.*?)\*\*/g,
        '<strong class="font-bold" style="color: var(--foreground);">$1</strong>',
      )
      // Bullet points
      .replace(/^[\-\*•] (.*$)/gim, '<li class="ml-6 mb-1 list-disc">$1</li>')
      // Tables - convert markdown tables to HTML
      .replace(/\|(.+)\|/g, (match, content) => {
        const cells = content
          .split("|")
          .map((cell) => cell.trim())
          .filter((cell) => cell.length > 0)
        const isHeader = content.includes("---")
        if (isHeader) {
          return "" // Skip separator rows
        }
        const cellElements = cells
          .map(
            (cell) =>
              `<td class="border px-3 py-2 text-sm" style="border-color: var(--border);">${cell}</td>`,
          )
          .join("")
        return `<tr>${cellElements}</tr>`
      })
      // Wrap table rows in table element
      .replace(
        /(<tr>.*<\/tr>)/gs,
        '<table class="w-full border-collapse border mt-4 mb-4" style="border-color: var(--border);">$1</table>',
      )
      // Line breaks
      .replace(/\n/g, "<br>")

    return formatted
  }

  function exportCSV() {
    const headers = [
      "Keyword",
      "Search Volume",
      "Difficulty",
      "Competition",
      "CPC",
      "Competitor Position",
      "Your Position",
      "Gap Type",
      "Opportunity Score",
    ]
    const rows = filteredKeywords.map((k) => [
      k.keyword,
      k.search_volume,
      k.difficulty,
      k.competition,
      k.cpc.toFixed(2),
      k.competitor_position,
      k.your_position || "Not ranking",
      k.gap_type,
      k.opportunity_score?.toFixed(2) || "",
    ])

    const csv = [headers, ...rows].map((row) => row.join(",")).join("\n")
    const blob = new Blob([csv], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `competitor-gap-analysis-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  // Calculate opportunity scores and filter
  const scoredKeywords = $derived(
    gapKeywords.map((k) => ({
      ...k,
      opportunity_score:
        (k.search_volume / (k.difficulty + 1)) *
        (k.gap_type === "missing" ? 2 : 1),
    })),
  )

  const filteredKeywords = $derived(
    scoredKeywords
      .filter((k) => k.search_volume >= volumeMin)
      .filter((k) => k.difficulty <= difficultyMax)
      .filter((k) => gapType === "all" || k.gap_type === gapType)
      .sort((a, b) => (b.opportunity_score || 0) - (a.opportunity_score || 0)),
  )

  // Quick wins: high volume, low difficulty
  const quickWins = $derived(
    filteredKeywords
      .filter((k) => k.search_volume > 1000 && k.difficulty < 30)
      .slice(0, 10),
  )
</script>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-3">
      <div
        class="w-10 h-10 flex items-center justify-center border-2"
        style="background: var(--primary); border-color: var(--border);"
      >
        <TrendingUp class="w-5 h-5" style="color: var(--primary-foreground);" />
      </div>
      <div>
        <h3 class="text-lg font-bold" style="color: var(--foreground);">
          Competitor Gap Analysis
        </h3>
        <p class="text-sm" style="color: var(--muted-foreground);">
          Find keyword opportunities your competitors are ranking for
        </p>
      </div>
    </div>
  </div>

  <!-- Domain Inputs -->
  <div class="space-y-4">
    <div>
      <label
        class="block text-sm font-medium mb-2"
        style="color: var(--foreground);"
      >
        Your Domain
      </label>
      <input
        bind:value={yourDomain}
        placeholder="example.com"
        class="w-full p-3 border-2"
        style="background: var(--background); border-color: var(--border); color: var(--foreground);"
        disabled={isLoading}
      />
    </div>

    <div>
      <label
        class="block text-sm font-medium mb-2"
        style="color: var(--foreground);"
      >
        Competitor Domains (up to 3)
      </label>
      <div class="space-y-2">
        {#each competitors as _, index}
          <div class="flex gap-2">
            <input
              bind:value={competitors[index]}
              placeholder="competitor{index + 1}.com"
              class="flex-1 p-3 border-2"
              style="background: var(--background); border-color: var(--border); color: var(--foreground);"
              disabled={isLoading}
            />
            {#if competitors.length > 1}
              <button
                on:click={() => removeCompetitor(index)}
                class="btn-secondary p-3"
                disabled={isLoading}
              >
                <X class="w-4 h-4" />
              </button>
            {/if}
          </div>
        {/each}
        {#if competitors.length < 3}
          <button
            on:click={addCompetitor}
            class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"
            disabled={isLoading}
          >
            <Plus class="w-4 h-4" />
            Add Competitor
          </button>
        {/if}
      </div>
    </div>
  </div>

  <!-- Gap Type Selection -->
  <div>
    <label
      class="block text-sm font-medium mb-2"
      style="color: var(--foreground);"
    >
      Analysis Type
    </label>
    <div class="grid md:grid-cols-3 gap-3">
      <button
        on:click={() => (gapType = "missing")}
        class="p-3 border-2 text-left {gapType === 'missing'
          ? 'border-primary'
          : ''}"
        style="background: var(--card); border-color: {gapType === 'missing'
          ? 'var(--primary)'
          : 'var(--border)'};"
        disabled={isLoading}
      >
        <p class="font-medium text-sm" style="color: var(--foreground);">
          Missing Keywords
        </p>
        <p class="text-xs mt-1" style="color: var(--muted-foreground);">
          Keywords competitors rank for but you don't
        </p>
      </button>
      <button
        on:click={() => (gapType = "lower_rank")}
        class="p-3 border-2 text-left {gapType === 'lower_rank'
          ? 'border-primary'
          : ''}"
        style="background: var(--card); border-color: {gapType === 'lower_rank'
          ? 'var(--primary)'
          : 'var(--border)'};"
        disabled={isLoading}
      >
        <p class="font-medium text-sm" style="color: var(--foreground);">
          Lower Rankings
        </p>
        <p class="text-xs mt-1" style="color: var(--muted-foreground);">
          Keywords where competitors outrank you
        </p>
      </button>
      <button
        on:click={() => (gapType = "all")}
        class="p-3 border-2 text-left {gapType === 'all'
          ? 'border-primary'
          : ''}"
        style="background: var(--card); border-color: {gapType === 'all'
          ? 'var(--primary)'
          : 'var(--border)'};"
        disabled={isLoading}
      >
        <p class="font-medium text-sm" style="color: var(--foreground);">
          All Gaps
        </p>
        <p class="text-xs mt-1" style="color: var(--muted-foreground);">
          Show all keyword opportunities
        </p>
      </button>
    </div>
  </div>

  <!-- Filters -->
  <div>
    <button
      on:click={() => (showFilters = !showFilters)}
      class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"
      disabled={isLoading}
    >
      <Filter class="w-4 h-4" />
      Advanced Filters
      <span
        class="text-xs {showFilters ? 'rotate-180' : ''} transition-transform"
        >▼</span
      >
    </button>

    {#if showFilters}
      <div
        class="mt-4 p-4 border-2 space-y-4"
        style="background: var(--muted); border-color: var(--border);"
      >
        <div class="grid md:grid-cols-3 gap-4">
          <div>
            <label
              class="block text-xs font-bold mb-2"
              style="color: var(--foreground);"
            >
              Target Location
            </label>
            <select
              bind:value={targetLocation}
              class="w-full p-2 border-2 text-sm"
              style="background: var(--background); border-color: var(--border); color: var(--foreground);"
              disabled={isLoading}
            >
              <option value="United States">United States</option>
              <option value="United Kingdom">United Kingdom</option>
              <option value="Canada">Canada</option>
              <option value="Australia">Australia</option>
              <option value="Global">Global</option>
            </select>
          </div>
          <div>
            <label
              class="block text-xs font-bold mb-2"
              style="color: var(--foreground);"
            >
              Min Volume
            </label>
            <input
              type="number"
              bind:value={volumeMin}
              class="w-full p-2 border-2 text-sm"
              style="background: var(--background); border-color: var(--border); color: var(--foreground);"
              disabled={isLoading}
            />
          </div>
          <div>
            <label
              class="block text-xs font-bold mb-2"
              style="color: var(--foreground);"
            >
              Max Difficulty
            </label>
            <input
              type="number"
              bind:value={difficultyMax}
              max="100"
              class="w-full p-2 border-2 text-sm"
              style="background: var(--background); border-color: var(--border); color: var(--foreground);"
              disabled={isLoading}
            />
          </div>
        </div>
      </div>
    {/if}
  </div>

  <!-- Analyze Button -->
  <button
    on:click={handleAnalyze}
    disabled={!yourDomain.trim() ||
      competitors.filter((c) => c.trim()).length === 0 ||
      isLoading}
    class="btn-primary px-6 py-3 font-bold flex items-center gap-2 w-full md:w-auto"
  >
    {#if isLoading}
      <Loader2 class="w-4 h-4 animate-spin" />
      Analyzing Competitor Gaps...
    {:else}
      <Target class="w-4 h-4" />
      Analyze Competitor Gaps
    {/if}
  </button>

  <!-- Progress Tracking Section -->
  {#if isLoading && progressSteps.length > 0}
    <div
      class="border-2 p-6"
      style="background: var(--card); border-color: var(--border);"
      transition:slide={{ duration: 300 }}
    >
      <h4 class="font-bold mb-4" style="color: var(--foreground);">
        Analyzing Competitor Gaps
      </h4>

      <!-- Progress Timeline -->
      <div class="space-y-4">
        {#each progressSteps as step (step.id)}
          <div
            class="flex items-start gap-3"
            transition:slide={{ duration: 300 }}
          >
            <div class="flex-shrink-0 mt-0.5">
              {#if step.status === "completed"}
                <div transition:fade={{ duration: 200 }}>
                  <CheckCircle2
                    class="w-5 h-5 animate-scale-in"
                    style="color: var(--primary);"
                  />
                </div>
              {:else if step.status === "active"}
                <Loader2
                  class="w-5 h-5 animate-spin"
                  style="color: var(--primary);"
                />
              {:else}
                <Circle
                  class="w-5 h-5 opacity-30"
                  style="color: var(--muted-foreground);"
                />
              {/if}
            </div>
            <div class="flex-1">
              <h5
                class="text-sm font-bold mb-1"
                style="color: {step.status === 'pending'
                  ? 'var(--muted-foreground)'
                  : 'var(--foreground)'};
                         opacity: {step.status === 'pending' ? '0.5' : '1'}"
              >
                {step.title}
              </h5>
              <p
                class="text-xs"
                style="color: var(--muted-foreground);
                        opacity: {step.status === 'pending' ? '0.5' : '1'}"
              >
                {step.description}
              </p>
              {#if step.status === "active" && step.progress}
                <div
                  class="mt-2 h-1 rounded-full overflow-hidden"
                  style="background: var(--muted);"
                >
                  <div
                    class="h-full transition-all duration-500 ease-out"
                    style="background: var(--primary); width: {step.progress}%"
                  ></div>
                </div>
              {/if}
            </div>
          </div>
        {/each}
      </div>

      <!-- Overall Progress Bar -->
      <div class="mt-6 pt-4 border-t" style="border-color: var(--border);">
        <div class="flex items-center justify-between mb-2">
          <span
            class="text-xs font-medium"
            style="color: var(--muted-foreground);"
          >
            Overall Progress
          </span>
          <span class="text-xs font-bold" style="color: var(--foreground);">
            {currentProgress}%
          </span>
        </div>
        <div
          class="h-2 rounded-full overflow-hidden"
          style="background: var(--muted);"
        >
          <div
            class="h-full transition-all duration-500 ease-out"
            style="background: linear-gradient(to right, var(--primary), var(--accent)); 
                      width: {currentProgress}%"
          ></div>
        </div>
      </div>
    </div>
  {/if}

  <!-- Quick Wins Section -->
  {#if quickWins.length > 0 && !isLoading}
    <div
      class="border-2 p-4"
      style="background: var(--accent); border-color: var(--border);"
    >
      <div class="flex items-center gap-2 mb-3">
        <AlertCircle class="w-5 h-5" style="color: var(--accent-foreground);" />
        <h4 class="font-bold" style="color: var(--accent-foreground);">
          Quick Win Opportunities
        </h4>
      </div>
      <div class="space-y-2">
        {#each quickWins as keyword}
          <div
            class="flex items-center justify-between p-2 border"
            style="background: var(--background); border-color: var(--border);"
          >
            <span class="text-sm font-medium" style="color: var(--foreground);">
              {keyword.keyword}
            </span>
            <div class="flex items-center gap-3 text-xs">
              <span style="color: var(--muted-foreground);"
                >Vol: {keyword.search_volume.toLocaleString()}</span
              >
              <span
                class="px-2 py-1 rounded"
                style="background: var(--primary); color: var(--primary-foreground);"
              >
                Difficulty: {keyword.difficulty}
              </span>
            </div>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- Mock Data Warning -->
  {#if isMockData && filteredKeywords.length > 0 && !isLoading}
    <div
      class="border-2 p-4 mb-4"
      style="background: #fef3c7; border-color: #f59e0b;"
    >
      <div class="flex items-center gap-2 mb-2">
        <AlertCircle class="w-5 h-5" style="color: #f59e0b;" />
        <h4 class="font-bold text-sm" style="color: #78350f;">
          Demo Data Notice
        </h4>
      </div>
      <p class="text-sm" style="color: #78350f;">
        You're viewing sample data. To get real keyword analysis for your
        domains, please configure your DataForSEO API credentials in the
        environment settings.
      </p>
      <p class="text-xs mt-2" style="color: #78350f;">
        The keywords shown below are generic examples and not specific to your
        actual domain.
      </p>
    </div>
  {/if}

  <!-- Results -->
  {#if filteredKeywords.length > 0 && !isLoading}
    <div
      class="border-2 p-6"
      style="background: var(--card); border-color: var(--border);"
    >
      <div class="flex items-center justify-between mb-4">
        <h4 class="font-bold" style="color: var(--foreground);">
          Gap Analysis Results ({filteredKeywords.length})
          {#if isMockData}
            <span
              class="text-xs font-normal px-2 py-1 rounded ml-2"
              style="background: #fef3c7; color: #78350f;"
            >
              DEMO DATA
            </span>
          {/if}
        </h4>
        <div class="flex gap-2">
          <button
            on:click={copyKeywords}
            class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
          >
            <Copy class="w-3 h-3" />
            Copy
          </button>
          <button
            on:click={exportCSV}
            class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
          >
            <Download class="w-3 h-3" />
            Export CSV
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b" style="border-color: var(--border);">
              <th
                class="text-left py-2 px-3 text-sm font-medium"
                style="color: var(--foreground);"
              >
                Keyword
              </th>
              <th
                class="text-right py-2 px-3 text-sm font-medium"
                style="color: var(--foreground);"
              >
                Volume
              </th>
              <th
                class="text-right py-2 px-3 text-sm font-medium"
                style="color: var(--foreground);"
              >
                Difficulty
              </th>
              <th
                class="text-center py-2 px-3 text-sm font-medium"
                style="color: var(--foreground);"
              >
                Gap Type
              </th>
              <th
                class="text-right py-2 px-3 text-sm font-medium"
                style="color: var(--foreground);"
              >
                Competitor Pos.
              </th>
              <th
                class="text-right py-2 px-3 text-sm font-medium"
                style="color: var(--foreground);"
              >
                Your Pos.
              </th>
              <th
                class="text-right py-2 px-3 text-sm font-medium"
                style="color: var(--foreground);"
              >
                Opportunity
              </th>
            </tr>
          </thead>
          <tbody>
            {#each filteredKeywords as keyword}
              <tr
                class="border-b hover:bg-muted/50"
                style="border-color: var(--border);"
              >
                <td class="py-2 px-3 text-sm" style="color: var(--foreground);">
                  {keyword.keyword}
                </td>
                <td
                  class="py-2 px-3 text-sm text-right"
                  style="color: var(--muted-foreground);"
                >
                  {keyword.search_volume.toLocaleString()}
                </td>
                <td class="py-2 px-3 text-sm text-right">
                  <span
                    class="px-2 py-1 text-xs rounded"
                    style="background: {keyword.difficulty < 30
                      ? 'var(--primary)'
                      : keyword.difficulty < 60
                        ? '#fbbf24'
                        : '#ef4444'}; color: white;"
                  >
                    {keyword.difficulty}
                  </span>
                </td>
                <td class="py-2 px-3 text-sm text-center">
                  <span
                    class="text-xs px-2 py-1 rounded"
                    style="background: {keyword.gap_type === 'missing'
                      ? '#ef4444'
                      : '#fbbf24'}; color: white;"
                  >
                    {keyword.gap_type === "missing"
                      ? "Not Ranking"
                      : "Lower Rank"}
                  </span>
                </td>
                <td
                  class="py-2 px-3 text-sm text-right"
                  style="color: var(--muted-foreground);"
                >
                  #{keyword.competitor_position}
                </td>
                <td
                  class="py-2 px-3 text-sm text-right"
                  style="color: var(--muted-foreground);"
                >
                  {keyword.your_position ? `#${keyword.your_position}` : "-"}
                </td>
                <td class="py-2 px-3 text-sm text-right">
                  <span class="font-medium" style="color: var(--primary);">
                    {keyword.opportunity_score?.toFixed(1) || "-"}
                  </span>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </div>
  {/if}

  <!-- AI Strategy Analysis Section -->
  {#if aiResponse && aiResponse.trim().length > 0 && !isLoading}
    <div
      class="border-2 p-6 mt-6"
      style="background: var(--card); border-color: var(--border);"
    >
      <h3 class="text-lg font-bold mb-4" style="color: var(--foreground);">
        📊 Complete SEO Strategy Analysis
      </h3>
      <div
        class="prose prose-sm max-w-none"
        style="color: var(--muted-foreground);"
      >
        <div class="ai-response-content">
          {@html formatMarkdownContent(aiResponse)}
        </div>
      </div>
    </div>
  {/if}
</div>
