// SEO Agent Components
// Export all SEO-specific components for easy importing

export { default as SEOChatArea } from './SEOChatArea.svelte'
export { default as SEOToolsSidebar } from './SEOToolsSidebar.svelte'
export { default as NicheDiscovery } from './NicheDiscovery.svelte'
export { default as CompetitorGapAnalysis } from './CompetitorGapAnalysis.svelte'

// Type definitions for SEO interfaces
export interface NicheKeyword {
  keyword: string
  search_volume: number
  difficulty: number
  competition: string
  cpc: number
  opportunity_score?: number
}

export interface GapKeyword {
  keyword: string
  search_volume: number
  difficulty: number
  competition: string
  cpc: number
  competitor_position: number
  your_position: number | null
  gap_type: "missing" | "lower_rank" | "opportunity"
  opportunity_score?: number
}

export interface DiscoveryFilters {
  industry: string
  location: string
  volumeRange: { min: number; max: number }
  maxDifficulty: number
}

export interface GapFilters {
  yourDomain: string
  competitors: string[]
  location: string
  minVolume: number
  maxDifficulty: number
  gapType: "missing" | "lower_rank" | "all"
}

export interface SEOProgressStep {
  id: number
  title: string
  description: string
  status: "pending" | "active" | "completed"
  progress?: number
}
