<script lang="ts">
  import { page } from "$app/stores"
  import { writable } from "svelte/store"
  import { onMount, tick } from "svelte"
  import {
    Building2,
    Zap,
    ChevronRight,
    Download,
    Clock,
    User,
    Bot,
    ChartBar,
    Eye,
    MessageSquare,
    CheckCircle2,
    Loader2,
    Circle,
    Search,
    Keyboard,
    History,
  } from "lucide-svelte"
  import SkeletonLoader from "./SkeletonLoader.svelte"
  import ProgressTracker from "./ProgressTracker.svelte"
  import ErrorBoundary from "./ErrorBoundary.svelte"

  // Props for messages and state
  export let messages: any[] = []
  export let isLoading = false
  export let progressSteps: any[] = []
  export let currentProgress = 0
  export let leftSidebarCollapsed = false
  export let rightSidebarCollapsed = false

  // Create a local writable store for messages to enable updates
  const messagesStore = writable(messages)

  // Update the store when messages prop changes
  $: messagesStore.set(messages)

  // Input and state management
  let input = ""
  let outputFormat = "comprehensive"
  let searchQuery = ""
  let showSearch = false
  let isInitialLoad = true
  let retryCount = 0
  let chatContainer: HTMLElement
  let inputElement: HTMLTextAreaElement

  // Output format options
  const outputFormats = [
    {
      value: "comprehensive",
      label: "Comprehensive",
      description: "Full detailed analysis",
    },
    {
      value: "executive",
      label: "Executive Summary",
      description: "High-level overview",
    },
    {
      value: "slide-ready",
      label: "Slide-ready",
      description: "Sections + headers for export",
    },
    {
      value: "battlecard",
      label: "Competitive Battlecard",
      description: "Strategic comparison format",
    },
  ]

  // Placeholder examples for animated cycling
  const placeholderExamples = [
    "Research Stripe's competitive positioning and market strategy...",
    "Analyze OpenAI's business model and recent developments...",
    "Compare Notion vs. Obsidian feature sets and pricing...",
    "Investigate Figma's growth strategy and market expansion...",
    "Study Shopify's competitive advantages in e-commerce...",
  ]

  let currentPlaceholder = placeholderExamples[0]
  let placeholderIndex = 0

  // Use actual messages if provided, otherwise show empty state
  $: displayMessages = $messagesStore.length > 0 ? $messagesStore : []

  function generateId(): string {
    return Math.random().toString(36).substring(2, 11)
  }

  async function sendMessage() {
    if (!input.trim() || isLoading) return

    // Add output format context to the message
    let formatPrefix = ""
    if (outputFormat === "executive") {
      formatPrefix =
        "[Executive Summary Format] Provide concise bullet points, key insights, and executive-level highlights. Focus on high-level strategic information that executives need to know quickly. Use clear sections and numbered citations. "
    } else if (outputFormat === "slide-ready") {
      formatPrefix =
        "[Slide-ready Format] Structure with clear sections and headers suitable for presentation export. Use numbered sections, clear headings, and bullet points that can be easily converted to slides. Include numbered citations for all claims. "
    } else if (outputFormat === "battlecard") {
      formatPrefix =
        "[Competitive Battlecard Format] Focus on strategic comparison and competitive positioning. Emphasize competitive advantages, market differentiation, and head-to-head comparisons. Include numbered citations and competitive analysis. "
    }

    const userMessage = formatPrefix + input.trim()
    input = ""
    isLoading = true

    // Initialize progress steps
    progressSteps = [
      {
        id: 1,
        title: "Initial Analysis",
        description: "Analyzing research request...",
        status: "pending",
      },
      {
        id: 2,
        title: "Web Search",
        description: "Conducting comprehensive search...",
        status: "pending",
      },
      {
        id: 3,
        title: "Financial Analysis",
        description: "Gathering financial data...",
        status: "pending",
      },
      {
        id: 4,
        title: "Market Research",
        description: "Analyzing market position...",
        status: "pending",
      },
      {
        id: 5,
        title: "Report Generation",
        description: "Creating research report...",
        status: "pending",
      },
    ]
    currentProgress = 0

    // Add user message to chat
    const newUserMessage = {
      id: generateId(),
      role: "user",
      content: userMessage,
      timestamp: new Date(),
    }
    messagesStore.update((msgs) => [...msgs, newUserMessage])
    messages = [...messages, newUserMessage]

    try {
      // Use streaming for progress updates
      const response = await fetch(
        `/dashboard/${$page.params.envSlug}/researcher?stream=true`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message: userMessage }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split("\n")

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6))

                if (data.type === "final_response") {
                  // Add assistant response to chat
                  const assistantMessage = {
                    id: generateId(),
                    role: "assistant",
                    content: data.response,
                    timestamp: new Date(),
                    isReport: true,
                  }
                  messagesStore.update((msgs) => [...msgs, assistantMessage])
                  messages = [...messages, assistantMessage]
                } else if (data.step) {
                  // Update progress
                  currentProgress = data.progress
                  progressSteps = progressSteps.map((step) => {
                    if (step.id === data.step) {
                      return {
                        ...step,
                        status: data.status,
                        description: data.action,
                      }
                    } else if (step.id < data.step) {
                      return { ...step, status: "completed" }
                    }
                    return step
                  })
                }
              } catch (e) {
                console.error("Error parsing SSE data:", e)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("Error sending message:", error)
      const errorMessage = {
        id: generateId(),
        role: "assistant",
        content:
          "I apologize, but Compass encountered an error while processing your request. Please try again.",
        timestamp: new Date(),
      }
      messagesStore.update((msgs) => [...msgs, errorMessage])
      messages = [...messages, errorMessage]
    } finally {
      isLoading = false
      // Reset progress
      progressSteps = []
      currentProgress = 0
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
  }

  function handleGlobalKeyDown(event: KeyboardEvent) {
    // Ctrl/Cmd + K for search
    if ((event.ctrlKey || event.metaKey) && event.key === "k") {
      event.preventDefault()
      showSearch = !showSearch
      if (showSearch) {
        tick().then(() => {
          const searchInput = document.querySelector(
            "#search-input",
          ) as HTMLInputElement
          searchInput?.focus()
        })
      }
    }

    // Escape to close search or cancel loading
    if (event.key === "Escape") {
      if (showSearch) {
        showSearch = false
        searchQuery = ""
      } else if (isLoading) {
        // Cancel current request (would need API support)
        console.log("Cancel request requested")
      }
    }

    // Ctrl/Cmd + Enter to send message
    if (
      (event.ctrlKey || event.metaKey) &&
      event.key === "Enter" &&
      !isLoading
    ) {
      sendMessage()
    }

    // Focus input with '/' key
    if (
      event.key === "/" &&
      !showSearch &&
      document.activeElement?.tagName !== "INPUT" &&
      document.activeElement?.tagName !== "TEXTAREA"
    ) {
      event.preventDefault()
      inputElement?.focus()
    }
  }

  function scrollToBottom() {
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight
    }
  }

  function retryLastMessage() {
    retryCount++
    sendMessage()
  }

  function searchMessages(query: string) {
    if (!query.trim()) return displayMessages

    const lowercaseQuery = query.toLowerCase()
    return displayMessages.filter((message) =>
      message.content.toLowerCase().includes(lowercaseQuery),
    )
  }

  $: filteredMessages = searchQuery
    ? searchMessages(searchQuery)
    : displayMessages

  function extractInsights(content: string): {
    summary: string
    insights: string[]
    badges: string[]
  } {
    // Simple insight extraction - in a real app this would be more sophisticated
    const lines = content.split("\n").filter((line) => line.trim())
    const summary = lines.slice(0, 2).join(" ").substring(0, 200) + "..."

    const insights = lines
      .filter(
        (line) =>
          line.includes("key") ||
          line.includes("important") ||
          line.includes("significant"),
      )
      .slice(0, 3)

    const badges = []
    if (content.includes("growth") || content.includes("increase"))
      badges.push("↑ Trending")
    if (content.includes("insight") || content.includes("analysis"))
      badges.push("💡 Insight")
    if (content.includes("challenge") || content.includes("weakness"))
      badges.push("⚠ Weakness")

    return { summary, insights, badges }
  }

  function downloadAsMarkdown(message: any) {
    const companyName = extractCompanyName(message.content)
    const timestamp = message.timestamp.toISOString().split("T")[0]
    const filename = `${companyName || "research-report"}-${timestamp}.md`

    const markdownContent = `# Marketing Research Report by Compass
**Generated on:** ${message.timestamp.toLocaleDateString()}
**Time:** ${message.timestamp.toLocaleTimeString()}
**Format:** ${outputFormat.charAt(0).toUpperCase() + outputFormat.slice(1)}

---

${message.content}

---

*Report generated by Compass - Your AI Market Researcher*
`

    const blob = new Blob([markdownContent], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }

  function extractCompanyName(content: string): string {
    // Simple extraction - looks for company name in the first line or headers
    const lines = content.split("\n")
    for (const line of lines.slice(0, 5)) {
      if (line.includes("Company:") || line.includes("Company Name:")) {
        return (
          line
            .split(":")[1]
            ?.trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
      if (
        line.startsWith("# ") &&
        !line.includes("Research") &&
        !line.includes("Report")
      ) {
        return (
          line
            .replace("# ", "")
            .trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
    }
    return ""
  }

  function formatContent(content: string): string {
    // Enhanced markdown to HTML conversion for professional display
    let formatted = content
      // Headers
      .replace(
        /^# (.*$)/gim,
        '<h1 class="text-3xl font-bold mb-6 mt-8 first:mt-0" style="color: var(--foreground); border-bottom: 2px solid var(--border); padding-bottom: 0.5rem;">$1</h1>',
      )
      .replace(
        /^## (.*$)/gim,
        '<h2 class="text-2xl font-bold mb-4 mt-8" style="color: var(--foreground);">$1</h2>',
      )
      .replace(
        /^### (.*$)/gim,
        '<h3 class="text-xl font-semibold mb-3 mt-6" style="color: var(--foreground);">$1</h3>',
      )
      .replace(
        /^#### (.*$)/gim,
        '<h4 class="text-lg font-semibold mb-2 mt-4" style="color: var(--foreground);">$1</h4>',
      )

      // Bold text
      .replace(
        /\*\*(.*?)\*\*/g,
        '<strong class="font-bold" style="color: var(--foreground);">$1</strong>',
      )

      // Italic text
      .replace(
        /\*(.*?)\*/g,
        '<em class="italic" style="color: var(--muted-foreground);">$1</em>',
      )

      // Code blocks
      .replace(
        /```([\s\S]*?)```/g,
        '<pre class="bg-muted p-4 rounded border-2 border-border my-4 overflow-x-auto"><code class="text-sm font-mono" style="color: var(--foreground);">$1</code></pre>',
      )

      // Inline code
      .replace(
        /`([^`]+)`/g,
        '<code class="bg-muted px-2 py-1 rounded text-sm font-mono" style="color: var(--foreground);">$1</code>',
      )

      // Lists
      .replace(
        /^[\s]*[-*+] (.+)$/gim,
        '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$1</li>',
      )
      // Numbered lists - but avoid matching citation references in "Sources and References" section
      .replace(
        /^[\s]*(\d+)\.\s+(.+)$/gim,
        '<li class="mb-2 ml-6" style="color: var(--muted-foreground); list-style-type: disc;">$2</li>',
      )

      // Blockquotes
      .replace(
        /^> (.+)$/gim,
        '<blockquote class="border-l-4 border-primary pl-4 italic my-4" style="color: var(--muted-foreground);">$1</blockquote>',
      )

      // Citations - Handle [1], [2], [3] etc. (but not markdown links)
      .replace(
        /\[(\d+)\]/g,
        '<sup class="citation-number bg-primary text-primary-foreground px-1 py-0.5 rounded text-xs font-bold ml-1">[$1]</sup>',
      )

      // Links
      .replace(
        /\[([^\]]+)\]\(([^)]+)\)/g,
        '<a href="$2" class="text-primary underline hover:opacity-70" target="_blank" rel="noopener noreferrer">$1</a>',
      )

      // Line breaks and paragraphs
      .replace(
        /\n\n/g,
        '</p><p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">',
      )
      .replace(/\n/g, "<br>")

    // Wrap content in paragraph if it doesn't start with a block element
    if (
      !formatted.startsWith("<h") &&
      !formatted.startsWith("<p") &&
      !formatted.startsWith("<ul") &&
      !formatted.startsWith("<ol") &&
      !formatted.startsWith("<blockquote")
    ) {
      formatted =
        '<p class="mb-4 leading-relaxed" style="color: var(--muted-foreground);">' +
        formatted +
        "</p>"
    }

    // Wrap lists in proper ul/ol tags
    formatted = formatted.replace(/(<li[^>]*>.*?<\/li>)/gs, (match) => {
      if (match.includes("list-style-type: disc")) {
        return '<ul class="mb-4">' + match + "</ul>"
      } else if (match.includes("list-style-type: decimal")) {
        return '<ol class="mb-4">' + match + "</ol>"
      }
      return match
    })

    return formatted
  }

  // Animated placeholder effect and setup
  onMount(() => {
    currentPlaceholder = placeholderExamples[0]
    isInitialLoad = false

    const interval = setInterval(() => {
      placeholderIndex = (placeholderIndex + 1) % placeholderExamples.length
      currentPlaceholder = placeholderExamples[placeholderIndex]
    }, 4000)

    // Add global keyboard listeners
    document.addEventListener("keydown", handleGlobalKeyDown)

    // Auto-scroll to bottom when new messages arrive
    const unsubscribe = messagesStore.subscribe(() => {
      tick().then(scrollToBottom)
    })

    return () => {
      clearInterval(interval)
      document.removeEventListener("keydown", handleGlobalKeyDown)
      unsubscribe()
    }
  })
</script>

<!-- Main Content Area -->
<main class="flex-1 flex flex-col bg-background">
  <!-- Header -->
  <header
    class="flex items-center justify-between p-6 border-b border-border bg-background"
  >
    <div class="flex items-center">
      <!-- Breadcrumb Navigation -->
      <nav
        class="flex items-center space-x-2 text-sm text-muted-foreground mb-2"
      >
        <a
          href="/dashboard/{$page.params.envSlug}"
          class="hover:text-foreground transition-colors"
        >
          Dashboard
        </a>
        <ChevronRight class="w-4 h-4" />
        <span class="text-foreground font-medium">Research Agent</span>
      </nav>
    </div>

    <!-- Header Actions -->
    <div class="flex items-center space-x-2">
      <!-- Search Toggle -->
      <button
        on:click={() => (showSearch = !showSearch)}
        class="p-2 hover:bg-accent rounded-lg transition-colors"
        title="Search messages (Ctrl+K)"
      >
        <Search class="w-4 h-4" />
      </button>

      <!-- Keyboard Shortcuts Info -->
      <button
        class="p-2 hover:bg-accent rounded-lg transition-colors"
        title="Keyboard shortcuts: Ctrl+K (search), / (focus input), Ctrl+Enter (send)"
      >
        <Keyboard class="w-4 h-4" />
      </button>

      <!-- Chat History -->
      <button
        class="p-2 hover:bg-accent rounded-lg transition-colors"
        title="Chat history"
      >
        <History class="w-4 h-4" />
      </button>

      <span
        class="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full"
      >
        Online
      </span>
    </div>
  </header>

  <!-- Search Bar -->
  {#if showSearch}
    <div class="px-6 py-3 border-b border-border bg-muted/30">
      <div class="max-w-4xl mx-auto relative">
        <input
          id="search-input"
          bind:value={searchQuery}
          placeholder="Search messages..."
          class="w-full px-4 py-2 pl-10 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-ring bg-background text-foreground"
        />
        <Search
          class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"
        />
        {#if searchQuery}
          <span
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground"
          >
            {filteredMessages.length} results
          </span>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Agent Branding -->
  <div class="px-6 py-4 border-b border-border">
    <div class="flex items-center space-x-4">
      <div
        class="w-12 h-12 flex items-center justify-center border-2 border-border bg-primary"
        style="box-shadow: var(--shadow-sm);"
      >
        <Building2 class="w-6 h-6 text-primary-foreground" />
      </div>
      <div>
        <h1 class="text-3xl font-black flex items-center gap-3 text-foreground">
          <Zap class="w-8 h-8 text-primary" />
          Compass
        </h1>
        <p class="text-muted-foreground">
          AI-powered research assistant for competitive analysis and market
          insights
        </p>
      </div>
    </div>
  </div>

  <!-- Chat Messages Area -->
  <div bind:this={chatContainer} class="flex-1 overflow-y-auto p-6">
    <div class="max-w-4xl mx-auto space-y-6">
      <ErrorBoundary onRetry={retryLastMessage}>
        {#if isInitialLoad}
          <!-- Initial Loading State -->
          <SkeletonLoader type="message" count={2} />
        {:else if filteredMessages.length === 0 && !searchQuery}
          <!-- Empty State -->
          <div class="text-center py-8">
            <div class="flex items-center justify-center gap-2 mb-6">
              <Zap class="w-6 h-6" style="color: var(--primary);" />
              <h3 class="text-2xl font-bold" style="color: var(--foreground);">
                Ready to Research
              </h3>
            </div>
            <p
              class="font-medium mb-8 max-w-2xl mx-auto"
              style="color: var(--muted-foreground);"
            >
              Get marketing intelligence on any company. Ask your research
              question below.
            </p>

            <!-- Keyboard Shortcuts Hint -->
            <div class="text-xs text-muted-foreground space-y-1">
              <p>
                <kbd class="px-2 py-1 bg-muted rounded">Ctrl+K</kbd> to search
              </p>
              <p>
                <kbd class="px-2 py-1 bg-muted rounded">/</kbd> to focus input
              </p>
              <p>
                <kbd class="px-2 py-1 bg-muted rounded">Ctrl+Enter</kbd> to send
              </p>
            </div>
          </div>
        {:else if filteredMessages.length === 0 && searchQuery}
          <!-- No Search Results -->
          <div class="text-center py-8">
            <Search class="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 class="text-lg font-semibold text-foreground mb-2">
              No results found
            </h3>
            <p class="text-muted-foreground">
              No messages match your search for "{searchQuery}"
            </p>
          </div>
        {/if}

        {#each filteredMessages as message (message.id)}
          <div class="flex items-start space-x-4">
            <div
              class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
              style="background: var(--{message.role === 'user'
                ? 'primary'
                : 'secondary'}); border-color: var(--border); box-shadow: var(--shadow-sm);"
            >
              {#if message.role === "user"}
                <User
                  class="w-5 h-5"
                  style="color: var(--primary-foreground);"
                />
              {:else}
                <Bot
                  class="w-5 h-5"
                  style="color: var(--secondary-foreground);"
                />
              {/if}
            </div>

            <div class="flex-1 max-w-4xl">
              <div class="flex items-center gap-2 mb-2">
                <span
                  class="text-sm font-bold"
                  style="color: var(--foreground);"
                >
                  {message.role === "user" ? "You" : "Compass"}
                </span>
                <div class="flex items-center gap-1">
                  <Clock
                    class="w-3 h-3"
                    style="color: var(--muted-foreground);"
                  />
                  <span class="text-xs" style="color: var(--muted-foreground);">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                {#if message.role === "assistant" && message.isReport}
                  <button
                    on:click={() => downloadAsMarkdown(message)}
                    class="btn-secondary px-2 py-1 text-xs flex items-center gap-1"
                    title="Download as Markdown"
                  >
                    <Download class="w-3 h-3" />
                    Download
                  </button>
                {/if}
              </div>

              {#if message.role === "user"}
                <div
                  class="p-4 border-2"
                  style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                >
                  <p
                    class="font-medium"
                    style="color: var(--primary-foreground);"
                  >
                    {message.content}
                  </p>
                </div>
              {:else}
                {@const insights = extractInsights(message.content)}
                <div
                  class="border-2"
                  style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow); border-radius: 0.5rem;"
                >
                  <!-- TL;DR Summary -->
                  {#if insights.summary}
                    <div class="p-4 border-b-2 border-border bg-muted/50">
                      <div class="flex items-start gap-2">
                        <span
                          class="text-xs font-bold px-2 py-1 bg-primary text-primary-foreground rounded"
                          >TL;DR</span
                        >
                        <div
                          class="text-sm font-medium formatted-summary"
                          style="color: var(--foreground);"
                        >
                          {@html formatContent(insights.summary)}
                        </div>
                      </div>
                    </div>
                  {/if}

                  <!-- Badges -->
                  {#if insights.badges.length > 0}
                    <div class="px-6 pt-4 pb-2">
                      <div class="flex flex-wrap gap-2">
                        {#each insights.badges as badge}
                          <span
                            class="text-xs font-bold px-2 py-1 border border-border rounded"
                            style="background: var(--accent); color: var(--accent-foreground);"
                          >
                            {badge}
                          </span>
                        {/each}
                      </div>
                    </div>
                  {/if}

                  <!-- Main Content -->
                  <div class="p-6">
                    <div class="formatted-content max-w-none">
                      {@html formatContent(message.content)}
                    </div>
                  </div>

                  <!-- Follow-up Actions -->
                  <div class="px-6 pb-4 border-t border-border">
                    <div class="flex flex-wrap gap-2 mt-4">
                      <button
                        on:click={() =>
                          (input = `Compare this analysis with their main competitor`)}
                        class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"
                      >
                        <Eye class="w-3 h-3" />
                        Compare with competitor
                      </button>
                      <button
                        on:click={() =>
                          (input = `Add visual charts and graphs to this analysis`)}
                        class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"
                      >
                        <ChartBar class="w-3 h-3" />
                        Add visuals
                      </button>
                      <button
                        on:click={() =>
                          (input = `Turn this analysis into presentation slides`)}
                        class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors rounded flex items-center gap-1"
                      >
                        <MessageSquare class="w-3 h-3" />
                        Turn into slides
                      </button>
                    </div>
                  </div>
                </div>
              {/if}
            </div>
          </div>
        {/each}

        <!-- Progress Tracking (if active) -->
        {#if isLoading && progressSteps.length > 0}
          <div class="border-2 border-border bg-background p-4 rounded-lg">
            <h3 class="font-semibold text-foreground mb-3">
              Research Progress
            </h3>
            <div class="space-y-2">
              {#each progressSteps as step}
                <div class="flex items-center space-x-3">
                  <div
                    class="w-4 h-4 rounded-full {step.status === 'completed'
                      ? 'bg-green-500'
                      : step.status === 'active'
                        ? 'bg-primary'
                        : 'bg-muted'}"
                  ></div>
                  <span
                    class="text-sm {step.status === 'completed'
                      ? 'text-green-600'
                      : 'text-foreground'}">{step.title}</span
                  >
                </div>
              {/each}
            </div>
            {#if currentProgress > 0}
              <div class="mt-3">
                <div class="w-full bg-muted rounded-full h-2">
                  <div
                    class="bg-primary h-2 rounded-full transition-all duration-300"
                    style="width: {currentProgress}%"
                  ></div>
                </div>
                <p class="text-xs text-muted-foreground mt-1">
                  {currentProgress}% complete
                </p>
              </div>
            {/if}
          </div>
        {/if}
      </ErrorBoundary>
    </div>
  </div>

  <!-- Progress Tracker -->
  <ProgressTracker
    steps={progressSteps}
    {currentProgress}
    isVisible={isLoading && progressSteps.length > 0}
  />

  <!-- Input Area -->
  <div class="p-6 border-t border-border bg-background">
    <div class="max-w-4xl mx-auto">
      <!-- Output Format Selector -->
      <div class="flex items-center gap-2 mb-4">
        <span class="text-sm font-bold" style="color: var(--muted-foreground);"
          >Format:</span
        >
        <select
          bind:value={outputFormat}
          class="px-3 py-1 text-sm border-2 border-border bg-card text-foreground font-medium rounded"
          style="border-radius: 0.375rem;"
        >
          {#each outputFormats as format}
            <option value={format.value}>{format.label}</option>
          {/each}
        </select>
      </div>

      <div class="relative">
        <textarea
          bind:this={inputElement}
          bind:value={input}
          on:keydown={handleKeyDown}
          placeholder={isLoading ? "Researching..." : currentPlaceholder}
          class="w-full px-4 py-3 pr-32 border border-border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent text-foreground bg-background placeholder:text-muted-foreground transition-all duration-200"
          class:opacity-50={isLoading}
          rows="1"
          style="min-height: 48px; max-height: 120px;"
          disabled={isLoading}
        ></textarea>
        <div class="absolute right-2 bottom-2 flex items-center space-x-2">
          <button class="p-2 hover:bg-accent rounded-lg transition-colors">
            <svg
              class="w-4 h-4 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
              ></path>
            </svg>
          </button>
          <button class="p-2 hover:bg-accent rounded-lg transition-colors">
            <svg
              class="w-4 h-4 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
              ></path>
            </svg>
          </button>
          <button
            on:click={sendMessage}
            disabled={!input.trim() || isLoading}
            class="p-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95"
            title={isLoading ? "Researching..." : "Send message (Ctrl+Enter)"}
          >
            {#if isLoading}
              <div
                class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"
              ></div>
            {:else}
              <Search class="w-4 h-4" />
            {/if}
          </button>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center space-x-4 mt-3">
        <button
          class="flex items-center px-3 py-1.5 text-sm text-muted-foreground hover:bg-accent rounded-lg border border-border transition-colors"
        >
          <svg
            class="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
            ></path>
          </svg>
          Attach
        </button>
        <button
          class="flex items-center px-3 py-1.5 text-sm text-muted-foreground hover:bg-accent rounded-lg border border-border transition-colors"
        >
          <svg
            class="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
            ></path>
          </svg>
          Voice Message
        </button>
        <button
          class="flex items-center px-3 py-1.5 text-sm text-primary-foreground bg-primary hover:bg-primary/90 rounded-lg border border-primary transition-colors"
        >
          <svg
            class="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
            ></path>
          </svg>
          Browse Prompts
        </button>
        <div class="ml-auto text-xs text-muted-foreground">0 / 3,000</div>
      </div>

      <!-- Disclaimer -->
      <p class="text-xs text-muted-foreground text-center mt-4">
        Research may generate inaccurate information about companies, markets,
        or facts. Model: Compass AI v1.3
      </p>
    </div>
  </div>
</main>

<style>
  /* Enhanced animations and micro-interactions */
  .formatted-content {
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Smooth hover effects for interactive elements */
  button:hover {
    transform: translateY(-1px);
  }

  button:active {
    transform: translateY(0);
  }

  /* Enhanced focus states */
  textarea:focus,
  input:focus {
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  }

  /* Loading pulse animation */
  .loading-pulse {
    animation: pulse 1.5s ease-in-out infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* Smooth transitions for all interactive elements */
  * {
    transition:
      transform 0.2s ease,
      opacity 0.2s ease,
      box-shadow 0.2s ease;
  }

  /* Custom scrollbar for chat area */
  .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
  }

  .overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
  }

  .overflow-y-auto::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 3px;
  }

  .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
</style>
