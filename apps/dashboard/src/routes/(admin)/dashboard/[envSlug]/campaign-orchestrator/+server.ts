import { json } from "@sveltejs/kit"
import type { <PERSON>quest<PERSON>and<PERSON> } from "./$types"
import { orchestratorAgent } from "$lib/agents"

// Input validation function
function validateCompanyInput(message: string): {
  isValid: boolean
  error?: string
  extractedInput?: string
} {
  const trimmed = message.trim()

  if (trimmed.length === 0) {
    return { isValid: false, error: "Company name or domain cannot be empty" }
  }

  if (trimmed.length > 200) {
    return {
      isValid: false,
      error: "Input too long. Please provide a company name or domain.",
    }
  }

  // Extract company name or domain from common patterns
  const patterns = [
    /(?:research|analyze|tell me about|information on|investigate)\s+(.+?)(?:\s|$)/i,
    /^(.+?)$/i, // Fallback to entire message
  ]

  let extractedInput = trimmed
  for (const pattern of patterns) {
    const match = trimmed.match(pattern)
    if (match && match[1]) {
      extractedInput = match[1].trim()
      break
    }
  }

  // Basic domain validation (optional)
  const domainPattern =
    /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/
  const isDomain = domainPattern.test(extractedInput)

  // Basic company name validation (allow letters, numbers, spaces, common punctuation)
  const companyNamePattern = /^[a-zA-Z0-9\s\-\.\,\&\(\)]+$/
  const isCompanyName = companyNamePattern.test(extractedInput)

  if (!isDomain && !isCompanyName) {
    return {
      isValid: false,
      error:
        "Please provide a valid company name or domain (e.g., 'coreweave.com' or 'CoreWeave')",
    }
  }

  return { isValid: true, extractedInput }
}

// Response formatting and validation function
function formatResponse(agentResponse: string): any {
  try {
    // Try to parse as JSON first
    const parsed = JSON.parse(agentResponse)

    // Validate required structure
    if (parsed.targetCompany && parsed.competitors && parsed.metadata) {
      return {
        success: true,
        data: parsed,
        formatted: true,
      }
    }

    // If not properly structured, wrap in a standard format
    return {
      success: true,
      data: {
        targetCompany: null,
        competitors: [],
        intelligence: {
          market_insights: "Analysis in progress",
          competitive_landscape: "Data being processed",
          key_differentiators: "Information being gathered",
        },
        metadata: {
          totalCompanies: 0,
          totalContacts: 0,
          processingTime: "N/A",
          dataQuality: "Partial",
        },
        raw_response: agentResponse,
      },
      formatted: false,
    }
  } catch (error) {
    // If not JSON, wrap the text response
    return {
      success: true,
      data: {
        targetCompany: null,
        competitors: [],
        intelligence: {
          market_insights: agentResponse,
          competitive_landscape: "Raw text response provided",
          key_differentiators: "See market insights for full analysis",
        },
        metadata: {
          totalCompanies: 0,
          totalContacts: 0,
          processingTime: "N/A",
          dataQuality: "Raw",
        },
      },
      formatted: false,
    }
  }
}

// Keep the original POST handler for non-streaming requests
export const POST: RequestHandler = async ({ request, locals, url }) => {
  // Verify user is authenticated
  const { session } = locals.auth
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 })
  }

  // Check if client wants streaming response
  const wantsStream = url.searchParams.get("stream") === "true"

  if (wantsStream) {
    return handleStreamingRequest(request)
  }

  try {
    const { message } = await request.json()

    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 })
    }

    // Validate and extract company input
    const validationResult = validateCompanyInput(message)
    if (!validationResult.isValid) {
      return json({ error: validationResult.error }, { status: 400 })
    }

    console.log(
      "Campaign Orchestrator (Company Research) request received:",
      message.substring(0, 100) + "...",
    )

    // Generate response using the orchestrator agent with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () =>
          reject(
            new Error("Agent timeout after 120 seconds - try a simpler query"),
          ),
        120000,
      )
    })

    const agentPromise = orchestratorAgent.generate([
      {
        role: "user",
        content: message,
      },
    ])

    const response: any = await Promise.race([agentPromise, timeoutPromise])

    console.log(
      "Campaign Orchestrator agent response generated, length:",
      response.text?.length || 0,
    )

    // Parse the JSON response from the agent
    let parsedData = null
    let responseText = response.text

    try {
      // Try to extract and parse JSON from the response
      const jsonMatch = response.text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        parsedData = JSON.parse(jsonMatch[0])
        console.log("Successfully parsed JSON data from agent response")

        // Create a cleaner response text for display
        responseText = `Based on comprehensive research, I've analyzed ${parsedData.targetCompany?.name || "the target company"} and identified ${parsedData.competitors?.length || 0} key competitors with detailed intelligence.`
      }
    } catch (error) {
      console.log("Could not parse JSON from agent response, using raw text")
    }

    // Format and validate the response
    const formattedResponse = formatResponse(responseText)

    return json({
      response: responseText,
      data: parsedData || formattedResponse.data,
      formatted: formattedResponse.formatted,
      success: formattedResponse.success,
    })
  } catch (error) {
    console.error("Campaign Orchestrator agent error:", error)

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes("timeout")) {
        return json(
          {
            error:
              "Request timeout - the research process took too long. Please try with a simpler query.",
            type: "timeout",
          },
          { status: 408 },
        )
      }

      if (error.message.includes("API")) {
        return json(
          {
            error:
              "External API error - there was an issue with our research services. Please try again later.",
            type: "api_error",
          },
          { status: 502 },
        )
      }

      if (error.message.includes("rate limit")) {
        return json(
          {
            error:
              "Rate limit exceeded - please wait a moment before trying again.",
            type: "rate_limit",
          },
          { status: 429 },
        )
      }
    }

    return json(
      {
        error: "An error occurred processing your request. Please try again.",
        type: "internal_error",
      },
      { status: 500 },
    )
  }
}

// Streaming request handler
async function handleStreamingRequest(request: Request) {
  try {
    const { message } = await request.json()

    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 })
    }

    // Validate and extract company input
    const validationResult = validateCompanyInput(message)
    if (!validationResult.isValid) {
      return json({ error: validationResult.error }, { status: 400 })
    }

    console.log(
      "Campaign Orchestrator streaming request:",
      message.substring(0, 100),
    )

    const stream = new ReadableStream({
      start(controller) {
        const encoder = new TextEncoder()

        const send = (data: any) => {
          const chunk = encoder.encode(`data: ${JSON.stringify(data)}\n\n`)
          controller.enqueue(chunk)
        }

        // Start the orchestration process
        performOrchestrationWithProgress(message, send)
          .then(() => {
            controller.close()
          })
          .catch((error) => {
            console.error("Streaming orchestration error:", error)

            let errorMessage = "Error occurred during processing"
            let errorType = "internal_error"

            if (error instanceof Error) {
              if (error.message.includes("timeout")) {
                errorMessage =
                  "Request timeout - please try with a simpler query"
                errorType = "timeout"
              } else if (error.message.includes("API")) {
                errorMessage = "External API error - please try again later"
                errorType = "api_error"
              } else if (error.message.includes("rate limit")) {
                errorMessage =
                  "Rate limit exceeded - please wait before trying again"
                errorType = "rate_limit"
              }
            }

            send({
              step: -1,
              action: errorMessage,
              error: error.message,
              error_type: errorType,
              status: "error",
            })
            controller.close()
          })
      },
    })

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    })
  } catch (error) {
    console.error("Streaming setup error:", error)
    return json(
      { error: "An error occurred setting up the stream" },
      { status: 500 },
    )
  }
}

// Progress-based orchestration function
async function performOrchestrationWithProgress(
  message: string,
  send: (data: any) => void,
) {
  const steps = [
    "Processing input and extracting company information",
    "Enriching target company data with Apollo API",
    "Gathering competitive intelligence with Exa search",
    "Discovering similar companies and competitors",
    "Retrieving contact information for all companies",
    "Formatting comprehensive research results",
  ]

  try {
    // Step 1: Input processing
    send({
      step: 1,
      action: steps[0],
      progress: 10,
      status: "processing",
    })

    await new Promise((resolve) => setTimeout(resolve, 1000))

    // Step 2-6: Agent processing with progress updates
    for (let i = 1; i < steps.length - 1; i++) {
      send({
        step: i + 1,
        action: steps[i],
        progress: 10 + i * 15,
        status: "processing",
      })
      await new Promise((resolve) => setTimeout(resolve, 2000))
    }

    // Final agent call
    console.log("Starting orchestrator agent generation...")

    let response: any
    try {
      response = await Promise.race([
        orchestratorAgent.generate([
          {
            role: "user",
            content: message,
          },
        ]),
        new Promise((_, reject) =>
          setTimeout(
            () =>
              reject(new Error("Agent generation timeout after 120 seconds")),
            120000,
          ),
        ),
      ])

      console.log("Orchestrator agent generation completed")
    } catch (error) {
      console.error("Orchestrator agent generation failed:", error)
      throw error
    }

    // Parse the JSON response from the agent
    let parsedData = null
    let responseText = response.text

    try {
      // Try to extract and parse JSON from the response
      const jsonMatch = response.text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        parsedData = JSON.parse(jsonMatch[0])
        console.log("Successfully parsed JSON data from agent response")

        // Create a cleaner response text for display
        responseText = `Based on comprehensive research, I've analyzed ${parsedData.targetCompany?.name || "the target company"} and identified ${parsedData.competitors?.length || 0} key competitors with detailed intelligence.`
      }
    } catch (error) {
      console.log("Could not parse JSON from agent response, using raw text")
    }

    send({
      step: 6,
      action: steps[5],
      progress: 100,
      status: "completed",
      response: responseText,
      data: parsedData,
    })
  } catch (error) {
    console.error("Orchestration process failed:", error)
    throw error
  }
}

export const GET: RequestHandler = async () => {
  return json({
    message: "Catalyst - AI Campaign Orchestrator",
    description:
      "Company research orchestrator with integrated Apollo and Exa APIs",
    endpoints: {
      "POST /": "Submit company research request",
      "POST /?stream=true": "Submit request with streaming progress updates",
    },
    usage: {
      input:
        "Domain name or company name (e.g., 'coreweave.com', 'singlestore')",
      output:
        "Comprehensive company intelligence with competitor analysis and contacts",
    },
  })
}
