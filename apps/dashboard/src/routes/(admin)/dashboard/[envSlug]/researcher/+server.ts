import { json } from "@sveltejs/kit"
import type { Request<PERSON><PERSON><PERSON> } from "./$types"
import { companyResearcherAgent } from "$lib/agents"

// Keep the original POST handler for non-streaming requests
export const POST: RequestHandler = async ({ request, locals, url }) => {
  // Verify user is authenticated
  const { session } = locals.auth
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 })
  }

  // Check if client wants streaming response
  const wantsStream = url.searchParams.get("stream") === "true"

  if (wantsStream) {
    return handleStreamingRequest(request)
  }

  try {
    const { message } = await request.json()

    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 })
    }

    console.log(
      "Researcher agent request received:",
      message.substring(0, 100) + "...",
    )

    // Generate response using the agent with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () =>
          reject(
            new Error("Agent timeout after 120 seconds - try a simpler query"),
          ),
        120000,
      )
    })

    const agentPromise = companyResearcherAgent.generate([
      {
        role: "user",
        content: message,
      },
    ])

    const response: any = await Promise.race([agentPromise, timeoutPromise])

    console.log(
      "Researcher agent response generated, length:",
      response.text?.length || 0,
    )

    // Debug: Log citation patterns in the raw response
    const citationMatches = response.text?.match(/\[\d+\]/g) || []
    console.log("Citations found in raw response:", citationMatches)
    console.log(
      "First 500 chars of response:",
      response.text?.substring(0, 500),
    )

    return json({
      response: response.text,
    })
  } catch (error) {
    console.error("Error in researcher agent:", error)
    return json({ error: "Internal server error" }, { status: 500 })
  }
}

// Streaming handler for progress updates
async function handleStreamingRequest(request: Request) {
  const { message } = await request.json()

  if (!message || typeof message !== "string") {
    return json({ error: "Message is required" }, { status: 400 })
  }

  console.log(
    "Researcher streaming request received:",
    message.substring(0, 100) + "...",
  )

  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder()

      const send = (data: any) => {
        const chunk = encoder.encode(`data: ${JSON.stringify(data)}\n\n`)
        controller.enqueue(chunk)
      }

      // Start the research process
      performResearchWithProgress(message, send)
        .then(() => {
          controller.close()
        })
        .catch((error) => {
          console.error("Streaming error:", error)
          send({ error: "Research failed", details: error.message })
          controller.close()
        })
    },
  })

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    },
  })
}

async function performResearchWithProgress(
  message: string,
  send: (data: any) => void,
) {
  try {
    // Step 1: Initial Analysis
    send({
      step: 1,
      action:
        "Analyzing research request and extracting company information...",
      progress: 5,
      status: "active",
    })

    await new Promise((resolve) => setTimeout(resolve, 1500))

    send({
      step: 1,
      action: "Research scope identified",
      progress: 15,
      status: "completed",
    })

    // Step 2: Web Search
    send({
      step: 2,
      action: "Conducting comprehensive web search for company information...",
      progress: 20,
      status: "active",
    })

    await new Promise((resolve) => setTimeout(resolve, 3000))

    send({
      step: 2,
      action: "Found relevant sources and company data",
      progress: 40,
      status: "completed",
    })

    // Step 3: Financial Analysis
    send({
      step: 3,
      action: "Gathering financial performance and metrics data...",
      progress: 45,
      status: "active",
    })

    await new Promise((resolve) => setTimeout(resolve, 2500))

    send({
      step: 3,
      action: "Financial analysis completed",
      progress: 60,
      status: "completed",
    })

    // Step 4: Market Research
    send({
      step: 4,
      action: "Analyzing market position and competitive landscape...",
      progress: 65,
      status: "active",
    })

    await new Promise((resolve) => setTimeout(resolve, 2000))

    send({
      step: 4,
      action: "Market analysis and competitive research completed",
      progress: 80,
      status: "completed",
    })

    // Step 5: Report Generation
    send({
      step: 5,
      action: "Generating comprehensive research report...",
      progress: 85,
      status: "active",
    })

    // Generate the actual response
    const response = await companyResearcherAgent.generate([
      {
        role: "user",
        content: message,
      },
    ])

    send({
      step: 5,
      action: "Research report generated successfully!",
      progress: 100,
      status: "completed",
    })

    // Debug: Log citation patterns in the streaming response
    const citationMatches = response.text?.match(/\[\d+\]/g) || []
    console.log("Citations found in streaming response:", citationMatches)
    console.log(
      "First 500 chars of streaming response:",
      response.text?.substring(0, 500),
    )

    // Send the final response
    send({
      type: "final_response",
      response: response.text,
    })
  } catch (error) {
    console.error("Research process failed:", error)
    throw error
  }
}
