import { json } from '@sveltejs/kit'
import { PRIVATE_OPENAI_API_KEY } from '$env/static/private'
import type { RequestHandler } from './$types'

// Simple in-memory rate limiting
const rateLimitMap = new Map<string, { requests: number; resetTime: number }>()
const RATE_LIMIT_REQUESTS = 5 // 5 requests per window
const RATE_LIMIT_WINDOW = 60 * 60 * 1000 // 1 hour in milliseconds

// Pre-cached demo results for popular companies
const DEMO_RESULTS = {
  'stripe': {
    summary: "Stripe is positioned as the leading payment infrastructure for businesses, with strong developer-first messaging and focus on global scalability.",
    analysis: `### Executive Summary - **Brand Perception**: Stripe is recognized as the gold standard for payment infrastructure, with exceptional developer experience and global reach.

**Recent Marketing Strategy**: 
- Developer-first content marketing with comprehensive documentation
- Thought leadership in fintech and global commerce
- Partnership-driven growth with major platforms

**Key Differentiators**:
- Superior API design and developer experience
- Global payment processing capabilities
- Strong compliance and security positioning
- Comprehensive financial services suite

**Market Position**: Clear market leader in online payments infrastructure, competing primarily with PayPal, Square, and traditional payment processors.`,
    insights: {
      summary: "Stripe dominates through developer experience excellence and continuous platform expansion into financial services.",
      tags: ["Payments", "Developer Tools", "Fintech", "Global", "B2B"]
    }
  },
  'notion': {
    summary: "Notion has positioned itself as the all-in-one workspace, competing with traditional productivity tools through community-driven growth.",
    analysis: `### Executive Summary - **Brand Perception**: Notion is seen as the flexible, customizable workspace that adapts to any workflow.

**Recent Marketing Strategy**:
- Community-driven growth with template sharing
- Educational content around productivity and knowledge management
- Influencer partnerships with productivity experts
- Student and startup-friendly pricing

**Key Differentiators**:
- Blocks-based editing system
- Highly customizable workspace
- Strong template community
- Seamless database functionality

**Market Position**: Strong challenger to Microsoft Office and Google Workspace, particularly popular with startups and knowledge workers.`,
    insights: {
      summary: "Notion's community-first approach and flexibility have made it a favorite among modern knowledge workers and creative professionals.",
      tags: ["Productivity", "Workspace", "Community", "Templates", "Knowledge Management"]
    }
  }
}

function getClientIP(request: Request): string {
  return request.headers.get('x-forwarded-for')?.split(',')[0] || 
         request.headers.get('x-real-ip') || 
         'unknown'
}

function isRateLimited(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitMap.get(clientIP)
  
  if (!clientData || now > clientData.resetTime) {
    // Reset or create new entry
    rateLimitMap.set(clientIP, {
      requests: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    })
    return false
  }
  
  if (clientData.requests >= RATE_LIMIT_REQUESTS) {
    return true
  }
  
  clientData.requests++
  return false
}

function generateDemoAnalysis(company: string): any {
  const companyLower = company.toLowerCase()
  
  // Check if we have pre-cached results
  if (DEMO_RESULTS[companyLower as keyof typeof DEMO_RESULTS]) {
    return DEMO_RESULTS[companyLower as keyof typeof DEMO_RESULTS]
  }
  
  // Generate a generic demo response
  return {
    summary: `${company} is a technology company with a focus on innovation and market leadership.`,
    analysis: `### Executive Summary - **Brand Perception**: ${company} is recognized for its innovative approach and strong market presence.

**Recent Marketing Strategy**:
- Digital-first marketing approach
- Content marketing and thought leadership
- Strategic partnerships and collaborations
- Customer success stories and case studies

**Key Differentiators**:
- Innovative product offerings
- Strong customer experience
- Market leadership position
- Technology-driven solutions

**Market Position**: Well-positioned in its respective market with strong competitive advantages.

*Note: This is a demo response. Full analysis available with account registration.*`,
    insights: {
      summary: `${company} demonstrates strong market positioning through innovation and customer-centric approach.`,
      tags: ["Technology", "Innovation", "Market Leader", "Demo"]
    }
  }
}

export const POST: RequestHandler = async ({ request }) => {
  try {
    const clientIP = getClientIP(request)
    
    // Check rate limiting
    if (isRateLimited(clientIP)) {
      return json(
        { 
          error: 'Rate limit exceeded. Please try again later or sign up for unlimited access.',
          rateLimited: true 
        },
        { status: 429 }
      )
    }
    
    const { message } = await request.json()
    
    if (!message || typeof message !== 'string') {
      return json({ error: 'Invalid message format' }, { status: 400 })
    }
    
    // Simple company extraction from message
    const companyMatch = message.match(/(?:research|analyze|tell me about|information on)\s+(.+?)(?:\s|$)/i)
    const company = companyMatch?.[1]?.trim() || 'Sample Company'
    
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const result = generateDemoAnalysis(company)
    
    return json({
      response: result.analysis,
      metadata: {
        company,
        demo: true,
        insights: result.insights
      }
    })
    
  } catch (error) {
    console.error('Demo researcher error:', error)
    return json(
      { error: 'An error occurred processing your request' },
      { status: 500 }
    )
  }
}

export const GET: RequestHandler = async () => {
  return json({
    message: 'Company Researcher Demo API',
    rateLimit: {
      requests: RATE_LIMIT_REQUESTS,
      window: RATE_LIMIT_WINDOW / 1000 / 60 + ' minutes'
    },
    availableCompanies: Object.keys(DEMO_RESULTS)
  })
}