<script lang="ts">
  import "../app.css"
  import { navigating, page } from "$app/stores"
  import { expoOut } from "svelte/easing"
  import { slide } from "svelte/transition"
  import { onMount } from "svelte"

  import { setEnvironmentState } from "$lib/states"
  import ThemeProvider from "$lib/components/ThemeProvider.svelte"
  import {
    pageTransitionStore,
    autoConfigureTransition,
  } from "$lib/stores/page-transitions"
  import { enhancedFade } from "$lib/animations/svelte-transitions"
  import { staggerPageElements } from "$lib/animations/page-transitions"
  import {
    initializeAnimationPerformance,
    initializeAnimationTesting,
  } from "$lib/animations/index"

  let { children, data } = $props()
  let previousRoute = ""

  setEnvironmentState(data.environment)

  $effect(() => {
    setEnvironmentState(data.environment)
  })

  // Handle route changes for page transitions
  $effect(() => {
    if ($page.route.id) {
      const currentRoute = $page.route.id

      if (previousRoute && previousRoute !== currentRoute) {
        const { transitionType, direction } = autoConfigureTransition(
          previousRoute,
          currentRoute,
        )
        pageTransitionStore.startTransition(
          transitionType,
          direction,
          previousRoute,
          currentRoute,
        )

        // End transition after navigation completes
        setTimeout(() => {
          pageTransitionStore.endTransition()
          staggerPageElements()
        }, 150)
      }

      previousRoute = currentRoute
    }
  })

  onMount(() => {
    // Initialize animation performance monitoring and testing
    initializeAnimationPerformance()
    initializeAnimationTesting()

    // Initial page load animations
    staggerPageElements()
  })
</script>

<ThemeProvider themeCSS={data.themeCSS} />

{#if $navigating}
  <!-- Enhanced loading animation with page transition support -->
  <div
    class="fixed w-full top-0 right-0 left-0 h-1 z-50 bg-primary"
    in:slide={{ delay: 100, duration: 12000, axis: "x", easing: expoOut }}
  ></div>
{/if}

<!-- Page transition overlay -->
{#if $pageTransitionStore.isTransitioning}
  <div
    class="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm"
    in:enhancedFade={{ duration: 150 }}
    out:enhancedFade={{ duration: 150 }}
  ></div>
{/if}

<!-- Main content with page transitions -->
<main
  class="page-content"
  class:transitioning={$pageTransitionStore.isTransitioning}
>
  {@render children()}
</main>
