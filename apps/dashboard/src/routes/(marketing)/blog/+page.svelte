<script lang="ts">
  import { sortedBlogPosts, blogInfo } from "./posts"
  import * as Card from "$lib/components/ui/card"
</script>

<svelte:head>
  <title>{blogInfo.name}</title>
  <meta name="description" content="Our blog posts." />
</svelte:head>

<div class="py-8 lg:py-12 px-6 max-w-lg mx-auto">
  <div
    class="text-3xl lg:text-5xl font-medium text-primary flex gap-3 items-baseline text-center place-content-center"
  >
    <div class="text-center leading-relaxed font-bold text-primary">
      {blogInfo.name}
    </div>
    <a href="/blog/rss.xml" target="_blank" rel="noreferrer">
      <img
        class="flex-none w-5 h-5 object-contain"
        src="/images/rss.svg"
        alt="rss feed"
      />
    </a>
  </div>
  <div class="text-lg text-center">A demo blog with sample content.</div>

  {#each sortedBlogPosts as post}
    <a href={post.link}>
      <Card.Root class="my-6">
        <Card.Content class="shadow-xl p-6 flex flex-row overflow-hidden">
          <div class="flex-none w-6 md:w-32 bg-secondary"></div>
          <div class="py-6 px-6">
            <div class="text-xl">{post.title}</div>
            <div class="text-sm text-accent">
              {post.parsedDate?.toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
                year: "numeric",
              })}
            </div>
            <div class="text-muted-foreground">{post.description}</div>
          </div>
        </Card.Content>
      </Card.Root>
    </a>
  {/each}
</div>
