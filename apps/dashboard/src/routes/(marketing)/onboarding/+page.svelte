<script lang="ts">
  import { superForm } from "sveltekit-superforms/client"
  import { zod } from "sveltekit-superforms/adapters"

  import { environmentSchema } from "$lib/schemas"
  import * as Form from "$lib/components/ui/form"
  import { Input } from "$lib/components/ui/input"
  import { Button } from "$lib/components/ui/button"
  import * as Card from "$lib/components/ui/card"
  import { goto } from "$app/navigation"
  import { getEnvironmentState } from "$lib/states/environment.svelte.js"
  import { onMount } from "svelte"
  import { Loader2, AlertCircle } from "lucide-svelte"
  import { fly } from "svelte/transition"

  let { data, form: actionForm } = $props()

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let envState = getEnvironmentState()

  let envCheck = $state(true)

  const form = superForm(data.form, {
    validators: zod(environmentSchema),
  })

  onMount(() => {
    if (envState.value) {
      goto(`/dashboard/${envState.value.slug}`)
    } else {
      envCheck = false
    }
  })

  $effect(() => {
    if (actionForm?.env) {
      envState.value = actionForm.env
      goto(`/dashboard/${actionForm.env.slug}`)
    }
  })

  const { form: formData, enhance, errors, delayed } = form
</script>

<div class="min-h-screen flex items-center justify-center px-4">
  <div class="w-full max-w-[500px]">
    {#if !envCheck}
      <Card.Root class="card-brutal">
        <Card.Header>
          <Card.Title class="text-2xl font-bold text-center"
            >Create your company workspace</Card.Title
          >
        </Card.Header>
        <Card.Content>
          <form method="post" use:enhance class="space-y-4">
            <Form.Field {form} name="name">
              <Form.Control let:attrs>
                <Form.Label>Company Workspace Name</Form.Label>
                <Input {...attrs} bind:value={$formData.name} />
                <Form.FieldErrors />
              </Form.Control>
            </Form.Field>

            {#if $errors._errors}
              <div
                class="flex items-center gap-2 p-3 mt-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400"
                transition:fly={{ y: -10, duration: 300 }}
              >
                <AlertCircle size={16} class="flex-shrink-0" />
                <span>{$errors._errors[0]}</span>
              </div>
            {/if}

            <Button disabled={$delayed} type="submit" class="w-full">
              {#if $delayed}
                <Loader2 class="animate-spin mr-2" size={16} />
                Creating...
              {:else}
                Create
              {/if}
            </Button>
          </form>
        </Card.Content>
      </Card.Root>
    {:else}
      <div class="flex items-center justify-center">
        <Loader2 class="animate-spin" size={32} />
      </div>
    {/if}
  </div>
</div>
