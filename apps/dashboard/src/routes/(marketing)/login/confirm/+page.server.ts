import { otpCodeSchema } from "$lib/schemas"
import { redirect } from "@sveltejs/kit"
import { fail, setError, superValidate } from "sveltekit-superforms"
import { zod } from "sveltekit-superforms/adapters"

export const load = async ({ url }) => {
  const email = url.searchParams.get("email")
  const type = url.searchParams.get("type") || "email_change"

  if (!email) {
    return redirect(303, "/")
  }

  const form = await superValidate({ email }, zod(otpCodeSchema), {
    errors: false,
  })

  return { form, type }
}

export const actions = {
  default: async ({ request, locals: { supabase }, url }) => {
    const form = await superValidate(request, zod(otpCodeSchema))

    if (!form.valid) {
      return fail(400, { form })
    }

    // Get the type from URL params to determine OTP type
    const otpType = url.searchParams.get("type") || "email_change"

    // Map our type to Supabase OTP types
    const supabaseOtpType = otpType === "signup" ? "signup" : "email_change"

    const { error } = await supabase.auth.verifyOtp({
      type: supabaseOtpType,
      token: form.data.code,
      email: form.data.email,
    })

    if (error) {
      console.error(error)
      return setError(form, "Something went wrong", { status: 500 })
    }

    // Redirect based on the type
    if (otpType === "signup") {
      redirect(300, "/login/sign_in?verified=true")
    } else {
      redirect(300, "/")
    }
  },
}
