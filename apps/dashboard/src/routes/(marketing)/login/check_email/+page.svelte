<script lang="ts">
  import * as Card from "$lib/components/ui/card"
  import { Button } from "$lib/components/ui/button"

  let { data } = $props()
</script>

<svelte:head>
  <title>Check Your Email</title>
</svelte:head>

<Card.Root class="mt-6">
  <Card.Header>
    <Card.Title class="text-2xl font-bold text-center"
      >Check Your Email</Card.Title
    >
    <Card.Description class="text-center">
      We've sent a confirmation link to <strong>{data.email}</strong>
    </Card.Description>
  </Card.Header>
  <Card.Content class="text-center space-y-4">
    <div class="space-y-2">
      {#if data.type === "email_change"}
        <p class="text-sm text-muted-foreground">
          Please check your email and click the confirmation link to verify your
          new email address.
        </p>
        <p class="text-sm text-muted-foreground">
          You may need to confirm the change on both your old and new email
          addresses.
        </p>
      {:else}
        <p class="text-sm text-muted-foreground">
          Please check your email and click the confirmation link to verify your
          account.
        </p>
        <p class="text-sm text-muted-foreground">
          After confirming your email, you'll be able to sign in with your
          credentials.
        </p>
      {/if}
    </div>

    <div class="pt-4">
      <a href="/login/sign_in" class="w-full">
        <Button variant="outline" class="w-full">Go to Sign In</Button>
      </a>
    </div>

    <div class="text-xs text-muted-foreground">
      <p>Didn't receive the email? Check your spam folder or</p>
      <a href="/login/sign_up" class="underline hover:text-primary"
        >try signing up again</a
      >
    </div>
  </Card.Content>
</Card.Root>
