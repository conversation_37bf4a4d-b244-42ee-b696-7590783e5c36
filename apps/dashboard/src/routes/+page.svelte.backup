<svelte:head>
  <title>Robynn.ai - Strategic Marketing Leadership</title>
  <meta name="description" content="Strategic marketing leadership that scales with your ambitions. We combine seasoned CMO expertise with cutting-edge AI to transform your go-to-market strategy." />
</svelte:head>

<script lang="ts">
  import { onMount } from 'svelte';
  import { derived } from 'svelte/store';
  import { spring } from 'svelte/motion';
  import { scrollY } from '$lib/stores/scroll';
  import { 
    ArrowRight, 
    Play, 
    CheckCircle, 
    Users, 
    TrendingUp, 
    Zap, 
    Target, 
    BarChart3, 
    Sparkles, 
    ChevronDown 
  } from 'lucide-svelte';

  const isScrolled = derived(scrollY, (y) => y > 50);
  
  const heroOpacity = spring(1);
  const heroScale = spring(1);

  $: {
    heroOpacity.set(1 - Math.min($scrollY, 300) / 300);
    heroScale.set(1 - (Math.min($scrollY, 300) / 300) * 0.2);
  }

  function scrollToSection(id: string) {
    document.getElementById(id)?.scrollIntoView({ behavior: 'smooth' });
  }

  onMount(() => {
    // No need to apply theme manually - handled by unified CSS
  });
</script>

<div class="min-h-screen">

<!-- Navigation -->
<nav class="fixed top-0 w-full z-50 transition-all duration-500 {$isScrolled ? 'nav-blur' : 'bg-transparent'}">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="flex items-center justify-between h-16">
      <div class="flex items-center space-x-2">
        <div class="w-8 h-8 flex items-center justify-center bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm">
          <span class="font-bold text-sm">R</span>
        </div>
        <span class="text-xl font-bold text-foreground">Robynn.ai</span>
      </div>
      
      <div class="hidden md:flex items-center space-x-8">
        <button 
          on:click={() => scrollToSection('approach')} 
          class="font-medium transition-colors hover:opacity-70"
          style="color: var(--muted-foreground);"
        >
          Approach
        </button>
        <button 
          on:click={() => scrollToSection('results')} 
          class="font-medium transition-colors hover:opacity-70"
          style="color: var(--muted-foreground);"
        >
          Results
        </button>
        <button 
          on:click={() => scrollToSection('stories')} 
          class="font-medium transition-colors hover:opacity-70"
          style="color: var(--muted-foreground);"
        >
          Stories
        </button>
        <a href="/login" class="btn-primary px-6 py-2 font-semibold">
          Start Your Journey
        </a>
      </div>
    </div>
  </div>
</nav>

<!-- Hero Section -->
<section 
  class="relative min-h-screen flex items-center justify-center overflow-hidden"
  style="opacity: {$heroOpacity}; transform: scale({$heroScale});"
>
  <!-- Background Elements -->
  <div class="absolute inset-0" style="background: var(--background);"></div>
  <div 
    class="absolute top-20 right-20 w-72 h-72 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob border-2"
    style="background: var(--secondary); border-color: var(--border);"
  ></div>
  <div 
    class="absolute top-40 left-20 w-72 h-72 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000 border-2"
    style="background: var(--accent); border-color: var(--border);"
  ></div>
  <div 
    class="absolute bottom-20 left-1/2 w-72 h-72 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000 border-2"
    style="background: var(--primary); border-color: var(--border);"
  ></div>

  <div class="relative z-10 max-w-7xl mx-auto px-6 lg:px-8 grid lg:grid-cols-2 gap-12 items-center">
    <div>
      <div 
        class="inline-flex items-center px-4 py-2 text-sm font-bold mb-6 border-2"
        style="background: var(--secondary); color: var(--secondary-foreground); border-color: var(--border); box-shadow: var(--shadow-sm);"
      >
        <Sparkles class="w-4 h-4 mr-2" />
        Strategic Marketing Leadership
      </div>
      
      <h1 class="text-5xl lg:text-7xl font-black leading-tight mb-6" style="color: var(--foreground);">
        The CMO you've been{' '}
        <span class="gradient-text-primary">
          looking for
        </span>
      </h1>
      
      <p class="text-xl leading-relaxed mb-8 max-w-lg font-medium" style="color: var(--muted-foreground);">
        Strategic marketing leadership that scales with your ambitions. 
        We combine seasoned CMO expertise with cutting-edge AI to transform 
        your go-to-market strategy.
      </p>
      
      <div class="flex flex-col sm:flex-row gap-4">
        <a href="/login" class="btn-primary px-8 py-4 font-bold flex items-center justify-center group">
          Start Your Growth Story
          <ArrowRight class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
        </a>
        
        <a href="/login/sign_in" class="btn-secondary px-8 py-4 font-bold flex items-center justify-center">
          <Play class="mr-2 w-5 h-5" />
          Sign In
        </a>
      </div>
      
      <div 
        class="flex items-center mt-8 text-sm font-medium"
        style="color: var(--muted-foreground);"
      >
        <div class="flex -space-x-2 mr-3">
          <div 
            class="w-8 h-8 border-2"
            style="background: var(--primary); border-color: var(--background);"
          ></div>
          <div 
            class="w-8 h-8 border-2"
            style="background: var(--accent); border-color: var(--background);"
          ></div>
          <div 
            class="w-8 h-8 border-2"
            style="background: var(--secondary); border-color: var(--background);"
          ></div>
        </div>
        Trusted by 50+ scaling startups
      </div>
    </div>

    <div class="relative">
      <!-- Dashboard Mockup -->
      <div class="dashboard-card p-8">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-bold" style="color: var(--foreground);">Growth Dashboard</h3>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 border" style="background: var(--destructive); border-color: var(--border);"></div>
            <div class="w-3 h-3 border" style="background: var(--secondary); border-color: var(--border);"></div>
            <div class="w-3 h-3 border" style="background: var(--primary); border-color: var(--border);"></div>
          </div>
        </div>
        
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="metric-card p-4" style="background: var(--primary);">
            <TrendingUp class="w-6 h-6 mb-2" style="color: var(--primary-foreground);" />
            <div class="text-2xl font-black" style="color: var(--primary-foreground);">+300%</div>
            <div class="text-sm font-bold" style="color: var(--primary-foreground);">Pipeline Growth</div>
          </div>
          
          <div class="metric-card p-4" style="background: var(--accent);">
            <Target class="w-6 h-6 mb-2" style="color: var(--accent-foreground);" />
            <div class="text-2xl font-black" style="color: var(--accent-foreground);">-60%</div>
            <div class="text-sm font-bold" style="color: var(--accent-foreground);">CAC Reduction</div>
          </div>
        </div>
        
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium" style="color: var(--muted-foreground);">Email Campaigns</span>
            <span class="text-sm font-bold" style="color: var(--foreground);">94%</span>
          </div>
          <div class="progress-bar w-full h-3">
            <div class="progress-fill h-3 w-[94%]"></div>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium" style="color: var(--muted-foreground);">Lead Quality</span>
            <span class="text-sm font-bold" style="color: var(--foreground);">87%</span>
          </div>
          <div class="progress-bar w-full h-3">
            <div class="progress-fill h-3 w-[87%]"></div>
          </div>
        </div>
      </div>
      
      <!-- Floating Elements -->
      <div class="floating-element absolute -top-4 -right-4 p-4">
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 animate-pulse" style="background: var(--primary);"></div>
          <span class="text-sm font-bold" style="color: var(--foreground);">Live Insights</span>
        </div>
      </div>
      
      <div class="floating-element absolute -bottom-4 -left-4 p-4">
        <div class="flex items-center space-x-2">
          <Zap class="w-4 h-4" style="color: var(--secondary);" />
          <span class="text-sm font-bold" style="color: var(--foreground);">AI-Powered</span>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Scroll Indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2">
    <ChevronDown class="w-6 h-6 animate-bounce" style="color: var(--muted-foreground);" />
  </div>
</section>

<!-- Problem Section -->
<section class="py-24 section-muted">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl lg:text-5xl font-black mb-6" style="color: var(--foreground);">
        You've built something{' '}
        <span class="gradient-text-secondary">
          remarkable
        </span>
      </h2>
      <p class="text-xl max-w-3xl mx-auto leading-relaxed font-medium" style="color: var(--muted-foreground);">
        You're past the early stage hustle. Your product works. Your customers love it. 
        You've hit that magical 5M+ revenue milestone. But now you're facing a new challenge.
      </p>
    </div>

    <div class="grid lg:grid-cols-3 gap-8">
      {#each [
        {
          icon: Users,
          title: "Expensive Team Building",
          description: "VP of Marketing + team costs $500K+ annually",
          color: "var(--destructive)"
        },
        {
          icon: BarChart3,
          title: "Scattered Marketing",
          description: "Tactics without strategy, campaigns without cohesion",
          color: "var(--primary)"
        },
        {
          icon: Target,
          title: "Unclear ROI",
          description: "Spending money without knowing what's working",
          color: "var(--secondary)"
        }
      ] as item}
        <div class="card-brutal p-8">
          <div 
            class="w-12 h-12 flex items-center justify-center mb-6 border-2"
            style="background: {item.color}; border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <svelte:component this={item.icon} class="w-6 h-6" style="color: var(--background);" />
          </div>
          <h3 class="text-xl font-bold mb-4" style="color: var(--foreground);">{item.title}</h3>
          <p class="leading-relaxed font-medium" style="color: var(--muted-foreground);">{item.description}</p>
        </div>
      {/each}
    </div>
  </div>
</section>

<!-- Approach Section -->
<section id="approach" class="py-24" style="background: var(--background);">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl lg:text-5xl font-black mb-6" style="color: var(--foreground);">
        Think different about{' '}
        <span class="gradient-text-primary">
          marketing leadership
        </span>
      </h2>
      <p class="text-xl max-w-3xl mx-auto leading-relaxed font-medium" style="color: var(--muted-foreground);">
        While others offer fractional hours, we offer strategic partnership. 
        Our CMO-led team doesn't just execute your ideas—we help shape your entire go-to-market strategy.
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-center">
      <div class="space-y-8">
        {#each [
          {
            icon: Users,
            title: "Strategic Partnership",
            description: "Your marketing co-founder, not your vendor"
          },
          {
            icon: Zap,
            title: "AI-Native Approach",
            description: "Built for efficiency, powered by intelligence"
          },
          {
            icon: TrendingUp,
            title: "Measurable Results",
            description: "Pipeline growth you can track and predict"
          }
        ] as item}
          <div class="flex items-start space-x-4">
            <div 
              class="w-12 h-12 flex items-center justify-center flex-shrink-0 border-2"
              style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
            >
              <svelte:component this={item.icon} class="w-6 h-6" style="color: var(--primary-foreground);" />
            </div>
            <div>
              <h3 class="text-xl font-bold mb-2" style="color: var(--foreground);">{item.title}</h3>
              <p class="leading-relaxed font-medium" style="color: var(--muted-foreground);">{item.description}</p>
            </div>
          </div>
        {/each}
      </div>

      <div class="relative">
        <div 
          class="p-8 border-2"
          style="background: var(--foreground); border-color: var(--border); box-shadow: var(--shadow-lg);"
        >
          <h3 class="text-2xl font-black mb-6" style="color: var(--background);">Strategic Leadership, Not Just Services</h3>
          <p class="mb-6 leading-relaxed font-medium" style="color: var(--muted);">
            You get the strategic thinking of a seasoned CMO, the execution power of a full marketing team, 
            and the efficiency of AI-native tools. All without the politics, overhead, or six-figure salaries.
          </p>
          
          <div class="space-y-4">
            {#each [
              "Comprehensive GTM strategy development",
              "Product-led growth optimization",
              "AI-powered campaign automation",
              "Real-time performance analytics"
            ] as item}
              <div class="flex items-center space-x-3">
                <CheckCircle class="w-5 h-5 flex-shrink-0" style="color: var(--secondary);" />
                <span class="font-medium" style="color: var(--muted);">{item}</span>
              </div>
            {/each}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Results Section -->
<section id="results" class="py-24 section-muted">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl lg:text-5xl font-black mb-6" style="color: var(--foreground);">
        Growth you can{' '}
        <span class="gradient-text-primary">
          measure
        </span>
        . Impact you can feel.
      </h2>
      <p class="text-xl max-w-3xl mx-auto leading-relaxed font-medium" style="color: var(--muted-foreground);">
        Our clients typically see 40% improvement in marketing efficiency within 90 days, 
        60% reduction in customer acquisition costs within 6 months, and 3x pipeline growth within the first year.
      </p>
    </div>

    <div class="grid md:grid-cols-3 gap-8">
      {#each [
        {
          metric: "3x",
          label: "Pipeline Growth",
          icon: TrendingUp,
          color: "var(--primary)"
        },
        {
          metric: "60%",
          label: "CAC Reduction",
          icon: Target,
          color: "var(--accent)"
        },
        {
          metric: "45%",
          label: "Faster Sales Cycles",
          icon: Zap,
          color: "var(--secondary)"
        }
      ] as item}
        <div class="text-center">
          <div 
            class="w-20 h-20 flex items-center justify-center mx-auto mb-6 border-2"
            style="background: {item.color}; border-color: var(--border); box-shadow: var(--shadow-lg);"
          >
            <svelte:component this={item.icon} class="w-10 h-10" style="color: var(--background);" />
          </div>
          <div class="text-5xl font-black mb-2" style="color: var(--foreground);">{item.metric}</div>
          <div class="text-lg font-bold" style="color: var(--muted-foreground);">{item.label}</div>
        </div>
      {/each}
    </div>
  </div>
</section>

<!-- Stories Section -->
<section id="stories" class="py-24" style="background: var(--background);">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl lg:text-5xl font-black mb-6" style="color: var(--foreground);">
        Real companies.{' '}
        <span class="gradient-text-secondary">
          Real results
        </span>
        .
      </h2>
      <p class="text-xl max-w-3xl mx-auto leading-relaxed font-medium" style="color: var(--muted-foreground);">
        See how we've helped AI and technology companies transform their go-to-market strategy 
        and achieve remarkable growth.
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-12">
      {#each [
        {
          company: "MedAI Solutions",
          industry: "Healthcare AI Startup - Series A",
          logo: "M",
          story: "A brilliant healthcare AI startup with superior diagnostic technology couldn't break through in a crowded market. We repositioned them around \"Diagnostic Velocity\" and built a comprehensive GTM strategy.",
          results: [
            { metric: "300%", label: "Pipeline Growth" },
            { metric: "60%", label: "Shorter Sales Cycle" }
          ],
          quote: "Working with Robynn was like having a strategic co-founder who understood both our technology and our market better than we did.",
          author: "Dr. Sarah Chen, CEO"
        },
        {
          company: "Nexus AI Services",
          industry: "AI Services Platform - Series A",
          logo: "N",
          story: "A successful Canadian AI services company was struggling to gain traction in the US market. We rebuilt their market entry strategy from the ground up.",
          results: [
            { metric: "$8M", label: "US Pipeline" },
            { metric: "150%", label: "Goal Exceeded" }
          ],
          quote: "They transformed our US market entry from a costly experiment into our fastest-growing revenue stream.",
          author: "Marcus Thompson, CEO"
        }
      ] as story}
        <div class="card-brutal p-8" style="background: var(--muted);">
          <div class="flex items-center mb-6">
            <div 
              class="w-12 h-12 flex items-center justify-center font-black text-lg mr-4 border-2"
              style="background: var(--primary); color: var(--primary-foreground); border-color: var(--border); box-shadow: var(--shadow-sm);"
            >
              {story.logo}
            </div>
            <div>
              <h3 class="text-xl font-black" style="color: var(--foreground);">{story.company}</h3>
              <p class="text-sm font-medium" style="color: var(--muted-foreground);">{story.industry}</p>
            </div>
          </div>
          
          <p class="mb-6 leading-relaxed font-medium" style="color: var(--foreground);">{story.story}</p>
          
          <div class="grid grid-cols-2 gap-4 mb-6">
            {#each story.results as result}
              <div 
                class="p-4 text-center border-2"
                style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"
              >
                <div class="text-2xl font-black" style="color: var(--foreground);">{result.metric}</div>
                <div class="text-sm font-bold" style="color: var(--muted-foreground);">{result.label}</div>
              </div>
            {/each}
          </div>
          
          <blockquote 
            class="pl-4 italic mb-4 border-l-4 font-medium"
            style="border-color: var(--primary); color: var(--foreground);"
          >
            "{story.quote}"
          </blockquote>
          <cite class="text-sm font-bold" style="color: var(--muted-foreground);">— {story.author}</cite>
        </div>
      {/each}
    </div>
  </div>
</section>

<!-- Team Section -->
<section class="py-24 section-muted">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl lg:text-5xl font-black mb-6" style="color: var(--foreground);">
        The{' '}
        <span class="gradient-text-primary">
          Robynn Team
        </span>
      </h2>
      <p class="text-xl max-w-3xl mx-auto leading-relaxed font-medium" style="color: var(--muted-foreground);">
        Meet the strategic minds behind your growth. We're not just marketers—we're growth architects 
        with deep expertise in AI, technology, and scaling businesses.
      </p>
    </div>

    <div class="grid lg:grid-cols-3 gap-8">
      {#each [
        {
          name: "Sarah Chen",
          role: "Strategic Marketing Leader",
          bio: "Former VP Marketing at two unicorn startups. 15+ years scaling B2B SaaS companies from $5M to $100M+ ARR. Expert in product-led growth and AI-native marketing strategies.",
          expertise: ["Go-to-Market Strategy", "Product-Led Growth", "AI Marketing"]
        },
        {
          name: "Marcus Rodriguez",
          role: "Growth Operations Director",
          bio: "Former Head of Growth at Series B AI company. Built marketing automation systems that scaled 10x without proportional team growth. Expert in RevOps and marketing technology.",
          expertise: ["Marketing Automation", "Revenue Operations", "Growth Analytics"]
        },
        {
          name: "Dr. Emily Watson",
          role: "AI Strategy Consultant",
          bio: "PhD in Computer Science, former ML researcher at Google. Bridges the gap between AI capabilities and marketing applications. Expert in AI-powered customer insights and predictive modeling.",
          expertise: ["AI Implementation", "Predictive Analytics", "Customer Intelligence"]
        }
      ] as member}
        <div class="card-brutal p-8" style="background: var(--background);">
          <!-- Profile Image Placeholder -->
          <div 
            class="w-32 h-32 mx-auto mb-6 border-2 flex items-center justify-center text-4xl font-black"
            style="background: var(--primary); color: var(--primary-foreground); border-color: var(--border); box-shadow: var(--shadow-lg);"
          >
            {member.name.split(' ').map(n => n[0]).join('')}
          </div>
          
          <div class="text-center mb-6">
            <h3 class="text-xl font-black mb-2" style="color: var(--foreground);">{member.name}</h3>
            <p class="text-sm font-bold" style="color: var(--primary);">{member.role}</p>
          </div>
          
          <p class="mb-6 leading-relaxed font-medium text-sm" style="color: var(--muted-foreground);">
            {member.bio}
          </p>
          
          <div class="space-y-2">
            <h4 class="text-sm font-bold" style="color: var(--foreground);">Expertise:</h4>
            <div class="flex flex-wrap gap-2">
              {#each member.expertise as skill}
                <span 
                  class="px-3 py-1 text-xs font-bold border-2"
                  style="background: var(--secondary); color: var(--secondary-foreground); border-color: var(--border); box-shadow: var(--shadow-xs);"
                >
                  {skill}
                </span>
              {/each}
            </div>
          </div>
        </div>
      {/each}
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-24 section-dark">
  <div class="max-w-7xl mx-auto px-6 lg:px-8 text-center">
    <div>
      <h2 class="text-4xl lg:text-5xl font-black mb-6" style="color: var(--background);">
        Your next chapter starts with a{' '}
        <span class="gradient-text-secondary">
          conversation
        </span>
      </h2>
      <p class="text-xl max-w-3xl mx-auto mb-12 leading-relaxed font-medium" style="color: var(--muted);">
        We don't believe in hard sells or high-pressure tactics. We believe in finding the right fit. 
        If you're ready to transform your marketing from a cost center into a growth engine, let's talk.
      </p>
      
      <div class="flex flex-col sm:flex-row gap-6 justify-center">
        <a 
          href="/contact_us"
          class="px-8 py-4 font-bold flex items-center justify-center border-2 transition-all hover:scale-105 hover:translate-x-[-2px] hover:translate-y-[-2px]"
          style="background: var(--background); color: var(--foreground); border-color: var(--background); box-shadow: var(--shadow);"
        >
          Schedule Your Discovery Call
          <ArrowRight class="ml-2 w-5 h-5" />
        </a>
        
        <a 
          href="/login"
          class="px-8 py-4 font-bold border-2 transition-all hover:scale-105 hover:translate-x-[-2px] hover:translate-y-[-2px]"
          style="background: transparent; color: var(--background); border-color: var(--background); box-shadow: var(--shadow);"
        >
          Get Started
        </a>
      </div>
      
      <p class="mt-8 font-bold" style="color: var(--muted);">Ready to grow smarter, not just faster?</p>
    </div>
  </div>
</section>

<!-- Footer -->
<footer class="py-12 border-t-2" style="background: var(--foreground); border-color: var(--border);">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <div 
          class="w-8 h-8 flex items-center justify-center border-2"
          style="background: var(--primary); border-color: var(--background); box-shadow: var(--shadow-xs);"
        >
          <span style="color: var(--primary-foreground);" class="font-bold text-sm">R</span>
        </div>
        <span class="text-xl font-black" style="color: var(--background);">Robynn.ai</span>
      </div>
      
      <div class="flex items-center space-x-6">
        <a 
          href="mailto:<EMAIL>" 
          class="transition-colors hover:opacity-70 font-medium"
          style="color: var(--muted);"
        >
          <EMAIL>
        </a>
        <p class="text-sm font-medium" style="color: var(--muted);">
          © 2025 Robynn.ai. Built for the next generation of growth leaders.
        </p>
      </div>
    </div>
  </div>
</footer>

</div>
